image: alpine/java:21-jdk

stages:
  - build_deploy

build-job:
  stage: build_deploy
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: always
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
      when: always
    - when: never
  before_script:
    - 'command -v base64 > /dev/null || ( apk add --no-cache base64 )'
    - 'command -v mvn > /dev/null || ( apk add --no-cache maven )'
    - 'command -v ssh-agent >/dev/null || ( apk add --no-cache openssh-client )'
    - 'command -v base64 > /dev/null || ( apk add --no-cache base64 )'
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | base64 -d | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - echo "$SSH_KNOWN_HOSTS" >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
    - '[[ ! -d ~/.m2 ]] && mkdir ~/.m2'
    - echo "$M2_SETTINGS" | base64 -d > ~/.m2/settings.xml
  script:
    - cd ws_dundas_sp && mvn package
    - WAR_FILE=$(ls target/*.war|tail -n 1|cut -d/ -f 2)
    - echo $WAR_FILE
    - WAR_NAME=$(echo $WAR_FILE | rev | cut -d'.' -f2- | rev)
    - echo $WAR_NAME
    - scp -o StrictHostKeyChecking=no -r -P 28 "target/$WAR_FILE"  admin@***********:/tmp/
    - ssh admin@*********** -p 28 "/opt/payara/payara6/bin/asadmin --user=admin undeploy $WAR_NAME || echo 'Undeploy failed or application not found, continuing...'"
    - ssh admin@*********** -p 28 "/opt/payara/payara6/bin/asadmin --user=admin deploy --type war --force=true /tmp/$WAR_FILE"
