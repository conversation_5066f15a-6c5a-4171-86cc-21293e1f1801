@host={{$dotenv TEST_HOST}}
@token={{$dotenv TEST_TOKEN}}

### GET: Single Life Quote from a single company for all products
### Endpoint:
### GET http://{{host}}/webresources/lifeSingleCompAll/{face_amount}/{date_of_birth}/{gender}/{smoking}/{province_code}/{company_id}
###
### Path Parameters:
### - face_amount     (e.g. 500000)        → Coverage amount in canadian dollars
### - date_of_birth   (e.g. 1979-01-01)    → Date of birth in yyyy-MM-dd format
### - gender          (M / F)              → Gender: M = Male, F = Female
### - smoking         (Y / N)              → Smoking status: Y = Yes, N = No
### - province_code   (e.g. ON)            → Province code (Canada)
### - company_id      (e.g. 29)            → Carrier/company ID
###
### token: {{token}}                       → Authorization token
###
### Sample Request:
GET http://{{host}}/webresources/lifeSingleCompAll/100000/1979-01-01/F/N/AB/12
Content-Type: application/json
token: {{token}}


### GET: Single life quote from a single company for a specific product
### Endpoint:
### GET http://{{host}}/webresources/lifeSingleProd/{face_amount}/{date_of_birth}/{gender}/{smoking}/{province_code}/{company_id}/{prod_id}
###
### Path Parameters:
### - face_amount     (e.g. 500000)        → Coverage amount in canadian dollars
### - date_of_birth   (e.g. 1979-01-01)    → Date of birth in yyyy-MM-dd format
### - gender          (M / F)              → Gender: M = Male, F = Female
### - smoking         (Y / N)              → Smoking status: Y = Yes, N = No
### - province_code   (e.g. QC)            → Province code (Canada)
### - company_id      (e.g. 485)           → Carrier/company ID
### - prod_id         (e.g. 7451)          → Product ID
###
### Headers:
### - token: {{token}}                     → Authorization token
###
### Sample Request:
GET http://{{host}}/webresources/lifeSingleProd/500000/1979-01-01/M/N/QC/82/8964
Content-Type: application/json
token: {{token}}


### GET: All insurance company names
###
### Headers:
### - token: {{token}}                     → Authorization token
###
### Sample Request:
GET http://{{host}}/webresources/info/insurance-companies
Content-Type: application/json
token: {{token}}

### GET: All single life insurance or critical insurance company names with condition
### GET http://{{host}}/webresources/info/insurance-companies/{date_of_birth}/{face_amount}/{type_class}
### - face_amount     (e.g. 500000)        → Coverage amount in canadian dollars
### - date_of_birth   (e.g. 1979-01-01)    → Date of birth in yyyy-MM-dd format
### - type_class      (e.g. LIFE OR CRIT)  → Life or Critical illness insurance 
###
### Headers:
### - token: {{token}}                     → Authorization token
###
### Sample Request:
GET http://{{host}}/webresources/info/insurance-companies/1985-01-01/150000/CRIT
Content-Type: application/json
token: {{token}}

### GET: Joint Life product types from multiple companies by conditions
### Endpoint:
### GET http://{{host}}/webresources/jointProductTypes/{face_amount}/{client1DOB}/{client2DOB}/{jointType}/{levelOrDecr}
###
### Path Parameters:
### - face_amount     (e.g. 500000)        → Coverage amount in canadian dollars
### - date_of_birth   (e.g. 1979-01-01)    → Date of birth in yyyy-MM-dd format
### - jointType      (F / L)              → F=joint first to die L=joint last to die
### - levelOrDecr	L / D	Coverage type: L = Level, D = Decreasing
### 
###
### Headers:
### - token: {{token}}                     → Authorization token
###
### Sample Request:
GET http://{{host}}/webresources/info/jointProductTypes/250000/1990-05-02/1990-05-02/L/L
Content-Type: application/json
token: {{token}}

### GET: Joint Life Quote from multiple companies for Specific Product Type
### Endpoint:
### GET http://{{host}}/webresources/lifeJointMulti/{product_type_id}/{face_amount}/{province_code}/{client1DOB}/{client1Gender}/{client1Smoking}/{healthClass1}/{client2DOB}/{client2Gender}/{client2Smoking}/{healthClass2}/{jointType}/{levelOrDecr}
###
### Path Parameters:
### - face_amount     (e.g. 500000)        → Coverage amount in canadian dollars
### - date_of_birth   (e.g. 1979-01-01)    → Date of birth in yyyy-MM-dd format
### - gender          (M / F)              → Gender: M = Male, F = Female
### - smoking         (Y / N)              → Smoking status: Y = Yes, N = No
### - province_code   (e.g. QC)            → Province code (Canada)
### - jointType      (F / L)              → F=joint first to die L=joint last to die
### - levelOrDecr	L / D	Coverage type: L = Level, D = Decreasing
### 
###
### Headers:
### - token: {{token}}                     → Authorization token
###
### Sample Request:
GET http://{{host}}/webresources/lifeJointMulti/86/250000/QC/1990-05-02/M/Y/S/1990-05-02/M/Y/S/F/L
Content-Type: application/json
token: {{token}}

### GET: Joint Life Quote from single company for Specific Product
### Endpoint:
### GET http://{{host}}/webresources/lifeJointSingleProd/{face_amount}/{province_code}/{client1DOB}/{client1Gender}/{client1Smoking}/{client2DOB}/{client2Gender}/{client2Smoking}/{jointType}/{company_id}/{prod_id}
###
###
### Path Parameters:
### - face_amount     (e.g. 500000)        → Coverage amount in canadian dollars
### - date_of_birth   (e.g. 1979-01-01)    → Date of birth in yyyy-MM-dd format
### - gender          (M / F)              → Gender: M = Male, F = Female
### - smoking         (Y / N)              → Smoking status: Y = Yes, N = No
### - province_code   (e.g. ON)            → Province code (Canada)
### - jointType      (F / L)              → F=joint first to die L=joint last to die
### - company_id      (e.g. 29)            → Carrier/company ID
### - prod_id         (e.g. 7451)          → Product ID
###
### token: {{token}}                       → Authorization token
###
### Sample Request:
GET http://{{host}}/webresources/lifeJointSingleProd/250000/QC/1990-05-02/M/N/1990-05-02/M/N/F/12/8927
Content-Type: application/json
token: {{token}}


### POST: Create a new insurance lead
### Endpoint: POST http://{{host}}/api/v1/leads
###
### Headers:
### - Content-Type: application/json  → Specifies that the request body is in JSON format.
### - token: {{token}}                       → Authorization token#
### Body (JSON):
### - companyId         (integer)       → ID of the insurance company.
### - productId         (integer)       → ID of the specific insurance product.
### - insuranceAmount   (number)        → The requested coverage amount.
### - annualPremium     (number)        → The calculated annual premium for the quote.
### - monthlyPremium    (number)        → The calculated monthly premium for the quote.
### - primaryInsured    (object)        → Information about the primary person to be insured.
###   - firstName       (string)        → First name of the primary insured.
###   - lastName        (string)        → Last name of the primary insured.
###   - dateOfBirth     (string)        → Date of birth (YYYY-MM-DD) of the primary insured.
###   - gender          (string)        → Gender of the primary insured (e.g., "MALE", "FEMALE").
### - secondaryInsured  (object, optional) → Information about the secondary person (for joint policies).
###   - firstName       (string)        → First name of the secondary insured.
###   - lastName        (string)        → Last name of the secondary insured.
###   - dateOfBirth     (string)        → Date of birth (YYYY-MM-DD) of the secondary insured.
###   - gender          (string)        → Gender of the secondary insured (e.g., "MALE", "FEMALE").
### - provinceCode      (string)        → Province code (e.g., "ON", "QC").
### - email             (string)        → Contact email address for the lead.
### - phoneNumber       (string)        → Contact phone number for the lead.
### - languagePreference(string)        → Preferred language for communication (e.g., "ENGLISH", "FRENCH").
### - message           (string, optional) → Any additional message or notes from the lead.
###
### Sample Request:
POST http:///{{host}}/api/v1/leads
Content-Type: application/json
token: {{token}}
# {
#   "companyId": 1,
#   "productId": 100,
#   "insuranceAmount": 1000000.00,
#   "annualPremium": 1200.00,
#   "monthlyPremium": 100.00,
#   "primaryInsured": {
#     "firstName": "John",
#     "lastName": "Doe",
#     "dateOfBirth": "1990-01-01",
#     "gender": "MALE"
#   },
#   "secondaryInsured": {
#     "firstName": "Jane",
#     "lastName": "Doe",
#     "dateOfBirth": "1991-02-02",
#     "gender": "FEMALE"
#   },
#   "provinceCode": "ON",
#   "email": "<EMAIL>",
#   "phoneNumber": "************",
#   "languagePreference": "ENGLISH",
#   "message": "Insurance inquiry for family coverage"
# }

### GET: Critical illness insurance quote from a single company for all products
### Endpoint:
### GET http://{{host}}/webresources/critSingleCompAll/{face_amount}/{date_of_birth}/{gender}/{smoking}/{province_code}/{company_id}/{health_class}
###
### Path Parameters:
### - face_amount     (e.g. 500000)        → Coverage amount in canadian dollars
### - date_of_birth   (e.g. 1979-01-01)    → Date of birth in yyyy-MM-dd format
### - gender          (M / F)              → Gender: M = Male, F = Female
### - smoking         (Y / N)              → Smoking status: Y = Yes, N = No
### - province_code   (e.g. ON)            → Province code (Canada)
### - company_id      (e.g. 29)            → Carrier/company ID
### - health_class    (e.g. S)             → client health class, default S for standard
###
### token: {{token}}                       → Authorization token
###
### Sample Request:
GET http://{{host}}/webresources/critSingleCompAll/150000/1985-01-01/M/N/QC/77/S
Content-Type: application/json
token: {{token}}


### GET: Critical illness insurance quote from a single company for a specific product
### Endpoint:
### GET http://{{host}}/webresources/critSingleProd/{face_amount}/{date_of_birth}/{gender}/{smoking}/{province_code}/{company_id}/{prod_id}
###
### Path Parameters:
### - face_amount     (e.g. 150000)        → Coverage amount in canadian dollars
### - date_of_birth   (e.g. 1985-01-01)    → Date of birth in yyyy-MM-dd format
### - gender          (M / F)              → Gender: M = Male, F = Female
### - smoking         (Y / N)              → Smoking status: Y = Yes, N = No
### - province_code   (e.g. QC)            → Province code (Canada)
### - company_id      (e.g. 77)            → Carrier/company ID
### - prod_id         (e.g. 8443)          → Product ID
###
### token: {{token}}                       → Authorization token
###
### Sample Request:
GET http://{{host}}/webresources/critSingleProd/150000/1985-01-01/M/N/QC/77/8443
Content-Type: application/json
token: {{token}}


### GET: Life or Critical illness insurance quote from multiple companies
### Endpoint:
### GET http://{{host}}/webresources/multiCompAll/{face_amount}/{date_of_birth}/{gender}/{smoking}/{province_code}/{levelOrDecr}/{prodClass}
###
### Path Parameters:
### - face_amount     (e.g. 150000)        → Coverage amount in canadian dollars
### - date_of_birth   (e.g. 1985-01-01)    → Date of birth in yyyy-MM-dd format
### - gender          (M / F)              → Gender: M = Male, F = Female
### - smoking         (Y / N)              → Smoking status: Y = Yes, N = No
### - province_code   (e.g. QC)            → Province code (Canada)
### - levelOrDecr	    (L / D) 	           → Coverage type: L = Level, D = Decreasing
### - prodClass       (e.g. LIFE OR CRIT)  → Life or Critical illness insurance
###
### token: {{token}}                       → Authorization token
###
### Sample Request:
GET http://{{host}}/webresources/multiCompAll/275000/1986-07-24/M/N/QC/L/CRIT
Content-Type: application/json
token: {{token}}