# Database Schema Analysis for Advisor Profile API

## Overview

This document provides a detailed analysis of the actual SKYTEST database schema for the Advisor Profile API implementation. This analysis is based on the complete schema file (`sql_schema.txt`) and corrects previous implementation errors.

**CRITICAL**: All field names, data types, and relationships in this document are based on the actual database schema and must be used exactly as specified.

## Core Tables

### ADVISOR Table
- **Primary Key**: `ADVISOR_INT_ID` (NUMBER(8))
- **Foreign Key**: `ADVISOR_INT_ID` references `CONTACT.CONTACT_INT_ID`
- **Key Fields**:
  - `ADVISOR_ID` (NUMBER(8)) - Business identifier
  - `NAME` (VARCHAR2(256)) - Advisor name
  - `STATUS` (NUMBER(4)) - Status code
  - `ADVISOR_TYPE` (NUMBER(4)) - Type code
  - `CREATION_DATE` (DATE)
  - `LAST_MODIFICATION_DATE` (DATE)
  - `ADVISOR_NUMBER` (VARCHAR2(15)) - Business number
  - `NOTE` (VARCHAR2(1024)) - Notes
  - `SIN` (VARCHAR2(11)) - Social Insurance Number
  - `CORPORATE_NAME` (VARCHAR2(256)) - Corporate name
  - `MASTER_CODE` (VARCHAR2(16)) - Master code

### CONTACT Table
- **Primary Key**: `CONTACT_INT_ID` (NUMBER(11))
- **Key Fields**:
  - `FIRSTNAME` (VARCHAR2(35))
  - `MIDDLENAME` (VARCHAR2(35))
  - `LASTNAME` (VARCHAR2(55))
  - `BIRTH_DATE` (DATE)
  - `GENDER` (NUMBER(4))
  - `PREFERRED_LANGUAGE` (NUMBER(4))
  - `SALUTATION` (NUMBER)
  - `CONTACT_TYPE` (NUMBER(4))
  - `JOB_TITLE` (VARCHAR2(100))
  - `JOB_ROLE` (VARCHAR2(100))
  - `MARITAL_STATUS` (NUMBER(8))
  - `INCOME` (NUMBER(19,4))
  - `MASTER_CODE` (VARCHAR2(32))

### USERS Table
- **Primary Key**: `USER_INT_ID` (NUMBER(8))
- **Foreign Key**: `USER_INT_ID` references `CONTACT.CONTACT_INT_ID`
- **Key Fields**:
  - `USERNAME` (VARCHAR2(128))
  - `ACTIVE` (VARCHAR2(1))
  - `USER_TYPE` (NUMBER(4))
  - `MASTER_CODE` (VARCHAR2(16))

## Critical Relationships

### Primary Relationships
1. **ADVISOR ↔ CONTACT**: `ADVISOR.ADVISOR_INT_ID = CONTACT.CONTACT_INT_ID`
   - This is a 1:1 relationship where the advisor's primary key references the contact
2. **USERS ↔ CONTACT**: `USERS.USER_INT_ID = CONTACT.CONTACT_INT_ID`
   - This is a 1:1 relationship where the user's primary key references the contact

### Many-to-Many Relationships (via Junction Tables)
1. **CONTACT ↔ ADDRESS**: via `CONTACT_ADDRESS` junction table
2. **CONTACT ↔ PHONE**: via `CONTACT_PHONE` junction table
3. **CONTACT ↔ EMAIL**: via `CONTACT_EMAIL` junction table

## Supporting Tables

### ADDRESS Table
- **Primary Key**: `ADDRESS_INT_ID` (NUMBER(8))
- **Key Fields**:
  - `ADDRESS_LINE1` (VARCHAR2(40)) - **CORRECTED**: Not VARCHAR2(128)
  - `ADDRESS_LINE2` (VARCHAR2(40)) - **CORRECTED**: Not VARCHAR2(128)
  - `ADDRESS_LINE3` (VARCHAR2(40)) - **NEW**: Additional address line
  - `CITY` (VARCHAR2(40)) - **CORRECTED**: Not VARCHAR2(64)
  - `POSTAL_CODE` (VARCHAR2(10)) - **CORRECTED**: Not VARCHAR2(16)
  - `IS_PRIMARY` (VARCHAR2(1))
  - `TYPE` (NUMBER(8)) - **CORRECTED**: Not NUMBER(4)
  - `PROVINCE` (VARCHAR2(5)) - **CORRECTED**: FK to PROVINCE.PROVINCE_CODE (VARCHAR2(6))
  - `COUNTRY` (VARCHAR2(3)) - **CORRECTED**: FK to COUNTRY.COUNTRY_CODE (VARCHAR2(3))
  - `CARE_OF` (VARCHAR2(254)) - **NEW**: Care of field
  - `OWNER_INT_ID` (NUMBER(8)) - **NEW**: Owner reference
  - `OWNER_TYPE` (NUMBER(4)) - **NEW**: Owner type

### PHONE Table
- **Primary Key**: `PHONE_INT_ID` (NUMBER(8))
- **Key Fields**:
  - `PHONE_NUMBER` (VARCHAR2(20)) - **CORRECTED**: Not VARCHAR2(32)
  - `AREA_CODE` (VARCHAR2(4)) - **CORRECTED**: Not VARCHAR2(8)
  - `EXTENSION` (VARCHAR2(16))
  - `IS_PRIMARY` (VARCHAR2(1))
  - `TYPE` (NUMBER(8)) - **CORRECTED**: Not NUMBER(4)
  - `OWNER_INT_ID` (NUMBER(8)) - **NEW**: Owner reference
  - `OWNER_TYPE` (NUMBER(4)) - **NEW**: Owner type
  - `DO_NOT_CALL` (VARCHAR2(1)) - **NEW**: Do not call flag

### EMAIL Table
- **Primary Key**: `EMAIL_INT_ID` (NUMBER(8))
- **Key Fields**:
  - `EMAIL_ADDRESS` (VARCHAR2(100)) - **CORRECTED**: Not VARCHAR2(128)
  - `IS_PRIMARY` (VARCHAR2(1))
  - `TYPE` (NUMBER(8)) - **CORRECTED**: Not NUMBER(4)
  - `OWNER_INT_ID` (NUMBER(8)) - **NEW**: Owner reference
  - `OWNER_TYPE` (NUMBER(4)) - **NEW**: Owner type
  - `SEND_SOLICITATION` (VARCHAR2(1)) - **NEW**: Solicitation flag

### PROVINCE Table
- **Primary Key**: `PROVINCE_CODE` (VARCHAR2(6)) - **CORRECTED**: Not VARCHAR2(2)
- **Key Fields**:
  - `COUNTRY` (VARCHAR2(3)) - **CORRECTED**: FK to COUNTRY.COUNTRY_CODE (VARCHAR2(3))
  - `NAME_EN` (VARCHAR2(40)) - **CORRECTED**: Not VARCHAR2(128)
  - `NAME_FR` (VARCHAR2(40)) - **CORRECTED**: Not VARCHAR2(128)
  - `OLD_CODE` (NUMBER(4)) - **NEW**: Legacy code
  - `ORDERING` (NUMBER(4)) - **NEW**: Display order

### COUNTRY Table
- **Primary Key**: `COUNTRY_CODE` (VARCHAR2(3)) - **CORRECTED**: Not VARCHAR2(2)
- **Key Fields**:
  - `COUNTRY_NAME_EN` (VARCHAR2(50)) - **CORRECTED**: Not VARCHAR2(128)
  - `COUNTRY_NAME_FR` (VARCHAR2(50)) - **CORRECTED**: Not VARCHAR2(128)
  - `ALPHA_2` (VARCHAR2(2)) - **NEW**: 2-character country code
  - `OLD_CODE` (NUMBER(4)) - **NEW**: Legacy code

## Junction Tables

### CONTACT_ADDRESS
- **Composite Key**: `(CONTACT, ADDRESS)` - **CORRECTED**: Primary key is `(ADDRESS, CONTACT)`
- **Fields**:
  - `CONTACT` (NUMBER(8)) - FK to CONTACT.CONTACT_INT_ID
  - `ADDRESS` (NUMBER(8)) - FK to ADDRESS.ADDRESS_INT_ID

### CONTACT_PHONE
- **Composite Key**: `(CONTACT, PHONE)`
- **Fields**:
  - `CONTACT` (NUMBER(8)) - FK to CONTACT.CONTACT_INT_ID
  - `PHONE` (NUMBER(8)) - FK to PHONE.PHONE_INT_ID

### CONTACT_EMAIL
- **Composite Key**: `(CONTACT, EMAIL)`
- **Fields**:
  - `CONTACT` (NUMBER(8)) - FK to CONTACT.CONTACT_INT_ID
  - `EMAIL` (NUMBER(8)) - FK to EMAIL.EMAIL_INT_ID

## Extended Tables for Future Phases

### COMPANY Table
- **Primary Key**: `COMPANY_INT_ID` (NUMBER(8))
- **Key Fields**: (To be analyzed in Phase 3)

### LICENSE Table
- **Primary Key**: `LICENSE_INT_ID` (NUMBER(8))
- **Key Fields**: (To be analyzed in Phase 3)

### LICENSE_LIABILITY Table
- **Primary Key**: `LICENSE_LIABILITY_INT_ID` (NUMBER(8))
- **Key Fields**: (To be analyzed in Phase 3)

### CONTRACT_SETUP Table
- **Primary Key**: `CONTRACT_SETUP_INT_ID` (NUMBER(8))
- **Key Fields**: (To be analyzed in Phase 3)

### CONTRACT_EFT Table
- **Primary Key**: `CONTRACT_EFT_INT_ID` (NUMBER(8))
- **Key Fields**: (To be analyzed in Phase 3)

### ONBOARDING_STATUS Table
- **Primary Key**: `ONBOARDING_STATUS_INT_ID` (NUMBER(8))
- **Key Fields**: (To be analyzed in Phase 3)

## Corrected Query Strategy

To retrieve complete advisor profile data, the query must:

1. Start with ADVISOR table
2. INNER JOIN with CONTACT table on `ADVISOR.ADVISOR_INT_ID = CONTACT.CONTACT_INT_ID`
3. LEFT JOIN with USERS table on `CONTACT.CONTACT_INT_ID = USERS.USER_INT_ID`
4. LEFT JOIN with junction tables and related tables:
   - `CONTACT_ADDRESS` → `ADDRESS` → `PROVINCE` → `COUNTRY`
   - `CONTACT_PHONE` → `PHONE`
   - `CONTACT_EMAIL` → `EMAIL`

## Key Corrections Made

### Previous Implementation Issues
1. **Wrong table relationships**: Assumed ADVISOR had direct contact fields
2. **Incorrect field names**: Used assumed field names instead of actual schema
3. **Missing junction tables**: Didn't account for many-to-many relationships
4. **Wrong JOIN conditions**: Used incorrect foreign key relationships
5. **Incorrect data types**: Assumed VARCHAR fields were NUMBER and vice versa
6. **Wrong field lengths**: Used incorrect VARCHAR2 lengths for multiple fields
7. **Missing fields**: Didn't account for additional fields like ADDRESS_LINE3, CARE_OF, etc.

### Corrected Implementation
1. **Proper relationships**: ADVISOR_INT_ID = CONTACT_INT_ID (1:1 relationship)
2. **Actual field names**: Using exact field names from schema analysis
3. **Junction table JOINs**: Properly handling many-to-many relationships
4. **Correct foreign keys**: Using actual foreign key constraints from schema
5. **Proper data types**: Using correct data types for all fields
6. **Accurate field lengths**: Using exact VARCHAR2 lengths from schema
7. **Complete field coverage**: Including all relevant fields from actual schema

### Critical Data Type Corrections
- `ADDRESS_LINE1/2`: VARCHAR2(40) not VARCHAR2(128)
- `PHONE_NUMBER`: VARCHAR2(20) not VARCHAR2(32)
- `AREA_CODE`: VARCHAR2(4) not VARCHAR2(8)
- `EMAIL_ADDRESS`: VARCHAR2(100) not VARCHAR2(128)
- `PROVINCE_CODE`: VARCHAR2(6) not VARCHAR2(2)
- `COUNTRY_CODE`: VARCHAR2(3) not VARCHAR2(2)
- `TYPE` fields: NUMBER(8) not NUMBER(4) in ADDRESS, PHONE, EMAIL tables

## Corrected SQL Query Template

```sql
SELECT
    -- Advisor fields
    a.ADVISOR_INT_ID,
    a.ADVISOR_ID,
    a.NAME,
    a.STATUS,
    a.ADVISOR_TYPE,
    a.CREATION_DATE,
    a.LAST_MODIFICATION_DATE,
    a.ADVISOR_NUMBER,
    a.NOTE,
    a.SIN,
    a.CORPORATE_NAME,
    -- Contact fields
    c.CONTACT_INT_ID,
    c.FIRSTNAME,
    c.MIDDLENAME,
    c.LASTNAME,
    c.BIRTH_DATE,
    c.GENDER,
    c.PREFERRED_LANGUAGE,
    c.SALUTATION,
    c.CONTACT_TYPE,
    c.JOB_TITLE,
    c.JOB_ROLE,
    c.MARITAL_STATUS,
    c.INCOME,
    -- User fields
    u.USERNAME,
    u.ACTIVE,
    u.USER_TYPE,
    -- Address fields
    addr.ADDRESS_INT_ID,
    addr.ADDRESS_LINE1,
    addr.ADDRESS_LINE2,
    addr.ADDRESS_LINE3,
    addr.CITY,
    addr.POSTAL_CODE,
    addr.IS_PRIMARY as ADDR_IS_PRIMARY,
    addr.TYPE as ADDRESS_TYPE,
    addr.CARE_OF,
    -- Province/Country
    prov.PROVINCE_CODE,
    prov.NAME_EN as PROVINCE_NAME_EN,
    prov.NAME_FR as PROVINCE_NAME_FR,
    ctry.COUNTRY_CODE,
    ctry.COUNTRY_NAME_EN,
    ctry.COUNTRY_NAME_FR,
    -- Phone fields
    ph.PHONE_INT_ID,
    ph.PHONE_NUMBER,
    ph.AREA_CODE,
    ph.EXTENSION,
    ph.IS_PRIMARY as PHONE_IS_PRIMARY,
    ph.TYPE as PHONE_TYPE,
    ph.DO_NOT_CALL,
    -- Email fields
    em.EMAIL_INT_ID,
    em.EMAIL_ADDRESS,
    em.IS_PRIMARY as EMAIL_IS_PRIMARY,
    em.TYPE as EMAIL_TYPE,
    em.SEND_SOLICITATION
FROM ADVISOR a
    INNER JOIN CONTACT c ON a.ADVISOR_INT_ID = c.CONTACT_INT_ID
    LEFT JOIN USERS u ON c.CONTACT_INT_ID = u.USER_INT_ID
    LEFT JOIN CONTACT_ADDRESS ca ON c.CONTACT_INT_ID = ca.CONTACT
    LEFT JOIN ADDRESS addr ON ca.ADDRESS = addr.ADDRESS_INT_ID
    LEFT JOIN PROVINCE prov ON addr.PROVINCE = prov.PROVINCE_CODE
    LEFT JOIN COUNTRY ctry ON prov.COUNTRY = ctry.COUNTRY_CODE
    LEFT JOIN CONTACT_PHONE cp ON c.CONTACT_INT_ID = cp.CONTACT
    LEFT JOIN PHONE ph ON cp.PHONE = ph.PHONE_INT_ID
    LEFT JOIN CONTACT_EMAIL ce ON c.CONTACT_INT_ID = ce.CONTACT
    LEFT JOIN EMAIL em ON ce.EMAIL = em.EMAIL_INT_ID
WHERE a.ADVISOR_INT_ID = ?
ORDER BY addr.IS_PRIMARY DESC, ph.IS_PRIMARY DESC, em.IS_PRIMARY DESC
```

## Implementation Notes

1. **Multiple Results**: Due to many-to-many relationships, a single advisor may return multiple rows
2. **Result Aggregation**: The ResultSetExtractor must aggregate related data (addresses, phones, emails)
3. **NULL Handling**: LEFT JOINs will produce NULLs for advisors without addresses/phones/emails
4. **Performance**: Consider using separate queries for large result sets instead of complex JOINs
5. **Data Type Precision**: All VARCHAR2 field lengths must match exactly to avoid truncation
6. **Field Mapping**: Entity setters must match exact database field names and types

## Code Fixes Required

### 1. AdvisorProfileRepository.java
- Update SQL query to include all corrected field names and types
- Add missing fields like ADDRESS_LINE3, CARE_OF, SIN, CORPORATE_NAME
- Correct VARCHAR2 field lengths in comments

### 2. AdvisorMapper.java
- Update field mappings to handle new/corrected fields
- Ensure proper data type conversions
- Add null checks for new optional fields

### 3. DTO Classes
- Verify DTO field types match database types
- Add missing fields to DTOs if needed for API completeness

### 4. Entity Method Calls
- Verify all skynet entity setter/getter method calls exist
- Check method signatures match expected data types
- Handle any missing methods in skynet entities

## Validation Checklist

- [x] All table names match actual schema
- [x] All column names match actual schema (case-sensitive)
- [x] All data types match actual schema
- [x] All VARCHAR2 lengths match actual schema
- [x] All foreign key relationships are correct
- [x] Junction table relationships are properly handled
- [x] SQL queries use exact field names from schema
- [ ] Entity method calls match skynet entity signatures (requires skynet-ejb.jar access)

## Completion Status

✅ **COMPLETED**: Database schema analysis and code corrections
✅ **COMPLETED**: All SQL queries updated to match actual schema
✅ **COMPLETED**: All DTO classes updated with new fields
✅ **COMPLETED**: Repository and Mapper classes updated
❌ **PENDING**: Skynet entity method validation (requires runtime testing or jar inspection)

**Last Updated**: 2025-01-01
**Status**: Database schema analysis and corrections COMPLETE
