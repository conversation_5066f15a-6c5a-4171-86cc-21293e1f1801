# Consolidated API Design Principles

**Goal: To guide the development of REST APIs that directly support the implementation of user-facing page functionalities. This document prioritizes API designs that are intuitive for front-end consumption, align with user workflows, and efficiently deliver the necessary data for each page and feature.**

This document outlines the key design principles and conventions to be followed for all REST APIs developed for the Advisor Profile module and potentially other modules within the new Spring Boot application. Adherence to these principles will ensure consistency, maintainability, and ease of use for API consumers, particularly front-end developers integrating with these APIs.

## 1. Resource-Oriented Design
*   **Nouns for URLs:** Use nouns to represent resources (e.g., `/advisors`, `/advisors/{advisorId}/licenses`).
*   **Plural Nouns:** Prefer plural nouns for resource collections (e.g., `/advisors` instead of `/advisor`).
*   **Clear Hierarchy:** Structure URLs to reflect the relationship between resources (e.g., `/advisors/{advisorId}/notes`).

## 2. HTTP Methods (Verbs)
*   **`GET`:** Retrieve a resource or a collection of resources. Should be safe and idempotent.
*   **`POST`:** Create a new resource. Not idempotent (multiple identical requests will create multiple resources).
*   **`PUT`:** Update an existing resource completely. Should be idempotent.
*   **`PATCH`:** Partially update an existing resource. Should be idempotent.
*   **`DELETE`:** Remove a resource. Should be idempotent.
*   Avoid using `GET` for state changes or operations with side effects.

## 3. Status Codes
*   Use standard HTTP status codes to indicate the outcome of an API request.
    *   **2xx (Success):**
        *   `200 OK`: General success for `GET`, `PUT`, `PATCH`, `DELETE`.
        *   `201 Created`: Resource successfully created (typically for `POST`). Include a `Location` header pointing to the new resource.
        *   `204 No Content`: Success, but no data to return (e.g., for `DELETE` or `PUT` if no content is returned).
    *   **3xx (Redirection):**
        *   `301 Moved Permanently`: Resource URL has changed permanently.
        *   `304 Not Modified`: Client can use cached version.
    *   **4xx (Client Errors):**
        *   `400 Bad Request`: Invalid request payload or parameters. Provide clear error messages.
        *   `401 Unauthorized`: Authentication is required and has failed or has not yet been provided.
        *   `403 Forbidden`: Authenticated user does not have permission to access the resource.
        *   `404 Not Found`: The requested resource does not exist.
        *   `405 Method Not Allowed`: The HTTP method used is not supported for this resource.
        *   `409 Conflict`: Request conflicts with the current state of the resource (e.g., duplicate entry).
        *   `422 Unprocessable Entity`: The request was well-formed but was unable to be followed due to semantic errors (e.g., validation errors).
    *   **5xx (Server Errors):**
        *   `500 Internal Server Error`: A generic error message for an unexpected condition on the server.
        *   `503 Service Unavailable`: The server is temporarily unable to handle the request (e.g., overloaded or down for maintenance).

## 4. Request and Response Payloads
*   **JSON:** Use JSON as the primary format for request and response bodies.
*   **Content-Type Header:**
    *   Requests: `Content-Type: application/json`.
    *   Responses: `Content-Type: application/json`.
*   **Consistent Naming:**
    *   Use camelCase for JSON property keys (e.g., `advisorId`, `firstName`).
*   **Clear Error Responses:** For `4xx` and `5xx` errors, provide a consistent JSON error response body:
    ```json
    {
      "timestamp": "2025-06-06T10:30:00Z",
      "status": 400,
      "error": "Bad Request",
      "message": "Validation failed for field 'email': must be a well-formed email address.",
      "path": "/api/v1/advisors",
      "details": [ // Optional: for multiple validation errors
        {
          "field": "email",
          "message": "must be a well-formed email address"
        },
        {
          "field": "phoneNumber",
          "message": "must not be blank"
        }
      ]
    }
    ```

## 5. Versioning
*   **URL Path Versioning:** Include the API version in the URL path (e.g., `/api/v1/advisors`). This is the most straightforward approach.
*   Increment version for breaking changes.

## 6. Filtering, Sorting, and Pagination
*   **Filtering:** Allow filtering of collections using query parameters (e.g., `GET /advisors?status=ACTIVE&province=QC`).
*   **Sorting:** Allow sorting of collections using query parameters (e.g., `GET /advisors?sortBy=lastName&order=asc`).
*   **Pagination:** Implement pagination for large collections to improve performance and reduce payload size.
    *   Use query parameters like `page`, `size` (or `limit`, `offset`).
    *   Include pagination metadata in the response (e.g., total items, total pages, current page, next/prev links).
    ```json
    {
      "content": [ /* array of resources */ ],
      "pageable": {
        "sort": {
          "sorted": true,
          "unsorted": false,
          "empty": false
        },
        "offset": 0,
        "pageNumber": 0,
        "pageSize": 20,
        "paged": true,
        "unpaged": false
      },
      "totalPages": 5,
      "totalElements": 98,
      "last": false,
      "size": 20,
      "number": 0,
      "sort": {
        "sorted": true,
        "unsorted": false,
        "empty": false
      },
      "numberOfElements": 20,
      "first": true,
      "empty": false
    }
    ```
    *(Example based on Spring Data JPA Page object)*

## 7. Security
*   **HTTPS:** Always use HTTPS to encrypt communication.
*   **Authentication:** Use a robust authentication mechanism (e.g., OAuth 2.0, JWT).
*   **Authorization:** Implement proper authorization checks to ensure users can only access/modify resources they are permitted to.
*   **Input Validation:** Validate all input data to prevent security vulnerabilities (e.g., SQL injection, XSS).
*   **Principle of Least Privilege:** Grant only the necessary permissions.

## 8. Idempotency
*   Ensure `GET`, `PUT`, `DELETE`, and `PATCH` operations are idempotent.
*   For `POST` operations that might be retried by clients due to network issues, consider mechanisms to prevent duplicate resource creation (e.g., using a client-generated idempotency key in the request header).

## 9. HATEOAS (Hypermedia as the Engine of Application State) - Optional but Recommended
*   Responses can include links to related resources or actions, allowing clients to discover API capabilities dynamically.
    ```json
    {
      "advisorId": "123",
      "firstName": "John",
      "lastName": "Doe",
      "_links": {
        "self": { "href": "/api/v1/advisors/123" },
        "notes": { "href": "/api/v1/advisors/123/notes" },
        "licenses": { "href": "/api/v1/advisors/123/licenses" }
      }
    }
    ```

## 10. Documentation
*   Maintain comprehensive API documentation (e.g., using OpenAPI/Swagger).
*   Documentation should include:
    *   Available endpoints.
    *   HTTP methods.
    *   Request/response formats and examples.
    *   Authentication methods.
    *   Error codes and messages.

## 11. Date and Time Handling
*   Use ISO 8601 format for dates and times in requests and responses (e.g., `YYYY-MM-DDTHH:mm:ss.sssZ` or `YYYY-MM-DD`).
*   Prefer UTC for storing and transmitting timestamps to avoid timezone issues.

## 12. Rate Limiting
*   Implement rate limiting to protect the API from abuse and ensure fair usage.

These principles will serve as a guide for developing consistent and robust REST APIs.
