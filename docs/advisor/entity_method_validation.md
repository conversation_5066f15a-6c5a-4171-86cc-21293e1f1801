# Skynet Entity Method Validation

## Overview

This document tracks the validation status of skynet entity method calls used in the Advisor Profile implementation. All methods listed below are called in our code and need to be verified against the actual skynet-ejb.jar entity classes.

## Entity Method Calls to Validate

### Advisor Entity (com.insurfact.skynet.entity.Advisor)

**Existing Methods (assumed to exist):**
- `getAdvisorIntId()` / `setAdvisorIntId(Long)`
- `getAdvisorId()` / `setAdvisorId(Long)`
- `getName()` / `setName(String)`
- `getStatus()` / `setStatus(Integer)`
- `getAdvisorType()` / `setAdvisorType(Integer)`
- `getCreationDate()` / `setCreationDate(Timestamp)`
- `getLastModificationDate()` / `setLastModificationDate(Timestamp)`
- `getAdvisorNumber()` / `setAdvisorNumber(String)`
- `getNote()` / `setNote(String)`
- `getContact()` / `setContact(Contact)`
- `getUsers()` / `setUsers(Users)`

**NEW Methods (need validation):**
- `getSin()` / `setSin(String)` - for SIN field
- `getCorporateName()` / `setCorporateName(String)` - for CORPORATE_NAME field

### Contact Entity (com.insurfact.skynet.entity.Contact)

**Existing Methods (assumed to exist):**
- `getContactIntId()` / `setContactIntId(Long)`
- `getFirstname()` / `setFirstname(String)`
- `getLastname()` / `setLastname(String)`
- `getMiddlename()` / `setMiddlename(String)`
- `getBirthDate()` / `setBirthDate(Date)`
- `getGender()` / `setGender(Integer)`
- `getPreferredLanguage()` / `setPreferredLanguage(Integer)`
- `getSalutation()` / `setSalutation(Integer)`
- `getContactType()` / `setContactType(Integer)`
- `getAddressList()` / `setAddressList(List<Address>)`
- `getPhoneList()` / `setPhoneList(List<Phone>)`
- `getEmailList()` / `setEmailList(List<Email>)`

**NEW Methods (need validation):**
- `getJobTitle()` / `setJobTitle(String)` - for JOB_TITLE field
- `getJobRole()` / `setJobRole(String)` - for JOB_ROLE field
- `getMaritalStatus()` / `setMaritalStatus(Integer)` - for MARITAL_STATUS field
- `getIncome()` / `setIncome(BigDecimal)` - for INCOME field

### Address Entity (com.insurfact.skynet.entity.Address)

**Existing Methods (assumed to exist):**
- `getAddressIntId()` / `setAddressIntId(Long)`
- `getType()` / `setType(Integer)`
- `getAddressLine1()` / `setAddressLine1(String)`
- `getAddressLine2()` / `setAddressLine2(String)`
- `getCity()` / `setCity(String)`
- `getPostalCode()` / `setPostalCode(String)`
- `getIsPrimary()` / `setIsPrimary(Boolean)`
- `getProvince()` / `setProvince(Province)`
- `getCountry()` / `setCountry(Country)`

**NEW Methods (need validation):**
- `getAddressLine3()` / `setAddressLine3(String)` - for ADDRESS_LINE3 field
- `getCareOf()` / `setCareOf(String)` - for CARE_OF field

### Phone Entity (com.insurfact.skynet.entity.Phone)

**Existing Methods (assumed to exist):**
- `getPhoneIntId()` / `setPhoneIntId(Long)`
- `getType()` / `setType(Integer)`
- `getAreaCode()` / `setAreaCode(String)`
- `getPhoneNumber()` / `setPhoneNumber(String)`
- `getExtension()` / `setExtension(String)`
- `getIsPrimary()` / `setIsPrimary(Boolean)`

**NEW Methods (need validation):**
- `getDoNotCall()` / `setDoNotCall(Boolean)` - for DO_NOT_CALL field

### Email Entity (com.insurfact.skynet.entity.Email)

**Existing Methods (assumed to exist):**
- `getEmailIntId()` / `setEmailIntId(Long)`
- `getType()` / `setType(Integer)`
- `getEmailAddress()` / `setEmailAddress(String)`
- `getIsPrimary()` / `setIsPrimary(Boolean)`

**NEW Methods (need validation):**
- `getSendSolicitation()` / `setSendSolicitation(Boolean)` - for SEND_SOLICITATION field

### Users Entity (com.insurfact.skynet.entity.Users)

**Existing Methods (assumed to exist):**
- `getUserIntId()` / `setUserIntId(Long)`
- `getUsername()` / `setUsername(String)`
- `getActive()` / `setActive(String)`

**NEW Methods (need validation):**
- `getUserType()` / `setUserType(Integer)` - for USER_TYPE field

### Province Entity (com.insurfact.skynet.entity.Province)

**Existing Methods (assumed to exist):**
- `getProvinceCode()` / `setProvinceCode(String)`
- `getNameEn()` / `setNameEn(String)`
- `getNameFr()` / `setNameFr(String)`

### Country Entity (com.insurfact.skynet.entity.Country)

**Existing Methods (assumed to exist):**
- `getCountryCode()` / `setCountryCode(String)`
- `getCountryNameEn()` / `setCountryNameEn(String)`
- `getCountryNameFr()` / `setCountryNameFr(String)`

## Validation Status

❌ **NOT VALIDATED** - All NEW methods need to be verified against actual skynet entity classes
✅ **VALIDATED** - None yet

## Next Steps

1. Access skynet-ejb.jar to verify entity class structures
2. Confirm all NEW method calls exist with correct signatures
3. Create fallback handling for any missing methods
4. Update this document with validation results
