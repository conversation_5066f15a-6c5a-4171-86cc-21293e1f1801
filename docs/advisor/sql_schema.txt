create table SKYTEST.FD_ALLOCATION
(
    ALLOC_SEQ                    NUMBER(11)              not null
        constraint FD_ALLOCATION_PK
            primary key,
    ALLOC_CREATION_DATE          DATE                    not null,
    ALLOC_FUNDATAKEY             NUMBER(11),
    ALLOC_FUNDATA_DATE           DATE,
    ALLOC_TYPE                   VARCHAR2(8),
    ALLOC_PERCENT                NUMBER(11, 6),
    ALLOC_ENABLED                VARCHAR2(1) default 'Y' not null,
    ALLOC_DESC_EN                VARCHAR2(32),
    ALLOC_DESC_FR                VARCHAR2(32),
    ALLOC_LAST_MODIFICATION_DATE DATE
)
/

create index SKYTEST.FD_ALLOCATION_IDX1
    on SKYTEST.FD_ALLOCATION (ALLOC_FUNDATAKEY)
/

create table SKYTEST.A_FILE
(
    A_FILE_INT_ID     NUMBER(8)     not null,
    FILENAME          VARCHAR2(128) not null,
    FILE_PATH         VARCHAR2(128) not null,
    VERSION           NUMBER(5),
    MGMT_COMPANY_CODE VARCHAR2(3),
    DEALER_CODE       VARCHAR2(4),
    RECORD_COUNT      NUMBER(8),
    CREATION_DATE     DATE,
    EFFECTIVE_DATE    DATE,
    DAY_CODE          VARCHAR2(2),
    MONTH_CODE        VARCHAR2(1),
    YEAR_CODE         VARCHAR2(1),
    FILE_CODE         VARCHAR2(2),
    FILE_TYPE         VARCHAR2(3),
    SEQUENCE          NUMBER(6),
    DOWNLOADED        VARCHAR2(1),
    IMPORTED          VARCHAR2(1)
)
/

create index SKYTEST.A_FILE_IDX1
    on SKYTEST.A_FILE (A_FILE_INT_ID)
/

alter table SKYTEST.A_FILE
    add constraint A_FILE_RECORD_PK
        primary key (A_FILE_INT_ID)
/

create table SKYTEST.SUN_LAPSE_PENDING_TOTALS
(
    AGENTID               NUMBER(11) not null
        primary key,
    ADVISORNUMBER         VARCHAR2(15),
    OLD_AGENT_FIRSTNAME   VARCHAR2(150),
    OLD_AGENT_LASTNAME    VARCHAR2(150),
    SUNTERMDATE           DATE,
    TOTPOLS               NUMBER(8),
    TOTCLI                NUMBER(8),
    TOTPREM               NUMBER(10, 2),
    BRANCH                VARCHAR2(64),
    MARKETING_REG         VARCHAR2(64),
    BRANCH_NAME_EN        VARCHAR2(64),
    MARKETING_REG_NAME_EN VARCHAR2(64),
    BRANCH_NAME_FR        VARCHAR2(64),
    MARKETING_REG_NAME_FR VARCHAR2(64)
)
/

create table SKYTEST.FD_BENCHMARK
(
    ASSOC_CAN_INDEX_ID      NUMBER(11)    not null
        constraint FD_BENCHMARK_PK
            primary key,
    CREATION_DATE           DATE          not null,
    ASSOC_CAN_INDEX_NAME    VARCHAR2(100) not null,
    ASSOC_CAN_INDEX_NAME_FR VARCHAR2(100),
    ONE_MONTH_RETURN        NUMBER(11, 6) not null,
    THREE_MONTH_RETURN      NUMBER(11, 6) not null,
    YTD_RETURN              NUMBER(11, 6) not null,
    INCEPT_RETURN           NUMBER(11, 6) not null,
    YR1_COMPOUND_RETURN     NUMBER(11, 6),
    YR2_COMPOUND_RETURN     NUMBER(11, 6),
    YR3_COMPOUND_RETURN     NUMBER(11, 6),
    YR4_COMPOUND_RETURN     NUMBER(11, 6),
    YR5_COMPOUND_RETURN     NUMBER(11, 6),
    YR3_ANNUALIZED_STD_DEV  NUMBER(11, 6),
    FUNDATA_DATE            DATE
)
/

create table SKYTEST.FD_MANAGER2
(
    MANAGER_SEQ                    NUMBER(11) not null
        constraint FD_MANAGER2_PK
            primary key,
    MANAGER_CREATION_DATE          DATE       not null,
    MANAGER_FUNDATAKEY             NUMBER(11),
    MANAGER_KEY                    NUMBER(11),
    MANAGER_NAME                   VARCHAR2(64),
    MANAGER_BIO_EN                 VARCHAR2(512),
    MANAGER_BIO_FR                 VARCHAR2(512),
    MANAGER_ENABLED                CHAR       not null,
    MANAGER_LAST_MODIFICATION_DATE DATE,
    FD_FUND2                       NUMBER(11)
)
/

create table SKYTEST.LATLONGCANADA
(
    POSTALCODE   VARCHAR2(7),
    CITY         VARCHAR2(50),
    CITYTYPE     VARCHAR2(2),
    PROVINCE     VARCHAR2(30),
    PROVINCEABBR VARCHAR2(2),
    AREACODE     VARCHAR2(7),
    TIMEZONE     VARCHAR2(30),
    DST          VARCHAR2(1),
    LATITUDE     NUMBER(10, 7),
    LONGITUDE    NUMBER(10, 7)
)
/

create table SKYTEST.CANNEX_INTEREST_RATE_NEW_USTD
(
    INTEREST_RATE_INT_ID NUMBER(10) not null
        constraint SYS_C0097874
            primary key
        constraint SYS_C0097871
            check ("INTEREST_RATE_INT_ID" IS NOT NULL)
        constraint SYS_C0097872
            check ("INTEREST_RATE_INT_ID" IS NOT NULL)
        constraint SYS_C0097873
            check ("INTEREST_RATE_INT_ID" IS NOT NULL),
    DTYPE                VARCHAR2(31),
    CHANGE               NUMBER(6, 4),
    CHANGE_DATE_STRING   VARCHAR2(8),
    CHANGE_TIME_STRING   VARCHAR2(20),
    CREATION_DATE        TIMESTAMP(6),
    INTEREST_RATE_VALUE  NUMBER(6, 4),
    LAST_MODIFICATION    TIMESTAMP(6),
    TERM_VALUE           VARCHAR2(255),
    UNIQUE_NUMBER        NUMBER(19),
    CANNEX_PRODUCT       NUMBER(10),
    DONT_DELETE          VARCHAR2(1),
    PURCHASE_RATE        NUMBER(6, 4),
    PURCHASE_RATE_CHANGE NUMBER(6, 4),
    CASH_ADVANCE_RATE    NUMBER(6, 4),
    CASH_ADVANCE_CHANGE  NUMBER(6, 4),
    MIN_AMOUNT           NUMBER(14, 2),
    MAX_AMOUNT           NUMBER(14, 2),
    PAYMENT_FREQUENCY    VARCHAR2(2),
    TERM_DURATION        VARCHAR2(1)
)
/

create table SKYTEST.WEBSECURING
(
    ID            NUMBER not null
        constraint SYS_C0090977
            primary key
        constraint SYS_C0090976
            check ("ID" IS NOT NULL),
    USERNAME      VARCHAR2(255),
    PASSWORD      VARCHAR2(255),
    TOKEN         VARCHAR2(255),
    IP            VARCHAR2(255),
    REQUEST_COUNT NUMBER(38),
    CNAME         VARCHAR2(255)
)
/

create table SKYTEST.FD_FUND_COMPANY_PY
(
    FUND_COMPANY_INT_ID    NUMBER(8) not null,
    GROUP_KEY              NUMBER(8) not null
        primary key,
    OLD_FD_MASTER_ID       NUMBER(8),
    FUND_COMPANY_EN        VARCHAR2(32),
    FUND_COMPANY_FR        VARCHAR2(32),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE
)
/

create table SKYTEST.TYPE_CLASS
(
    TYPE_CLASS_INT_ID      NUMBER(8)    not null
        constraint TYPE_CLASS_PK
            primary key,
    TYPE_NAME              VARCHAR2(64) not null
        constraint UNIQUE_NAME
            unique,
    DESC_EN                VARCHAR2(64),
    DESC_FR                VARCHAR2(64),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    CLIEDIS_NAME           VARCHAR2(128),
    CLIEDIS_LINK           VARCHAR2(256),
    CLIEDIS_EXTENSION      VARCHAR2(1)
)
/

create table SKYTEST.AT_FILE
(
    AT_FILE_INT_ID                NUMBER(8) not null,
    A_FILE_INT_ID                 NUMBER(8) not null,
    MGMT_COMPANY_CODE             VARCHAR2(3),
    FUND_ID                       VARCHAR2(5),
    FUND_ACCOUNT_ID               VARCHAR2(15),
    SALESREP_CODE                 VARCHAR2(5),
    DEALER_CODE                   VARCHAR2(4),
    BENEFICIAL_OWNER_LASTNAME     VARCHAR2(20),
    BENEFICIAL_OWNER_FIRSTNAME    VARCHAR2(20),
    SPOUSAL_ACCOUNT_FLAG          VARCHAR2(1),
    DESIGNATION                   VARCHAR2(1),
    ACCOUNT_TYPE                  VARCHAR2(2),
    LOCKED_IN_CODE                VARCHAR2(1),
    TAX_CODE                      VARCHAR2(3),
    ACCOUNT_STATUS                VARCHAR2(1),
    TRANSACTION_TYPE              VARCHAR2(1),
    TRADE_DATE                    DATE,
    SETTLEMENT_DATE               DATE,
    POS_NEG_IND                   VARCHAR2(1),
    GROSS_AMOUNT                  NUMBER(15, 5),
    NET_AMOUNT                    NUMBER(15, 5),
    UNIT_PRICE                    NUMBER(15, 5),
    UNIT_TRANSACTED               NUMBER(15, 5),
    SETTLEMENT_AMOUNT             NUMBER(15, 5),
    TOTAL_ASSIGNED                NUMBER(15, 5),
    TOTAL_UNASSIGNED              NUMBER(15, 5),
    CLIENT_PAID_COMM              NUMBER(15, 5),
    DEALER_CLIENT_PAID_COMM       NUMBER(15, 5),
    FUND_PARTNER_SHIP_PAID_COMM   NUMBER(15, 5),
    DEALER_FUND_PARTNER_PAID_COMM NUMBER(15, 5),
    RAW_RECORD                    VARCHAR2(700),
    PROCESSING_STATUS             VARCHAR2(1),
    PROCESS_DATE                  DATE,
    SEQUENCE_NUMBER               NUMBER(8) not null,
    BENEFICIAL_OWNER_SIN          VARCHAR2(9),
    TRANSACTION_TYPE_DETAIL       VARCHAR2(1),
    FEES                          NUMBER(15, 5),
    SETTLEMENT_STATUS             VARCHAR2(1),
    RETURN_CODE                   VARCHAR2(2),
    RETURN_CODE_DETAIL            VARCHAR2(3),
    NET_GROSS_INDICATOR           VARCHAR2(1)
)
/

create index SKYTEST.AT_FILE_IDX4
    on SKYTEST.AT_FILE (PROCESS_DATE)
/

create index SKYTEST.AT_FILE_IDX3
    on SKYTEST.AT_FILE (MGMT_COMPANY_CODE, FUND_ACCOUNT_ID)
/

create index SKYTEST.AT_FILE_IDX2
    on SKYTEST.AT_FILE (A_FILE_INT_ID)
/

create index SKYTEST.AT_FILE_IDX1
    on SKYTEST.AT_FILE (AT_FILE_INT_ID)
/

create index SKYTEST.AT_FILE_IDX6
    on SKYTEST.AT_FILE (TRADE_DATE)
/

create index SKYTEST.AT_FILE_IDX5
    on SKYTEST.AT_FILE (PROCESSING_STATUS, ACCOUNT_STATUS, MGMT_COMPANY_CODE)
/

alter table SKYTEST.AT_FILE
    add constraint AT_FILE_PK
        primary key (AT_FILE_INT_ID)
/

create table SKYTEST.PRODUCT_CLASS
(
    PRODUCT_CLASS_INT_ID   NUMBER(8) not null,
    PRODUCT_CLASS          VARCHAR2(11),
    DEFINITION_EN          VARCHAR2(50),
    DEFINITION_FR          VARCHAR2(50),
    LAST_MODIFICATION_DATE DATE,
    CREATION_DATE          DATE
)
/

create index SKYTEST.PRODUCT_CLASS_IDX1
    on SKYTEST.PRODUCT_CLASS (PRODUCT_CLASS_INT_ID)
/

create index SKYTEST.PRODUCT_CLASS_IDX2
    on SKYTEST.PRODUCT_CLASS (PRODUCT_CLASS)
/

alter table SKYTEST.PRODUCT_CLASS
    add constraint PRODUCT_CLASS_PK
        primary key (PRODUCT_CLASS_INT_ID)
/

create table SKYTEST.PRODUCT_SUPPLIER_TYPE
(
    PRODUCT_SUPPLIER_TYPE_INT_ID NUMBER(8) not null,
    PRODUCT_SUPPLIER_TYPE        NUMBER(4),
    PRODUCT_SUPP_TYPE_DESC_EN    VARCHAR2(100),
    PRODUCT_SUPP_TYPE_DESC_FR    VARCHAR2(100),
    CODE_TYPE                    VARCHAR2(2),
    CREATION_DATE                DATE,
    LAST_MODIFICATION_DATE       DATE
)
/

create index SKYTEST.PRODUCT_SUPP_TYPE_IDX1
    on SKYTEST.PRODUCT_SUPPLIER_TYPE (PRODUCT_SUPPLIER_TYPE_INT_ID)
/

create index SKYTEST.PRODUCT_SUPP_TYPE_IDX2
    on SKYTEST.PRODUCT_SUPPLIER_TYPE (PRODUCT_SUPPLIER_TYPE)
/

alter table SKYTEST.PRODUCT_SUPPLIER_TYPE
    add constraint COMPANY_TYPE_PK
        primary key (PRODUCT_SUPPLIER_TYPE_INT_ID)
/

create table SKYTEST.FD_ALERT
(
    ALERT_SEQ                    NUMBER(11)    not null
        constraint PK_FD_ALERT
            primary key,
    ALERT_CREATION_DATE          DATE          not null,
    ALERT_CLIENT                 VARCHAR2(50)  not null,
    ALERT_FUNDATAKEY             VARCHAR2(50)  not null,
    ALERT_ABOVE                  VARCHAR2(100),
    ALERT_BELOW                  VARCHAR2(100),
    ALERT_ENABLED                VARCHAR2(10),
    ALERT_FUNDNAME               VARCHAR2(200) not null,
    ALERT_PROFILE                VARCHAR2(50),
    ALERT_LAST_MODIFICATION_DATE DATE
)
/

create index SKYTEST.FD_ALERT_IDX1
    on SKYTEST.FD_ALERT (ALERT_FUNDATAKEY)
/

create table SKYTEST.SERVICE_PROFILE
(
    SERVICE_INT_ID NUMBER(8) not null
        constraint SERVICE_PROFILE_PK
            primary key,
    NAME_ENG       VARCHAR2(100),
    NAME_FRE       VARCHAR2(100),
    DATE_ADDED     DATE,
    ANNUAL_PRICE   NUMBER(9, 2),
    MONTHLY_PRICE  NUMBER(9, 2),
    DETAILS_ENG    VARCHAR2(500),
    DETAILS_FRE    VARCHAR2(500),
    TOKEN_NEEDED   VARCHAR2(1)
)
/

comment on column SKYTEST.SERVICE_PROFILE.TOKEN_NEEDED is 'Y/N'
/

create table SKYTEST.COUNTRY
(
    COUNTRY_CODE    VARCHAR2(3) default '0' not null,
    COUNTRY_NAME_EN VARCHAR2(50)            not null,
    COUNTRY_NAME_FR VARCHAR2(50)            not null,
    ALPHA_2         VARCHAR2(2),
    OLD_CODE        NUMBER(4)
)
/

comment on column SKYTEST.COUNTRY.COUNTRY_CODE is 'The country unique identifier'
/

comment on column SKYTEST.COUNTRY.COUNTRY_NAME_EN is 'The 2 character long country abbreviation.'
/

comment on column SKYTEST.COUNTRY.COUNTRY_NAME_FR is 'The country french name'
/

create unique index SKYTEST.COUNTRY_IDX1
    on SKYTEST.COUNTRY (COUNTRY_CODE)
/

alter table SKYTEST.COUNTRY
    add constraint COUNTRY_PK
        primary key (COUNTRY_CODE)
/

create table SKYTEST.INVEST_TRANS_TYPE
(
    INVEST_TRANS_TYPE_INT_ID NUMBER(8) not null,
    INVEST_TRANSACTION_TYPE  VARCHAR2(1),
    DEFINITION_EN            VARCHAR2(100),
    DEFINITION_FR            VARCHAR2(100)
)
/

create index SKYTEST.INVEST_TRANS_TYPE_IDX1
    on SKYTEST.INVEST_TRANS_TYPE (INVEST_TRANS_TYPE_INT_ID)
/

alter table SKYTEST.INVEST_TRANS_TYPE
    add constraint INVEST_TRANS_TYPE_PK
        primary key (INVEST_TRANS_TYPE_INT_ID)
/

create table SKYTEST.SUN_LAPSED_TOTALS
(
    AGENTID               NUMBER(11) not null
        primary key,
    ADVISORNUMBER         VARCHAR2(15),
    OLD_AGENT_FIRSTNAME   VARCHAR2(150),
    OLD_AGENT_LASTNAME    VARCHAR2(150),
    SUNTERMDATE           DATE,
    TOTPOLS               NUMBER(8),
    TOTCLI                NUMBER(8),
    TOTPREM               NUMBER(10, 2),
    BRANCH                VARCHAR2(64),
    MARKETING_REG         VARCHAR2(64),
    BRANCH_NAME_EN        VARCHAR2(64),
    MARKETING_REG_NAME_EN VARCHAR2(64),
    BRANCH_NAME_FR        VARCHAR2(64),
    MARKETING_REG_NAME_FR VARCHAR2(64)
)
/

create table SKYTEST.SUN_MISSED_PAYMENT_TOTALS
(
    AGENTID               NUMBER(11) not null
        primary key,
    ADVISORNUMBER         VARCHAR2(15),
    OLD_AGENT_FIRSTNAME   VARCHAR2(150),
    OLD_AGENT_LASTNAME    VARCHAR2(150),
    SUNTERMDATE           DATE,
    TOTPOLS               NUMBER(8),
    TOTCLI                NUMBER(8),
    TOTPREM               NUMBER(10, 2),
    BRANCH                VARCHAR2(64),
    MARKETING_REG         VARCHAR2(64),
    BRANCH_NAME_EN        VARCHAR2(64),
    MARKETING_REG_NAME_EN VARCHAR2(64),
    BRANCH_NAME_FR        VARCHAR2(64),
    MARKETING_REG_NAME_FR VARCHAR2(64)
)
/

create table SKYTEST.BANK_INFO_PAC
(
    BANK_INFO_PAC_INT_ID       NUMBER(9) not null
        constraint BANK_INFO_PAC_PK
            primary key,
    CREATION_DATE              DATE,
    LAST_MODIFICATION_DATE     DATE,
    LAST_MODIFY_USER           VARCHAR2(20),
    AMOUNT                     NUMBER(11, 2),
    LOAN_REPAY_AMOUNT          NUMBER(11, 2),
    FIRST_MONTH                NUMBER(4),
    FIRST_YEAR                 NUMBER(4),
    DRAW_DAY                   NUMBER(4),
    BANK_NAME                  VARCHAR2(128),
    TRANSIT                    VARCHAR2(15),
    INSTITUTION_NUMBER         VARCHAR2(3),
    ACCOUNT_NUMBER             VARCHAR2(15),
    POLICY_OWNER_ACCOUNT_OWNER VARCHAR2(1),
    HOLDER_NAME                VARCHAR2(75)
)
/

create table SKYTEST.TRANSACTION_TYPE
(
    TRANSACTION_TYPE_INT_ID NUMBER(8)   not null
        constraint TRANSACTION_TYPE_PK
            primary key,
    TRANSACTION_TYPE        VARCHAR2(1) not null,
    DEFINITION_EN           VARCHAR2(100),
    DEFINITION_FR           VARCHAR2(100)
)
/

create table SKYTEST.PRODUCT_POLICY_CRIT_ILLNESS
(
    PRODUCT_POLICY_CRIT_ILL_INT_ID NUMBER(8) not null,
    BENEFITID                      NUMBER(8),
    DISCOUNTPRICE                  VARCHAR2(1),
    MAXINSURED                     NUMBER(10, 2),
    COVERAGETYPE                   NUMBER(2)
)
/

create index SKYTEST.PRODUCT_POL_CRIT_ILL_IDX1
    on SKYTEST.PRODUCT_POLICY_CRIT_ILLNESS (PRODUCT_POLICY_CRIT_ILL_INT_ID)
/

alter table SKYTEST.PRODUCT_POLICY_CRIT_ILLNESS
    add constraint PRODUCT_POL_CRIT_ILL_PK
        primary key (PRODUCT_POLICY_CRIT_ILL_INT_ID)
/

create table SKYTEST.PRODUCT_POLICY_DI
(
    PRODUCT_POLICY_DI_INT_ID   NUMBER(8) not null,
    SING_JO1_JOLAST            NUMBER(2) not null,
    LAST_RENEWABLE_AGE         NUMBER(3),
    LAST_CONVERTABLE_AGE       NUMBER(2),
    CONVERTABLE_WAITING_PERIOD NUMBER(2),
    PAYABLE_TO_AGE             NUMBER(3),
    AGETYPE                    VARCHAR2(1),
    MLOW_ISSUE_AGE             NUMBER(3),
    MHIGH_ISSUE_AGE            NUMBER(3),
    FLOW_ISSUE_AGE             NUMBER(3),
    FHIGH_ISSUE_AGE            NUMBER(3),
    MLOW_ISSUE_AGE_SMOKER      NUMBER(3),
    MHIGH_ISSUE_AGE_SMOKER     NUMBER(3),
    FLOW_ISSUE_AGE_SMOKER      NUMBER(3),
    FHIGH_ISSUE_AGE_SMOKER     NUMBER(3),
    MULTI_LIFE                 VARCHAR2(1),
    BACK_DATE                  NUMBER(2),
    DISCOUNTPRICE              VARCHAR2(1),
    ACCIDENT                   VARCHAR2(1),
    SICKNESS                   VARCHAR2(1),
    ASRIDER                    VARCHAR2(1),
    ONLY_ONE_PERIOD            VARCHAR2(1) default 'N'
)
/

create index SKYTEST.PRODUCT_POLICY_DI_IDX1
    on SKYTEST.PRODUCT_POLICY_DI (PRODUCT_POLICY_DI_INT_ID)
/

alter table SKYTEST.PRODUCT_POLICY_DI
    add constraint PRODUCT_POL_DI_PK
        primary key (PRODUCT_POLICY_DI_INT_ID)
/

create table SKYTEST.CANNEX_INTEREST_RATE_NEW_LOAN
(
    INTEREST_RATE_INT_ID NUMBER(10) not null
        constraint SYS_C0097870
            primary key
        constraint SYS_C0097867
            check ("INTEREST_RATE_INT_ID" IS NOT NULL)
        constraint SYS_C0097868
            check ("INTEREST_RATE_INT_ID" IS NOT NULL)
        constraint SYS_C0097869
            check ("INTEREST_RATE_INT_ID" IS NOT NULL),
    DTYPE                VARCHAR2(31),
    CHANGE               NUMBER(6, 4),
    CHANGE_DATE_STRING   VARCHAR2(8),
    CHANGE_TIME_STRING   VARCHAR2(20),
    CREATION_DATE        TIMESTAMP(6),
    INTEREST_RATE_VALUE  NUMBER(6, 4),
    LAST_MODIFICATION    TIMESTAMP(6),
    TERM_VALUE           VARCHAR2(255),
    UNIQUE_NUMBER        NUMBER(19),
    CANNEX_PRODUCT       NUMBER(10),
    DONT_DELETE          VARCHAR2(1),
    PURCHASE_RATE        NUMBER(6, 4),
    PURCHASE_RATE_CHANGE NUMBER(6, 4),
    CASH_ADVANCE_RATE    NUMBER(6, 4),
    CASH_ADVANCE_CHANGE  NUMBER(6, 4),
    MIN_AMOUNT           NUMBER(14, 2),
    MAX_AMOUNT           NUMBER(14, 2),
    MIN_BALANCE          NUMBER(10, 2),
    MAX_BALANCE          NUMBER(10, 2)
)
/

create table SKYTEST.FD_ALLOCATION_PY
(
    ALLOC_SEQ                    NUMBER(11)              not null
        primary key,
    ALLOC_CREATION_DATE          DATE                    not null,
    ALLOC_FUNDATAKEY             NUMBER(11),
    ALLOC_FUNDATA_DATE           DATE,
    ALLOC_TYPE                   VARCHAR2(8),
    ALLOC_PERCENT                NUMBER(11, 6),
    ALLOC_ENABLED                VARCHAR2(1) default 'Y' not null,
    ALLOC_DESC_EN                VARCHAR2(32),
    ALLOC_DESC_FR                VARCHAR2(32),
    ALLOC_LAST_MODIFICATION_DATE DATE
)
/

create index SKYTEST."FD_ALLOCATION_IDX1_copy1"
    on SKYTEST.FD_ALLOCATION_PY (ALLOC_FUNDATAKEY)
/

create table SKYTEST.FD_FUNDSERVE_PY
(
    SERV_SEQ                    NUMBER(11) not null
        primary key,
    SERV_CREATION_DATE          DATE,
    SERV_FUNDATAKEY             NUMBER(11),
    SERV_CODE                   VARCHAR2(8),
    SERV_LOAD_CODE              VARCHAR2(4),
    SERV_LOAD_DESC_EN           VARCHAR2(64),
    SERV_LOAD_DESC_FR           VARCHAR2(64),
    SERV_LAST_MODIFICATION_DATE VARCHAR2(7),
    FD_FUND                     VARCHAR2(11)
)
/

create table SKYTEST.FD_FUND_MANAGER_PY
(
    MANAGER_SEQ                    NUMBER(11)  not null
        primary key,
    MANAGER_CREATION_DATE          DATE        not null,
    MANAGER_FUNDATAKEY             NUMBER(11),
    MANAGER_KEY                    NUMBER(11),
    MANAGER_NAME                   VARCHAR2(64),
    MANAGER_BIO_EN                 VARCHAR2(512),
    MANAGER_BIO_FR                 VARCHAR2(512),
    MANAGER_ENABLED                VARCHAR2(1) not null,
    MANAGER_LAST_MODIFICATION_DATE DATE,
    FD_FUND2                       NUMBER(11)
)
/

create table SKYTEST.WELLNESS_PLAN_PRODUCT
(
    WELLNESS_PRODUCT_ID NUMBER(8) not null
        constraint WELLNESS_PLAN_PRODUCT_PK
            primary key,
    WELLNESS_PLAN       NUMBER(8),
    PRODUCTID           NUMBER(8),
    COMPANYID           NUMBER(8),
    RATE                NUMBER(11, 5),
    MIN_VALUE           NUMBER(15, 5),
    MAX_VALUE           NUMBER(15, 5),
    INITIAL_VALUE       NUMBER(15, 5)
)
/

create table SKYTEST.COMMISSION_PAYABLE_TYPE
(
    COMM_PAY_TYPE_INT_ID NUMBER(11)    not null
        constraint COMMISSION_PAYABLE_TYPE_PK
            primary key,
    COMM_TYPE_ID_OLD     NUMBER(11),
    DEFINITION_EN        VARCHAR2(100) not null,
    DEFINITION_FR        VARCHAR2(100),
    ABBR_EN              VARCHAR2(20),
    ABBR_FR              VARCHAR2(20),
    CATEGORY             VARCHAR2(2),
    DEBIT_CREDIT         VARCHAR2(1),
    PRODUCT_RIDER        VARCHAR2(1)
)
/

create table SKYTEST.UNDERWRITING_REQUIREMENT
(
    UW_REQUIREMENT_INT_ID  NUMBER(9)     not null
        constraint UNDERWRITING_REQUIREMENT_PK
            primary key,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    NAME                   VARCHAR2(128) not null,
    VALUE                  NUMBER(11)    not null,
    SUBLIST_TYPE           NUMBER(4)     not null,
    DESC_EN                VARCHAR2(256),
    DESC_FR                VARCHAR2(256),
    FOLLOW_UP_DAY          NUMBER(4),
    TEMPLATE_ID            NUMBER(9),
    DEPRECATED             VARCHAR2(1),
    EMAIL_ADVISOR          VARCHAR2(1),
    OLD_SYSTEM_CODE        VARCHAR2(4),
    TYPE_CLASS             NUMBER(8)
        constraint UNDERWRITING_REQUIREMENT__FK1
            references SKYTEST.TYPE_CLASS,
    DEFINITION_EN          VARCHAR2(2000),
    DEFINITION_FR          VARCHAR2(2000),
    LIFE_ADMIN_REQ         VARCHAR2(1),
    CRIT_ADMIN_REQ         VARCHAR2(1),
    DI_ADMIN_REQ           VARCHAR2(1),
    SEG_ADMIN_REQ          VARCHAR2(1),
    GIC_ADMIN_REQ          VARCHAR2(1),
    US_ONLY                VARCHAR2(1),
    CANADA_ONLY            VARCHAR2(1),
    US_CANADA              VARCHAR2(1),
    POLICY_FOLLOW_UP       NUMBER(4)
)
/

create table SKYTEST.FD_MAST_QUARTILE
(
    MAST_SEQ                NUMBER not null
        constraint FD_MAST_QUARTILE_PK
            primary key,
    MAST_CREATION_TIMESTAMP DATE   not null,
    MAST_ONE_MONTH_RETURN   NUMBER(28, 6),
    MAST_THREE_MONTH_RETURN NUMBER(28, 6),
    MAST_YTD_RETURN         NUMBER(28, 6),
    MAST_ONE_YR_RETURN      NUMBER(28, 6),
    MAST_TWO_YR_RETURN      NUMBER(28, 6),
    MAST_THREE_YR_RETURN    NUMBER(28, 6),
    MAST_FOUR_YR_RETURN     NUMBER(28, 6),
    MAST_FIVE_YR_RETURN     NUMBER(28, 6),
    MAST_FUNDATAKEY         NUMBER not null,
    MAST_INCEPTION_RETURN   NUMBER(28, 6)
)
/

create index SKYTEST.FD_MAST_QUARTILE_IDX2
    on SKYTEST.FD_MAST_QUARTILE (MAST_FUNDATAKEY)
/

create table SKYTEST.FD_MONTHLY_BEST_BY_CAT
(
    PERF_SEQ      NUMBER(11),
    CREATION_DATE DATE,
    SEQ_NUMBER    NUMBER(5) not null
        constraint FD_MONTHLY_BEST_BY_CAT_PK
            primary key
)
/

create table SKYTEST.FD_DAILY_MOVERS
(
    SEQ_NUMBER    NUMBER(5) not null
        constraint FD_BEST_DAILY_MOVERS_PK
            primary key,
    FUND_DATAKEY  NUMBER(9),
    CREATION_DATE DATE
)
/

create table SKYTEST.AGENT
(
    AGENTID            NUMBER(11)   not null
        constraint AGENTID_PK
            primary key,
    AGENTTYPE          NUMBER(5)    not null,
    CREATEDATE         DATE         not null,
    FIRSTNAME          VARCHAR2(35) not null,
    LASTNAME           VARCHAR2(35) not null,
    LASTUPDATEDATE     DATE,
    LASTUPDATEUSER     VARCHAR2(30),
    PREFERREDLANGUAGE  NUMBER(1),
    SEX                VARCHAR2(1),
    AGENTINDICATOR     VARCHAR2(10),
    AGENTSTATUS        NUMBER(4),
    STATUSDATE         DATE,
    BIRTHDAY           DATE,
    SIN                VARCHAR2(11),
    DEATHDATE          DATE,
    RETIREMENTDATE     DATE,
    NOTEID             NUMBER(11),
    CORPORATENAME      VARCHAR2(100),
    INSURFACT_CUSTOMER VARCHAR2(1),
    OLD_AGENT_KEY      NUMBER(11),
    ADVISORNUMBER      VARCHAR2(15),
    AIRMILESNUM        VARCHAR2(15),
    PREFPARACOMP1      NUMBER(3),
    PREFPARACOMP2      NUMBER(3),
    AIRMILESNAMEONCARD VARCHAR2(70),
    SUNTERMINATED      VARCHAR2(1),
    SUNDELDATE         DATE,
    FIRSTCONTACT       DATE,
    BLKSOLD            VARCHAR2(1),
    BLKSOLDDTE         DATE,
    BLKSOLDAGENTID     NUMBER(11),
    DRIVERSLICENSE     VARCHAR2(30),
    SUNTERMDATE        DATE,
    TRANSDTE           DATE,
    TRANSAGENTID       NUMBER(11),
    TMPQUICKBOOKS      VARCHAR2(1),
    SUNLIFE_BONUS      NUMBER(5, 2),
    SUNLIFE_BONUSDATE  DATE,
    EFTLETTERSENT      DATE,
    SUNVIP             VARCHAR2(1),
    FUNDSERVCODE       VARCHAR2(35),
    FUNDSERSENT        DATE,
    TEL                VARCHAR2(20),
    FAX                VARCHAR2(20),
    EMAIL              VARCHAR2(100)
)
/

create index SKYTEST.AGENT_IDX1
    on SKYTEST.AGENT (LASTNAME, FIRSTNAME)
/

create index SKYTEST.AGENT_IDX2
    on SKYTEST.AGENT (UPPER("LASTNAME"))
/

create index SKYTEST.AGENT_IDX3
    on SKYTEST.AGENT (UPPER("CORPORATENAME"))
/

create table SKYTEST.CONTACT_ADDRESS
(
    CONTACT NUMBER(8) not null,
    ADDRESS NUMBER(8) not null,
    constraint CONTACT_ADDRESS_PK
        primary key (ADDRESS, CONTACT)
)
/

create index SKYTEST.CONTACT_ADDRESS_IDX1
    on SKYTEST.CONTACT_ADDRESS (ADDRESS)
/

create index SKYTEST.CONTACT_ADDRESS_IDX2
    on SKYTEST.CONTACT_ADDRESS (CONTACT)
/

create table SKYTEST.SUN_ALL_POLICY_WITH_ADVISORT
(
    POLICYID              NUMBER(11),
    CLIENTID              NUMBER(11),
    SMOKER                VARCHAR2(1),
    ISSUEAGE              NUMBER(2),
    FIRSTNAME             VARCHAR2(50),
    LASTNAME              VARCHAR2(50),
    SEX                   VARCHAR2(1),
    BIRTHDAY              DATE,
    PREFERREDLANGUAGE     NUMBER(1),
    PHONE_CLASS           VARCHAR2(20),
    AREACODE              VARCHAR2(6),
    PHONENUMBER           VARCHAR2(25),
    ADDRESSTYPE           VARCHAR2(20),
    ADDRESSLINE1          VARCHAR2(100),
    ADDRESSLINE2          VARCHAR2(100),
    CITY                  VARCHAR2(50),
    PROVSTATE             NUMBER(5),
    NAME_EN               VARCHAR2(40),
    NAME_FR               VARCHAR2(40),
    POSTALZIPCODE         VARCHAR2(12),
    COUNTRY               NUMBER(5),
    ADVISORNUMBER         VARCHAR2(15),
    OLD_AGENT_FIRSTNAME   VARCHAR2(150),
    OLD_AGENT_LASTNAME    VARCHAR2(150),
    AGENTID               NUMBER(15),
    AGENT_FIRSTNAME       VARCHAR2(150),
    AGENT_LASTNAME        VARCHAR2(150),
    BRANCH                VARCHAR2(64),
    SUNTERMINATED         VARCHAR2(1),
    SUNTERMDATE           DATE,
    COMPANYID             NUMBER(3),
    NAMEENGLISH           VARCHAR2(60),
    NAMEFRENCH            VARCHAR2(60),
    PRODUCTID             NUMBER(11),
    PRODUCTTYPE           VARCHAR2(10),
    DESCENGLISH           VARCHAR2(60),
    DESCFRENCH            VARCHAR2(60),
    ENGLISH_NAME          VARCHAR2(64),
    FRENCH_NAME           VARCHAR2(64),
    SHORT_ENGLISH_DESC    VARCHAR2(1000),
    SHORT_FRENCH_DESC     VARCHAR2(1000),
    LAST_RENEWABLE_AGE    NUMBER(3),
    LAST_CONVERTABLE_AGE  NUMBER(2),
    PRODUCTCLASS          VARCHAR2(10),
    TERM_OR_PERM          VARCHAR2(1),
    CI_NUM_ILL            NUMBER(2),
    POLICYNUMBER          VARCHAR2(15),
    POLICYSTATUS          NUMBER(4),
    ISSUEDATE             DATE,
    FACEAMT               NUMBER(19),
    ANNPREM               NUMBER(19),
    MODPREM               NUMBER(19),
    STATUSDATE            DATE,
    EXPIRYDATE            DATE,
    TERMEXPDTE            DATE,
    PAYFREQ               NUMBER(4),
    SUN_POLICY_ID         NUMBER(11) not null
        constraint SUN_POLICY_PK_ID
            primary key,
    MARKETING_REG         VARCHAR2(64),
    MARKETING_REG_NAME_EN VARCHAR2(64),
    BRANCH_NAME_FR        VARCHAR2(64),
    MARKETING_REG_NAME_FR VARCHAR2(64),
    BRANCH_NAME_EN        VARCHAR2(64),
    NO_AGENT_FLAG         VARCHAR2(1),
    POLICY_DESCRIPTION_EN VARCHAR2(100),
    POLICY_DESCRIPTION_FR VARCHAR2(100)
)
/

create trigger SKYTEST.SUN_ON_INSERT
    before insert
    on SKYTEST.SUN_ALL_POLICY_WITH_ADVISORT
    for each row
BEGIN
  SELECT sun_sequence_pk.nextval
  INTO :new.SUN_POLICY_ID
  FROM dual;
END;
/

create table SKYTEST.CANNEX_COMPANY
(
    COMPANY_INT_ID       NUMBER(10)  not null
        primary key,
    COMPANY_CODE         VARCHAR2(4) not null,
    COMPANY_DESC_EN      VARCHAR2(128),
    COMPANY_DESC_FR      VARCHAR2(128),
    IPNO                 VARCHAR2(4) not null,
    COMPANY_METHOD       VARCHAR2(16),
    COMPANY_WEBSITE_RATE VARCHAR2(256),
    COMPANY_TYPE         NUMBER(4),
    LAST_MODIFICATION    DATE,
    CREATION_DATE        DATE,
    DONT_DELETE          VARCHAR2(1)
)
/

create index SKYTEST.CANNEX_COMPANY_INDEX1
    on SKYTEST.CANNEX_COMPANY (COMPANY_TYPE)
/

create table SKYTEST.CANNEX_INTEREST_RATE_NEW_LPRM
(
    INTEREST_RATE_INT_ID NUMBER(10) not null
        constraint SYS_C0097862
            primary key
        constraint SYS_C0097859
            check ("INTEREST_RATE_INT_ID" IS NOT NULL)
        constraint SYS_C0097860
            check ("INTEREST_RATE_INT_ID" IS NOT NULL)
        constraint SYS_C0097861
            check ("INTEREST_RATE_INT_ID" IS NOT NULL),
    DTYPE                VARCHAR2(31),
    CHANGE               NUMBER(6, 4),
    CHANGE_DATE_STRING   VARCHAR2(8),
    CHANGE_TIME_STRING   VARCHAR2(20),
    CREATION_DATE        TIMESTAMP(6),
    INTEREST_RATE_VALUE  NUMBER(6, 4),
    LAST_MODIFICATION    TIMESTAMP(6),
    TERM_VALUE           VARCHAR2(255),
    UNIQUE_NUMBER        NUMBER(19),
    CANNEX_PRODUCT       NUMBER(10),
    DONT_DELETE          VARCHAR2(1),
    PURCHASE_RATE        NUMBER(6, 4),
    PURCHASE_RATE_CHANGE NUMBER(6, 4),
    CASH_ADVANCE_RATE    NUMBER(6, 4),
    CASH_ADVANCE_CHANGE  NUMBER(6, 4),
    MIN_AMOUNT           NUMBER(14, 2),
    MAX_AMOUNT           NUMBER(14, 2)
)
/

create table SKYTEST.CANNEX_INTEREST_RATE_NEW_TERM
(
    INTEREST_RATE_INT_ID NUMBER(10) not null
        constraint SYS_C0099237
            primary key
        constraint SYS_C0099233
            check ("INTEREST_RATE_INT_ID" IS NOT NULL)
        constraint SYS_C0099234
            check ("INTEREST_RATE_INT_ID" IS NOT NULL)
        constraint SYS_C0099235
            check ("INTEREST_RATE_INT_ID" IS NOT NULL)
        constraint SYS_C0099236
            check ("INTEREST_RATE_INT_ID" IS NOT NULL),
    DTYPE                VARCHAR2(31),
    CHANGE               NUMBER(6, 4),
    CHANGE_DATE_STRING   VARCHAR2(8),
    CHANGE_TIME_STRING   VARCHAR2(20),
    CREATION_DATE        TIMESTAMP(6),
    INTEREST_RATE_VALUE  NUMBER(6, 4),
    LAST_MODIFICATION    TIMESTAMP(6),
    TERM_VALUE           VARCHAR2(255),
    UNIQUE_NUMBER        NUMBER(19),
    CANNEX_PRODUCT       NUMBER(10),
    DONT_DELETE          VARCHAR2(1),
    PURCHASE_RATE        NUMBER(6, 4),
    PURCHASE_RATE_CHANGE NUMBER(6, 4),
    CASH_ADVANCE_RATE    NUMBER(6, 4),
    CASH_ADVANCE_CHANGE  NUMBER(6, 4),
    MIN_AMOUNT           NUMBER(14, 2),
    MAX_AMOUNT           NUMBER(14, 2),
    PAYMENT_FREQUENCY    VARCHAR2(2),
    TERM_DURATION        VARCHAR2(1)
)
/

create table SKYTEST.FD_BENCHMARK_PY
(
    ASSOC_CAN_INDEX_ID      NUMBER(11)    not null
        primary key,
    CREATION_DATE           DATE          not null,
    ASSOC_CAN_INDEX_NAME    VARCHAR2(100) not null,
    ASSOC_CAN_INDEX_NAME_FR VARCHAR2(100),
    ONE_MONTH_RETURN        NUMBER(11, 6) not null,
    THREE_MONTH_RETURN      NUMBER(11, 6) not null,
    YTD_RETURN              NUMBER(11, 6) not null,
    INCEPT_RETURN           NUMBER(11, 6) not null,
    YR1_COMPOUND_RETURN     NUMBER(11, 6),
    YR2_COMPOUND_RETURN     NUMBER(11, 6),
    YR3_COMPOUND_RETURN     NUMBER(11, 6),
    YR4_COMPOUND_RETURN     NUMBER(11, 6),
    YR5_COMPOUND_RETURN     NUMBER(11, 6),
    YR3_ANNUALIZED_STD_DEV  NUMBER(11, 6),
    FUNDATA_DATE            DATE
)
/

create table SKYTEST.FFLEX_ORDER
(
    FUND_ENABLED                   VARCHAR2(1),
    FUND_FUNDATAKEY                NUMBER(11) not null
        constraint FFLEX_ORDER_PK
            primary key,
    FUND_NAME_EN                   VARCHAR2(250),
    FUND_NAME_FR                   VARCHAR2(250),
    FD_TYPE                        NUMBER(11),
    PERF_ONE_DAY_RETURN            NUMBER(11, 6),
    DAILY_PRICE_NAVPS              NUMBER(15, 6),
    PERF_ONE_MONTH_RETURN          NUMBER(11, 6),
    PERF_THREE_MONTH_RETURN        NUMBER(11, 6),
    PERF_YTD_RETURN                NUMBER(11, 6),
    FUND_RRSP_CODE                 VARCHAR2(12),
    FUND_OBJECTIVE_EN              VARCHAR2(500),
    FUND_OBJECTIVE_FR              VARCHAR2(500),
    BENCHMARK_YTD_RETURN           NUMBER(11, 6),
    FUND_TOTAL_ASSETS              NUMBER(15, 6),
    FUND_LOAD_EN                   VARCHAR2(255),
    FUND_LOAD_FR                   VARCHAR2(255),
    PRICE_CHANGE_FROM_PREVIOUS_DAY NUMBER(15, 6),
    PERF_FUND_GRADE                VARCHAR2(1),
    ONE_MONTH_QUARTILE             VARCHAR2(1),
    THREE_MONTH_QUARTILE           VARCHAR2(1),
    YR3_ANNUALIZED_STD_DEV         NUMBER(11, 6),
    YR3_VOLATILITY_RANKING         NUMBER(2),
    FUND_MER                       NUMBER(15, 6),
    DAILY_PRICE_NAVPS_DATE         DATE
)
/

create table SKYTEST.WEB_CONTENT
(
    WEB_CONTENT_INT_ID     NUMBER(8) not null
        constraint WEB_CONTENT_PK
            primary key,
    CONTENT_NAME           VARCHAR2(256),
    CONTENT_LEVEL          NUMBER(4),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    EFFECTIVE_DATE         DATE,
    EXPIRATION_DATE        DATE,
    CONTENT_EN             VARCHAR2(4000),
    CONTENT_FR             VARCHAR2(4000),
    CONTENT_FORMAT         NUMBER(4),
    CONTENT_CATEGORY       NUMBER(4),
    OVERRIDE_CSS           VARCHAR2(1024),
    TESTING_MODE           VARCHAR2(1),
    TAGS                   VARCHAR2(256),
    CONTENT_TITLE_EN       VARCHAR2(256) default 256,
    CONTENT_TITLE_FR       VARCHAR2(256),
    IMAGE_TYPE             NUMBER(4),
    RECIPIENT_TYPE         NUMBER(4)
)
/

create index SKYTEST.WEB_CONTENT_INDEX1
    on SKYTEST.WEB_CONTENT (CONTENT_NAME)
/

create index SKYTEST.WEB_CONTENT_INDEX2
    on SKYTEST.WEB_CONTENT (EXPIRATION_DATE, WEB_CONTENT_INT_ID, CONTENT_CATEGORY)
/

create index SKYTEST.WEB_CONTENT_INDEX3
    on SKYTEST.WEB_CONTENT (TAGS)
/

create table SKYTEST.TYPES
(
    TYPE_INT_ID            NUMBER(8) not null
        constraint TYPES_PK
            primary key,
    TYPE_CLASS             NUMBER(8) not null
        constraint TYPES_TYPE_CLASS_FK1
            references SKYTEST.TYPE_CLASS,
    TYPE_VALUE             NUMBER    not null,
    DESC_EN                VARCHAR2(128),
    DESC_FR                VARCHAR2(128),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    SUB_TYPE               NUMBER(8),
    OLD_TYPE               NUMBER(8),
    ACORD_NAME             VARCHAR2(128),
    DEFINITION_EN          VARCHAR2(1024),
    DEFINITION_FR          VARCHAR2(1024),
    CATEGORY               VARCHAR2(32),
    LIFE_ADMIN_REQ         VARCHAR2(1),
    CRIT_ADMIN_REQ         VARCHAR2(1),
    DI_ADMIN_REQ           VARCHAR2(1),
    SEG_ADMIN_REQ          VARCHAR2(1),
    GIC_ADMIN_REQ          VARCHAR2(1),
    US_ONLY                VARCHAR2(1),
    CANADA_ONLY            VARCHAR2(1),
    US_CANADA              VARCHAR2(1),
    POLICY_FOLLOW_UP       NUMBER(4),
    FOLLOW_UP_DAY          NUMBER(4),
    OLD_SYSTEM_CODE        VARCHAR2(4),
    ORDER_FIELD            NUMBER(4),
    COVERAGETYPE           VARCHAR2(1)
)
/

comment on column SKYTEST.TYPES.COVERAGETYPE is 'used by widget and mobile for productlist filter'
/

create table SKYTEST.FD_FILE_IMPORTING
(
    FILE_SEQ         NUMBER                    not null
        constraint SYS_C0197869
            primary key
        constraint SYS_C0197860
            check ("FILE_SEQ" IS NOT NULL)
        constraint SYS_C0197863
            check ("FILE_SEQ" IS NOT NULL)
        constraint SYS_C0197866
            check ("FILE_SEQ" IS NOT NULL),
    LAST_IMPORT_DATE TIMESTAMP(6)              not null
        constraint SYS_C0197861
            check ("LAST_IMPORT_DATE" IS NOT NULL)
        constraint SYS_C0197864
            check ("LAST_IMPORT_DATE" IS NOT NULL)
        constraint SYS_C0197867
            check ("LAST_IMPORT_DATE" IS NOT NULL),
    FILENAME         VARCHAR2(150) default 'Y' not null
        constraint SYS_C0197862
            check ("FILENAME" IS NOT NULL)
        constraint SYS_C0197865
            check ("FILENAME" IS NOT NULL)
        constraint SYS_C0197868
            check ("FILENAME" IS NOT NULL)
)
/

create table SKYTEST.FD_HOLDING
(
    HOLDING_SEQ                    NUMBER(11) not null
        constraint HOLDING_SEQ_PK
            primary key,
    HOLDING_CREATION_DATE          DATE       not null,
    HOLDING_FUNDATA_DATE           DATE,
    HOLDING_RANK                   NUMBER(11),
    HOLDING_SECURITY_NAME          VARCHAR2(128),
    HOLDING_MARKET_PERCENT         NUMBER(11, 6),
    HOLDING_ENABLED                VARCHAR2(1),
    HOLDING_FUNDATAKEY             NUMBER(11),
    HOLDING_LAST_MODIFICATION_DATE DATE,
    FD_FUND                        NUMBER(11),
    SECURITY_NAME_ID               NUMBER(11)
)
/

create index SKYTEST.FD_HOLDING_INDEX3
    on SKYTEST.FD_HOLDING (SECURITY_NAME_ID)
/

create index SKYTEST.FD_HOLDING_INDEX2
    on SKYTEST.FD_HOLDING (FD_FUND)
/

create index SKYTEST.FD_HOLDING_INDEX1
    on SKYTEST.FD_HOLDING (HOLDING_SECURITY_NAME)
/

create index SKYTEST.FD_HOLDING_IDX1
    on SKYTEST.FD_HOLDING (HOLDING_FUNDATAKEY)
/

create table SKYTEST.FD_PERF_BEST_BY_TYPE
(
    PERF_BEST_BY_TYPE_INT_ID      NUMBER(9) not null
        constraint FD_PERF_BEST_BY_TYPE_PK
            primary key,
    ONE_MONTH_RET_VALUE           NUMBER(9, 4),
    ONE_MONTH_RET_DATAKEY         NUMBER(9),
    THREE_MONTH_RET_DATAKEY       NUMBER(9),
    THREE_MONTH_RET_VALUE         NUMBER(9, 4),
    YTD_RET_VALUE                 NUMBER(9, 4),
    YTD_RET_DATAKEY               NUMBER(9),
    INCEPTION_RET_DATAKEY         NUMBER(9),
    INCEPTION_RET_VALUE           NUMBER(9, 4),
    ONE_YR_COMPOUND_RET_VALUE     NUMBER(9, 4),
    ONE_YR_COMPOUND_RET_DATAKEY   NUMBER(9),
    TWO_YR_COMPOUND_RET_VALUE     NUMBER(9, 4),
    TWO_YR_COMPOUND_RET_DATAKEY   NUMBER(9),
    THREE_YR_COMPOUND_RET_DATAKEY NUMBER(9),
    THREE_YR_COMPOUND_RET_VALUE   NUMBER(9, 4),
    FOUR_YR_COMPOUND_RET_DATAKEY  NUMBER(9),
    FOUR_YR_COMPOUND_RET_VALUE    NUMBER(9, 4),
    FIVE_YR_COMPOUND_RET_DATAKEY  NUMBER(9),
    FIVE_YR_COMPOUND_RET_VALUE    NUMBER(9, 4),
    CREATION_DATE                 DATE,
    LAST_MODIFICATION_DATE        DATE,
    FUND_TYPE_EN                  VARCHAR2(1000),
    FUND_TYPE_FR                  VARCHAR2(1000)
)
/

create index SKYTEST.FD_PERF_BEST_BY_TYPE_IDX2
    on SKYTEST.FD_PERF_BEST_BY_TYPE (FUND_TYPE_EN)
/

create index SKYTEST.FD_PERF_BEST_BY_TYPE_IDX3
    on SKYTEST.FD_PERF_BEST_BY_TYPE (FUND_TYPE_FR)
/

create table SKYTEST.SADVISOR
(
    ADVISOR_INT_ID   NUMBER(10) not null
        primary key,
    ADDRESS_LINE1    VARCHAR2(128),
    ADDRESS_LINE2    VARCHAR2(128),
    ADVISOR          NUMBER(10),
    AGENCY_NAME      VARCHAR2(128),
    AGENCY_NUMBER    VARCHAR2(20),
    AGENT_NUMBER     VARCHAR2(20),
    AREA_CODE        VARCHAR2(3),
    CITY             VARCHAR2(64),
    COUNTRY          VARCHAR2(32),
    EMAIL            VARCHAR2(128),
    EXTENSION        VARCHAR2(10),
    FIRSTNAME        VARCHAR2(64),
    LANGUAGE         VARCHAR2(3),
    LASTNAME         VARCHAR2(64),
    MARKETING_REGION VARCHAR2(128),
    POSTAL_CODE      VARCHAR2(7),
    PROVINCE         VARCHAR2(32),
    PUBLIC_NAME      VARCHAR2(128),
    TELEPHONE        VARCHAR2(9)
)
/

create table SKYTEST.USER_LOGIN_COUNT
(
    USER_INT_ID  NUMBER(8) not null
        primary key,
    ACCESS_TOTAL NUMBER(10)
)
/

create table SKYTEST.SUN_HEADING_TO_LAPSE_TOTALS
(
    AGENTID               NUMBER(11) not null
        primary key,
    ADVISORNUMBER         VARCHAR2(15),
    OLD_AGENT_FIRSTNAME   VARCHAR2(150),
    OLD_AGENT_LASTNAME    VARCHAR2(150),
    SUNTERMDATE           DATE,
    TOTPOLS               NUMBER(8),
    TOTCLI                NUMBER(8),
    TOTPREM               NUMBER(10, 2),
    BRANCH                VARCHAR2(64),
    MARKETING_REG         VARCHAR2(64),
    BRANCH_NAME_EN        VARCHAR2(64),
    MARKETING_REG_NAME_EN VARCHAR2(64),
    BRANCH_NAME_FR        VARCHAR2(64),
    MARKETING_REG_NAME_FR VARCHAR2(64)
)
/

create table SKYTEST.CONTRACT_PROVINCE
(
    CONTRACT NUMBER(8)   not null,
    PROVINCE VARCHAR2(2) not null,
    constraint CONTRACT_PROVINCE_PK
        primary key (PROVINCE, CONTRACT)
)
/

create index SKYTEST.CONTRACT_PROVINCE_IDX2
    on SKYTEST.CONTRACT_PROVINCE (PROVINCE)
/

create index SKYTEST.CONTRACT_PROVINCE_IDX1
    on SKYTEST.CONTRACT_PROVINCE (CONTRACT)
/

create table SKYTEST.XML_FILE
(
    XML_FILE_INT_ID   NUMBER(8)     not null
        constraint XML_FILE_RECORD_PK
            primary key,
    FILENAME          VARCHAR2(128) not null,
    FILE_PATH         VARCHAR2(128) not null,
    VERSION           NUMBER(5),
    MGMT_COMPANY_CODE VARCHAR2(3),
    DEALER_CODE       VARCHAR2(4),
    RECORD_COUNT      NUMBER(8),
    CREATION_DATE     DATE,
    EFFECTIVE_DATE    DATE,
    FILE_CODE         VARCHAR2(2),
    FILE_TYPE         VARCHAR2(3),
    SEQUENCE          NUMBER(6),
    SUB_RECORD_COUNT  NUMBER(5),
    DOWNLOADED        VARCHAR2(1),
    IMPORTED          VARCHAR2(1)
)
/

create table SKYTEST.PS_ACCOUNT_DETAIL
(
    PS_ACCOUNT_DETAIL_INT_ID  NUMBER(8) not null
        constraint PS_ACCOUNT_DETAIL_INT_PK
            primary key,
    XML_FILE_INT_ID           NUMBER(8) not null
        constraint XML_FILE_ACCT_DETAIL_FK
            references SKYTEST.XML_FILE,
    MGMT_COMPANY_CODE         VARCHAR2(3),
    FUND_ACCOUNT_IDENTIFIER   VARCHAR2(15),
    DEALER_ACCOUNT_IDENTIFIER VARCHAR2(15),
    SALES_REP_CODE            VARCHAR2(5),
    ACCOUNT_DESIGNATION       VARCHAR2(1),
    ACCOUNT_TYPE              VARCHAR2(2),
    ACCOUNT_STATUS            VARCHAR2(1),
    ACCOUNT_CREATION_DATE     DATE,
    TAX_CODE                  VARCHAR2(3),
    SPOUSAL_ACCOUT_FLAG       VARCHAR2(1),
    INTERMEDIARY_CODE         VARCHAR2(4),
    INTERMIDIARY_ACCOUNT_ID   VARCHAR2(15),
    DEALER_CODE               VARCHAR2(4),
    LOCKED_IN_CODE            VARCHAR2(1),
    PROCESS_DATE              DATE,
    PROCESSING_STATUS         VARCHAR2(1),
    ACCOUNT_CLOSE_DATE        DATE,
    LAST_MODIFY_DATE          DATE
)
/

create table SKYTEST.MASTER_GROUP
(
    MASTER_GROUP_INT_ID    NUMBER(8) not null
        constraint MASTER_GROUP_PK
            primary key,
    MASTER_GROUP_CODE      VARCHAR2(32),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    DESCRIPTION_EN         VARCHAR2(256),
    DESCRIPTION_FR         VARCHAR2(256)
)
/

create index SKYTEST.INDEX11
    on SKYTEST.MASTER_GROUP (MASTER_GROUP_CODE)
/

create table SKYTEST.FD_MASTER_CATEGORY
(
    MASTER_CATEGORY_INT_ID NUMBER(11) not null
        constraint FD_MASTER_CATEGORY_PK
            primary key,
    ENGLISH_NAME           VARCHAR2(64),
    FRENCH_NAME            VARCHAR2(64),
    DESCRIPT_ENG           VARCHAR2(1500),
    DESCRIPT_FRE           VARCHAR2(1500),
    LASTUPDATE             DATE
)
/

create table SKYTEST.FD_CATEGORY2
(
    CATEGORY_ID     NUMBER(11) not null
        constraint FD_CATEGORY2_PK
            primary key,
    ENGLISH_NAME    VARCHAR2(64),
    FRENCH_NAME     VARCHAR2(64),
    NO_SEARCH       VARCHAR2(1),
    DESCRIPT_ENG    VARCHAR2(1500),
    DESCRIPT_FRE    VARCHAR2(1500),
    MASTER_CATEGORY NUMBER(11)
        constraint FD_CATEGORY2_FK1
            references SKYTEST.FD_MASTER_CATEGORY,
    CREATE_DATE     DATE,
    MODIF_DATE      DATE,
    CREATE_DATE_INT NUMBER(8),
    CREATE_TIME_INT NUMBER(6),
    MODIF_DATE_INT  NUMBER(8),
    MODIF_TIME_INT  NUMBER(6)
)
/

create table SKYTEST.FD_FUNDSERV2
(
    SERV_SEQ                    NUMBER(11) not null
        constraint FD_FUNDSERV2_PK
            primary key,
    SERV_CREATION_DATE          DATE,
    SERV_FUNDATAKEY             NUMBER(11),
    SERV_CODE                   VARCHAR2(8),
    SERV_LOAD_CODE              VARCHAR2(4),
    SERV_LOAD_DESC_EN           VARCHAR2(64),
    SERV_LOAD_DESC_FR           VARCHAR2(64),
    SERV_LAST_MODIFICATION_DATE DATE,
    FD_FUND                     NUMBER(11)
)
/

create table SKYTEST.FD_FUND_TYPE_PY
(
    CATEGORY_INT_ID    NUMBER(8)  not null,
    TYPE_KEY           NUMBER(11) not null,
    TYPE_ENGLISH_NAME  VARCHAR2(64),
    TYPE_FRENCH_NAME   VARCHAR2(64),
    NO_SEARCH          VARCHAR2(1),
    CIFSC_TYPE_DESC_EN VARCHAR2(1500),
    CIFSC_TYPE_DESC_FR VARCHAR2(1500),
    MASTER_CATEGORY    NUMBER(11)
        constraint FD_FUND_TYPE_PY_FD_MASTER_FK1
            references SKYTEST.FD_MASTER_CATEGORY,
    TYPE_CREATE_DATE   DATE
)
/

create unique index SKYTEST.FD_FUND_TYPE_PY_INDEX1
    on SKYTEST.FD_FUND_TYPE_PY (CATEGORY_INT_ID)
/

create unique index SKYTEST.FD_FUND_TYPE_PY_INDEX2
    on SKYTEST.FD_FUND_TYPE_PY (TYPE_KEY)
/

alter table SKYTEST.FD_FUND_TYPE_PY
    add constraint FD_FUND_TYPE_PY_PK
        primary key (TYPE_KEY)
/

create table SKYTEST.FD_SECURITYNAME_PY
(
    SECURITY_NAME_INT_ID        NUMBER(8)     not null
        primary key,
    SECURITY_NAME               VARCHAR2(128) not null,
    CREATION_DATE               DATE,
    SECURITY_FUND_HOLDING_TOTAL NUMBER(8)
)
/

create table SKYTEST.CLIEDIS_CODES
(
    CLIEDIS_CODES_INT_ID   NUMBER(9)     not null
        constraint CLIEDIS_CODES_PK
            primary key,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    NAME                   VARCHAR2(128) not null,
    VALUE                  NUMBER(11)    not null,
    SUBLIST_TYPE           NUMBER(4)     not null,
    DESC_EN                VARCHAR2(256),
    DESC_FR                VARCHAR2(256),
    FOLLOW_UP_DAY          NUMBER(4),
    TEMPLATE_ID            NUMBER(9),
    DEPRECATED             VARCHAR2(1),
    EMAIL_ADVISOR          VARCHAR2(1),
    OLD_SYSTEM_CODE        VARCHAR2(4),
    TYPE_CLASS             NUMBER(8)
        constraint CLIEDIS_CODES_FK1
            references SKYTEST.TYPE_CLASS,
    DEFINITION_EN          VARCHAR2(2000),
    DEFINITION_FR          VARCHAR2(2000),
    LIFE_ADMIN_REQ         VARCHAR2(1),
    CRIT_ADMIN_REQ         VARCHAR2(1),
    DI_ADMIN_REQ           VARCHAR2(1),
    SEG_ADMIN_REQ          VARCHAR2(1),
    GIC_ADMIN_REQ          VARCHAR2(1),
    US_ONLY                VARCHAR2(1),
    CANADA_ONLY            VARCHAR2(1),
    US_CANADA              VARCHAR2(1),
    POLICY_FOLLOW_UP       NUMBER(4)
)
/

create table SKYTEST.YAHOO_FINANCE_FEED
(
    YAHOO_FEED_INT_ID      NUMBER(9)    not null
        constraint YAHOO_FINANCE_FEED_PK
            primary key,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    TICKER                 VARCHAR2(20) not null,
    NAME                   VARCHAR2(256),
    ENABLED                VARCHAR2(1)  not null,
    START_TIME             DATE,
    END_TIME               DATE,
    WEEK_DAYS              VARCHAR2(1),
    WEEKEND                VARCHAR2(1),
    NAME_FR                VARCHAR2(256),
    LOW_52WEEKS            NUMBER(12, 4),
    HIGH_52WEEKS           NUMBER(12, 4)
)
/

create table SKYTEST.FD_CATEGORY
(
    CATEGORY_INT_ID NUMBER(8) not null
        constraint FD_CATEGORY_PK
            primary key,
    CATEGORY_ID     NUMBER(4),
    ENGLISH_NAME    VARCHAR2(64),
    FRENCH_NAME     VARCHAR2(64),
    NO_SEARCH       VARCHAR2(1),
    DESCRIPT_ENG    VARCHAR2(1500),
    DESCRIPT_FRE    VARCHAR2(1500),
    MASTER_CATEGORY NUMBER(11)
        constraint FD_CATEGORY_FK1
            references SKYTEST.FD_MASTER_CATEGORY
)
/

create table SKYTEST.FD_BEST_OVERALL
(
    BEST_OVERALL_INT_ID           NUMBER(8) not null
        constraint FD_BEST_OVERALL_PK
            primary key,
    CREATION_DATE                 DATE,
    ONE_MONTH_RETURN              NUMBER(11, 4),
    ONE_MONTH_RETURN_FUND         NUMBER(11),
    THREE_MONTH_RETURN            NUMBER(11, 4),
    THREE_MONTH_RETURN_FUND       NUMBER(11),
    YTD_RETURN                    NUMBER(11, 4),
    YTD_RETURN_FUND               NUMBER(11),
    INCEPTION_RETURN              NUMBER(11, 4),
    INCEPTION_RETURN_FUND         NUMBER(11),
    ONE_YR_COMPOUND_RETURN        NUMBER(11, 4),
    ONE_YR_COMPOUND_RETURN_FUND   NUMBER(11),
    TWO_YR_COMPOUND_RETURN        NUMBER(11, 4),
    TWO_YR_COMPOUND_RETURN_FUND   NUMBER(11),
    THREE_YR_COMPOUND_RETURN      NUMBER(11, 4),
    THREE_YR_COMPOUND_RETURN_FUND NUMBER(11),
    FOUR_YR_COMPOUND_RETURN       NUMBER(11, 4),
    FOUR_YR_COMPOUND_RETURN_FUND  NUMBER(11),
    FIVE_YR_COMPOUND_RETURN       NUMBER(11, 4),
    FIVE_YR_COMPOUND_RETURN_FUND  NUMBER(11)
)
/

create table SKYTEST.FD_SECURITY_NAME
(
    SECURITY_NAME_INT_ID NUMBER(8)     not null
        constraint FD_SECURITY_NAME_PK
            primary key,
    SECURITY_NAME        VARCHAR2(128) not null,
    CREATION_DATE        DATE
)
/

create table SKYTEST.ACCOUNT_TYPE
(
    ACCOUNT_TYPE_INT_ID NUMBER(8)   not null
        constraint ACCOUNT_TYPE_PK
            primary key,
    ACCOUNT_TYPE_ID     VARCHAR2(2) not null,
    DEFINITION_EN       VARCHAR2(50),
    DEFINITION_FR       VARCHAR2(50)
)
/

create unique index SKYTEST.ACCOUNT_TYPE_IDX1
    on SKYTEST.ACCOUNT_TYPE (ACCOUNT_TYPE_ID)
/

alter table SKYTEST.ACCOUNT_TYPE
    add constraint UNQ_ACCOUNT_TYPE_0
        unique (ACCOUNT_TYPE_ID)
/

create table SKYTEST.CLASSIFICATION
(
    CLASSIFICATION_INT_ID NUMBER(8) not null,
    ABBREVIATION          VARCHAR2(20),
    DEFINITION_EN         VARCHAR2(50),
    DEFINITION_FR         VARCHAR2(50)
)
/

create index SKYTEST.CLASSIFICATION_IDX1
    on SKYTEST.CLASSIFICATION (CLASSIFICATION_INT_ID)
/

alter table SKYTEST.CLASSIFICATION
    add constraint CLASSIFICATION_PK
        primary key (CLASSIFICATION_INT_ID)
/

create table SKYTEST.ROSTER
(
    AGCYNO           VARCHAR2(20),
    AGENCY_NAME      VARCHAR2(100),
    MARKETING_REGION VARCHAR2(50),
    AGENTNO          VARCHAR2(20) not null
        constraint SYS_C0057815
            primary key,
    FIRST_NAME       VARCHAR2(25),
    AGTPUBN          VARCHAR2(50),
    SURNAME          VARCHAR2(50),
    AGPERCI          VARCHAR2(5),
    AGPERTI          VARCHAR2(5),
    LANG             VARCHAR2(5),
    STRADD1          VARCHAR2(50),
    STRADD2          VARCHAR2(50),
    MUNICIP          VARCHAR2(50),
    DISTRCT          VARCHAR2(5),
    POSTLCD          VARCHAR2(10),
    AREACODE         VARCHAR2(20),
    TELXCHG          VARCHAR2(20),
    TELLOCL          VARCHAR2(20),
    TELXTEN          VARCHAR2(20),
    EMAIL            VARCHAR2(100),
    EMAIL2           VARCHAR2(100),
    ROSTER_DATE      DATE
)
/

create table SKYTEST.SUN_TERMINATED_TOTALS
(
    AGENTID             NUMBER(11) not null
        primary key,
    ADVISORNUMBER       VARCHAR2(15),
    OLD_AGENT_FIRSTNAME VARCHAR2(255),
    OLD_AGENT_LASTNAME  VARCHAR2(255),
    SUNTERMDATE         DATE,
    TOTPOLS             NUMBER(5),
    TOTCLI              NUMBER(5),
    TOTPREM             NUMBER(10, 2)
)
/

create table SKYTEST.SUN_INFORCE_TOTALS
(
    AGENTID               NUMBER(11),
    ADVISORNUMBER         VARCHAR2(15),
    OLD_AGENT_FIRSTNAME   VARCHAR2(150),
    OLD_AGENT_LASTNAME    VARCHAR2(150),
    SUNTERMDATE           DATE,
    TOTPOLS               NUMBER(8),
    TOTCLI                NUMBER(8),
    TOTPREM               NUMBER(10, 2),
    BRANCH                VARCHAR2(64),
    MARKETING_REG         VARCHAR2(64),
    BRANCH_NAME_EN        VARCHAR2(64),
    MARKETING_REG_NAME_EN VARCHAR2(64),
    BRANCH_NAME_FR        VARCHAR2(64),
    MARKETING_REG_NAME_FR VARCHAR2(64),
    POLICYSTATUS          NUMBER(4)
)
/

create table SKYTEST.PRODUCT_POLICY_GROUP_BEN
(
    PRODUCT_POL_GROUP_BEN_INT_ID NUMBER(8) not null,
    BENEFITID                    NUMBER(8),
    DISCOUNTPRICE                VARCHAR2(1),
    MAXINSURED                   NUMBER(10, 2),
    COVERAGETYPE                 NUMBER(2)
)
/

create index SKYTEST.PRODUCT_POL_GRP_BEN_IDX2
    on SKYTEST.PRODUCT_POLICY_GROUP_BEN (PRODUCT_POL_GROUP_BEN_INT_ID)
/

alter table SKYTEST.PRODUCT_POLICY_GROUP_BEN
    add constraint PRODUCT_POL_GRP_BEN_PK
        primary key (PRODUCT_POL_GROUP_BEN_INT_ID)
/

create table SKYTEST.FD_QUARTILES
(
    FUNDATAKEY             NUMBER(11) not null
        constraint FD_QUARTILES_PK
            primary key,
    ASSOC_CAN_INDEX_KEY    NUMBER(11),
    CREATION_DATE          DATE       not null,
    ONE_MONTH_QUARTILE     NUMBER(1),
    THREE_MONTH_QUARTILE   NUMBER(1),
    YTD_QUARTILE           NUMBER(1),
    YR1_COMPOUND_RETURN_Q  NUMBER(1),
    YR2_COMPOUND_RETURN_Q  NUMBER(1),
    YR3_COMPOUND_RETURN_Q  NUMBER(1),
    YR4_COMPOUND_RETURN_Q  NUMBER(1),
    YR5_COMPOUND_RETURN_Q  NUMBER(1),
    YR3_ANNUALIZED_STD_DEV NUMBER(11, 6),
    YR3_VOLATILITY_RANKING NUMBER(2)
)
/

create table SKYTEST.FD_PERFORMANCE2
(
    PERF_SEQ                      NUMBER(11) not null
        constraint FD_PERFORMANCE2_PK
            primary key,
    PERF_CREATION_DATE            DATE,
    PERF_ENABLED                  CHAR,
    PERF_FUNDATAKEY               NUMBER(11),
    PERF_FUNDATA_DATE             DATE,
    PERF_ONE_MONTH_RETURN         NUMBER(11, 6),
    PERF_THREE_MONTH_RETURN       NUMBER(11, 6),
    PERF_YTD_RETURN               NUMBER(11, 6),
    PERF_INCEPTION_RETURN         NUMBER(11, 6),
    PERF_ONE_YR_COMPOUND_RETURN   NUMBER(11, 6),
    PERF_TWO_YR_COMPOUND_RETURN   NUMBER(11, 6),
    PERF_THREE_YR_COMPOUND_RETURN NUMBER(11, 6),
    PERF_FOUR_YR_COMPOUND_RETURN  NUMBER(11, 6),
    PERF_FIVE_YR_COMPOUND_RETURN  NUMBER(11, 6),
    PERF_FUNDGRADE                CHAR,
    PERF_LAST_MODIFICATION_DATE   DATE,
    FD_FUND                       NUMBER(11)
)
/

create table SKYTEST.FD_BEST_OVERALL2
(
    BEST_OVERALL_INT_ID           NUMBER(8) not null
        constraint FD_BEST_OVERALL2_PK
            primary key,
    CREATION_DATE                 DATE,
    ONE_MONTH_RETURN              NUMBER(11, 4),
    ONE_MONTH_RETURN_FUND         NUMBER(11),
    THREE_MONTH_RETURN            NUMBER(11, 4),
    THREE_MONTH_RETURN_FUND       NUMBER(11),
    YTD_RETURN                    NUMBER(11, 4),
    YTD_RETURN_FUND               NUMBER(11),
    INCEPTION_RETURN              NUMBER(11, 4),
    INCEPTION_RETURN_FUND         NUMBER(11),
    ONE_YR_COMPOUND_RETURN        NUMBER(11, 4),
    ONE_YR_COMPOUND_RETURN_FUND   NUMBER(11),
    TWO_YR_COMPOUND_RETURN        NUMBER(11, 4),
    TWO_YR_COMPOUND_RETURN_FUND   NUMBER(11),
    THREE_YR_COMPOUND_RETURN      NUMBER(11, 4),
    THREE_YR_COMPOUND_RETURN_FUND NUMBER(11),
    FOUR_YR_COMPOUND_RETURN       NUMBER(11, 4),
    FOUR_YR_COMPOUND_RETURN_FUND  NUMBER(11),
    FIVE_YR_COMPOUND_RETURN       NUMBER(11, 4),
    FIVE_YR_COMPOUND_RETURN_FUND  NUMBER(11)
)
/

create table SKYTEST.CANNEX_INTEREST_RATE_NEW_RRIF
(
    INTEREST_RATE_INT_ID NUMBER(10) not null
        constraint CANNEX_INTEREST_RATE_NEW__PK1
            primary key,
    DTYPE                VARCHAR2(31),
    CHANGE               NUMBER(6, 4),
    CHANGE_DATE_STRING   VARCHAR2(8),
    CHANGE_TIME_STRING   VARCHAR2(20),
    CREATION_DATE        TIMESTAMP(6),
    INTEREST_RATE_VALUE  NUMBER(6, 4),
    LAST_MODIFICATION    TIMESTAMP(6),
    TERM_VALUE           VARCHAR2(255),
    UNIQUE_NUMBER        NUMBER(19),
    CANNEX_PRODUCT       NUMBER(10),
    DONT_DELETE          VARCHAR2(1),
    PURCHASE_RATE        NUMBER(6, 4),
    PURCHASE_RATE_CHANGE NUMBER(6, 4),
    CASH_ADVANCE_RATE    NUMBER(6, 4),
    CASH_ADVANCE_CHANGE  NUMBER(6, 4),
    MIN_AMOUNT           NUMBER(14, 2),
    MAX_AMOUNT           NUMBER(14, 2),
    PAYMENT_FREQUENCY    VARCHAR2(2),
    TERM_DURATION        VARCHAR2(1)
)
/

create table SKYTEST.FD_PERFORMANCE_PY
(
    PERF_SEQ                      NUMBER(11) not null
        primary key,
    PERF_CREATION_DATE            DATE,
    PERF_ENABLED                  CHAR,
    PERF_FUNDATAKEY               NUMBER(11),
    PERF_FUNDATA_DATE             DATE,
    PERF_ONE_MONTH_RETURN         NUMBER(11, 6),
    PERF_THREE_MONTH_RETURN       NUMBER(11, 6),
    PERF_YTD_RETURN               NUMBER(11, 6),
    PERF_INCEPTION_RETURN         NUMBER(11, 6),
    PERF_ONE_YR_COMPOUND_RETURN   NUMBER(11, 6),
    PERF_TWO_YR_COMPOUND_RETURN   NUMBER(11, 6),
    PERF_THREE_YR_COMPOUND_RETURN NUMBER(11, 6),
    PERF_FOUR_YR_COMPOUND_RETURN  NUMBER(11, 6),
    PERF_FIVE_YR_COMPOUND_RETURN  NUMBER(11, 6),
    PERF_FUNDGRADE                CHAR,
    PERF_LAST_MODIFICATION_DATE   DATE,
    FD_FUND                       NUMBER(11)
)
/

create table SKYTEST.FD_DAILY_MOVERS_PY
(
    SEQ_NUMBER                     NUMBER(5) not null
        primary key,
    FUND_FUNDATAKEY                NUMBER(9),
    CREATION_DATE                  DATE,
    PRICE_CHANGE_FROM_PREVIOUS_DAY NUMBER(15, 6),
    PERF_ONE_DAY_RETURN            NUMBER(11, 6),
    FUND_CREATION_DATE             DATE      not null,
    FUND_NAME_EN                   VARCHAR2(250),
    FUND_NAME_FR                   VARCHAR2(250)
)
/

create table SKYTEST.FD_FUND_COMPANY
(
    FUND_COMPANY_INT_ID    NUMBER(8) not null,
    GROUP_KEY              NUMBER(8) not null
        constraint FD_FUND_COMPANY_PK
            primary key,
    OLD_FD_MASTER_ID       NUMBER(8),
    FUND_COMPANY_EN        VARCHAR2(32),
    FUND_COMPANY_FR        VARCHAR2(32),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE
)
/

create table SKYTEST.FD_FUND2
(
    FUND_SEQ                    NUMBER(11) not null
        constraint FD_FUND2_PK
            primary key,
    FUND_CREATION_DATE          DATE       not null,
    FUND_ENABLED                VARCHAR2(1),
    FUND_FUNDATAKEY             NUMBER(11),
    FUND_NAME_EN                VARCHAR2(64),
    FUND_NAME_FR                VARCHAR2(64),
    FUND_OBJECTIVE_EN           VARCHAR2(512),
    FUND_OBJECTIVE_FR           VARCHAR2(512),
    FUND_STRATEGY_EN            VARCHAR2(512),
    FUND_STRATEGY_FR            VARCHAR2(512),
    FUND_RRSP_CODE              VARCHAR2(1),
    FUND_TOTAL_ASSETS           NUMBER(15, 6),
    FUND_MER                    NUMBER(15, 6),
    FUND_AVAIL                  VARCHAR2(1),
    FUND_LOAD_EN                VARCHAR2(32),
    FUND_LOAD_FR                VARCHAR2(32),
    FUND_CLASS_EN               VARCHAR2(16),
    FUND_CLASS_FR               VARCHAR2(16),
    FUND_LAST_MODIFICATION_DATE DATE,
    MATURITY_BENEFIT            NUMBER(5, 2),
    MATURITY_BENEFIT_DESC_EN    VARCHAR2(300),
    MATURITY_BENEFIT_DESC_FR    VARCHAR2(300),
    DEATH_BENEFIT               NUMBER(5, 2),
    DEATH_BENEFIT_DESC_EN       VARCHAR2(300),
    DEATH_BENEFIT_DESC_FR       VARCHAR2(300),
    GENERAL_NOTE_EN             VARCHAR2(300),
    GENERAL_NOTE_FR             VARCHAR2(300),
    FD_CATEGORY                 NUMBER(11)
        constraint FD_FUND2_FK1
            references SKYTEST.FD_CATEGORY2,
    FUND_GRADE                  VARCHAR2(1),
    NAVPS                       NUMBER(15, 6),
    AVERAGE                     NUMBER(15, 6),
    NAVPS_DATE                  DATE,
    STRATEGY_INDEX              NUMBER(8),
    FUND_COMPANY                NUMBER(8)
        constraint FD_FUND2_FK2
            references SKYTEST.FD_FUND_COMPANY
)
/

create table SKYTEST.AH_FILE
(
    AH_FILE_INT_ID                NUMBER(8) not null,
    A_FILE_INT_ID                 NUMBER(8) not null,
    MGMT_COMPANY_CODE             VARCHAR2(3),
    FUND_ID                       VARCHAR2(5),
    FUND_ACCOUNT_ID               VARCHAR2(15),
    SALESREP_CODE                 VARCHAR2(5),
    DEALER_CODE                   VARCHAR2(4),
    BENEFICIAL_OWNER_LASTNAME     VARCHAR2(20),
    BENEFICIAL_OWNER_FIRSTNAME    VARCHAR2(20),
    SPOUSAL_ACCOUNT_FLAG          VARCHAR2(1),
    DESIGNATION                   VARCHAR2(1),
    ACCOUNT_TYPE                  VARCHAR2(2),
    LOCKED_IN_CODE                VARCHAR2(1),
    TAX_CODE                      VARCHAR2(3),
    ACCOUNT_STATUS                VARCHAR2(1),
    TRANSACTION_TYPE              VARCHAR2(1),
    TRADE_DATE                    DATE,
    SETTLEMENT_DATE               DATE,
    POS_NEG_IND                   VARCHAR2(1),
    GROSS_AMOUNT                  NUMBER(15, 5),
    NET_AMOUNT                    NUMBER(15, 5),
    UNIT_PRICE                    NUMBER(15, 5),
    UNIT_TRANSACTED               NUMBER(15, 5),
    SETTLEMENT_AMOUNT             NUMBER(15, 5),
    TOTAL_ASSIGNED                NUMBER(15, 5),
    TOTAL_UNASSIGNED              NUMBER(15, 5),
    CLIENT_PAID_COMM              NUMBER(15, 5),
    DEALER_CLIENT_PAID_COMM       NUMBER(15, 5),
    FUND_PARTNER_SHIP_PAID_COMM   NUMBER(15, 5),
    DEALER_FUND_PARTNER_PAID_COMM NUMBER(15, 5),
    RAW_RECORD                    VARCHAR2(700),
    PROCESS_DATE                  DATE,
    PROCESSING_STATUS             VARCHAR2(1),
    SEQUENCE_NUMBER               NUMBER(8) not null,
    BENEFICIAL_OWNER_SIN          VARCHAR2(9)
)
/

create index SKYTEST.AH_FILE_IDX1
    on SKYTEST.AH_FILE (AH_FILE_INT_ID)
/

create index SKYTEST.AH_FILE_IDX2
    on SKYTEST.AH_FILE (A_FILE_INT_ID)
/

create index SKYTEST.AH_FILE_IDX3
    on SKYTEST.AH_FILE (MGMT_COMPANY_CODE, FUND_ACCOUNT_ID)
/

create index SKYTEST.AH_FILE_IDX4
    on SKYTEST.AH_FILE (PROCESS_DATE)
/

create index SKYTEST.AH_FILE_IDX5
    on SKYTEST.AH_FILE (PROCESSING_STATUS, ACCOUNT_STATUS, MGMT_COMPANY_CODE)
/

create index SKYTEST.AH_FILE_IDX6
    on SKYTEST.AH_FILE (TRADE_DATE)
/

alter table SKYTEST.AH_FILE
    add constraint AH_FILE_PK
        primary key (AH_FILE_INT_ID)
/

create table SKYTEST.CPP_FYC
(
    "DATE"        DATE,
    POLICY        VARCHAR2(255),
    "CLIENT NAME" VARCHAR2(255),
    AGENT         NUMBER(10),
    "LEVEL"       NUMBER(10),
    PREMIUM       NUMBER(10, 2),
    DURATION      NUMBER(10),
    RATE          NUMBER(10),
    COMMISSION    NUMBER(10, 2)
)
/

create table SKYTEST.WELLNESS_PLAN
(
    WELLNESS_PLAN_ID       NUMBER(8) not null
        constraint WELLNESS_PLAN_PK
            primary key,
    NAME_EN                VARCHAR2(20),
    NAME_FR                VARCHAR2(20),
    DESC_EN                VARCHAR2(20),
    DESC_FR                VARCHAR2(20),
    SORT_ORDER             NUMBER(4),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    MODIFIED_BY            VARCHAR2(128)
)
/

create table SKYTEST.PRODUCT_TYPE
(
    PRODUCT_TYPE_INT_ID    NUMBER(8)    not null,
    DEFINITION_EN          VARCHAR2(60) not null,
    DEFINITION_FR          VARCHAR2(60) not null,
    ABBREVIATION           VARCHAR2(20),
    PRODUCT_CLASS          NUMBER(8)    not null
        constraint PRODUCT_TYPE_PRODUCT_CLASS_FK
            references SKYTEST.PRODUCT_CLASS,
    PRODUCT_TYPEID         NUMBER(11),
    LAST_MODIFICATION_DATE DATE,
    RENEW_YEAR             NUMBER(3),
    CONVERTABLE            VARCHAR2(1),
    RENEWABLE              VARCHAR2(1),
    AGE_PAYABLE            NUMBER(3),
    YEARS_PAYABLE          NUMBER(3),
    WITHVALUES             VARCHAR2(1),
    GUARANTEED             VARCHAR2(1),
    CREATION_DATE          DATE
)
/

comment on table SKYTEST.PRODUCT_TYPE is 'Reference Table: The type of product'
/

create unique index SKYTEST.PRODUCT_TYPE_IDX1
    on SKYTEST.PRODUCT_TYPE (PRODUCT_TYPE_INT_ID)
/

create index SKYTEST.PRODUCT_TYPE_IDX2
    on SKYTEST.PRODUCT_TYPE (PRODUCT_CLASS)
/

alter table SKYTEST.PRODUCT_TYPE
    add constraint PRODUCTTYPE_PK
        primary key (PRODUCT_TYPE_INT_ID)
/

create table SKYTEST.PROVINCE
(
    PROVINCE_CODE    VARCHAR2(6)  not null,
    COUNTRY          VARCHAR2(3)  not null
        constraint PROVINCE_COUNTRY_FK
            references SKYTEST.COUNTRY,
    NAME_EN          VARCHAR2(40) not null,
    NAME_FR          VARCHAR2(40) not null,
    OLD_CODE         NUMBER(4),
    ORDERING         NUMBER(4),
    PROVINCE_CODE_FR VARCHAR2(8),
    PROVINCE_CODE_EN VARCHAR2(8)
)
/

create unique index SKYTEST.PROVINCE_IDX1
    on SKYTEST.PROVINCE (PROVINCE_CODE)
/

create index SKYTEST.PROVINCE_IDX2
    on SKYTEST.PROVINCE (COUNTRY)
/

alter table SKYTEST.PROVINCE
    add constraint PROVINCE_PK
        primary key (PROVINCE_CODE)
/

create table SKYTEST.CANNEX_COMPANY_PROVINCE
(
    CANNEX_COMPANY NUMBER(10)  not null
        constraint COMPANY_CANNEX_PROVINCE_C_FK1
            references SKYTEST.CANNEX_COMPANY,
    PROVINCE       VARCHAR2(2) not null
        constraint COMPANY_CANNEX_PROVINCE_P_FK1
            references SKYTEST.PROVINCE,
    IPNO           VARCHAR2(4),
    constraint COMPANY_CANNEX_PROVINCE_PK
        primary key (CANNEX_COMPANY, PROVINCE)
)
/

create table SKYTEST.FD_DAILY
(
    DAILY_SEQ                NUMBER           not null
        constraint FD_DAILY_PK
            primary key,
    DAILY_CREATION_TIMESTAMP DATE             not null,
    DAILY_FUNDATAKEY         NUMBER(11),
    DAILY_FUNDATA_DATE       DATE,
    DAILY_EFFECTIVE_YIELD    NUMBER(28, 6),
    DAILY_ENABLED            CHAR default 'Y' not null,
    DAILY_NAVPS              NUMBER(28, 6),
    DAILY_YTDRETURN          NUMBER(28, 6)
)
/

create index SKYTEST.FD_DAILY_INDEX1
    on SKYTEST.FD_DAILY (DAILY_FUNDATA_DATE)
/

create index SKYTEST.FD_DAILY_INDEX2
    on SKYTEST.FD_DAILY (DAILY_FUNDATAKEY, DAILY_FUNDATA_DATE)
/

create index SKYTEST.FD_DAILY_IDX2
    on SKYTEST.FD_DAILY (DAILY_FUNDATAKEY)
/

create table SKYTEST.CONTACT_EMAIL
(
    CONTACT NUMBER(8) not null,
    EMAIL   NUMBER(8) not null,
    constraint CONTACT_EMAIL_PK
        primary key (CONTACT, EMAIL)
)
/

create index SKYTEST.CONTACT_EMAIL_IDX1
    on SKYTEST.CONTACT_EMAIL (CONTACT)
/

create index SKYTEST.CONTACT_EMAIL_IDX2
    on SKYTEST.CONTACT_EMAIL (EMAIL)
/

create table SKYTEST.AUDIT_TRAIL_SRC
(
    AUDIT_TRAIL_SRC_INT_ID NUMBER(8) not null,
    TABLE_NAME             VARCHAR2(30),
    CREATION_DATE          DATE,
    LAST_MODIFIED_DATE     DATE
)
/

create index SKYTEST.AUDIT_TRAIL_SRC_IDX1
    on SKYTEST.AUDIT_TRAIL_SRC (AUDIT_TRAIL_SRC_INT_ID)
/

alter table SKYTEST.AUDIT_TRAIL_SRC
    add constraint AUDIT_TRAIL_SRC_PK
        primary key (AUDIT_TRAIL_SRC_INT_ID)
/

create table SKYTEST.DISTRIBUTION
(
    DISTRIBUTION_INT_ID NUMBER(8) not null,
    ABBREVIATION        VARCHAR2(20),
    DEFINITION_EN       VARCHAR2(50),
    DEFINITION_FR       VARCHAR2(50)
)
/

create index SKYTEST.DISTRIBUTION_IDX1
    on SKYTEST.DISTRIBUTION (DISTRIBUTION_INT_ID)
/

alter table SKYTEST.DISTRIBUTION
    add constraint DISTRIBUTION_PK
        primary key (DISTRIBUTION_INT_ID)
/

create table SKYTEST.SUN_ALL_POLICY_TABLE
(
    AGENTID               NUMBER(15),
    ADVISORNUMBER         VARCHAR2(15),
    OLD_AGENT_FIRSTNAME   VARCHAR2(150),
    OLD_AGENT_LASTNAME    VARCHAR2(150),
    SUNTERMDATE           DATE,
    TOTPOLS               NUMBER(9),
    TOTCLI                NUMBER(9),
    TOTPREM               NUMBER(9),
    BRANCH                VARCHAR2(64),
    MARKETING_REG         VARCHAR2(64),
    BRANCH_NAME_EN        VARCHAR2(64),
    BRANCH_NAME_FR        VARCHAR2(64),
    MARKETING_REG_NAME_EN VARCHAR2(64),
    MARKETING_REG_NAME_FR VARCHAR2(64),
    POLICYSTATUS          NUMBER(4)
)
/

create table SKYTEST.RIDER_TYPE
(
    RIDER_TYPE_INT NUMBER(8) not null,
    RIDER_TYPE     NUMBER(3),
    RIDER_DESC_EN  VARCHAR2(75),
    RIDER_DESC_FR  VARCHAR2(75)
)
/

create index SKYTEST.RIDER_TYPE_IDX1
    on SKYTEST.RIDER_TYPE (RIDER_TYPE_INT)
/

create index SKYTEST.RIDER_TYPE_IDX2
    on SKYTEST.RIDER_TYPE (RIDER_TYPE)
/

alter table SKYTEST.RIDER_TYPE
    add constraint RIDER_TYPE_PK
        primary key (RIDER_TYPE_INT)
/

create table SKYTEST.RIDER
(
    RIDER_INT_ID           NUMBER(8) not null,
    RIDER_TYPE             NUMBER(8) not null
        constraint RIDER_RIDER_TYPE_FK
            references SKYTEST.RIDER_TYPE,
    SHORTENG               VARCHAR2(256),
    SHORTFRE               VARCHAR2(256),
    PROXY                  VARCHAR2(1),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    RIDER_ID               NUMBER(9),
    AVAILABLE              VARCHAR2(1),
    MANAGEMENT_ONLY        VARCHAR2(1)
)
/

create index SKYTEST.RIDER_IDX1
    on SKYTEST.RIDER (RIDER_INT_ID)
/

create index SKYTEST.RIDER_IDX2
    on SKYTEST.RIDER (RIDER_TYPE)
/

alter table SKYTEST.RIDER
    add constraint RIDER_PK
        primary key (RIDER_INT_ID)
/

create table SKYTEST.RIDER_COMMISSION
(
    RIDER_COMMISSION_INT_ID NUMBER(11)   not null,
    RIDER                   NUMBER(8)    not null
        constraint RIDER_COMMISSION_RIDER_FK
            references SKYTEST.RIDER,
    PRODUCT_ID              NUMBER(11),
    COMMISSION_PERCENT      NUMBER(5, 2),
    FROM_YEAR               NUMBER(2),
    TO_YEAR                 NUMBER(2),
    FROM_DOLLAR             NUMBER(9),
    TO_DOLLAR               NUMBER(9),
    LAST_UPDATE_DATE        DATE         not null,
    LAST_UPDATE_USER        VARCHAR2(30) not null,
    MINAGE                  NUMBER(3),
    MAXAGE                  NUMBER(3),
    POLICY_FEE              NUMBER(5, 2),
    MIN_FACE                NUMBER(10, 2),
    MAX_FACE                NUMBER(10, 2),
    CALC_BY_PREMOR_COMM     VARCHAR2(2),
    START_DATE              DATE,
    START_DATE_1            DATE,
    END_DATE                DATE,
    FORMULA_CODE            VARCHAR2(2)
)
/

create index SKYTEST.RIDER_COMMISSION_IDX1
    on SKYTEST.RIDER_COMMISSION (RIDER_COMMISSION_INT_ID)
/

create index SKYTEST.RIDER_COMMISSION_IDX2
    on SKYTEST.RIDER_COMMISSION (RIDER)
/

alter table SKYTEST.RIDER_COMMISSION
    add constraint RIDER_COMMISSION_PK
        primary key (RIDER_COMMISSION_INT_ID)
/

create table SKYTEST.PRODUCT_POLICY_LIFE
(
    PRODUCT_POLICY_LIFE_INT_ID NUMBER(8) not null,
    SING_JO1_JOLAST            NUMBER(2) not null,
    LAST_RENEWABLE_AGE         NUMBER(3) not null,
    LAST_CONVERTABLE_AGE       NUMBER(2) not null,
    CONVERTABLE_WAITING_PERIOD NUMBER(2),
    PAYABLE_TO_AGE             NUMBER(3) not null,
    AGETYPE                    VARCHAR2(1),
    MLOW_ISSUE_AGE             NUMBER(3),
    MHIGH_ISSUE_AGE            NUMBER(3),
    FLOW_ISSUE_AGE             NUMBER(3),
    FHIGH_ISSUE_AGE            NUMBER(3),
    LOW_WAIVER_ISSUE_AGE       NUMBER(3),
    HIGH_WAIVER_ISSUE_AGE      NUMBER(3),
    LAST_WAIVER_AGE            NUMBER(3),
    MLOW_ISSUE_AGE_SMOKER      NUMBER(3),
    MHIGH_ISSUE_AGE_SMOKER     NUMBER(3),
    FLOW_ISSUE_AGE_SMOKER      NUMBER(3),
    FHIGH_ISSUE_AGE_SMOKER     NUMBER(3),
    INIT_WAIVER_F              NUMBER(8, 3),
    INIT_WAIVER_M              NUMBER(8, 3),
    RENEW_WAIVER_F             NUMBER(8, 3),
    RENEW_WAIVER_M             NUMBER(8, 3),
    ROP_LOW_AGE                NUMBER(3),
    ROP_HIGH_AGE               NUMBER(3),
    MIN_BUS_FACE_AMOUNT        NUMBER(10, 2),
    MAX_BUS_FACE_AMOUNT        NUMBER(10, 2),
    MULTI_LIFE                 VARCHAR2(1),
    CI_NUM_ILL                 NUMBER(2),
    ROP_INCLUDED               VARCHAR2(3),
    ROP_MATRIX                 VARCHAR2(3),
    BACK_DATE                  NUMBER(2),
    DISCOUNTPRICE              VARCHAR2(1),
    ASRIDER                    VARCHAR2(1),
    ONLY_ONE_PERIOD            VARCHAR2(1) default 'N'
)
/

create index SKYTEST.PRODUCT_POLICY_LIFE_IDX1
    on SKYTEST.PRODUCT_POLICY_LIFE (PRODUCT_POLICY_LIFE_INT_ID)
/

alter table SKYTEST.PRODUCT_POLICY_LIFE
    add constraint PRODUCT_POL_LIFEL_PK
        primary key (PRODUCT_POLICY_LIFE_INT_ID)
/

create table SKYTEST.PS_FUND_POSITION
(
    PS_FUND_POSITION_INT_ID   NUMBER(8) not null
        constraint PS_FUND_POSITION_INT_PK
            primary key,
    PS_ACCOUNT_DETAIL         NUMBER(8) not null
        constraint PS_ACCOUNT_DETAIL_POSITION_FK
            references SKYTEST.PS_ACCOUNT_DETAIL,
    FUND_IDENTIFIER           VARCHAR2(5),
    TOTAL_UNISSUED            NUMBER(22, 9),
    TOTAL_ISSUED              NUMBER(22, 9),
    AVERAGE_COST              VARCHAR2(8),
    DIVIDEND_OPTION           VARCHAR2(1),
    SYSTEMATIC_PLAN_INDICATOR VARCHAR2(1),
    PROCESSING_STATUS         VARCHAR2(1),
    PROCESS_DATE              DATE,
    LAST_MODIFY_DATE          DATE
)
/

create table SKYTEST.CANNEX_PRODUCT
(
    PROD_INT_ID        NUMBER(10) not null
        primary key,
    DTYPE              VARCHAR2(31),
    CHANGE_DATE_STRING VARCHAR2(8),
    CHANGE_TIME_STRING VARCHAR2(6),
    CREATION_DATE      TIMESTAMP(6),
    LAST_MODIFICATION  TIMESTAMP(6),
    PRODUCT_TYPE       VARCHAR2(4),
    PURCHASE_FLAG      VARCHAR2(1),
    TERM_TYPE          VARCHAR2(1),
    TERM_UNIT          VARCHAR2(255),
    CANNEX_COMPANY     NUMBER(10)
        constraint CNNEX_PROD_COMPANY_FK
            references SKYTEST.CANNEX_COMPANY,
    COMPOUND_FREQUENCY VARCHAR2(2),
    MAX_AMOUNT         NUMBER(14, 2),
    MIN_AMOUNT         NUMBER(14, 2),
    PAYMENT_FREQUENCY  VARCHAR2(2),
    REDEEMABILITY      VARCHAR2(1),
    TYPE               VARCHAR2(2),
    TAX_INDICATOR      VARCHAR2(1),
    INTEREST_TYPE      VARCHAR2(1),
    PRODUCT_CODE       VARCHAR2(4),
    PRODUCT_NAME       VARCHAR2(40),
    UNIQUE_NUM         VARCHAR2(10),
    OPEN_CLOSED        VARCHAR2(1),
    RATE_TYPE          VARCHAR2(1),
    RESIDENCY          VARCHAR2(1),
    TERM_TYPE_ID       NUMBER(4),
    PRODUCT_TYPE_ID    NUMBER(4),
    NO_LIMIT           VARCHAR2(1),
    DONT_DELETE        VARCHAR2(1)
)
/

comment on column SKYTEST.CANNEX_PRODUCT.TERM_TYPE_ID is 'DURATION TYPE'
/

comment on column SKYTEST.CANNEX_PRODUCT.PRODUCT_TYPE_ID is 'PRODUCT TYPE'
/

create index SKYTEST.CANNEX_PRODUCT_INDEX1
    on SKYTEST.CANNEX_PRODUCT (PRODUCT_TYPE)
/

create index SKYTEST.CANNEX_PRODUCT_INDEX2
    on SKYTEST.CANNEX_PRODUCT (CANNEX_COMPANY)
/

create index SKYTEST.CANNEX_PRODUCT_INDEX3
    on SKYTEST.CANNEX_PRODUCT (PRODUCT_TYPE_ID)
/

create index SKYTEST.CANNEX_PRODUCT_INDEX4
    on SKYTEST.CANNEX_PRODUCT (CANNEX_COMPANY, PRODUCT_TYPE_ID, TERM_TYPE_ID, MIN_AMOUNT, MAX_AMOUNT)
/

create table SKYTEST.LICENSE_CATEGORY
(
    LICENSE_CATEGORY_INT_ID NUMBER(8)   not null
        constraint LICENSE_CATEGORY_PK
            primary key,
    LICENSE_CATEGORY_CODE   VARCHAR2(8),
    CATEGORY_DESC_EN        VARCHAR2(128),
    CATEGORY_DESC_FR        VARCHAR2(128),
    PROVINCE                VARCHAR2(2) not null,
    CREATION_DATE           DATE,
    LAST_MODIFICATION_DATE  DATE
)
/

create table SKYTEST.MORTGAGE
(
    MORTGAGE_INT_ID        NUMBER(8) not null
        constraint MORTGAGE_PK
            primary key,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    PRODUCT                NUMBER(8)
)
/

create table SKYTEST.FD_SECURITY_NAME2
(
    SECURITY_NAME_INT_ID        NUMBER(8)     not null
        constraint FD_SECURITY_NAME2_PK
            primary key,
    SECURITY_NAME               VARCHAR2(128) not null,
    CREATION_DATE               DATE,
    SECURITY_FUND_HOLDING_TOTAL NUMBER(8)
)
/

create table SKYTEST.CANNEX_COMPANY_NEW
(
    COMPANY_INT_ID       NUMBER(10)  not null
        constraint CANNEX_COMPANY_NEW_PK
            primary key
        constraint SYS_C0091791
            check ("COMPANY_INT_ID" IS NOT NULL),
    COMPANY_CODE         VARCHAR2(4) not null
        constraint SYS_C0091792
            check ("COMPANY_CODE" IS NOT NULL),
    COMPANY_DESC_EN      VARCHAR2(256),
    COMPANY_DESC_FR      VARCHAR2(256),
    IPNO                 VARCHAR2(4) not null
        constraint SYS_C0091793
            check ("IPNO" IS NOT NULL),
    COMPANY_METHOD       VARCHAR2(16),
    COMPANY_WEBSITE_RATE VARCHAR2(256),
    COMPANY_TYPE         NUMBER(4),
    LAST_MODIFICATION    DATE,
    CREATION_DATE        DATE,
    DONT_DELETE          VARCHAR2(1),
    COMPANY_WEBSITE_FR   VARCHAR2(256)
)
/

create table SKYTEST.CANNEX_COMPANY_PROVINCE_NEW
(
    CANNEX_COMPANY_NEW NUMBER(10)
        references SKYTEST.CANNEX_COMPANY_NEW,
    PROVINCE           VARCHAR2(2)
        references SKYTEST.PROVINCE,
    IPNO               VARCHAR2(4)
)
/

create table SKYTEST.CANNEX_PRODUCT_NEW
(
    PROD_INT_ID         NUMBER(10) not null
        constraint CANNEX_PRODUCT_NEW_PK
            primary key
        constraint SYS_C0091789
            check ("PROD_INT_ID" IS NOT NULL)
        constraint SYS_C0092654
            check ("PROD_INT_ID" IS NOT NULL),
    DTYPE               VARCHAR2(31),
    CHANGE_DATE_STRING  VARCHAR2(8),
    CHANGE_TIME_STRING  VARCHAR2(6),
    CREATION_DATE       TIMESTAMP(6),
    LAST_MODIFICATION   TIMESTAMP(6),
    PRODUCT_TYPE        VARCHAR2(4),
    PURCHASE_FLAG       VARCHAR2(1),
    TERM_TYPE           VARCHAR2(1),
    TERM_UNIT           VARCHAR2(255),
    CANNEX_COMPANY      NUMBER(10)
        constraint CNNEX_PROD_COMPANY_NEW_FK
            references SKYTEST.CANNEX_COMPANY_NEW,
    COMPOUND_FREQUENCY  VARCHAR2(2),
    MAX_AMOUNT          NUMBER(14, 2),
    MIN_AMOUNT          NUMBER(14, 2),
    PAYMENT_FREQUENCY   VARCHAR2(2),
    REDEEMABILITY       VARCHAR2(1),
    TYPE                VARCHAR2(2),
    TAX_INDICATOR       VARCHAR2(1),
    INTEREST_TYPE       VARCHAR2(1),
    PRODUCT_CODE        VARCHAR2(4),
    PRODUCT_NAME        VARCHAR2(40),
    UNIQUE_NUM          VARCHAR2(10),
    OPEN_CLOSED         VARCHAR2(1),
    RATE_TYPE           VARCHAR2(1),
    RESIDENCY           VARCHAR2(1),
    TERM_TYPE_ID        NUMBER(4),
    PRODUCT_TYPE_ID     NUMBER(4),
    NO_LIMIT            VARCHAR2(1),
    DONT_DELETE         VARCHAR2(1),
    PRODUCT_NAME_FR     VARCHAR2(256),
    CURRENCY            VARCHAR2(3),
    CARD_TYPE           VARCHAR2(2),
    FEE_AMOUNT          NUMBER(6, 2),
    FEE_FREQUENCY       VARCHAR2(2),
    FEE_GRACE_MONTHS    NUMBER(3) default '',
    INTEREST_GRACE_DAYS NUMBER(3),
    NEW_ACCOUNTS        VARCHAR2(1),
    REWARDS             VARCHAR2(1),
    TERMS               NUMBER(1)
)
/

create table SKYTEST.CANNEX_INTEREST_RATE_NEW_MORT
(
    INTEREST_RATE_INT_ID NUMBER(10) not null
        constraint CANNEX_INTEREST_RATE_NEW__PK
            primary key,
    DTYPE                VARCHAR2(31),
    CHANGE               NUMBER(6, 4),
    CHANGE_DATE_STRING   VARCHAR2(8),
    CHANGE_TIME_STRING   VARCHAR2(20),
    CREATION_DATE        TIMESTAMP(6),
    INTEREST_RATE_VALUE  NUMBER(6, 4),
    LAST_MODIFICATION    TIMESTAMP(6),
    TERM_VALUE           VARCHAR2(255),
    UNIQUE_NUMBER        NUMBER(19),
    CANNEX_PRODUCT       NUMBER(10),
    DONT_DELETE          VARCHAR2(1),
    PURCHASE_RATE        NUMBER(6, 4),
    PURCHASE_RATE_CHANGE NUMBER(6, 4),
    CASH_ADVANCE_RATE    NUMBER(6, 4),
    CASH_ADVANCE_CHANGE  NUMBER(6, 4),
    MIN_AMOUNT           NUMBER(14, 2),
    MAX_AMOUNT           NUMBER(14, 2),
    PAYMENT_FREQUENCY    VARCHAR2(2),
    TERM_DURATION        VARCHAR2(1)
)
/

create table SKYTEST.FD_HOLDINGS_PY
(
    HOLDING_SEQ                    NUMBER(11) not null
        primary key,
    HOLDING_CREATION_DATE          DATE       not null,
    HOLDING_FUNDATA_DATE           DATE,
    HOLDING_RANK                   NUMBER(11),
    HOLDING_SECURITY_NAME          VARCHAR2(128),
    HOLDING_MARKET_PERCENT         NUMBER(11, 6),
    HOLDING_ENABLED                VARCHAR2(1),
    HOLDING_FUNDATAKEY             NUMBER(11),
    HOLDING_LAST_MODIFICATION_DATE DATE,
    FD_FUND2                       NUMBER(11),
    SECURITY_NAME_ID               NUMBER(11)
)
/

create index SKYTEST."FD_HOLDING2_INDEX3_copy1"
    on SKYTEST.FD_HOLDINGS_PY (FD_FUND2)
/

create index SKYTEST."FD_HOLDING2_INDEX4_copy1"
    on SKYTEST.FD_HOLDINGS_PY (SECURITY_NAME_ID)
/

create index SKYTEST."FD_HOLDING2_INDEX2_copy1"
    on SKYTEST.FD_HOLDINGS_PY (HOLDING_SECURITY_NAME)
/

create index SKYTEST."FD_HOLDING2_INDEX1_copy1"
    on SKYTEST.FD_HOLDINGS_PY (HOLDING_FUNDATAKEY)
/

create table SKYTEST.FD_ENABLED_PY
(
    FUND_SEQ           NUMBER(11) not null
        primary key,
    FUND_CREATION_DATE DATE       not null,
    FUND_ENABLED       VARCHAR2(1),
    FUND_FUNDATAKEY    NUMBER(11)
)
/

create table SKYTEST.CANNEX_INTEREST_RATE_NEW_DEPA
(
    INTEREST_RATE_INT_ID NUMBER(10) not null
        primary key,
    DTYPE                VARCHAR2(31),
    CHANGE               NUMBER(6, 4),
    CHANGE_DATE_STRING   VARCHAR2(8),
    CHANGE_TIME_STRING   VARCHAR2(20),
    CREATION_DATE        TIMESTAMP(6),
    INTEREST_RATE_VALUE  NUMBER(6, 4),
    LAST_MODIFICATION    TIMESTAMP(6),
    TERM_VALUE           VARCHAR2(255),
    UNIQUE_NUMBER        NUMBER(19),
    CANNEX_PRODUCT       NUMBER(10),
    DONT_DELETE          VARCHAR2(1),
    PURCHASE_RATE        NUMBER(6, 4),
    PURCHASE_RATE_CHANGE NUMBER(6, 4),
    CASH_ADVANCE_RATE    NUMBER(6, 4),
    CASH_ADVANCE_CHANGE  NUMBER(6, 4),
    MIN_AMOUNT           NUMBER(14, 2),
    MAX_AMOUNT           NUMBER(14, 2)
)
/

create table SKYTEST.SURVEY
(
    SURVEY_INT_ID          NUMBER(11) not null
        constraint SURVEY_PK
            primary key,
    SURVEY_ID              VARCHAR2(256),
    VERSION                VARCHAR2(20),
    COMPANY_ID             NUMBER(4),
    NAME                   VARCHAR2(256),
    NAME_FR                VARCHAR2(256),
    MODIFIED_BY            VARCHAR2(128),
    DESCRIPTION            VARCHAR2(1024),
    DESCRIPTION_FR         VARCHAR2(1024),
    HEADER                 VARCHAR2(256),
    HEADER_FR              VARCHAR2(256),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    PLAN_TYPE              VARCHAR2(20),
    ACTIVE_DATE            DATE,
    INACTIVE_DATE          DATE
)
/

comment on column SKYTEST.SURVEY.SURVEY_ID is 'external hash id'
/

comment on column SKYTEST.SURVEY.COMPANY_ID is 'IM_Company id'
/

create table SKYTEST.SURVEY_SECTION
(
    SURVEY_SECTION_INT_ID  NUMBER(11) not null
        constraint SURVEY_SECTION_PK
            primary key,
    SEQUENCE               NUMBER(4),
    NAME                   VARCHAR2(128),
    NAME_FR                VARCHAR2(128),
    HEADER                 VARCHAR2(128),
    HEADER_FR              VARCHAR2(128),
    DESCRIPTION            VARCHAR2(2048),
    DESCRIPTION_FR         VARCHAR2(2048),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    MODIFIED_BY            VARCHAR2(128),
    SURVEY                 NUMBER(11)
        constraint SURVEY_SECTION_FK1
            references SKYTEST.SURVEY,
    SURVEY_END             VARCHAR2(1)
)
/

comment on column SKYTEST.SURVEY_SECTION.SEQUENCE is 'ORDERING SEQ'
/

comment on column SKYTEST.SURVEY_SECTION.SURVEY is 'FK TO SURVEY'
/

create table SKYTEST.SURVEY_PRODUCTS
(
    SURVEY_PRODUCTS_INT_ID NUMBER(8) not null
        constraint SURVEY_PRODUCTS_PK
            primary key,
    SURVEY_SECTION         NUMBER(8)
        constraint SURVEY_PRODUCTS_FK1
            references SKYTEST.SURVEY_SECTION,
    PRODUCTID              NUMBER(8),
    COMPANYID              NUMBER(8),
    POSITIVE               NUMBER(1)
)
/

comment on column SKYTEST.SURVEY_PRODUCTS.POSITIVE is '1 for yes
0 for no'
/

create table SKYTEST.TMP
(
    OLD_PROD_ID     NUMBER(11),
    PROD_NAME_ENG   VARCHAR2(70),
    PROD_NAME_FRE   VARCHAR2(70),
    SUPPLIER        NUMBER(5),
    PROD_TYPE       NUMBER(5),
    AVAILABLE       VARCHAR2(70),
    LAST_MOD_DATE   DATE,
    ACTIVE_DATE     DATE,
    INACTIVE_DATE   DATE,
    MANAGEMENT_ONLY VARCHAR2(1),
    NEW_PROD_TYPE   NUMBER(5)
)
/

create table SKYTEST.POLICY_TMP
(
    CLIFNAME       VARCHAR2(45),
    CLILNAME       VARCHAR2(45),
    DOB            DATE,
    ISSUEAGE       NUMBER(2),
    AGENTNO        NUMBER(7),
    AGENTNAME      VARCHAR2(75),
    STATUS         VARCHAR2(25),
    ISSUEDATE      DATE,
    PAIDTODATE     DATE,
    POLNUM         VARCHAR2(15),
    PLAN           VARCHAR2(75),
    PLANID         NUMBER(11),
    SMOKER         VARCHAR2(1),
    CLASS          NUMBER(7),
    PREMIUM        NUMBER(8, 2),
    FACEAMT        NUMBER(11, 2),
    SEX            VARCHAR2(1),
    PAYMODE        VARCHAR2(15),
    ADDR           VARCHAR2(75),
    CITY           VARCHAR2(25),
    PROV           VARCHAR2(2),
    POSTCODE       VARCHAR2(7),
    PHONEHOME      VARCHAR2(45),
    PHONEOFFICE    VARCHAR2(45),
    COUNTRY        VARCHAR2(5),
    CNTRNO         VARCHAR2(25),
    ADVISOR_ID     NUMBER(11),
    CONTRACT_ID    NUMBER(11),
    OLD_CNTRID     NUMBER(11),
    OLD_CLIENTID   NUMBER(11),
    POLICYID       NUMBER(11),
    NEWCLIENTID    NUMBER(11),
    ACCOUNTID      NUMBER(11),
    ADVISOR_INT_ID NUMBER(11)
)
/

create table SKYTEST.FD_BEST_CATEGORY
(
    BEST_CATEGORY_INT_ID          NUMBER(8) not null
        constraint FD_BEST_CATEGORY_PK
            primary key,
    FD_CATEGORY                   NUMBER(8)
        constraint FD_BEST_CATEGORY_FK1
            references SKYTEST.FD_CATEGORY,
    CREATION_DATE                 DATE,
    ONE_MONTH_RETURN              NUMBER(11, 4),
    ONE_MONTH_RETURN_FUND         NUMBER(11),
    THREE_MONTH_RETURN            NUMBER(11, 4),
    THREE_MONTH_RETURN_FUND       NUMBER(11),
    YTD_RETURN                    NUMBER(11, 4),
    YTD_RETURN_FUND               NUMBER(11),
    INCEPTION_RETURN              NUMBER(11, 4),
    INCEPTION_RETURN_FUND         NUMBER(11),
    ONE_YR_COMPOUND_RETURN        NUMBER(11, 4),
    ONE_YR_COMPOUND_RETURN_FUND   NUMBER(11),
    TWO_YR_COMPOUND_RETURN        NUMBER(11, 4),
    TWO_YR_COMPOUND_RETURN_FUND   NUMBER(11),
    THREE_YR_COMPOUND_RETURN      NUMBER(11, 4),
    THREE_YR_COMPOUND_RETURN_FUND NUMBER(11),
    FOUR_YR_COMPOUND_RETURN       NUMBER(11, 4),
    FOUR_YR_COMPOUND_RETURN_FUND  NUMBER(11),
    FIVE_YR_COMPOUND_RETURN       NUMBER(11, 4),
    FIVE_YR_COMPOUND_RETURN_FUND  NUMBER(11)
)
/

create table SKYTEST.DEALER
(
    DEALER_INT_ID  NUMBER(8) not null,
    DEALER_CODE    VARCHAR2(4),
    DEALER_DESC_EN VARCHAR2(128),
    ACTIVE         CHAR      not null,
    DEALER_DESC_FR VARCHAR2(128)
)
/

create unique index SKYTEST.DEALER__IDX
    on SKYTEST.DEALER (DEALER_INT_ID)
/

alter table SKYTEST.DEALER
    add constraint DEALER_PK
        primary key (DEALER_INT_ID)
/

create table SKYTEST.CONTACT_PHONE
(
    CONTACT NUMBER(8) not null,
    PHONE   NUMBER(8) not null,
    constraint CONTACT_PHONE_PK
        primary key (CONTACT, PHONE)
)
/

create index SKYTEST.CONTACT_PHONE_IDX1
    on SKYTEST.CONTACT_PHONE (CONTACT)
/

create index SKYTEST.CONTACT_PHONE_IDX2
    on SKYTEST.CONTACT_PHONE (PHONE)
/

create table SKYTEST.LOAD_TYPE
(
    LOAD_TYPE_INT_ID  NUMBER(8) not null,
    ABBREVIATION      VARCHAR2(20),
    LOAD_TYPE_DESC_EN VARCHAR2(50),
    LOAD_TYPE_DESC_FR VARCHAR2(50)
)
/

create index SKYTEST.LOAD_TYPE_IDX1
    on SKYTEST.LOAD_TYPE (LOAD_TYPE_INT_ID)
/

alter table SKYTEST.LOAD_TYPE
    add constraint LOAD_TYPE_PK
        primary key (LOAD_TYPE_INT_ID)
/

create table SKYTEST.TAXES
(
    TAX_CODE_INT_ID NUMBER(8)    not null,
    TAX_CODE        VARCHAR2(3)  not null,
    PROVINCE_INT_ID NUMBER(8)    not null,
    TAX_DESC_EN     VARCHAR2(50) not null,
    TAX_DESC_FR     VARCHAR2(50),
    TAX_RATE        NUMBER(6, 5) not null,
    TAX_ON_TAX      VARCHAR2(1)  not null,
    EFFECTIVE_DATE  DATE,
    END_DATE        DATE
)
/

comment on column SKYTEST.TAXES.PROVINCE_INT_ID is 'from province TABLE use field OLD_CODE'
/

comment on column SKYTEST.TAXES.TAX_ON_TAX is 'If Y(es), then include the tax_code = GST in the calculation of tax payable on invoice line'
/

create index SKYTEST.TAXES_IDX1
    on SKYTEST.TAXES (TAX_CODE_INT_ID)
/

alter table SKYTEST.TAXES
    add constraint TAXES_PK
        primary key (TAX_CODE_INT_ID)
/

create table SKYTEST.CANNEX_INTEREST_RATE
(
    INTEREST_RATE_INT_ID NUMBER(10) not null
        primary key,
    DTYPE                VARCHAR2(31),
    CHANGE               NUMBER(6, 4),
    CHANGE_DATE_STRING   VARCHAR2(8),
    CHANGE_TIME_STRING   VARCHAR2(20),
    CREATION_DATE        TIMESTAMP(6),
    INTEREST_RATE_VALUE  NUMBER(6, 4),
    LAST_MODIFICATION    TIMESTAMP(6),
    TERM_VALUE           VARCHAR2(255),
    UNIQUE_NUMBER        NUMBER(19),
    CANNEX_PRODUCT       NUMBER(10)
        constraint CNNXNTRESTRATENTRSTRATEPRODUCT
            references SKYTEST.CANNEX_PRODUCT,
    DONT_DELETE          VARCHAR2(1)
)
/

create table SKYTEST.LICENSE_PROV_BOARD
(
    LICENSE_PROV_BOARD_INT_ID NUMBER(8)   not null
        constraint LICENSE_PROV_BOARD_PK
            primary key,
    PROVINCE                  VARCHAR2(2) not null,
    BOARD_NAME_EN             VARCHAR2(100),
    BOARD_NAME_FR             VARCHAR2(100),
    URL_MAIN                  VARCHAR2(100),
    URL_PERS_LICENSE          VARCHAR2(100),
    URL_CORP_LICENSE          VARCHAR2(100),
    CREATION_DATE             DATE,
    LAST_MODIFICATION_DATE    DATE
)
/

create table SKYTEST.CANNEX_INTEREST_RATE_NEW_ANTC
(
    INTEREST_RATE_INT_ID NUMBER(10)   not null
        constraint SYS_C0097866
            primary key
        constraint SYS_C0097863
            check ("INTEREST_RATE_INT_ID" IS NOT NULL)
        constraint SYS_C0097864
            check ("INTEREST_RATE_INT_ID" IS NOT NULL)
        constraint SYS_C0097865
            check ("INTEREST_RATE_INT_ID" IS NOT NULL),
    ANNUITANT_PROVINCE   VARCHAR2(10),
    SEX                  VARCHAR2(1),
    SEX_OF_JOINT         VARCHAR2(1),
    PERCENTAGE_CP        NUMBER(10, 4),
    PERCENTAGE_CS        NUMBER(10, 4),
    PREMIUM_OR_INCOME    NUMBER(10, 2),
    GUARANTEE_PERIOD     NUMBER(10, 3),
    AGES                 NUMBER(10),
    INCOME_AGE55         NUMBER(10, 2),
    INCOME_AGE60         NUMBER(10, 2),
    INCOME_AGE65         NUMBER(10, 2),
    INCOME_AGE69         NUMBER(10, 2),
    INCOME_AGE70         NUMBER(10, 2),
    INCOME_AGE75         NUMBER(10, 2),
    INCOME_AGE80         NUMBER(10, 2),
    IPNO                 VARCHAR2(4)  not null
        constraint SYS_C0098043
            check ("IPNO" IS NOT NULL),
    COMPANY_CODE         VARCHAR2(50) not null
        constraint SYS_C0098044
            check ("COMPANY_CODE" IS NOT NULL),
    COMPANY_DESC_EN      VARCHAR2(128),
    COMPANY_DESC_FR      VARCHAR2(128),
    COMPANY_TYPE         NUMBER(4),
    PRODUCT_TYPE         VARCHAR2(4),
    CHANGE_DATE_STRING   VARCHAR2(8),
    CHANGE_TIME_STRING   VARCHAR2(6),
    PURCHASE_FLAG        VARCHAR2(1),
    SURVEY_TYPE          VARCHAR2(12),
    FUND_TYPE            VARCHAR2(13)
)
/

create table SKYTEST.FD_FUND_FLEX_SEARCH_PY
(
    FUND_SEQ                       NUMBER(11) not null
        primary key,
    FUND_CREATION_DATE             DATE       not null,
    FUND_ENABLED                   VARCHAR2(1),
    FUND_FUNDATAKEY                NUMBER(11),
    FUND_NAME_EN                   VARCHAR2(250),
    FUND_NAME_FR                   VARCHAR2(250),
    FUND_RRSP_CODE                 VARCHAR2(1),
    FUND_TOTAL_ASSETS              NUMBER(15, 6),
    FUND_MER                       NUMBER(15, 6),
    FUND_AVAIL                     VARCHAR2(1),
    FUND_LOAD_EN                   VARCHAR2(250),
    FUND_LOAD_FR                   VARCHAR2(250),
    FUND_CLASS_EN                  VARCHAR2(16),
    FUND_CLASS_FR                  VARCHAR2(16),
    FUND_LAST_MODIFICATION_DATE    DATE,
    FD_TYPE                        NUMBER(11)
        constraint FD_FUND_FLEX_SEARCH_PY_FK1
            references SKYTEST.FD_FUND_TYPE_PY,
    PERF_FUND_GRADE                VARCHAR2(1),
    DAILY_PRICE_NAVPS              NUMBER(15, 6),
    PRICE_CHANGE_FROM_PREVIOUS_DAY NUMBER(15, 6),
    DAILY_PRICE_NAVPS_DATE         DATE,
    STRATEGY_INDEX                 NUMBER(8),
    FUND_COMPANY                   NUMBER(8)
        constraint SYS_C0056795
            references SKYTEST.FD_FUND_COMPANY_PY,
    PERF_ONE_MONTH_RETURN          NUMBER(11, 6),
    PERF_THREE_MONTH_RETURN        NUMBER(11, 6),
    PERF_YTD_RETURN                NUMBER(11, 6),
    PERF_INCEPTION_RETURN          NUMBER(11, 6),
    ONE_MONTH_QUARTILE             NUMBER(1),
    THREE_MONTH_QUARTILE           NUMBER(1),
    YTD_QUARTILE                   NUMBER(1),
    YR3_ANNUALIZED_STD_DEV         NUMBER(11, 6),
    YR3_VOLATILITY_RANKING         NUMBER(2),
    PREVIOUS_DAY_NAVPS             NUMBER(15, 6),
    PERF_ONE_DAY_RETURN            NUMBER(11, 6)
)
/

create unique index SKYTEST.FD_FUND_FLEX_SEARCH_PY_INDEX1
    on SKYTEST.FD_FUND_FLEX_SEARCH_PY (FUND_FUNDATAKEY)
/

alter table SKYTEST.FD_FUND_FLEX_SEARCH_PY
    add constraint FD_FUND_FLEX_SEARCH_PY_UK1
        unique (FUND_FUNDATAKEY)
/

create table SKYTEST.FD_FUND_QUARTILES_PY
(
    FUNDATAKEY             NUMBER(11) not null
        primary key,
    ASSOC_CAN_INDEX_KEY    NUMBER(11),
    CREATION_DATE          DATE       not null,
    ONE_MONTH_QUARTILE     NUMBER(1),
    THREE_MONTH_QUARTILE   NUMBER(1),
    YTD_QUARTILE           NUMBER(1),
    YR1_COMPOUND_RETURN_Q  NUMBER(1),
    YR2_COMPOUND_RETURN_Q  NUMBER(1),
    YR3_COMPOUND_RETURN_Q  NUMBER(1),
    YR4_COMPOUND_RETURN_Q  NUMBER(1),
    YR5_COMPOUND_RETURN_Q  NUMBER(1),
    YR3_ANNUALIZED_STD_DEV NUMBER(11, 6),
    YR3_VOLATILITY_RANKING NUMBER(2)
)
/

create table SKYTEST.PRICE_DISTRIBUTION
(
    PRICE_DISTRIBUTION_INT_ID NUMBER(8) not null,
    RECORD_TYPE               VARCHAR2(3),
    MGMT_COMPANY_CODE         VARCHAR2(3),
    FUND_ID                   VARCHAR2(5),
    PRICE_RECORD_DATE         DATE,
    DISTRIBUTION_PAYMENT_DATE DATE,
    PRICE_STATUS_IND          VARCHAR2(2),
    PRICE_RATE                NUMBER(11, 8),
    POSITIVE_NEGATIVE_IND     VARCHAR2(1),
    CURRENCY_IND              VARCHAR2(2)
)
/

create index SKYTEST.PRICE_DISTRIBUTION_IDX3
    on SKYTEST.PRICE_DISTRIBUTION (PRICE_RECORD_DATE)
/

create index SKYTEST.PRICE_DISTRIBUTION_IDX2
    on SKYTEST.PRICE_DISTRIBUTION (RECORD_TYPE, MGMT_COMPANY_CODE, FUND_ID)
/

create unique index SKYTEST.PRICE_DISTRIBUTION_IDX1
    on SKYTEST.PRICE_DISTRIBUTION (PRICE_DISTRIBUTION_INT_ID)
/

alter table SKYTEST.PRICE_DISTRIBUTION
    add constraint PRICE_DISTRIBUTION_PK
        primary key (PRICE_DISTRIBUTION_INT_ID)
/

create table SKYTEST.TMP_SUN_ROSTER
(
    AGENT_NO   VARCHAR2(20),
    FIRST_NAME VARCHAR2(35),
    LAST_NAME  VARCHAR2(35),
    AGENCY_NO  VARCHAR2(20)
)
/

create table SKYTEST.FD_CAT_QUARTILE
(
    CAT_SEQ                NUMBER(11) not null
        constraint FD_CAT_QUARTILE_PK
            primary key,
    CAT_CREATION_TIMESTAMP DATE       not null,
    CAT_ONE_MONTH_RETURN   NUMBER(28, 6),
    CAT_THREE_MONTH_RETURN NUMBER(28, 6),
    CAT_YTD_RETURN         NUMBER(28, 6),
    CAT_INCEPTION_RETURN   NUMBER(28, 6),
    CAT_ONE_YR_RETURN      NUMBER(28, 6),
    CAT_TWO_YR_RETURN      NUMBER(28, 6),
    CAT_THREE_YR_RETURN    NUMBER(28, 6),
    CAT_FOUR_YR_RETURN     NUMBER(28, 6),
    CAT_FIVE_YR_RETURN     NUMBER(28, 6),
    CAT_FUNDATAKEY         NUMBER(11) not null
)
/

create index SKYTEST.FD_CAT_QUARTILE_IDX2
    on SKYTEST.FD_CAT_QUARTILE (CAT_FUNDATAKEY)
/

create table SKYTEST.FD_CATMAST_QUARTILE
(
    CATMAST_SEQ                NUMBER not null
        constraint FD_CATMAST_QUARTILE_PK
            primary key,
    CATMAST_CREATION_TIMESTAMP DATE   not null,
    CATMAST_ONE_MONTH_RETURN   NUMBER(28, 6),
    CATMAST_THREE_MONTH_RETURN NUMBER(28, 6),
    CATMAST_YTD_RETURN         NUMBER(28, 6),
    CATMAST_INCEPTION_RETURN   NUMBER(28, 6),
    CATMAST_ONE_YR_RETURN      NUMBER(28, 6),
    CATMAST_TWO_YR_RETURN      NUMBER(28, 6),
    CATMAST_THREE_YR_RETURN    NUMBER(28, 6),
    CATMAST_FOUR_YR_RETURN     NUMBER(28, 6),
    CATMAST_FIVE_YR_RETURN     NUMBER(28, 6),
    CATMAST_FUNDATAKEY         NUMBER not null
)
/

create index SKYTEST.FD_CATMAST_QUARTILE_IDX2
    on SKYTEST.FD_CATMAST_QUARTILE (CATMAST_FUNDATAKEY)
/

create table SKYTEST.CLIENT_TMP
(
    FNAME VARCHAR2(45),
    LNAME VARCHAR2(45),
    DOB   DATE,
    CLIID NUMBER(11)
)
/

create table SKYTEST.FD_MASTER
(
    FD_MASTER_INT_ID       NUMBER(8) not null
        constraint FD_MASTER_PK
            primary key,
    FUND_MASTER_EN         VARCHAR2(32),
    FUND_MASTER_FR         VARCHAR2(32),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE
)
/

create table SKYTEST.FD_FUND
(
    FUND_SEQ                    NUMBER(11) not null
        constraint FUND_SEQ_PK
            primary key,
    FUND_CREATION_DATE          DATE       not null,
    FUND_ENABLED                VARCHAR2(1) default 'Y',
    FUND_FUNDATAKEY             NUMBER(11),
    FUND_NAME_EN                VARCHAR2(64),
    FUND_NAME_FR                VARCHAR2(64),
    FUND_MASTER_NAME_EN         VARCHAR2(32),
    FUND_MASTER_NAME_FR         VARCHAR2(32),
    FUND_TYPE_EN                VARCHAR2(64),
    FUND_TYPE_FR                VARCHAR2(64),
    FUND_OBJECTIVE_EN           VARCHAR2(512),
    FUND_OBJECTIVE_FR           VARCHAR2(512),
    FUND_STRATEGY_EN            VARCHAR2(512),
    FUND_STRATEGY_FR            VARCHAR2(512),
    FUND_RRSP_CODE              VARCHAR2(1),
    FUND_TOTAL_ASSETS           NUMBER(15, 6),
    FUND_MER                    NUMBER(15, 6),
    FUND_AVAIL                  VARCHAR2(1),
    FUND_LOAD_EN                VARCHAR2(32),
    FUND_LOAD_FR                VARCHAR2(32),
    FUND_CLASS_EN               VARCHAR2(16),
    FUND_CLASS_FR               VARCHAR2(16),
    FUND_LAST_MODIFICATION_DATE DATE,
    MATURITY_BENEFIT            NUMBER(5, 2),
    MATURITY_BENEFIT_DESC_EN    VARCHAR2(300),
    MATURITY_BENEFIT_DESC_FR    VARCHAR2(300),
    DEATH_BENEFIT               NUMBER(5, 2),
    DEATH_BENEFIT_DESC_EN       VARCHAR2(300),
    DEATH_BENEFIT_DESC_FR       VARCHAR2(300),
    GENERAL_NOTE_EN             VARCHAR2(300),
    GENERAL_NOTE_FR             VARCHAR2(300),
    FD_CATEGORY                 NUMBER(11)
        constraint FD_FUND_FK1
            references SKYTEST.FD_CATEGORY,
    FUND_GRADE                  VARCHAR2(1),
    FD_MASTER                   NUMBER(8)
        constraint FD_FUND_FK2
            references SKYTEST.FD_MASTER,
    NAVPS                       NUMBER(15, 6),
    AVERAGE                     NUMBER(15, 6),
    NAVPS_DATE                  DATE,
    STRATEGY_INDEX              NUMBER(8),
    FUND_COMPANY                NUMBER(8)
)
/

comment on column SKYTEST.FD_FUND.FD_MASTER is 'SOFT LINK BACK TO FD_MASTER TABLE'
/

create table SKYTEST.FD_MANAGER
(
    MANAGER_SEQ                    NUMBER(11)       not null
        constraint MANAGER_SEQ_PK
            primary key,
    MANAGER_CREATION_DATE          DATE             not null,
    MANAGER_FUNDATAKEY             NUMBER(11),
    MANAGER_KEY                    NUMBER(11),
    MANAGER_NAME                   VARCHAR2(64),
    MANAGER_BIO_EN                 VARCHAR2(512),
    MANAGER_BIO_FR                 VARCHAR2(512),
    MANAGER_ENABLED                CHAR default 'Y' not null,
    MANAGER_LAST_MODIFICATION_DATE DATE,
    FD_FUND                        NUMBER(11)
        constraint FD_MANAGER_FK1
            references SKYTEST.FD_FUND
)
/

create index SKYTEST.FD_MANAGER_INDEX2
    on SKYTEST.FD_MANAGER (FD_FUND)
/

create index SKYTEST.FD_MANAGER_INDEX1
    on SKYTEST.FD_MANAGER (MANAGER_NAME)
/

create index SKYTEST.FD_MANAGER_IDX1
    on SKYTEST.FD_MANAGER (MANAGER_FUNDATAKEY)
/

create table SKYTEST.FD_PERFORMANCE
(
    PERF_SEQ                      NUMBER(11) not null
        constraint PERF_SEQ_PK
            primary key,
    PERF_CREATION_DATE            DATE,
    PERF_ENABLED                  CHAR default 'Y',
    PERF_FUNDATAKEY               NUMBER(11),
    PERF_FUNDATA_DATE             DATE,
    PERF_ONE_MONTH_RETURN         NUMBER(11, 6),
    PERF_THREE_MONTH_RETURN       NUMBER(11, 6),
    PERF_YTD_RETURN               NUMBER(11, 6),
    PERF_INCEPTION_RETURN         NUMBER(11, 6),
    PERF_ONE_YR_COMPOUND_RETURN   NUMBER(11, 6),
    PERF_TWO_YR_COMPOUND_RETURN   NUMBER(11, 6),
    PERF_THREE_YR_COMPOUND_RETURN NUMBER(11, 6),
    PERF_FOUR_YR_COMPOUND_RETURN  NUMBER(11, 6),
    PERF_FIVE_YR_COMPOUND_RETURN  NUMBER(11, 6),
    PERF_FUNDGRADE                CHAR,
    PERF_LAST_MODIFICATION_DATE   DATE,
    FD_FUND                       NUMBER(11)
        constraint FD_PERFORMANCE_FK1
            references SKYTEST.FD_FUND
)
/

create index SKYTEST.FD_PERFORMANCE_INDEX1
    on SKYTEST.FD_PERFORMANCE (FD_FUND)
/

create index SKYTEST.FD_PERFORMANCE_IDX11
    on SKYTEST.FD_PERFORMANCE (PERF_FIVE_YR_COMPOUND_RETURN)
/

create index SKYTEST.FD_PERFORMANCE_IDX10
    on SKYTEST.FD_PERFORMANCE (PERF_FOUR_YR_COMPOUND_RETURN)
/

create index SKYTEST.FD_PERFORMANCE_IDX9
    on SKYTEST.FD_PERFORMANCE (PERF_THREE_YR_COMPOUND_RETURN)
/

create index SKYTEST.FD_PERFORMANCE_IDX8
    on SKYTEST.FD_PERFORMANCE (PERF_TWO_YR_COMPOUND_RETURN)
/

create index SKYTEST.FD_PERFORMANCE_IDX7
    on SKYTEST.FD_PERFORMANCE (PERF_ONE_YR_COMPOUND_RETURN)
/

create index SKYTEST.FD_PERFORMANCE_IDX6
    on SKYTEST.FD_PERFORMANCE (PERF_INCEPTION_RETURN)
/

create index SKYTEST.FD_PERFORMANCE_IDX5
    on SKYTEST.FD_PERFORMANCE (PERF_YTD_RETURN)
/

create index SKYTEST.FD_PERFORMANCE_IDX4
    on SKYTEST.FD_PERFORMANCE (PERF_THREE_MONTH_RETURN)
/

create index SKYTEST.FD_PERFORMANCE_IDX3
    on SKYTEST.FD_PERFORMANCE (PERF_ONE_MONTH_RETURN)
/

create index SKYTEST.FD_PERFORMANCE_IDX1
    on SKYTEST.FD_PERFORMANCE (PERF_FUNDATAKEY)
/

create table SKYTEST.FD_FUNDSERV
(
    SERV_SEQ                    NUMBER(11) not null
        constraint PK_FD_FUNDSERV
            primary key,
    SERV_CREATION_DATE          DATE,
    SERV_FUNDATAKEY             NUMBER(11),
    SERV_CODE                   VARCHAR2(8),
    SERV_LOAD_CODE              VARCHAR2(4),
    SERV_LOAD_DESC_EN           VARCHAR2(64),
    SERV_LOAD_DESC_FR           VARCHAR2(64),
    SERV_LAST_MODIFICATION_DATE DATE,
    FD_FUND                     NUMBER(11)
        constraint FD_FUNDSERV_FK1
            references SKYTEST.FD_FUND
)
/

create index SKYTEST.FD_FUNDSERV_INDEX1
    on SKYTEST.FD_FUNDSERV (FD_FUND)
/

create index SKYTEST.FD_FUNDSERV_IDX1
    on SKYTEST.FD_FUNDSERV (SERV_CODE)
/

create index SKYTEST.FD_FUND_IDX1
    on SKYTEST.FD_FUND (FUND_FUNDATAKEY)
/

create index SKYTEST.FD_FUND_IDX5
    on SKYTEST.FD_FUND (FUND_CLASS_EN)
/

create index SKYTEST.FD_FUND_IDX6
    on SKYTEST.FD_FUND (FUND_CLASS_FR)
/

create index SKYTEST.FD_FUND_INDEX4
    on SKYTEST.FD_FUND (FD_CATEGORY, FD_MASTER, FUND_GRADE)
/

create index SKYTEST.FD_FUND_INDEX1
    on SKYTEST.FD_FUND (FUND_NAME_EN)
/

create index SKYTEST.FD_FUND_INDEX2
    on SKYTEST.FD_FUND (FUND_NAME_FR)
/

create index SKYTEST.FD_FUND_INDEX6
    on SKYTEST.FD_FUND (FD_CATEGORY)
/

create index SKYTEST.FD_FUND_INDEX7
    on SKYTEST.FD_FUND (FD_MASTER, FUND_GRADE)
/

create index SKYTEST.FD_FUND_INDEX3
    on SKYTEST.FD_FUND (FUND_MER desc)
/

create table SKYTEST.IM_ADDRESS
(
    ADDRESSID        NUMBER(19)    not null
        primary key,
    ADDRESSLINE1     VARCHAR2(100) not null,
    ADDRESSLINE2     VARCHAR2(100),
    ADDRESSTYPE      VARCHAR2(20)  not null,
    AGENT_SLOT       VARCHAR2(1),
    AGENTCOMPANYID   NUMBER(19),
    AGENTSLOTID      NUMBER(19),
    CITY             VARCHAR2(50),
    CLIENTID         NUMBER(19),
    CORPORATENAME    VARCHAR2(100),
    COUNTRY          NUMBER(10),
    DONOTSEND        VARCHAR2(1),
    ENDDATE          TIMESTAMP(6),
    ENTITYID         VARCHAR2(10)  not null,
    ISPRIMARY        VARCHAR2(1)   not null,
    LASTUPDATEDATE   TIMESTAMP(6),
    LASTUPDATEUSER   VARCHAR2(30),
    POSTALZIPCODE    VARCHAR2(12),
    PROVSTATE        NUMBER(10),
    SENDSOLICITATION VARCHAR2(1),
    SENDTYPE         NUMBER(10),
    STARTDATE        TIMESTAMP(6)
)
/

create table SKYTEST.AUDIT_TRAIL
(
    AUDIT_TRAIL_INT_ID           NUMBER(8) not null,
    AUDIT_TRAIL_SRC_INT_ID       NUMBER(8),
    CREATION_LAST_MOD_FIELD_DATE DATE,
    BEFORE_CHANGE                VARCHAR2(2048),
    AFTER_CHANGE                 VARCHAR2(2048),
    ORIGIN                       VARCHAR2(15),
    "UID"                        VARCHAR2(15),
    AUDIT_TRAIL_TYPE             NUMBER(5)
)
/

create index SKYTEST.AUDIT_TRAIL_IDX1
    on SKYTEST.AUDIT_TRAIL (AUDIT_TRAIL_INT_ID)
/

create index SKYTEST.AUDIT_TRAIL_IDX2
    on SKYTEST.AUDIT_TRAIL (AUDIT_TRAIL_SRC_INT_ID)
/

alter table SKYTEST.AUDIT_TRAIL
    add constraint AUDIT_TRAIL_PK
        primary key (AUDIT_TRAIL_INT_ID)
/

create table SKYTEST.FIXED_LENGTH_FILES
(
    FILE_TYPE        VARCHAR2(3) not null,
    PROD_FILE_CODE   VARCHAR2(2),
    TEST_FILE_CODE   VARCHAR2(2),
    FILE_DESCRIPTION VARCHAR2(60),
    FILE_LENGTH      NUMBER(4),
    FILE_VERSION     VARCHAR2(6)
)
/

create index SKYTEST.FIXED_LENGTH_FILES_IDX1
    on SKYTEST.FIXED_LENGTH_FILES (FILE_TYPE)
/

alter table SKYTEST.FIXED_LENGTH_FILES
    add constraint AFILE_PK
        primary key (FILE_TYPE)
/

create table SKYTEST.SUN_DEATH_TOTALS
(
    AGENTID               NUMBER(11) not null
        primary key,
    ADVISORNUMBER         VARCHAR2(15),
    OLD_AGENT_FIRSTNAME   VARCHAR2(150),
    OLD_AGENT_LASTNAME    VARCHAR2(150),
    SUNTERMDATE           DATE,
    TOTPOLS               NUMBER(8),
    TOTCLI                NUMBER(8),
    TOTPREM               NUMBER(10, 2),
    BRANCH                VARCHAR2(64),
    MARKETING_REG         VARCHAR2(64),
    BRANCH_NAME_EN        VARCHAR2(64),
    MARKETING_REG_NAME_EN VARCHAR2(64),
    BRANCH_NAME_FR        VARCHAR2(64),
    MARKETING_REG_NAME_FR VARCHAR2(64)
)
/

create table SKYTEST.SUN_ALL_POLICY_TABLEX
(
    AGENTID               NUMBER(15),
    ADVISORNUMBER         VARCHAR2(15),
    OLD_AGENT_FIRSTNAME   VARCHAR2(150),
    OLD_AGENT_LASTNAME    VARCHAR2(150),
    SUNTERMDATE           DATE,
    TOTPOLS               NUMBER(9),
    TOTCLI                NUMBER(9),
    TOTPREM               NUMBER(9),
    BRANCH                VARCHAR2(64),
    MARKETING_REG         VARCHAR2(64),
    BRANCH_NAME_EN        VARCHAR2(64),
    BRANCH_NAME_FR        VARCHAR2(64),
    MARKETING_REG_NAME_EN VARCHAR2(64),
    MARKETING_REG_NAME_FR VARCHAR2(64),
    POLICYSTATUS          NUMBER(4)
)
/

create table SKYTEST.CONTRACT_NOTES
(
    CONTRACT_NOTES_INT_ID NUMBER(8) not null,
    CONTRACT              NUMBER(8) not null,
    TITLE                 VARCHAR2(80),
    IS_PRIVATE            VARCHAR2(1),
    LAST_UPDATE_DATE      DATE,
    LAST_UPDATE_USER      VARCHAR2(30),
    CREATED_DATE_TIME     DATE,
    CREATE_USER           VARCHAR2(30),
    NOTE                  VARCHAR2(2000)
)
/

create index SKYTEST.CONTRACT_NOTES_IDX1
    on SKYTEST.CONTRACT_NOTES (CONTRACT_NOTES_INT_ID)
/

create index SKYTEST.CONTRACT_NOTES_IDX2
    on SKYTEST.CONTRACT_NOTES (CONTRACT)
/

alter table SKYTEST.CONTRACT_NOTES
    add constraint CONTRACT_NOTES_PK
        primary key (CONTRACT_NOTES_INT_ID)
/

create table SKYTEST.TAXES_HISTORY
(
    TAXES_HISTORY_INT_ID NUMBER(11)   not null,
    TAXES                NUMBER(8)    not null
        constraint TAXES_HISTORY_TAXES_FK
            references SKYTEST.TAXES,
    TAX_CODE             VARCHAR2(3)  not null,
    PROVINCE_INT_ID      NUMBER(8)    not null,
    TAX_DESC_EN          VARCHAR2(50) not null,
    TAX_DESC_FR          VARCHAR2(50),
    TAX_RATE             NUMBER(5, 2) not null,
    TAX_ON_TAX           VARCHAR2(1)  not null,
    EFFECTIVE_DATE       DATE,
    END_DATE             DATE
)
/

create index SKYTEST.TAXES_HISTORY_IDX1
    on SKYTEST.TAXES_HISTORY (TAXES)
/

create index SKYTEST.TAXES_HISTORY_IDX2
    on SKYTEST.TAXES_HISTORY (TAXES_HISTORY_INT_ID)
/

create index SKYTEST.TAXES_HISTORY_IDX3
    on SKYTEST.TAXES_HISTORY (TAX_CODE)
/

alter table SKYTEST.TAXES_HISTORY
    add constraint TAXES_HIST_PK
        primary key (TAXES_HISTORY_INT_ID)
/

create table SKYTEST.FD_BEST_CATEGORY2
(
    BEST_CATEGORY_INT_ID          NUMBER(8) not null
        constraint FD_BEST_CATEGORY2_PK
            primary key,
    FD_CATEGORY2                  NUMBER(8)
        constraint FD_BEST_CATEGORY2_FD_CATE_FK1
            references SKYTEST.FD_CATEGORY2,
    CREATION_DATE                 DATE,
    ONE_MONTH_RETURN              NUMBER(11, 4),
    ONE_MONTH_RETURN_FUND         NUMBER(11),
    THREE_MONTH_RETURN            NUMBER(11, 4),
    THREE_MONTH_RETURN_FUND       NUMBER(11),
    YTD_RETURN                    NUMBER(11, 4),
    YTD_RETURN_FUND               NUMBER(11),
    INCEPTION_RETURN              NUMBER(11, 4),
    INCEPTION_RETURN_FUND         NUMBER(11),
    ONE_YR_COMPOUND_RETURN        NUMBER(11, 4),
    ONE_YR_COMPOUND_RETURN_FUND   NUMBER(11),
    TWO_YR_COMPOUND_RETURN        NUMBER(11, 4),
    TWO_YR_COMPOUND_RETURN_FUND   NUMBER(11),
    THREE_YR_COMPOUND_RETURN      NUMBER(11, 4),
    THREE_YR_COMPOUND_RETURN_FUND NUMBER(11),
    FOUR_YR_COMPOUND_RETURN       NUMBER(11, 4),
    FOUR_YR_COMPOUND_RETURN_FUND  NUMBER(11),
    FIVE_YR_COMPOUND_RETURN       NUMBER(11, 4),
    FIVE_YR_COMPOUND_RETURN_FUND  NUMBER(11)
)
/

create table SKYTEST.FD_HOLDING2
(
    HOLDING_SEQ                    NUMBER(11) not null
        constraint FD_HOLDING2_PK
            primary key,
    HOLDING_CREATION_DATE          DATE       not null,
    HOLDING_FUNDATA_DATE           DATE,
    HOLDING_RANK                   NUMBER(11),
    HOLDING_SECURITY_NAME          VARCHAR2(128),
    HOLDING_MARKET_PERCENT         NUMBER(11, 6),
    HOLDING_ENABLED                VARCHAR2(1),
    HOLDING_FUNDATAKEY             NUMBER(11),
    HOLDING_LAST_MODIFICATION_DATE DATE,
    FD_FUND2                       NUMBER(11),
    SECURITY_NAME_ID               NUMBER(11)
)
/

create index SKYTEST.FD_HOLDING2_INDEX4
    on SKYTEST.FD_HOLDING2 (SECURITY_NAME_ID)
/

create index SKYTEST.FD_HOLDING2_INDEX3
    on SKYTEST.FD_HOLDING2 (FD_FUND2)
/

create index SKYTEST.FD_HOLDING2_INDEX2
    on SKYTEST.FD_HOLDING2 (HOLDING_SECURITY_NAME)
/

create index SKYTEST.FD_HOLDING2_INDEX1
    on SKYTEST.FD_HOLDING2 (HOLDING_FUNDATAKEY)
/

create table SKYTEST.CANNEX_INTEREST_RATE_NEW_CARD
(
    INTEREST_RATE_INT_ID NUMBER(10) not null
        constraint SYS_C0093940
            primary key
        constraint SYS_C0091786
            check ("INTEREST_RATE_INT_ID" IS NOT NULL)
        constraint SYS_C0092653
            check ("INTEREST_RATE_INT_ID" IS NOT NULL),
    DTYPE                VARCHAR2(31),
    CHANGE               NUMBER(6, 4),
    CHANGE_DATE_STRING   VARCHAR2(8),
    CHANGE_TIME_STRING   VARCHAR2(20),
    CREATION_DATE        TIMESTAMP(6),
    INTEREST_RATE_VALUE  NUMBER(6, 4),
    LAST_MODIFICATION    TIMESTAMP(6),
    TERM_VALUE           VARCHAR2(255),
    UNIQUE_NUMBER        NUMBER(19),
    CANNEX_PRODUCT       NUMBER(10),
    DONT_DELETE          VARCHAR2(1),
    PURCHASE_RATE        NUMBER(6, 4),
    PURCHASE_RATE_CHANGE NUMBER(6, 4),
    CASH_ADVANCE_RATE    NUMBER(6, 4),
    CASH_ADVANCE_CHANGE  NUMBER(6, 4)
)
/

create table SKYTEST.LATLONGCANADA_NEW
(
    POSTALCODE      VARCHAR2(6) not null,
    FSA             VARCHAR2(3),
    LATITUDE        NUMBER(10, 7),
    LONGITUDE       NUMBER(10, 7),
    CITY            VARCHAR2(50),
    FSA1            VARCHAR2(1),
    FSA_PROV        NUMBER(2),
    AREA_TYPE       VARCHAR2(15),
    CITYTYPE        VARCHAR2(2),
    PROVINCE        VARCHAR2(30),
    PROVINCEABBR    VARCHAR2(2),
    AREACODE        VARCHAR2(7),
    TIMEZONE        VARCHAR2(30),
    DST             VARCHAR2(1),
    LAT_LONG_INT_ID NUMBER(11)  not null
        constraint LATLONGCANADA_NEW_PK
            primary key
)
/

create table SKYTEST.FD_GEN_FUND_INFO_PY
(
    FUND_SEQ                    NUMBER(11) not null
        primary key,
    FUND_CREATION_DATE          DATE       not null,
    FUND_FUNDATAKEY             NUMBER(11),
    FUND_OBJECTIVE_EN           VARCHAR2(512),
    FUND_OBJECTIVE_FR           VARCHAR2(512),
    FUND_STRATEGY_EN            VARCHAR2(512),
    FUND_STRATEGY_FR            VARCHAR2(512),
    FUND_LAST_MODIFICATION_DATE DATE,
    MATURITY_BENEFIT            NUMBER(5, 2),
    MATURITY_BENEFIT_DESC_EN    VARCHAR2(300),
    MATURITY_BENEFIT_DESC_FR    VARCHAR2(300),
    DEATH_BENEFIT               NUMBER(5, 2),
    DEATH_BENEFIT_DESC_EN       VARCHAR2(300),
    DEATH_BENEFIT_DESC_FR       VARCHAR2(300),
    GENERAL_NOTE_EN             VARCHAR2(300),
    GENERAL_NOTE_FR             VARCHAR2(300)
)
/

create table SKYTEST."delete1"
(
    "key"               NUMBER(10),
    "ename"             VARCHAR2(255),
    "fname"             VARCHAR2(255),
    FUND_ENABLED        VARCHAR2(1),
    FUND_FUNDATAKEY     NUMBER(11),
    FUND_NAME_EN        VARCHAR2(250),
    FUND_NAME_FR        VARCHAR2(250),
    PERF_ONE_DAY_RETURN NUMBER(11, 6)
)
/

create table SKYTEST.FFLEX_CATEGORY_ANNUAL
(
    FUND_ENABLED                   VARCHAR2(1),
    FUND_FUNDATAKEY                NUMBER(11),
    FUND_NAME_EN                   VARCHAR2(250),
    FUND_NAME_FR                   VARCHAR2(250),
    PERF_ONE_DAY_RETURN            NUMBER(11, 6),
    TYPE_KEY                       NUMBER(11) not null
        constraint FFLEX_FINAL_PK
            primary key,
    TYPE_ENGLISH_NAME              VARCHAR2(64),
    TYPE_FRENCH_NAME               VARCHAR2(64),
    DAILY_PRICE_NAVPS              NUMBER(15, 6),
    PERF_ONE_MONTH_RETURN          NUMBER(11, 6),
    PERF_THREE_MONTH_RETURN        NUMBER(11, 6),
    PERF_YTD_RETURN                NUMBER(11, 6),
    FUND_RRSP_CODE                 VARCHAR2(12),
    FUND_OBJECTIVE_EN              VARCHAR2(500),
    FUND_OBJECTIVE_FR              VARCHAR2(500),
    BENCHMARK_YTD_RETURN           NUMBER(11, 6),
    FUND_TOTAL_ASSETS              NUMBER(15, 6),
    FUND_LOAD_EN                   VARCHAR2(255),
    FUND_LOAD_FR                   VARCHAR2(255),
    PRICE_CHANGE_FROM_PREVIOUS_DAY NUMBER(15, 6),
    PERF_FUND_GRADE                VARCHAR2(1),
    ONE_MONTH_QUARTILE             VARCHAR2(1),
    THREE_MONTH_QUARTILE           VARCHAR2(1),
    YR3_ANNUALIZED_STD_DEV         NUMBER(11, 6),
    YR3_VOLATILITY_RANKING         NUMBER(2),
    FUND_MER                       NUMBER(15, 6),
    DAILY_PRICE_NAVPS_DATE         DATE
)
/

create table SKYTEST.DYNAMIC_INVOKER
(
    DYNAMIC_INVOKER_INT_ID NUMBER(8) not null
        constraint DYNAMIC_INVOKER_PK
            primary key,
    NAME                   VARCHAR2(64),
    DESCRIPTION            VARCHAR2(256),
    ANNUAL_METHOD          VARCHAR2(64),
    MONTHLY_METHOD         VARCHAR2(64),
    TYPE                   NUMBER(8),
    COMPANY_ID             NUMBER(8)
)
/

comment on column SKYTEST.DYNAMIC_INVOKER.TYPE is '1 = Enhanced Product'
/

create table SKYTEST.SCHEDULER_MANAGER
(
    SCHEDULER_INT_ID NUMBER(8) not null
        primary key,
    NAME             VARCHAR2(255),
    ACTIVE           VARCHAR2(2),
    ACTIVATION       VARCHAR2(255),
    LINKED           VARCHAR2(255)
)
/

comment on column SKYTEST.SCHEDULER_MANAGER.ACTIVATION is 'date when run'
/

comment on column SKYTEST.SCHEDULER_MANAGER.LINKED is 'says the name of the function is linked to'
/

create table SKYTEST.FD_PERF_BEST_OVERALL
(
    PERF_BEST_OVERALL_INT_ID      NUMBER(9) not null
        constraint FD_PERF_BEST_OVERALL_PK
            primary key,
    ONE_MONTH_RET_VALUE           NUMBER(9, 4),
    ONE_MONTH_RET_DATAKEY         NUMBER(9),
    THREE_MONTH_RET_DATAKEY       NUMBER(9),
    THREE_MONTH_RET_VALUE         NUMBER(9, 4),
    YTD_RET_VALUE                 NUMBER(9, 4),
    YTD_RET_DATAKEY               NUMBER(9),
    INCEPTION_RET_DATAKEY         NUMBER(9),
    INCEPTION_RET_VALUE           NUMBER(9, 4),
    ONE_YR_COMPOUND_RET_VALUE     NUMBER(9, 4),
    ONE_YR_COMPOUND_RET_DATAKEY   NUMBER(9),
    TWO_YR_COMPOUND_RET_VALUE     NUMBER(9, 4),
    TWO_YR_COMPOUND_RET_DATAKEY   NUMBER(9),
    THREE_YR_COMPOUND_RET_DATAKEY NUMBER(9),
    THREE_YR_COMPOUND_RET_VALUE   NUMBER(9, 4),
    FOUR_YR_COMPOUND_RET_DATAKEY  NUMBER(9),
    FOUR_YR_COMPOUND_RET_VALUE    NUMBER(9, 4),
    FIVE_YR_COMPOUND_RET_DATAKEY  NUMBER(9),
    FIVE_YR_COMPOUND_RET_VALUE    NUMBER(9, 4),
    CREATION_DATE                 DATE,
    LAST_MODIFICATION_DATE        DATE
)
/

create table SKYTEST.WEB_PAGE_DEFINITION
(
    PAGE_DEFINITION_INT_ID NUMBER(8) not null
        constraint WEB_PAGE_DEFINITION_PK
            primary key,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    SITE_ID                NUMBER(4),
    SITE_NAME              VARCHAR2(256),
    TITLE                  VARCHAR2(64),
    PAGE_ID                VARCHAR2(256),
    PAGE_NAME              VARCHAR2(256),
    CSS_OVERRIDE           VARCHAR2(2048),
    JS_OVERRIDE            VARCHAR2(2048),
    DEMO_MODE              VARCHAR2(1),
    PRODUCTION_MODE        VARCHAR2(1),
    WEB_CONTENT            NUMBER(8)
        constraint WEB_PAGE_DEFINITION_FK1
            references SKYTEST.WEB_CONTENT,
    EFFECTIVE_DATE         DATE,
    EXPIRATION_DATE        DATE
)
/

comment on column SKYTEST.WEB_PAGE_DEFINITION.SITE_ID is 'USING SITE_ID_TYPE'
/

comment on column SKYTEST.WEB_PAGE_DEFINITION.PAGE_ID is 'LOOKUP ID'
/

comment on column SKYTEST.WEB_PAGE_DEFINITION.PAGE_NAME is 'LOOKUP NAME'
/

create table SKYTEST.YAHOO_FINANCE_DATA
(
    YAHOO_FINANCE_INT_ID NUMBER(11) not null
        constraint YAHOO_FINANCE_DATA_PK
            primary key,
    CREATION_DATE        DATE,
    TICKER               VARCHAR2(16),
    PREVIOUS_CLOSE       NUMBER(12, 4),
    OPEN                 NUMBER(12, 4),
    ASK                  NUMBER(12, 4),
    BID                  NUMBER(12, 4),
    DAY_LOW              NUMBER(12, 4),
    DAY_HIGH             NUMBER(12, 4),
    CHANGE               NUMBER(12, 4),
    CHANGE_PERCENT       NUMBER(12, 4),
    TRADE_DATE_TIME      DATE,
    LAST_TRADE_PRICE     NUMBER(12, 4),
    CURRENT_VALUE        NUMBER(12, 4),
    LOW_52WEEKS          NUMBER(12, 4),
    HIGH_52WEEKS         NUMBER(12, 4),
    YAHOO_FEED           NUMBER(11)
        constraint YAHOO_FINANCE_DATA_FK1
            references SKYTEST.YAHOO_FINANCE_FEED,
    VOLUME               NUMBER(11)
)
/

comment on column SKYTEST.YAHOO_FINANCE_DATA.LAST_TRADE_PRICE is 'CURRENT_VALUE'
/

comment on column SKYTEST.YAHOO_FINANCE_DATA.CURRENT_VALUE is 'CALC : PREVIOUS_CLOSE + CHANGE'
/

create table SKYTEST.ONLINE_HELP
(
    ONLINE_HELP_INT_ID     NUMBER(8) not null
        constraint ONLINE_HELP_PK
            primary key,
    IS_HTML                VARCHAR2(1),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    ONLINE_CATEGORY        NUMBER(8),
    NAME                   VARCHAR2(256),
    TITLE_EN               VARCHAR2(256),
    TITLE_FR               VARCHAR2(256),
    CONTENT_EN             BLOB,
    CONTENT_FR             BLOB
)
/

create table SKYTEST.KNOWLEDGE_BASE
(
    KNOWLEDGE_INT_ID       NUMBER(11) not null
        constraint KNOWLEDGE_BASE_PK
            primary key,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    TAGS                   VARCHAR2(1024),
    TITLE_EN               VARCHAR2(256),
    TITLE_FR               VARCHAR2(256),
    WEB_CONTENT            NUMBER(11)
        constraint KNOWLEDGE_BASE_FK1
            references SKYTEST.WEB_CONTENT,
    ACTIVE                 VARCHAR2(1),
    CATEGORY               NUMBER(4),
    IMAGE                  BLOB
)
/

comment on column SKYTEST.KNOWLEDGE_BASE.CATEGORY is '2=HELP, 1=HINT, 3=KNOWLEDGE'
/

create table SKYTEST.DEFAULT_EMAILS
(
    DEFAULT_EMAILS_INT_ID NUMBER(8) not null
        primary key,
    TYPE                  NUMBER(8),
    SUBJECT_EN            VARCHAR2(255),
    SUBJECT_FR            VARCHAR2(255),
    CONTENT_EN            BLOB,
    CONTENT_FR            BLOB
)
/

create table SKYTEST.AGREEMENTS
(
    AGREEMENTS_INT_ID NUMBER(8) not null,
    AGREEMENT_TYPE    NUMBER(2),
    START_DATE        DATE,
    EXPIRE_DATE       DATE,
    REQUEST_DATE      DATE,
    LAST_USER         VARCHAR2(30),
    COMMENTS          VARCHAR2(2000),
    SEND_BY_METHOD    VARCHAR2(1),
    LAST_UPDATE_DATE  DATE,
    AGREEMENTS_IMAGE  BLOB
)
/

create index SKYTEST.AGREEMENTS_IDX1
    on SKYTEST.AGREEMENTS (AGREEMENTS_INT_ID)
/

alter table SKYTEST.AGREEMENTS
    add constraint AGREEMENTS_PK
        primary key (AGREEMENTS_INT_ID)
/

create table SKYTEST.LOGOS
(
    LOGOS_INT_ID NUMBER(8) not null
        primary key,
    TYPE_        NUMBER(4),
    TYPE_ID      NUMBER(8),
    LOGO_EN      BLOB,
    LOGO_FR      BLOB
)
/

comment on column SKYTEST.LOGOS.TYPE_ is '1--COMPANY ; 2--- PRODUCT; 3 .... KEEP ADDING'
/

create table SKYTEST.IMAGE_STORE
(
    IMAGE_STORE_INT_ID     NUMBER(8) not null
        constraint IMAGE_STORE_PK
            primary key,
    IMAGE_TYPE             NUMBER(4) not null,
    REFERENCE_INT_ID       NUMBER(8) not null,
    IMAGE_DESC_EN          VARCHAR2(256),
    IMAGE_DESC_FR          VARCHAR2(256),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    STORE                  BLOB,
    USER_NAME              VARCHAR2(128),
    MASTER_CODE            VARCHAR2(16),
    OWNER_TYPE             NUMBER(4),
    SUB_TYPE               NUMBER(4),
    OWNER_INT_ID           NUMBER(8),
    SUB_INT_ID             NUMBER(8),
    FILENAME               VARCHAR2(256)
)
/

create table SKYTEST.PRODUCT_NOTES
(
    PRODUCT_NOTE_INT_ID NUMBER(8) not null
        primary key,
    NOTE_EN             VARCHAR2(1500),
    NOTE_FR             VARCHAR2(1500),
    END_DATE            DATE,
    ACTIVE              VARCHAR2(2),
    TITLE_EN            VARCHAR2(255),
    TITLE_FR            VARCHAR2(255),
    NOTE_TYPE           NUMBER(8) not null
)
/

create table SKYTEST.PRODUCT
(
    PRODUCT_INT_ID         NUMBER(8) not null,
    PRODUCT_TYPE           NUMBER(8) not null
        constraint PRODUCT_PROD_SUPP_TYPE_FK
            references SKYTEST.PRODUCT_TYPE,
    PRODUCT_DESC_EN        VARCHAR2(100),
    PRODUCT_DESC_FR        VARCHAR2(100),
    AVAILABLE              VARCHAR2(1),
    LAST_MODIFICATION_DATE DATE,
    ACTIVE_DATE            DATE,
    INACTIVE_DATE          DATE,
    MANAGEMENT_ONLY        VARCHAR2(1),
    PRODUCT_ID             NUMBER(8),
    CREATION_DATE          DATE,
    PROXY                  VARCHAR2(1),
    PRODUCT_NOTE           NUMBER(8)
        constraint PRODUCT_NOTE_FK
            references SKYTEST.PRODUCT_NOTES,
    SHORT_ENGLISH_DESC     VARCHAR2(600),
    SHORT_FRENCH_DESC      VARCHAR2(600)
)
/

comment on table SKYTEST.PRODUCT is 'Product information for each company in Company Table'
/

create index SKYTEST.PRODUCT_IDX2
    on SKYTEST.PRODUCT (PRODUCT_DESC_EN)
/

create index SKYTEST.PRODUCT_IDX4
    on SKYTEST.PRODUCT (PRODUCT_DESC_FR)
/

create unique index SKYTEST.PRODUCT_IDX1
    on SKYTEST.PRODUCT (PRODUCT_INT_ID)
/

create index SKYTEST.PRODUCT_IDX3
    on SKYTEST.PRODUCT (PRODUCT_TYPE)
/

alter table SKYTEST.PRODUCT
    add constraint PRODUCT_PK
        primary key (PRODUCT_INT_ID)
/

create table SKYTEST.RIDER_PRODUCT
(
    RIDER_PRODUCT_INT_ID NUMBER(8) not null,
    PRODUCT              NUMBER(8) not null
        constraint RIDER_PRODUCT_PRODUCT_FK
            references SKYTEST.PRODUCT,
    RIDER                NUMBER(8) not null
        constraint RIDER_PRODUCT_RIDER_FK
            references SKYTEST.RIDER,
    SHORTENG             VARCHAR2(2000),
    SHORTFRE             VARCHAR2(3000),
    MINAGE               NUMBER(3),
    MAXAGE               NUMBER(3),
    LASTAGE              NUMBER(3),
    METHOD               VARCHAR2(1),
    DIV                  NUMBER(5, 2),
    EXCLUDE              VARCHAR2(1),
    ENGNAME              VARCHAR2(80),
    FRENAME              VARCHAR2(80),
    ACTIVE               VARCHAR2(1),
    POLICY_FEE_INCL      VARCHAR2(1),
    ANNUAL_METHOD_ID     NUMBER(11),
    MONTHLY_METHOD_ID    NUMBER(11),
    MIN_FACE_AMOUNT      NUMBER(10, 2),
    MAX_FACE_AMOUNT      NUMBER(10, 2),
    MIN_PREM_REQ         NUMBER(7, 2),
    MAX_PREM_REQ         NUMBER(10, 2),
    INCLUDED_IN_BASE     VARCHAR2(1),
    MINAGE_SMOKER        NUMBER(3),
    MAXAGE_SMOKER        NUMBER(3),
    RIDER_PRODUCT_ID     NUMBER(11),
    POLICY_FEE           NUMBER(7, 2),
    AVAILABLE_FOR        VARCHAR2(1),
    FIXED_PRICE          NUMBER(10, 2),
    UNIT_INCR            NUMBER(10, 2),
    MINAGE_CHILD         NUMBER(3),
    MAXAGE_CHILD         NUMBER(3),
    ENDAGE_CHILD         NUMBER(3),
    CALC_IN_FACE_AMNT    VARCHAR2(1),
    CLASS_SPEC           VARCHAR2(1),
    ABBRV                VARCHAR2(25),
    HAS_VALUES           VARCHAR2(1),
    RIDER_OR_BENEFIT     VARCHAR2(1),
    PAYABLE_YEARS        NUMBER(3),
    PAYABLE_AGE          NUMBER(3)
)
/

create index SKYTEST.RIDER_PRODUCT_IDX1
    on SKYTEST.RIDER_PRODUCT (PRODUCT)
/

create index SKYTEST.RIDER_PRODUCT_IDX2
    on SKYTEST.RIDER_PRODUCT (RIDER)
/

create index SKYTEST.RIDER_PRODUCT_IDX3
    on SKYTEST.RIDER_PRODUCT (RIDER_PRODUCT_INT_ID)
/

alter table SKYTEST.RIDER_PRODUCT
    add constraint RIDER_PRODUCT_PK
        primary key (RIDER_PRODUCT_INT_ID)
/

create table SKYTEST.PRODUCT_COMMISSION
(
    PRODUCT_COMMISSION_INT_ID NUMBER(11)   not null,
    PRODUCT                   NUMBER(8)    not null
        constraint PRODUCT_COMMISSION_PRODUCT_FK
            references SKYTEST.PRODUCT,
    COMMISSION_PERCENT        NUMBER(5, 2) not null,
    FROM_YEAR                 NUMBER(2),
    TO_YEAR                   NUMBER(2),
    FROM_DOLLAR               NUMBER(9),
    TO_DOLLAR                 NUMBER(9),
    LAST_MODIFICATION_DATE    DATE         not null,
    LAST_UPDATE_USER          VARCHAR2(30) not null,
    MINAGE                    NUMBER(3),
    MAXAGE                    NUMBER(3),
    POLICY_FEE                NUMBER(5, 2),
    MIN_FACE                  NUMBER(10, 2),
    MAX_FACE                  NUMBER(10, 2),
    CALC_BY_PREM_OR_COMM      VARCHAR2(2),
    START_DATE                DATE,
    END_DATE                  DATE,
    FORMULA_CODE              VARCHAR2(1),
    MOD_FACT_MONTH            NUMBER(12, 10),
    MOD_FACT_SEMI             NUMBER(12, 10),
    MOD_FACT_QUART            NUMBER(12, 10),
    PRODUCT_COMMISSION_ID     NUMBER(11),
    PROXY                     VARCHAR2(1),
    CREATION_DATE             DATE,
    CALC_BY_MODAL             VARCHAR2(1)
)
/

comment on column SKYTEST.PRODUCT_COMMISSION.PRODUCT_COMMISSION_ID is 'iq3.im_productcommission.prodcommissionid (primary key)'
/

create index SKYTEST.PRODUCT_COMMISSION_IDX1
    on SKYTEST.PRODUCT_COMMISSION (PRODUCT_COMMISSION_INT_ID)
/

create index SKYTEST.PRODUCT_COMMISSION_IDX2
    on SKYTEST.PRODUCT_COMMISSION (PRODUCT)
/

alter table SKYTEST.PRODUCT_COMMISSION
    add constraint PRODUCT_COMMISSION_PK
        primary key (PRODUCT_COMMISSION_INT_ID)
/

create table SKYTEST.PRODUCT_INVEST_ANNUITY
(
    PRODUCT_INVEST_ANNUITY_INT_ID NUMBER(8)   not null
        constraint PRODUCT_INV_ANN_FK
            references SKYTEST.PRODUCT,
    PRODUCT_TYPE                  NUMBER(8)   not null,
    PRODUCT_CLASS                 NUMBER(8),
    MGMT_COMPANY_CODE             VARCHAR2(3) not null,
    CURRENCY                      VARCHAR2(3),
    LOAD_TYPE                     VARCHAR2(20),
    SETTLEMENT                    VARCHAR2(5),
    CUT_OFF_TIME                  VARCHAR2(8),
    MONEY_MARKET_FLAG             VARCHAR2(1),
    COMMISSION                    NUMBER(5, 2),
    PAC_SWP                       VARCHAR2(7),
    DISTRIBUTION                  VARCHAR2(25),
    CLASSIFICATION                VARCHAR2(25),
    PRODUCT_DESC_EN               VARCHAR2(100),
    PRODUCT_DESC_FR               VARCHAR2(100),
    COMPANY                       NUMBER(8)   not null
)
/

comment on table SKYTEST.PRODUCT_INVEST_ANNUITY is 'Product information for each company in Company Table'
/

create unique index SKYTEST.PRODUCT_INV_ANN_IDX1
    on SKYTEST.PRODUCT_INVEST_ANNUITY (PRODUCT_INVEST_ANNUITY_INT_ID)
/

create index SKYTEST.PRODUCT_INV_ANN_IDX2
    on SKYTEST.PRODUCT_INVEST_ANNUITY (MGMT_COMPANY_CODE)
/

create index SKYTEST.PRODUCT_INV_ANN_IDX3
    on SKYTEST.PRODUCT_INVEST_ANNUITY (PRODUCT_TYPE)
/

alter table SKYTEST.PRODUCT_INVEST_ANNUITY
    add constraint PRODUCT_INV_ANN_PK
        primary key (PRODUCT_INVEST_ANNUITY_INT_ID)
/

create table SKYTEST.PRODUCT_INVEST_FUND
(
    PRODUCT_INVEST_FUND_INT_ID NUMBER(8)   not null
        constraint PRODUCT_INVEST_FUND_FK
            references SKYTEST.PRODUCT,
    MGMT_COMPANY_CODE          VARCHAR2(3) not null,
    FUND_ID                    VARCHAR2(5),
    CURRENCY                   VARCHAR2(3),
    LOAD_TYPE                  VARCHAR2(20),
    SETTLEMENT                 VARCHAR2(5),
    CUT_OFF_TIME               VARCHAR2(8),
    MONEY_MARKET_FLAG          VARCHAR2(1),
    COMMISSION                 NUMBER(5, 2),
    CLASSIFICATION             VARCHAR2(25),
    SHORT_NAME_EN              VARCHAR2(100),
    SHORT_NAME_FR              VARCHAR2(100)
)
/

comment on table SKYTEST.PRODUCT_INVEST_FUND is 'Product information for each company in Company Table'
/

create unique index SKYTEST.PRODUCT_INV_FUND_IDX1
    on SKYTEST.PRODUCT_INVEST_FUND (PRODUCT_INVEST_FUND_INT_ID)
/

create index SKYTEST.PRODUCT_INV_FUND_IDX2
    on SKYTEST.PRODUCT_INVEST_FUND (MGMT_COMPANY_CODE, FUND_ID)
/

alter table SKYTEST.PRODUCT_INVEST_FUND
    add constraint PRODUCT_INVEST_FUND_PK
        primary key (PRODUCT_INVEST_FUND_INT_ID)
/

create table SKYTEST.PRODUCT_INVEST_GIC
(
    PRODUCT_INVEST_GIC_INT_ID NUMBER(8)   not null
        constraint PRODUCT_INV_GIC_FK
            references SKYTEST.PRODUCT,
    PRODUCT_TYPE              NUMBER(8)   not null,
    PRODUCT_CLASS             NUMBER(8),
    MGMT_COMPANY_CODE         VARCHAR2(3) not null,
    CURRENCY                  VARCHAR2(3),
    LOAD_TYPE                 VARCHAR2(20),
    SETTLEMENT                VARCHAR2(5),
    CUT_OFF_TIME              VARCHAR2(8),
    MONEY_MARKET_FLAG         VARCHAR2(1),
    COMMISSION                NUMBER(5, 2),
    PAC_SWP                   VARCHAR2(7),
    DISTRIBUTION              VARCHAR2(25),
    CLASSIFICATION            VARCHAR2(25),
    PRODUCT_DESC_EN           VARCHAR2(100),
    PRODUCT_DESC_FR           VARCHAR2(100),
    COMPANY                   NUMBER(8)   not null
)
/

comment on table SKYTEST.PRODUCT_INVEST_GIC is 'Product information for each company in Company Table'
/

create unique index SKYTEST.PRODUCT_INV_GIC_IDX1
    on SKYTEST.PRODUCT_INVEST_GIC (PRODUCT_INVEST_GIC_INT_ID)
/

create index SKYTEST.PRODUCT_INV_GIC_IDX2
    on SKYTEST.PRODUCT_INVEST_GIC (MGMT_COMPANY_CODE)
/

create index SKYTEST.PRODUCT_INV_GIC_IDX3
    on SKYTEST.PRODUCT_INVEST_GIC (PRODUCT_TYPE)
/

alter table SKYTEST.PRODUCT_INVEST_GIC
    add constraint PRODUCT_INV_GIC_PK
        primary key (PRODUCT_INVEST_GIC_INT_ID)
/

create table SKYTEST.PRODUCT_MORTGAGE
(
    PRODUCT_MORTGAGE_INT_ID NUMBER(8) not null
        constraint PRODUCT_MORT_FK
            references SKYTEST.PRODUCT,
    PRODUCT_DESC_EN         VARCHAR2(100),
    PRODUCT_DESC_FR         VARCHAR2(100),
    LAST_UPDATED            DATE
)
/

create index SKYTEST.PRODUCT_MORTGAGE_IDX1
    on SKYTEST.PRODUCT_MORTGAGE (PRODUCT_MORTGAGE_INT_ID)
/

alter table SKYTEST.PRODUCT_MORTGAGE
    add constraint PRODUCT_MORT_PK
        primary key (PRODUCT_MORTGAGE_INT_ID)
/

create table SKYTEST.PRODUCT_POLICY_TRAVEL
(
    PRODUCT_POLICY_TRAVEL_INT_ID NUMBER(8) not null
        constraint PDODICT_POL_TRAVEL_FK
            references SKYTEST.PRODUCT,
    BENEFITID                    NUMBER(8),
    DISCOUNTPRICE                VARCHAR2(1),
    MAXINSURED                   NUMBER(10, 2),
    COVERAGETYPE                 NUMBER(2)
)
/

create index SKYTEST.PRODUCT_POLICY_TRAVEL_IDX1
    on SKYTEST.PRODUCT_POLICY_TRAVEL (PRODUCT_POLICY_TRAVEL_INT_ID)
/

alter table SKYTEST.PRODUCT_POLICY_TRAVEL
    add constraint PDODICT_POL_TRAVEL_PK
        primary key (PRODUCT_POLICY_TRAVEL_INT_ID)
/

create table SKYTEST.PROMOTIONS_PRODUCT
(
    PROMOTION_ID NUMBER(8) not null,
    PRODUCT_ID   NUMBER(8) not null
)
/

create table SKYTEST.PROVINCE_PROMOTION
(
    PROVINCE  VARCHAR2(6),
    PROMOTION NUMBER(8)
)
/

create table SKYTEST.NATIONAL_GEOGRAPHY
(
    NATIONAL_GEOGRAPHY_INT_ID NUMBER(8) not null
        primary key,
    NAME_EN                   VARCHAR2(255),
    NAME_FR                   VARCHAR2(255),
    CREATION_DATE             DATE
)
/

create table SKYTEST.MARKETING_REGION
(
    MARKETING_REGION_INT_ID NUMBER(8) not null
        primary key,
    NAME_EN                 VARCHAR2(255),
    NAME_FR                 VARCHAR2(255),
    CODE                    VARCHAR2(30),
    NATIONAL_GEOGRAPHYC     NUMBER(8)
        constraint NATIONAL_MARKETING
            references SKYTEST.NATIONAL_GEOGRAPHY,
    CREATION_DATE           DATE
)
/

create table SKYTEST.DISTRICT
(
    DISTRICT_INT_ID  NUMBER(8) not null
        constraint SYS_C0017930
            primary key,
    NAME_EN          VARCHAR2(255),
    NAME_FR          VARCHAR2(255),
    MARKETING_REGION NUMBER(8)
        constraint MARKETING_DISCTRIC
            references SKYTEST.MARKETING_REGION,
    NUMBER_CODE      NUMBER(8)
        constraint NUMBER_UNIQUE
            unique,
    CREATION_DATE    DATE
)
/

create table SKYTEST.FFLEX_CATEGORY_MONTHLY
(
    FUND_ENABLED                   VARCHAR2(1),
    FUND_FUNDATAKEY                NUMBER(11),
    FUND_NAME_EN                   VARCHAR2(250),
    FUND_NAME_FR                   VARCHAR2(250),
    PERF_ONE_DAY_RETURN            NUMBER(11, 6),
    TYPE_KEY                       NUMBER(11) not null
        primary key,
    TYPE_ENGLISH_NAME              VARCHAR2(64),
    TYPE_FRENCH_NAME               VARCHAR2(64),
    DAILY_PRICE_NAVPS              NUMBER(15, 6),
    PERF_ONE_MONTH_RETURN          NUMBER(11, 6),
    PERF_THREE_MONTH_RETURN        NUMBER(11, 6),
    PERF_YTD_RETURN                NUMBER(11, 6),
    FUND_RRSP_CODE                 VARCHAR2(12),
    FUND_OBJECTIVE_EN              VARCHAR2(500),
    FUND_OBJECTIVE_FR              VARCHAR2(500),
    BENCHMARK_YTD_RETURN           NUMBER(11, 6),
    FUND_TOTAL_ASSETS              NUMBER(11, 6),
    FUND_LOAD_EN                   VARCHAR2(255),
    FUND_LOAD_FR                   VARCHAR2(255),
    PRICE_CHANGE_FROM_PREVIOUS_DAY NUMBER(15, 6),
    PERF_FUND_GRADE                VARCHAR2(1),
    ONE_MONTH_QUARTILE             VARCHAR2(1),
    THREE_MONTH_QUARTILE           VARCHAR2(1),
    YR3_ANNUALIZED_STD_DEV         NUMBER(11, 6),
    YR3_VOLATILITY_RANKING         NUMBER(2),
    FUND_MER                       NUMBER(15, 6),
    DAILY_PRICE_NAVPS_DATE         DATE
)
/

create table SKYTEST.FFLEX_ORDER_MONTH
(
    FUND_ENABLED                   VARCHAR2(1),
    FUND_FUNDATAKEY                NUMBER(11) not null
        primary key,
    FUND_NAME_EN                   VARCHAR2(250),
    FUND_NAME_FR                   VARCHAR2(250),
    FD_TYPE                        NUMBER(11),
    PERF_ONE_DAY_RETURN            NUMBER(11, 6),
    DAILY_PRICE_NAVPS              NUMBER(15, 6),
    PERF_ONE_MONTH_RETURN          NUMBER(11, 6),
    PERF_THREE_MONTH_RETURN        NUMBER(11, 6),
    PERF_YTD_RETURN                NUMBER(11, 6),
    FUND_RRSP_CODE                 VARCHAR2(12),
    FUND_OBJECTIVE_EN              VARCHAR2(500),
    FUND_OBJECTIVE_FR              VARCHAR2(500),
    BENCHMARK_YTD_RETURN           NUMBER(11, 6),
    FUND_TOTAL_ASSETS              NUMBER(15, 6),
    FUND_LOAD_EN                   VARCHAR2(255),
    FUND_LOAD_FR                   VARCHAR2(255),
    PRICE_CHANGE_FROM_PREVIOUS_DAY NUMBER(15, 6),
    PERF_FUND_GRADE                VARCHAR2(1),
    ONE_MONTH_QUARTILE             VARCHAR2(1),
    THREE_MONTH_QUARTILE           VARCHAR2(1),
    YR3_ANNUALIZED_STD_DEV         NUMBER(11, 6),
    YR3_VOLATILITY_RANKING         NUMBER(2),
    FUND_MER                       NUMBER(15, 6),
    DAILY_PRICE_NAVPS_DATE         DATE
)
/

create table SKYTEST.INV_ORDER_STATUSES
(
    STATUS_INT_ID NUMBER(8) not null
        primary key,
    NAME_EN       VARCHAR2(100),
    NAME_FR       VARCHAR2(100)
)
/

create table SKYTEST.INV_TAX_RATES
(
    TAX_INT_ID   NUMBER(8) not null
        primary key,
    COUNTRY_CODE VARCHAR2(15),
    RATE         NUMBER(8)
)
/

create table SKYTEST.INV_PRODUCT_TYPES
(
    PRODUCT_TYPE_INT_ID NUMBER(8) not null
        primary key,
    TYPE_NAME_EN        VARCHAR2(150),
    TYPE_NAME_FR        VARCHAR2(150),
    SLUG                VARCHAR2(255)
)
/

create table SKYTEST.INV_PRODUCTS
(
    PRODUCT_INT_ID NUMBER(8) not null
        primary key,
    TYPE_FK        NUMBER(8)
        constraint INV_TYPE_FK_1
            references SKYTEST.INV_PRODUCT_TYPES,
    NAME_EN        VARCHAR2(150),
    NAME_FR        VARCHAR2(150),
    PRICE          NUMBER(12),
    CREATE_DATE    DATE,
    UPDATE_DATE    DATE
)
/

create table SKYTEST.INV_COUPON_TYPES
(
    TYPE_INT_ID NUMBER(8) not null
        primary key,
    NAME_EN     VARCHAR2(150),
    NAME_FR     VARCHAR2(150)
)
/

create table SKYTEST.INV_COUPONS
(
    COUPON_INT_ID NUMBER(8) not null
        primary key,
    TYPE_FK       NUMBER(8)
        constraint INV_TYPE_FK
            references SKYTEST.INV_COUPON_TYPES,
    CODE          VARCHAR2(100),
    VALUE         NUMBER(10)
)
/

create table SKYTEST.DATABASECHANGELOGLOCK
(
    ID          NUMBER    not null
        constraint PK_DATABASECHANGELOGLOCK
            primary key,
    LOCKED      NUMBER(1) not null,
    LOCKGRANTED TIMESTAMP(6),
    LOCKEDBY    VARCHAR2(255)
)
/

create table SKYTEST.DATABASECHANGELOG
(
    ID            VARCHAR2(255) not null,
    AUTHOR        VARCHAR2(255) not null,
    FILENAME      VARCHAR2(255) not null,
    DATEEXECUTED  TIMESTAMP(6)  not null,
    ORDEREXECUTED NUMBER        not null,
    EXECTYPE      VARCHAR2(10)  not null,
    MD5SUM        VARCHAR2(35),
    DESCRIPTION   VARCHAR2(255),
    COMMENTS      VARCHAR2(255),
    TAG           VARCHAR2(255),
    LIQUIBASE     VARCHAR2(20),
    CONTEXTS      VARCHAR2(255),
    LABELS        VARCHAR2(255),
    DEPLOYMENT_ID VARCHAR2(10)
)
/

create table SKYTEST.JHI_USER
(
    ID                 VARCHAR2(100) not null
        constraint PK_JHI_USER
            primary key,
    LOGIN              VARCHAR2(50)  not null
        constraint UX_USER_LOGIN
            unique,
    FIRST_NAME         VARCHAR2(50),
    LAST_NAME          VARCHAR2(50),
    EMAIL              VARCHAR2(191)
        constraint UX_USER_EMAIL
            unique,
    IMAGE_URL          VARCHAR2(256),
    ACTIVATED          NUMBER(1)     not null,
    LANG_KEY           VARCHAR2(10),
    CREATED_BY         VARCHAR2(50)  not null,
    CREATED_DATE       TIMESTAMP(6),
    LAST_MODIFIED_BY   VARCHAR2(50),
    LAST_MODIFIED_DATE TIMESTAMP(6)
)
/

create table SKYTEST.JHI_AUTHORITY
(
    NAME VARCHAR2(50) not null
        constraint PK_JHI_AUTHORITY
            primary key
)
/

create table SKYTEST.JHI_USER_AUTHORITY
(
    USER_ID        VARCHAR2(100) not null
        constraint FK_USER_ID
            references SKYTEST.JHI_USER,
    AUTHORITY_NAME VARCHAR2(50)  not null
        constraint FK_AUTHORITY_NAME
            references SKYTEST.JHI_AUTHORITY,
    primary key (USER_ID, AUTHORITY_NAME)
)
/

create table SKYTEST.GROUPS
(
    GROUP_INT_ID     NUMBER(8) not null
        primary key,
    GROUP_NAME       VARCHAR2(150),
    MASTER_GROUP     NUMBER(8)
        constraint GROUP_GROUP_FK
            references SKYTEST.GROUPS,
    OWNER            NUMBER(8),
    SPECIAL_DISCOUNT NUMBER(8)
)
/

comment on column SKYTEST.GROUPS.MASTER_GROUP is 'FOR THE TREE WITH ITSELFT'
/

comment on column SKYTEST.GROUPS.OWNER is 'OWNER OF THE GROUP(AGENCY OR COMPANY)'
/

create table SKYTEST.STORED_FILE_DESC
(
    STORED_FILE_DESC_INT_ID NUMBER(8) not null
        constraint SYS_C0021839
            primary key
        constraint SYS_C0021836
            check ("STORED_FILE_DESC_INT_ID" IS NOT NULL),
    FILE_EN                 BLOB,
    FILE_FR                 BLOB
)
/

create table SKYTEST.ACORD_TYPE_CODES
(
    TYPE_CODES_INT_ID NUMBER(12) not null
        primary key,
    DESCRIPTION_ENG   VARCHAR2(150),
    NAME              VARCHAR2(150),
    DESCRIPTION_FRE   VARCHAR2(150)
)
/

create table SKYTEST.ACORD_CODES
(
    CODES_INT_ID    NUMBER(12) not null
        primary key,
    TYPE_CODES      NUMBER(12)
        constraint TYPE_CODES_FK
            references SKYTEST.ACORD_TYPE_CODES,
    DESCRIPTION_ENG VARCHAR2(500),
    NAME            VARCHAR2(255),
    VALUE           VARCHAR2(30),
    DEFINITION      VARCHAR2(2500),
    NOTES           VARCHAR2(2500),
    DESCRIPTION_FRE VARCHAR2(500)
)
/

comment on column SKYTEST.ACORD_CODES.TYPE_CODES is 'FK to TYPE_CODES'
/

create table SKYTEST.WEB_CONTENT_LONG
(
    WEB_CONTENT_LONG_INT_ID NUMBER(8) not null
        primary key,
    WEB_CONTENT             NUMBER(8)
        constraint WEBCONTENT_LONG_FK
            references SKYTEST.WEB_CONTENT,
    LANGUAJE                NUMBER(2),
    CONTENT                 LONG
)
/

comment on column SKYTEST.WEB_CONTENT_LONG.LANGUAJE is '9----- english
12---- french'
/

create table SKYTEST.CAROUSEL_INFO
(
    CAROUSEL_INFO_INT_ID NUMBER(8) not null
        constraint SYS_C0027867
            primary key,
    TYPE                 NUMBER(1),
    TITLE_EN             VARCHAR2(100),
    TITLE_FR             VARCHAR2(100),
    DESCRIPTION_1        VARCHAR2(500),
    DESCRIPTION_2        VARCHAR2(500),
    IMAGE                BLOB,
    IMAGE_NAME           VARCHAR2(255)
)
/

-- Cyclic dependencies found

alter table SKYTEST.ACCOUNT
    add constraint ACCOUNT_PK
        primary key (ACCOUNT_INT_ID)
/

-- Cyclic dependencies found

create index SKYTEST.ACCOUNT_IDX1
    on SKYTEST.ACCOUNT (ACCOUNT_INT_ID)
/

-- Cyclic dependencies found

create index SKYTEST.ACCOUNT_IDX4
    on SKYTEST.ACCOUNT (ACCOUNT_NUMBER, MGMT_COMPANY_CODE, DEALER_CODE, SALESREP_CODE)
/

-- Cyclic dependencies found

create index SKYTEST.ACCOUNT_IDX3
    on SKYTEST.ACCOUNT (ACCOUNT_NUMBER, MGMT_COMPANY_CODE)
/

-- Cyclic dependencies found

create index SKYTEST.ACCOUNT_IDX2
    on SKYTEST.ACCOUNT (ACCOUNT_TYPE)
/

-- Cyclic dependencies found

create table SKYTEST.ACCOUNT_BENEFICIARY
(
    ACCOUNT_BENEFICIARY_INT_ID NUMBER(9) not null
        constraint ACCOUNT_BENEFICIARY_PK
            primary key,
    ACCOUNT                    NUMBER(9) not null
        constraint ACCOUNT_BENEFICIARY_ACC_FK1
            references SKYTEST.ACCOUNT,
    NAME                       VARCHAR2(120),
    SIN                        VARCHAR2(9),
    IRREVOCABLE                VARCHAR2(1),
    RELATIONSHIP               NUMBER(4),
    PRIMARY_CONTINGENT         VARCHAR2(1),
    GENDER                     NUMBER(4),
    BIRTH_DATE                 DATE,
    PERCENTAGE                 NUMBER(7, 4),
    LEVEL_SEQ                  NUMBER(2),
    ADDRESS_LINE1              VARCHAR2(128),
    ADDRESS_LINE2              VARCHAR2(128),
    CITY                       VARCHAR2(64),
    PROVINCE                   VARCHAR2(64),
    COUNTRY                    VARCHAR2(64),
    POSTAL_CODE                VARCHAR2(20),
    HOUSE_HOLD_ID              NUMBER(9),
    HOUSE_HOLD_PRIMARY         VARCHAR2(1),
    MARITAL_STATUS             NUMBER(4),
    PREFERRED_LANGUAGE         NUMBER(4),
    USE_HOUSE_HOLD_PRIMARY     VARCHAR2(1),
    LAST_UPDATE_USER           VARCHAR2(64),
    CREATION_DATE              DATE,
    LAST_MODIFICATION_DATE     DATE
)
/

-- Cyclic dependencies found

create table SKYTEST.ACCOUNT_FOLLOW_UP
(
    ACCOUNT_FOLLOW_UP_INT_ID  NUMBER(9) not null
        constraint ACCOUNT_FOLLOW_UP_PK
            primary key,
    ACCOUNT                   NUMBER(8) not null
        constraint ACCOUNT_FOLLOW_UP_ACCOUNT_FK1
            references SKYTEST.ACCOUNT,
    TYPES                     NUMBER(8)
        constraint ACCOUNT_FOLLOW_UP_TYPES_FK1
            references SKYTEST.TYPES,
    UNDERWRITTING_REQUIREMENT NUMBER(8)
        constraint ACCOUNT_FOLLOW_UP_UNDERWR_FK1
            references SKYTEST.UNDERWRITING_REQUIREMENT,
    CREATION_DATE             DATE,
    LAST_MODIFICATION_DATE    DATE,
    LAST_UPDATED_USER         VARCHAR2(64),
    LAST_UPDATED_DATE         DATE,
    FOLLOW_UP_DAY             NUMBER(4)
)
/

-- Cyclic dependencies found

alter table SKYTEST.ACCOUNT_MANAGER
    add constraint BROKERAGEMANAGER_PK
        primary key (ACCOUNT_MANAGER_INT_ID)
/

-- Cyclic dependencies found

create index SKYTEST.ACCOUNT_MANAGER_IDX1
    on SKYTEST.ACCOUNT_MANAGER (ACCOUNT_MANAGER_INT_ID)
/

-- Cyclic dependencies found

create table SKYTEST.ADDRESS
(
    ADDRESS_INT_ID         NUMBER(8)    not null,
    ADDRESS_LINE1          VARCHAR2(40) not null,
    ADDRESS_LINE2          VARCHAR2(40),
    ADDRESS_LINE3          VARCHAR2(40),
    CITY                   VARCHAR2(40),
    PROVINCE               VARCHAR2(5)  not null
        constraint ADDRESS_PROVINCE_FK
            references SKYTEST.PROVINCE,
    COUNTRY                VARCHAR2(3)  not null
        constraint ADDRESS_COUNTRY_FK
            references SKYTEST.COUNTRY,
    POSTAL_CODE            VARCHAR2(10) not null,
    IS_PRIMARY             VARCHAR2(1),
    TYPE                   NUMBER(8),
    CARE_OF                VARCHAR2(254),
    SEND_SOLICITATION      VARCHAR2(1),
    LATITUDE               VARCHAR2(64),
    LONGITUDE              VARCHAR2(64),
    OWNER_INT_ID           NUMBER(8),
    SHAREABLE              VARCHAR2(1),
    OWNER_TYPE             NUMBER(4),
    SEND_TYPE              NUMBER(4),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    LAST_UPDATE_USER       VARCHAR2(64),
    CARE_OF_OTHER_NAME     VARCHAR2(254),
    ONBOARDING             NUMBER(10)
        constraint ADDRESS_ONBOARDING_FK
            references SKYTEST.ONBOARDING_STATUS
)
/

create index SKYTEST.ADDRESS_IDX5
    on SKYTEST.ADDRESS (POSTAL_CODE)
/

create unique index SKYTEST.ADDRESS_IDX1
    on SKYTEST.ADDRESS (ADDRESS_INT_ID)
/

create index SKYTEST.ADDRESS_IDX2
    on SKYTEST.ADDRESS (ADDRESS_LINE1, ADDRESS_LINE2, ADDRESS_LINE3, POSTAL_CODE)
/

create index SKYTEST.ADDRESS_IDX3
    on SKYTEST.ADDRESS (PROVINCE)
/

create index SKYTEST.ADDRESS_IDX4
    on SKYTEST.ADDRESS (COUNTRY)
/

alter table SKYTEST.ADDRESS
    add constraint CLIENTADDRESS_PK
        primary key (ADDRESS_INT_ID)
/

-- Cyclic dependencies found

alter table SKYTEST.ADVISOR
    add constraint ADVISOR_PK
        primary key (ADVISOR_INT_ID)
/

-- Cyclic dependencies found

create index SKYTEST.ADVISOR_IDX1
    on SKYTEST.ADVISOR (ADVISOR_INT_ID)
/

-- Cyclic dependencies found

create index SKYTEST.ADVISOR_IDX4
    on SKYTEST.ADVISOR (ADVISOR_ID)
/

-- Cyclic dependencies found

create index SKYTEST.INDEX4
    on SKYTEST.ADVISOR (NAME)
/

-- Cyclic dependencies found

create index SKYTEST.INDEX12
    on SKYTEST.ADVISOR (SUNLIFE_ADVISOR)
/

-- Cyclic dependencies found

create index SKYTEST.INDEX5
    on SKYTEST.ADVISOR (MASTER_GROUP, NAME)
/

-- Cyclic dependencies found

create index SKYTEST.INDEX15
    on SKYTEST.ADVISOR_BUSINESS_STAT (CREATION_DATE, ADVISOR, PRODUCT_CLASS, PRODUCT_SUPPLIER, STATUS)
/

-- Cyclic dependencies found

create table SKYTEST.ADVISOR_MARKET_PLACE
(
    MARKET_PLACE_INT_ID NUMBER(10) not null
        primary key,
    CATEGORY            VARCHAR2(64),
    CLASS_NAME          VARCHAR2(64),
    TYPES_ID            NUMBER(10),
    ADVISOR             NUMBER(10) not null
        constraint ADVISOR_MARKET_PLACE_ADVISOR
            references SKYTEST.ADVISOR
)
/

-- Cyclic dependencies found

create table SKYTEST.ADVISOR_MASTER_GROUP
(
    ADVISOR      NUMBER(8)
        constraint ADVISOR_FK
            references SKYTEST.ADVISOR,
    MASTER_GROUP NUMBER(8)
        constraint MASTER_GROUP_FK
            references SKYTEST.MASTER_GROUP,
    MAIN         VARCHAR2(2)
)
/

-- Cyclic dependencies found

create table SKYTEST.ADVISOR_NOTES
(
    ADVISOR_NOTES_INT_ID NUMBER(8) not null,
    ADVISOR              NUMBER(8) not null
        constraint ADVISOR_NOTES_ADVISOR_FK
            references SKYTEST.ADVISOR,
    TITLE                VARCHAR2(80),
    IS_PRIVATE           VARCHAR2(1),
    LAST_UPDATE_DATE     DATE,
    LAST_UPDATE_USER     VARCHAR2(30),
    CREATED_DATE_TIME    DATE,
    CREATE_USER          VARCHAR2(30),
    NOTE                 VARCHAR2(2000)
)
/

create index SKYTEST.ADVISOR_NOTES_IDX1
    on SKYTEST.ADVISOR_NOTES (ADVISOR_NOTES_INT_ID)
/

create index SKYTEST.ADVISOR_NOTES_IDX2
    on SKYTEST.ADVISOR_NOTES (ADVISOR)
/

alter table SKYTEST.ADVISOR_NOTES
    add constraint ADVISOR_NOTES_PK
        primary key (ADVISOR_NOTES_INT_ID)
/

-- Cyclic dependencies found

create table SKYTEST.ADVISOR_PROFILE
(
    ADVISOR_PROFILE_INT_ID NUMBER(8) not null
        constraint ADVISOR_PROFILE_PK
            primary key,
    ADVISOR                NUMBER(8) not null
        constraint ADVISOR_PROFILE_FK1
            references SKYTEST.ADVISOR,
    PROFILE_NAME           VARCHAR2(20),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    MASTER_CODE            VARCHAR2(4)
)
/

-- Cyclic dependencies found

create table SKYTEST.AGA_ADVISORS
(
    AGA_ADVISORS_INT_ID    NUMBER(8) not null
        constraint AGA_ADVISORS_PK
            primary key,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    LAST_UPDATE_USERS      VARCHAR2(64),
    SHARE_LIABILITIES      VARCHAR2(1),
    SHARE_ADDRESS          VARCHAR2(1),
    AGA                    NUMBER(8)
        constraint AGA_ADVISORS_FK1
            references SKYTEST.ADVISOR,
    ADVISOR                NUMBER(8)
        constraint AGA_ADVISORS_FK2
            references SKYTEST.ADVISOR
)
/

-- Cyclic dependencies found

alter table SKYTEST.AGENCY
    add constraint AGENCY_PK
        primary key (AGENCY_INT_ID)
/

-- Cyclic dependencies found

create index SKYTEST.AGENCY_IDX1
    on SKYTEST.AGENCY (AGENCY_INT_ID)
/

-- Cyclic dependencies found

create index SKYTEST.INDEX7
    on SKYTEST.AGENCY (MASTER_GROUP, ORGANIZATION)
/

-- Cyclic dependencies found

create index SKYTEST.INDEX6
    on SKYTEST.AGENCY (MASTER_GROUP)
/

-- Cyclic dependencies found

create index SKYTEST.INDEX10
    on SKYTEST.AGENCY_ADVISOR (AGENCY)
/

-- Cyclic dependencies found

create table SKYTEST.AGENCY_FORMS
(
    AGENCY_FORMS_INT_ID NUMBER(11)  not null
        constraint AGENCY_FORMS_PK
            primary key,
    AGENCY              NUMBER(11)  not null
        constraint AGENCY_FORMS_FK1
            references SKYTEST.AGENCY,
    DASHBOARD_TYPE      NUMBER(4)   not null,
    FORM_TYPE           NUMBER(4)   not null,
    FORM_TITLE_ENG      VARCHAR2(100),
    FORM_TITLE_FRE      VARCHAR2(100),
    FORM_ENG            BLOB        not null,
    FORM_FRE            BLOB        not null,
    WEBSITE_TYPE        VARCHAR2(2) not null,
    START_DATE          DATE,
    END_DATE            DATE
)
/

create index SKYTEST.INDEX16
    on SKYTEST.AGENCY_FORMS (AGENCY, DASHBOARD_TYPE, WEBSITE_TYPE)
/

create index SKYTEST.INDEX17
    on SKYTEST.AGENCY_FORMS (AGENCY)
/

create index SKYTEST.INDEX18
    on SKYTEST.AGENCY_FORMS (DASHBOARD_TYPE)
/

create index SKYTEST.INDEX19
    on SKYTEST.AGENCY_FORMS (WEBSITE_TYPE)
/

-- Cyclic dependencies found

create table SKYTEST.AGREEMENT_REQUEST
(
    AGREEMENT_REQUEST_INT_ID NUMBER(8)   not null,
    ADVISOR                  NUMBER(8)   not null
        constraint AGREEMENT_REQUEST_ADVISOR_FK
            references SKYTEST.ADVISOR,
    REQUEST_DATE             DATE,
    PROVINCE                 VARCHAR2(2) not null
        constraint AGREEMENT_REQUEST_PROVINCE_FK
            references SKYTEST.PROVINCE,
    LICENCE_TYPE             NUMBER(8),
    AGREEMENT_TYPE           NUMBER(3),
    REQUEST_USER             VARCHAR2(30),
    COMMENTS                 VARCHAR2(2000),
    DOCUMENT_ID              NUMBER(8),
    SENT_DATE                DATE,
    DOCUMENT_NUMBER          VARCHAR2(20),
    REQUEST_REASON           NUMBER(3),
    SENT_BY                  VARCHAR2(1)
)
/

create index SKYTEST.AGREEMENT_REQUEST_IDX1
    on SKYTEST.AGREEMENT_REQUEST (AGREEMENT_REQUEST_INT_ID)
/

create index SKYTEST.AGREEMENT_REQUEST_IDX2
    on SKYTEST.AGREEMENT_REQUEST (ADVISOR)
/

alter table SKYTEST.AGREEMENT_REQUEST
    add constraint AGREEMENT_REQUEST_PK
        primary key (AGREEMENT_REQUEST_INT_ID)
/

-- Cyclic dependencies found

create table SKYTEST.ANNUITIY
(
    ANNUITIY_INT_ID        NUMBER(8) not null,
    ACCOUNT                NUMBER(8) not null
        constraint ANNUITIY_ACCOUNT_FK
            references SKYTEST.ACCOUNT,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE      not null,
    PRODUCT                NUMBER(8)
        constraint ANNUITIY_PRODUCT_FK
            references SKYTEST.PRODUCT
)
/

create index SKYTEST.ANNUITIY_IDX1
    on SKYTEST.ANNUITIY (ACCOUNT)
/

create index SKYTEST.ANNUITIY_IDX2
    on SKYTEST.ANNUITIY (ANNUITIY_INT_ID)
/

alter table SKYTEST.ANNUITIY
    add constraint ANNUITIES_PK
        primary key (ANNUITIY_INT_ID)
/

create table SKYTEST.ANNUITY_TRANSACTION
(
    ANNUITY_TRANSACTION_INT_ID NUMBER(8)     not null,
    ANNUITY                    NUMBER(8)     not null
        constraint ANNUITY_TRANSACTION_FK
            references SKYTEST.ANNUITIY,
    INVEST_TRANSACTION_TYPE    NUMBER(8)     not null
        constraint ANNUITY_TRANSACTION_TYPE_FK
            references SKYTEST.INVEST_TRANS_TYPE,
    MGMT_COMPANY_CODE          VARCHAR2(3),
    ACCOUNT_NUMBER             VARCHAR2(15),
    DESCRIPTION                VARCHAR2(200),
    TRANSACTION_TYPE_DETAIL    VARCHAR2(1),
    TRANS_UNITS                NUMBER(13, 4) not null,
    UNIT_PRICE                 NUMBER(7, 4),
    GROSS_AMOUNT               NUMBER(11, 2) not null,
    NET_AMOUNT                 NUMBER(11, 2) not null,
    TOTAL_ASSIGNED             NUMBER(13, 4) not null,
    TOTAL_UNASSIGNED           NUMBER(13, 4) not null,
    SETTLEMENT_STATUS          VARCHAR2(1),
    SETTLEMENT_DATE            DATE          not null,
    TRADE_DATE                 DATE          not null,
    CREATION_DATE              DATE          not null,
    TRANSACTION_DATE           DATE          not null,
    SEQUENCE_NUMBER            NUMBER(5),
    BENEFICIAL_OWNER_LASTNAME  VARCHAR2(20),
    BENEFICIAL_OWNER_FIRSTNAME VARCHAR2(20)
)
/

create index SKYTEST.ANNUITY_TRANSACTION_IDX3
    on SKYTEST.ANNUITY_TRANSACTION (INVEST_TRANSACTION_TYPE)
/

create index SKYTEST.ANNUITY_TRANSACTION_IDX1
    on SKYTEST.ANNUITY_TRANSACTION (ANNUITY_TRANSACTION_INT_ID)
/

create index SKYTEST.ANNUITY_TRANSACTION_IDX2
    on SKYTEST.ANNUITY_TRANSACTION (ANNUITY)
/

alter table SKYTEST.ANNUITY_TRANSACTION
    add constraint ANNUITY_TRANSACTION_PK
        primary key (ANNUITY_TRANSACTION_INT_ID)
/

create table SKYTEST.ANNUITY_TRANS_DETAILS
(
    ANNUITY_TRANS_DETAIL_INT_ID NUMBER(8) not null,
    ANNUITY_TRANSACTION         NUMBER(8) not null
        constraint ANNUITY_TRANS_DETAILS_FK
            references SKYTEST.ANNUITY_TRANSACTION,
    TRANSACTION_TYPE_DETAIL     VARCHAR2(1),
    RAW_RECORD                  VARCHAR2(700)
)
/

create index SKYTEST.ANNUITY_TRANS_DETAILS_IDX1
    on SKYTEST.ANNUITY_TRANS_DETAILS (ANNUITY_TRANSACTION)
/

create index SKYTEST.ANNUITY_TRANS_DETAILS_IDX2
    on SKYTEST.ANNUITY_TRANS_DETAILS (ANNUITY_TRANS_DETAIL_INT_ID)
/

alter table SKYTEST.ANNUITY_TRANS_DETAILS
    add constraint ANNUITY_TRANS_DET_PK
        primary key (ANNUITY_TRANS_DETAIL_INT_ID)
/

-- Cyclic dependencies found

create trigger SKYTEST.TRG_CALENDAR_EVENT_UPDATE
    before update
    on SKYTEST.CALENDAR_EVENT
    for each row
BEGIN
    :NEW.UPDATED_AT := CURRENT_TIMESTAMP;
END;
/

-- Cyclic dependencies found

alter table SKYTEST.CAMPAIGN
    add constraint CAMPAIGN_PK
        primary key (CAMPAIGN_INT_ID)
/

-- Cyclic dependencies found

create unique index SKYTEST.CAMPAIGN_PK1
    on SKYTEST.CAMPAIGN (CAMPAIGN_INT_ID)
/

-- Cyclic dependencies found

alter table SKYTEST.CLIENT
    add constraint CLIENT_PK
        primary key (CLIENT_INT_ID)
/

-- Cyclic dependencies found

create unique index SKYTEST.CLIENT_IDX1
    on SKYTEST.CLIENT (CLIENT_INT_ID)
/

-- Cyclic dependencies found

create index SKYTEST.CLIENT_IDX2
    on SKYTEST.CLIENT (CLIENT_NUMBER)
/

-- Cyclic dependencies found

create index SKYTEST.CLIENT_IDX3
    on SKYTEST.CLIENT (SIN)
/

-- Cyclic dependencies found

create index SKYTEST.CLIENT_IDX4
    on SKYTEST.CLIENT (CORPORATE_NAME)
/

-- Cyclic dependencies found

create index SKYTEST.INDEX3
    on SKYTEST.CLIENT (MASTER_CODE)
/

-- Cyclic dependencies found

create index SKYTEST.CLIENT_POLICY_IDX1
    on SKYTEST.CLIENT_POLICY (POLICY)
/

-- Cyclic dependencies found

create index SKYTEST.CLIENT_POLICY_IDX2
    on SKYTEST.CLIENT_POLICY (CLIENT)
/

-- Cyclic dependencies found

create index SKYTEST.INDEX8
    on SKYTEST.COMPANY (ADVISOR)
/

-- Cyclic dependencies found

create index SKYTEST.INDEX9
    on SKYTEST.COMPANY (AGENCY)
/

-- Cyclic dependencies found

alter table SKYTEST.CONTACT
    add constraint CONTACT_PK
        primary key (CONTACT_INT_ID)
/

-- Cyclic dependencies found

create table SKYTEST.ABOUT_ME
(
    ABOUT_ME_INT_ID        NUMBER(8) not null
        constraint ABOUT_ME_PK
            primary key
        constraint ABOUT_ME_CONTACT_FK
            references SKYTEST.CONTACT,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    ABOUT_ME_ENGLISH       VARCHAR2(3000),
    ABOUT_ME_FRENCH        VARCHAR2(3000),
    IMAGE                  BLOB,
    IMAGE_NAME             VARCHAR2(128),
    SPECIALTY_EN           VARCHAR2(255),
    WHO_I_WORK_WITH_EN     VARCHAR2(255),
    SPECIALTY_FR           VARCHAR2(255),
    WHO_I_WORK_WITH_FR     VARCHAR2(255)
)
/

comment on column SKYTEST.ABOUT_ME.ABOUT_ME_INT_ID is 'one to one relationship with contact'
/

-- Cyclic dependencies found

create table SKYTEST.ACCOUNT_MANAGER
(
    ACCOUNT_MANAGER_INT_ID  NUMBER(8) not null,
    CONTACT                 NUMBER(8)
        constraint ACCOUNT_MANAGER_CONTACT_FK
            references SKYTEST.CONTACT,
    CREATION_DATE           DATE,
    LAST_MODIFICATION_DATE  DATE,
    NOTE                    VARCHAR2(1024),
    ABBR                    VARCHAR2(20),
    BROKER_ID               NUMBER(8),
    GLOBAL_LIFE_COMM_RATE   NUMBER(5, 2),
    GLOBAL_INVEST_COMM_RATE NUMBER(5, 2)
)
/

-- Cyclic dependencies found

create table SKYTEST.CLIENT
(
    CLIENT_INT_ID          NUMBER(8) not null
        constraint CLIENT_CONTACT_FK
            references SKYTEST.CONTACT,
    CLIENT_NUMBER          NUMBER(8) not null,
    SIN                    VARCHAR2(11),
    CLIENT_TYPE            NUMBER(4),
    CORPORATE_NAME         VARCHAR2(256),
    DRIVERS_LICENSE        VARCHAR2(30),
    NETWORTH               NUMBER(19, 4),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    HOUSEHOLD              NUMBER(4),
    RELATIONSHIP           NUMBER(4),
    NOTEID                 NUMBER(8),
    MEMO                   VARCHAR2(1024),
    PASSPORT_NUMBER        VARCHAR2(30),
    MASTER_CODE            VARCHAR2(16),
    LEAD_SOURCE            VARCHAR2(128),
    CLIENT_STATUS          NUMBER(4),
    FED_BN                 VARCHAR2(256),
    PROV_BN                VARCHAR2(256),
    OLD_CLIENT_ID          NUMBER(11),
    LAST_UPDATE_USER       VARCHAR2(64),
    SMOKER                 VARCHAR2(1)
)
/

-- Cyclic dependencies found

create table SKYTEST.ADVISOR_CLIENT
(
    ADVISOR NUMBER(8) not null
        constraint ADVISOR_CLIENT_ADVISOR_FK1
            references SKYTEST.ADVISOR,
    CLIENT  NUMBER(8) not null
        constraint ADVISOR_CLIENT_CLIENT_FK1
            references SKYTEST.CLIENT,
    constraint ADVISOR_CLIENT_PK
        primary key (ADVISOR, CLIENT)
)
/

-- Cyclic dependencies found

create index SKYTEST.CONTACT_IDX1
    on SKYTEST.CONTACT (CONTACT_INT_ID)
/

-- Cyclic dependencies found

create index SKYTEST.CONTACT_IDX3
    on SKYTEST.CONTACT (FIRSTNAME, MIDDLENAME, LASTNAME)
/

-- Cyclic dependencies found

create index SKYTEST.CONTACT_IDX2
    on SKYTEST.CONTACT (LASTNAME, FIRSTNAME)
/

-- Cyclic dependencies found

create index SKYTEST.CONTACT_IDX4
    on SKYTEST.CONTACT (BIRTH_DATE)
/

-- Cyclic dependencies found

create index SKYTEST.INDEX1
    on SKYTEST.CONTACT (MASTER_CODE, FIRSTNAME, LASTNAME)
/

-- Cyclic dependencies found

create index SKYTEST.CONTACT_MASTER_CODE_IDX5
    on SKYTEST.CONTACT (MASTER_CODE)
/

-- Cyclic dependencies found

create table SKYTEST.CONTACT_ACTION
(
    CONTACT_ACTION_INT_ID  NUMBER(8) not null
        constraint CONTACT_ACTION_PK
            primary key,
    CONTACT_ACTION_TYPE    NUMBER(8) not null,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    NOTE                   VARCHAR2(256),
    PROFILE                NUMBER(8)
        constraint CONTACT_ACTION_PROFILE_FK
            references SKYTEST.PROFILE,
    NOTE_FR                VARCHAR2(255)
)
/

comment on column SKYTEST.CONTACT_ACTION.CONTACT_ACTION_TYPE is '0 ----- Documents upload permission
1-6 --- ni idea(please fill)
7 ----- Dashboard upload permission
8 ----- WebTool Page edit
9------Upload declines
10 ---- Manage Declined 
11-----Tools Masters
12-----Request Proof of License
13-----Receive Advisor Documents
14----- MGA/AGA Profile Management'
/

create table SKYTEST.CONTACT_ACTION_TYPES
(
    CONTACT_ACTION_ID NUMBER(8) not null
        constraint CONTACT_ACTION_IMAGETYPES_FK1
            references SKYTEST.CONTACT_ACTION,
    TYPES_INT_ID      NUMBER(8) not null
        constraint CONTACT_ACTION_IMAGETYPES_FK2
            references SKYTEST.TYPES
)
/

-- Cyclic dependencies found

create table SKYTEST.CONTACT_CONTACT_ACTION
(
    CONTACT_ID        NUMBER(8) not null
        constraint CONTACT_CONTACT_ACTION_CO_FK1
            references SKYTEST.CONTACT,
    CONTACT_ACTION_ID NUMBER(8) not null
        constraint CONTACT_CONTACT_ACTION_CO_FK2
            references SKYTEST.CONTACT_ACTION
)
/

-- Cyclic dependencies found

create table SKYTEST.CONTACT_DEFAULT_EMAILS
(
    CONTACT        NUMBER(8)
        constraint CONTACT_FR
            references SKYTEST.CONTACT,
    DEFAULT_EMAILS NUMBER(8)
        constraint DEFAULT_EMAIL_FK
            references SKYTEST.DEFAULT_EMAILS
)
/

-- Cyclic dependencies found

create table SKYTEST.CONTACTS_RELATIONSHIP
(
    CONTACTS_RELATIONSHIP_INT_ID NUMBER(11)  not null
        constraint CONTACTS_RELATIONSHIP_PK
            primary key,
    CREATION_DATE                DATE,
    LAST_MODIFICATION_DATE       DATE,
    TYPE                         VARCHAR2(1) not null,
    FIRST_CONTACT_INT_ID         NUMBER(11),
    SECOND_CONTACT_INT_ID        NUMBER(11),
    THIRD_CONTACT_INT_ID         NUMBER(11),
    FORTH_CONTACT_INT_ID         NUMBER(11),
    FIFTH_CONTACT_INT_ID         NUMBER(11),
    SIXTH_CONTACT_INT_ID         NUMBER(11),
    SEVENTH_CONTACT_INT_ID       NUMBER(11),
    EIGHTH_CONTACT_INT_ID        NUMBER(11),
    NINETH_CONTACT_INT_ID        NUMBER(11),
    TENTH_CONTACT_INT_ID         NUMBER(11),
    ADVISOR                      NUMBER(11)
        constraint CONTACTS_RELATIONSHIP_FK1
            references SKYTEST.ADVISOR
)
/

comment on column SKYTEST.CONTACTS_RELATIONSHIP.TYPE is 'S= SINGLE, C=COUPLE, H-HOUSEHOLD, B=BUSINESS,O=OWNERSHIP,P=BENIFICIARY'
/

-- Cyclic dependencies found

create index SKYTEST.CONTRACT_SETUP_IDX2
    on SKYTEST.CONTRACT_SETUP (PRODUCT_SUPPLIER)
/

-- Cyclic dependencies found

create table SKYTEST.DOOR
(
    DOOR_INT_ID            NUMBER(8) not null
        primary key,
    BUILDING_ID            NUMBER(8)
        constraint BUILDING_FK
            references SKYTEST.COMPANY,
    SIZE_INT               NUMBER(10, 4),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    LOT_NUMBER             VARCHAR2(40),
    NO_FRACTION            NUMBER(6),
    COMMON_PARTS_BY_1000   NUMBER(10, 4),
    NO_CADASTRAL           NUMBER(10),
    PROPERTY_TYPE          NUMBER(4),
    FREE                   VARCHAR2(2),
    UNIT_CODE              VARCHAR2(40)
)
/

comment on column SKYTEST.DOOR.BUILDING_ID is 'this is fk to the building(company)'
/

-- Cyclic dependencies found

create table SKYTEST.DOOR_CONTACT
(
    CONTACT NUMBER(8)
        constraint CONTACTDOOR_FK
            references SKYTEST.CONTACT,
    DOOR    NUMBER(8)
        constraint DOORCONTACT_FK
            references SKYTEST.DOOR,
    TYPE    NUMBER(8)
)
/

-- Cyclic dependencies found

create table SKYTEST.EMAIL
(
    EMAIL_INT_ID           NUMBER(8) not null,
    EMAIL_ADDRESS          VARCHAR2(100),
    IS_PRIMARY             VARCHAR2(1),
    TYPE                   NUMBER(8),
    SEND_SOLICITATION      VARCHAR2(1),
    OWNER_INT_ID           NUMBER(8),
    OWNER_TYPE             NUMBER(4),
    SHAREABLE              VARCHAR2(1),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    LAST_UPDATE_USER       VARCHAR2(64),
    FLAG                   VARCHAR2(1),
    ONBOARDING             NUMBER(10)
        constraint EMAIL_ONBOARDING_FK
            references SKYTEST.ONBOARDING_STATUS
)
/

create index SKYTEST.EMAIL_IDX1
    on SKYTEST.EMAIL (EMAIL_INT_ID)
/

create index SKYTEST.EMAIL_IDX2
    on SKYTEST.EMAIL (EMAIL_ADDRESS)
/

alter table SKYTEST.EMAIL
    add constraint EMAIL_PK
        primary key (EMAIL_INT_ID)
/

-- Cyclic dependencies found

alter table SKYTEST.EMPLOYEE
    add constraint EMPLOYEE_PK
        primary key (EMPLOYEE_INT_ID)
/

-- Cyclic dependencies found

create table SKYTEST.EMPLOYEE
(
    EMPLOYEE_INT_ID  NUMBER(8) not null
        constraint EMPLOYEE_CONTACT_FK
            references SKYTEST.CONTACT,
    EMPLOYEE_NUMBER  NUMBER(8) not null,
    REPORTS_TO       NUMBER(8)
        constraint EMPLOYEE_EMPLOYEE_FKV2
            references SKYTEST.EMPLOYEE,
    NAME             VARCHAR2(35),
    CREATION_DATE    DATE,
    LAST_MODIFY_DATE DATE,
    START_DATE       DATE,
    TERMINATED_DATE  DATE,
    SIN              VARCHAR2(11),
    STATUS           VARCHAR2(1),
    VACATION_DAYS    NUMBER(5, 2),
    REASON           VARCHAR2(1024),
    NOTES            VARCHAR2(1024),
    CUSTOM_FIELD1    VARCHAR2(1024),
    CUSTOM_FIELD2    VARCHAR2(1024),
    CUSTOM_FIELD3    VARCHAR2(1024)
)
/

create index SKYTEST.EMPLOYEE_IDX1
    on SKYTEST.EMPLOYEE (EMPLOYEE_INT_ID)
/

create index SKYTEST.EMPLOYEE_IDX4
    on SKYTEST.EMPLOYEE (REPORTS_TO)
/

-- Cyclic dependencies found

create index SKYTEST.FAST_TEXT_SEARCH_INDEX1
    on SKYTEST.FAST_TEXT_SEARCH (MASTER_GROUP, USERS, TYPE, TAGS)
/

-- Cyclic dependencies found

create table SKYTEST.GIC
(
    GIC_INT_ID              NUMBER(8) not null,
    ACCOUNT                 NUMBER(8) not null
        constraint GIC_ACCOUNT_FK
            references SKYTEST.ACCOUNT,
    CREATION_DATE           DATE,
    LAST_MODIFICATION_DATE  DATE,
    PRODUCT                 NUMBER(8)
        constraint GIC_PRODUCT_FK
            references SKYTEST.PRODUCT,
    PRINCIPAL_AMOUNT        NUMBER(11, 2),
    AMOUNT_TYPE             VARCHAR2(1),
    INITIAL_COMPD_FREQ      NUMBER(5),
    INITIAL_PAYMENT_FREQ    NUMBER(5),
    RATES                   NUMBER(11, 4),
    MATURITY_DATE           DATE,
    GUARANTY_MATURITY_VALUE NUMBER(11, 2),
    PAYMENT_METHOD          VARCHAR2(3),
    INSTITUTION_NUMBER      VARCHAR2(3),
    BANK_TRANSIT            VARCHAR2(20),
    BANK_ACCOUNT_TYPE       VARCHAR2(1),
    BANK_ACCOUNT_NUMBER     VARCHAR2(20),
    BANK_HOLDER_NAME        VARCHAR2(75),
    LAST_UPDATE_USER        VARCHAR2(64),
    INITIAL_AMOUNT          NUMBER(11, 2),
    PLAN_ID                 VARCHAR2(64)
)
/

create index SKYTEST.GIC_IDX1
    on SKYTEST.GIC (ACCOUNT)
/

create index SKYTEST.GIC_IDX2
    on SKYTEST.GIC (GIC_INT_ID)
/

alter table SKYTEST.GIC
    add constraint GIC_PK
        primary key (GIC_INT_ID)
/

create table SKYTEST.GIC_TRANSACTION
(
    GIC_TRANSACTION_INT_ID     NUMBER(8)     not null,
    GIC                        NUMBER(8)     not null
        constraint GIC_TRANSACTION_GIC_FK
            references SKYTEST.GIC,
    INVEST_TRANSACTION_TYPE    NUMBER(8)     not null
        constraint GIC_TRANSACTION_TYPE_FK
            references SKYTEST.INVEST_TRANS_TYPE,
    MGMT_COMPANY_CODE          VARCHAR2(3),
    ACCOUNT_NUMBER             VARCHAR2(15),
    DESCRIPTION                VARCHAR2(200),
    TRANSACTION_TYPE_DETAIL    VARCHAR2(1),
    TRANS_UNITS                NUMBER(13, 4) not null,
    UNIT_PRICE                 NUMBER(7, 4),
    GROSS_AMOUNT               NUMBER(11, 2) not null,
    NET_AMOUNT                 NUMBER(11, 2) not null,
    TOTAL_ASSIGNED             NUMBER(13, 4) not null,
    TOTAL_UNASSIGNED           NUMBER(13, 4) not null,
    SETTLEMENT_STATUS          VARCHAR2(1),
    SETTLEMENT_DATE            DATE          not null,
    TRADE_DATE                 DATE          not null,
    CREATION_DATE              DATE          not null,
    TRANSACTION_DATE           DATE          not null,
    SEQUENCE_NUMBER            NUMBER(5),
    BENEFICIAL_OWNER_LASTNAME  VARCHAR2(20),
    BENEFICIAL_OWNER_FIRSTNAME VARCHAR2(20)
)
/

create index SKYTEST.GIC_TRANSACTION_IDX3
    on SKYTEST.GIC_TRANSACTION (INVEST_TRANSACTION_TYPE)
/

create index SKYTEST.GIC_TRANSACTION_IDX1
    on SKYTEST.GIC_TRANSACTION (GIC_TRANSACTION_INT_ID)
/

create index SKYTEST.GIC_TRANSACTION_IDX2
    on SKYTEST.GIC_TRANSACTION (GIC)
/

alter table SKYTEST.GIC_TRANSACTION
    add constraint GIC_TRANS_PK
        primary key (GIC_TRANSACTION_INT_ID)
/

create table SKYTEST.GIC_TRANS_DETAILS
(
    GIC_TRANS_DETAIL_INT_ID NUMBER(8) not null,
    GIC_TRANSACTION         NUMBER(8) not null
        constraint GIC_TRANS_DETAILS_GIC_FK
            references SKYTEST.GIC_TRANSACTION,
    TRANSACTION_TYPE_DETAIL VARCHAR2(1),
    RAW_RECORD              VARCHAR2(700)
)
/

create index SKYTEST.GIC_TRANS_DETAILS_IDX1
    on SKYTEST.GIC_TRANS_DETAILS (GIC_TRANSACTION)
/

create index SKYTEST.GIC_TRANS_DETAILS_IDX2
    on SKYTEST.GIC_TRANS_DETAILS (GIC_TRANS_DETAIL_INT_ID)
/

alter table SKYTEST.GIC_TRANS_DETAILS
    add constraint GIC_TRANS_DET_PK
        primary key (GIC_TRANS_DETAIL_INT_ID)
/

-- Cyclic dependencies found

create table SKYTEST.GROUPS_ADVISORS
(
    "GROUP" NUMBER(8)
        constraint GROUP_FK
            references SKYTEST.GROUPS,
    ADVISOR NUMBER(8)
        constraint ADVISOR_FR_GROUP
            references SKYTEST.ADVISOR
)
/

-- Cyclic dependencies found

create table SKYTEST.HEIGHT_WEIGHT_PRODUCT
(
    HEIGHT_WEIGHT_PRODUCT_INT_ID NUMBER(8) not null
        primary key,
    HEIGHT_WEIGHT                NUMBER(8)
        constraint HEIGHT_WEIGHT_PRODUCT_FK
            references SKYTEST.HEIGHT_WEIGHT,
    PRODUCT_ID                   NUMBER(8),
    POSITIVE                     NUMBER(1)
)
/

comment on column SKYTEST.HEIGHT_WEIGHT_PRODUCT.POSITIVE is 'If 0 means that is not inside the range if 1 means that is inside'
/

-- Cyclic dependencies found

create table SKYTEST.HEIGHT_WEIGHT_SURVEY
(
    HEIGHT_WEIGHT NUMBER(8)
        constraint HEIGHT_WEIGHT_SURVEY_FK
            references SKYTEST.HEIGHT_WEIGHT,
    SURVEY        NUMBER(8)
        constraint SURVEY_HEIGHT_WEIGHT_FK
            references SKYTEST.SURVEY
)
/

-- Cyclic dependencies found

create table SKYTEST.HEIGHT_WEIGHT_VALUES
(
    HEIGHT_WEIGHT_VALUES_INT_ID NUMBER(8) not null
        primary key,
    MIN_HEIGHT                  NUMBER(4),
    MIN_WEIGHT                  NUMBER(4),
    HEIGHT_WEIGHT               NUMBER(8)
        constraint HEIGHT_WEIGHT_HEIGHT_WEIGHT_VALUE_FK
            references SKYTEST.HEIGHT_WEIGHT,
    MAX_HEIGHT                  NUMBER(4),
    MAX_WEIGHT                  NUMBER(4),
    MIN_HEIGHT_FEETS            VARCHAR2(20),
    MAX_HEIGHT_FEETS            VARCHAR2(20),
    MIN_WEIGHT_POUNDS           NUMBER(4),
    MAX_WEIGHT_POUNDS           NUMBER(4)
)
/

-- Cyclic dependencies found

create table SKYTEST.HOUSEHOLD_CONTACT
(
    HOUSEHOLD_INT_ID NUMBER(8) not null
        primary key,
    CONTACT1         NUMBER(8)
        constraint SYS_C0064269
            references SKYTEST.CONTACT
                on delete cascade,
    CONTACT2         NUMBER(8)
        constraint SYS_C0064270
            references SKYTEST.CONTACT
                on delete cascade,
    RELATIONSHIP     NUMBER(8),
    TYPE             NUMBER(8)
)
/

comment on column SKYTEST.HOUSEHOLD_CONTACT.RELATIONSHIP is 'this is the type member'
/

comment on column SKYTEST.HOUSEHOLD_CONTACT.TYPE is 'this is 1 if is family'
/

-- Cyclic dependencies found

create table SKYTEST.INV_INVOICES
(
    INVOICE_INT_ID NUMBER(8) not null
        primary key,
    TAX_FK         NUMBER(8)
        constraint INV_TAX_FK_1
            references SKYTEST.INV_TAX_RATES,
    VAT_NUMBER     VARCHAR2(15),
    INVOICE_DATE   DATE,
    BILLING_FK     NUMBER(8)
        constraint INV_BILLING_FK_1
            references SKYTEST.INV_ORDER_BILLING,
    COUNTRY_FK     VARCHAR2(3)
        constraint COUNTRY_FK_3
            references SKYTEST.COUNTRY,
    NETTO          NUMBER(12),
    BRUTTO         NUMBER(12),
    CONFIRMED      NUMBER(12)
)
/

-- Cyclic dependencies found

create table SKYTEST.INV_ORDER_BILLING
(
    BILLING_INT_ID NUMBER(8) not null
        primary key,
    ORDER_FK       NUMBER(8)
        constraint INV_ORDERS_FK_1
            references SKYTEST.INV_ORDERS,
    EMAIL          VARCHAR2(100),
    COMPANY        VARCHAR2(100),
    ADDRESS        VARCHAR2(100),
    ADDRESS_2      VARCHAR2(100),
    CITY           VARCHAR2(50),
    COUNTRY        VARCHAR2(50),
    POSTCODE       VARCHAR2(50)
)
/

-- Cyclic dependencies found

create table SKYTEST.INV_ORDER_COUPON
(
    ORDER_COUPONS_INT_ID NUMBER(8) not null
        primary key,
    ORDER_FK             NUMBER(8)
        constraint INV_ORDER_FK_3
            references SKYTEST.INV_ORDERS,
    COUPON_FK            NUMBER(8)
        constraint INV_COUPON_FK_1
            references SKYTEST.INV_COUPONS
)
/

-- Cyclic dependencies found

create table SKYTEST.INV_ORDER_PRODUCT
(
    ITEM_INT_ID NUMBER(8) not null
        primary key,
    ORDER_FK    NUMBER(8)
        constraint INV_ORDER_FK_2
            references SKYTEST.INV_ORDERS,
    PRODUCT_FK  NUMBER(8)
        constraint INV_PRODUCT_FK_1
            references SKYTEST.INV_PRODUCTS
)
/

-- Cyclic dependencies found

create table SKYTEST.INV_ORDERS
(
    ORDER_INT_ID   NUMBER(8) not null
        primary key,
    TRANSACTION_ID VARCHAR2(12),
    VALUE          NUMBER(8),
    CURRENCY       VARCHAR2(3),
    UPDATE_DATE    DATE,
    CREATE_DATE    DATE,
    STATUS_FK      NUMBER(8)
        constraint INV_STATUS_FK_1
            references SKYTEST.INV_ORDER_STATUSES,
    CONTACT_FK     NUMBER(8)
        constraint CONTACT_FK_3
            references SKYTEST.CONTACT
)
/

-- Cyclic dependencies found

alter table SKYTEST.LEAD
    add constraint LEAD_PK
        primary key (LEAD_INT_ID)
/

-- Cyclic dependencies found

create table SKYTEST.ADDRESS_BOOK
(
    ADDRESS_BOOK_INT_ID NUMBER(8) not null
        primary key,
    OWNER               NUMBER(8) not null,
    FIRSTNAME           VARCHAR2(255),
    LASTNAME            VARCHAR2(255),
    PHONE_NUMBER        VARCHAR2(12),
    ADDRESS_EMAIL       VARCHAR2(255),
    ADDRESS_BOOK_TYPE   NUMBER(8),
    ASSOCIATION         VARCHAR2(255),
    NOTES               VARCHAR2(1000),
    CONTACT             NUMBER(8),
    LEAD                NUMBER(8)
        constraint ADDRESS_BOOK_LEAD_FK
            references SKYTEST.LEAD,
    PREFERRED_LANGUAGE  NUMBER(4)
)
/

comment on column SKYTEST.ADDRESS_BOOK.CONTACT is 'FK to contact in case the contact is already on our db'
/

comment on column SKYTEST.ADDRESS_BOOK.LEAD is 'FK to lead in case the contact is already on our db'
/

comment on column SKYTEST.ADDRESS_BOOK.PREFERRED_LANGUAGE is 'Same as contact PREFERRED_LANGUAGE'
/

-- Cyclic dependencies found

create table SKYTEST.HOUSEHOLD_LEAD
(
    HOUSEHOLD_INT_ID NUMBER(8) not null
        primary key,
    LEAD1            NUMBER(8)
        constraint LEAD1_FK
            references SKYTEST.LEAD
                on delete cascade,
    LEAD2            NUMBER(8)
        constraint LEAD2_FK
            references SKYTEST.LEAD
                on delete cascade,
    RELATIONSHIP     NUMBER(8),
    TYPE             NUMBER(8)
)
/

comment on column SKYTEST.HOUSEHOLD_LEAD.RELATIONSHIP is 'this is the type member'
/

comment on column SKYTEST.HOUSEHOLD_LEAD.TYPE is 'this is 1 if is family'
/

-- Cyclic dependencies found

create index SKYTEST.LEAD_IDX1
    on SKYTEST.LEAD (LEAD_INT_ID)
/

-- Cyclic dependencies found

create index SKYTEST.LEAD_IDX2
    on SKYTEST.LEAD (LEAD_SOURCE)
/

-- Cyclic dependencies found

create index SKYTEST.INDEX2
    on SKYTEST.LEAD (MASTER_CODE)
/

-- Cyclic dependencies found

create table SKYTEST.LEADS_RELATIONSHIP
(
    LEADS_RELATIONSHIP_INT_ID NUMBER(11)  not null
        constraint SYS_C0063858
            primary key,
    CREATION_DATE             DATE,
    LAST_MODIFICATION_DATE    DATE,
    TYPE                      VARCHAR2(1) not null,
    FIRST_LEAD_INT_ID         NUMBER(11),
    SECOND_LEAD_INT_ID        NUMBER(11),
    THIRD_LEAD_INT_ID         NUMBER(11),
    FORTH_LEAD_INT_ID         NUMBER(11),
    FIFTH_LEAD_INT_ID         NUMBER(11),
    SIXTH_LEAD_INT_ID         NUMBER(11),
    SEVENTH_LEAD_INT_ID       NUMBER(11),
    EIGHTH_LEAD_INT_ID        NUMBER(11),
    NINETH_LEAD_INT_ID        NUMBER(11),
    TENTH_LEADT_INT_ID        NUMBER(11),
    ADVISOR                   NUMBER(11)
        constraint SYS_C0063859
            references SKYTEST.ADVISOR
                on delete set null
)
/

comment on column SKYTEST.LEADS_RELATIONSHIP.TYPE is 'S= SINGLE, C=COUPLE, H-HOUSEHOLD, B=BUSINESS,O=OWNERSHIP,P=BENIFICIARY'
/

-- Cyclic dependencies found

create table SKYTEST.LICENSE_LIABILITY_NOTES
(
    LICENSE_LIAB_NOTES_INT_ID NUMBER(8) not null,
    LICENSE_LIABILITY         NUMBER(8) not null
        constraint LICENSE_LIABILITY_NOTES_FK
            references SKYTEST.LICENSE_LIABILITY,
    TITLE                     VARCHAR2(80),
    IS_PRIVATE                VARCHAR2(1),
    LAST_UPDATE_DATE          DATE,
    LAST_UPDATE_USER          VARCHAR2(30),
    CREATED_DATE_TIME         DATE,
    CREATE_USER               VARCHAR2(30),
    NOTE                      VARCHAR2(2000)
)
/

create index SKYTEST.LICENSE_LIABILITY_NOTES_IDX1
    on SKYTEST.LICENSE_LIABILITY_NOTES (LICENSE_LIAB_NOTES_INT_ID)
/

create index SKYTEST.LICENSE_LIABILITY_NOTES_IDX2
    on SKYTEST.LICENSE_LIABILITY_NOTES (LICENSE_LIABILITY)
/

alter table SKYTEST.LICENSE_LIABILITY_NOTES
    add constraint LICENSE_LIAB_NOTES_PK
        primary key (LICENSE_LIAB_NOTES_INT_ID)
/

-- Cyclic dependencies found

alter table SKYTEST.MANUFACTURER
    add constraint MANUFACTURERS_PK
        primary key (MANUFACTURER_INT_ID)
/

-- Cyclic dependencies found

create unique index SKYTEST.MANUFACTURER_IDX1
    on SKYTEST.MANUFACTURER (MANUFACTURER_INT_ID)
/

-- Cyclic dependencies found

create index SKYTEST.MANUFACTURER_IDX3
    on SKYTEST.MANUFACTURER (PRODUCT_SUPPLIER)
/

-- Cyclic dependencies found

create index SKYTEST.MANUFACTURER_IDX2
    on SKYTEST.MANUFACTURER (MGMT_COMPANY_CODE, ACTIVE)
/

-- Cyclic dependencies found

alter table SKYTEST.MARKET_PLACE
    add constraint MARKET_PLACE_PK
        primary key (MARKET_PLACE_INT_ID)
/

-- Cyclic dependencies found

create unique index SKYTEST.ADVISOR_MARKET_PLACE_PK
    on SKYTEST.MARKET_PLACE (MARKET_PLACE_INT_ID)
/

-- Cyclic dependencies found

alter table SKYTEST.NOTIFICATIONS
    add constraint NOTIFICATIONS_PK
        primary key (NOTIFICATIONS_INT_ID)
/

-- Cyclic dependencies found

create index SKYTEST.NOTIFICATIONS_IDX1
    on SKYTEST.NOTIFICATIONS (NOTIFICATIONS_INT_ID)
/

-- Cyclic dependencies found

create index SKYTEST.OPPORTUNITY_IDX1
    on SKYTEST.OPPORTUNITY (OPPORTUNITY_AMOUNT)
/

-- Cyclic dependencies found

create index SKYTEST.OPPORTUNITY_IDX2
    on SKYTEST.OPPORTUNITY (LEAD)
/

-- Cyclic dependencies found

create table SKYTEST.ORGANIZATION
(
    ORGANIZATION_INT_ID    NUMBER(8) not null,
    SUB_ORGANIZATION_ID    NUMBER(8)
        constraint ORGANIZATION_ORGANIZATION_FK
            references SKYTEST.ORGANIZATION,
    ORGANIZATION_CODE      VARCHAR2(6),
    ORGANIZATION_DESC_EN   VARCHAR2(128),
    ORGANIZATION_DESC_FR   VARCHAR2(128),
    CHEQUE_NAME            VARCHAR2(128),
    SHORT_NAME             VARCHAR2(128),
    FIELD_NAME             VARCHAR2(64),
    FIELD_VALUE            VARCHAR2(1024),
    MASTER_CODE            VARCHAR2(16),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    ORGANIZATION_TYPE      NUMBER(8),
    WEBSITE                VARCHAR2(128),
    WEBSITE_FR             VARCHAR2(128)
)
/

create index SKYTEST.ORGANIZATION_IDX1
    on SKYTEST.ORGANIZATION (ORGANIZATION_INT_ID)
/

create index SKYTEST.ORGANIZATION_IDX2
    on SKYTEST.ORGANIZATION (ORGANIZATION_CODE)
/

create index SKYTEST.ORGANIZATION_IDX3
    on SKYTEST.ORGANIZATION (SUB_ORGANIZATION_ID)
/

alter table SKYTEST.ORGANIZATION
    add constraint ORGANIZATION_PK
        primary key (ORGANIZATION_INT_ID)
/

create table SKYTEST.PRODUCT_DISTRIBUTOR
(
    PRODUCT_DISTRIBUTOR_ID   NUMBER(8) not null
        constraint PRODUCT_DISTRIBUTOR_PK
            primary key,
    NAME_EN                  VARCHAR2(64),
    NAME_FR                  VARCHAR2(64),
    MASTER_GROUP             NUMBER(8)
        constraint PRODUCT_DISTRIBUTOR_FK2
            references SKYTEST.MASTER_GROUP,
    PRODUCT_DISTRIBUTOR_TYPE NUMBER(4),
    ORGANIZATION             NUMBER(8)
        constraint PRODUCT_DISTRIBUTOR_FK3
            references SKYTEST.ORGANIZATION,
    CONTACT                  NUMBER(8)
)
/

-- Cyclic dependencies found

create table SKYTEST.AGENCY
(
    AGENCY_INT_ID           NUMBER(8) not null,
    AGENCY_DESC_EN          VARCHAR2(64),
    AGENCY_DESC_FR          VARCHAR2(64),
    CREATION_DATE           DATE,
    LAST_MODIFY_DATE        DATE,
    DEALER                  NUMBER(8)
        constraint AGENCY_DEALER_FK
            references SKYTEST.DEALER,
    MASTER_GROUP            NUMBER(8)
        constraint AGENCY_MASTER_GROUP_FK
            references SKYTEST.MASTER_GROUP,
    AGENCY_TYPE             NUMBER(4),
    ORGANIZATION            NUMBER(8)
        constraint AGENCY_ORGANIZATION_FK
            references SKYTEST.ORGANIZATION,
    SUB_AGENCY_ID           NUMBER(8),
    INVEST_PERCENT_SHARE    NUMBER(5, 2),
    LAST_UPDATE_USER        VARCHAR2(64),
    USER_HOME               VARCHAR2(256),
    TOTAL_STORAGE_GIGABYTES NUMBER(5, 2),
    CONTACT                 NUMBER(8)
        constraint AGENCY_FK1
            references SKYTEST.CONTACT
)
/

-- Cyclic dependencies found

create table SKYTEST.AGENCY_ADVISOR
(
    AGENCY  NUMBER(8) not null
        constraint AGENCY_ADVISOR_FK1
            references SKYTEST.AGENCY,
    ADVISOR NUMBER(8) not null
        constraint AGENCY_ADVISOR_FK2
            references SKYTEST.ADVISOR,
    constraint AGENCY_ADVISOR_PK
        primary key (AGENCY, ADVISOR)
)
/

-- Cyclic dependencies found

create table SKYTEST.AGENCY_LICENSE_LIABILITY
(
    AGENCY            NUMBER not null
        constraint AGENCY_LICENSE_LIABILITY_A_FK1
            references SKYTEST.AGENCY,
    LICENSE_LIABILITY NUMBER not null
        constraint AGENCY_LICENSE_LIABILITY_L_FK1
            references SKYTEST.LICENSE_LIABILITY,
    constraint AGENCY_LIENSE_LIABILITY_PK
        primary key (AGENCY, LICENSE_LIABILITY)
)
/

-- Cyclic dependencies found

create table SKYTEST.CONTRACT_ROUTE
(
    CONTRACT_ROUTE_INT_ID  NUMBER(8) not null
        constraint CONTRACT_ROUTE_PK
            primary key,
    AGENCY                 NUMBER(8)
        constraint CONTRACT_ROUTE_AGENCY_FK
            references SKYTEST.AGENCY,
    ACCOUNT_MANAGER        NUMBER(8)
        constraint CONTRACT_ROUTE_ACCOUNT_MAN_FK
            references SKYTEST.ACCOUNT_MANAGER,
    PRIMARY                VARCHAR2(1),
    OVERRIDE               NUMBER(5, 2),
    SHARE_PERCENT          NUMBER(5, 2),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    CONTRACT_SETUP         NUMBER(8)
        constraint CONTRACT_ROUTE_CONTRACT_S_FK
            references SKYTEST.CONTRACT_SETUP
)
/

-- Cyclic dependencies found

create table SKYTEST.PARKING_SPACE
(
    PARKING_SPACE_INT_ID   NUMBER(8) not null
        primary key,
    BUILDING               NUMBER(8)
        constraint BUILDING_FK1
            references SKYTEST.COMPANY,
    DOOR                   NUMBER(8)
        constraint DOOR_FK1
            references SKYTEST.DOOR,
    PARKING_TYPE           NUMBER(8),
    PARKING_SPACE_NUMBER   VARCHAR2(60),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    FREE                   VARCHAR2(2)
)
/

-- Cyclic dependencies found

create table SKYTEST.PARKING_SPACE_CONTACT
(
    CONTACT       NUMBER(8)
        constraint CONTACTPARKING_FK
            references SKYTEST.CONTACT,
    PARKING_SPACE NUMBER(8)
        constraint PARKING_SPACECONTACT_FK
            references SKYTEST.PARKING_SPACE
)
/

-- Cyclic dependencies found

create table SKYTEST.PAYMENT
(
    PAYMENT_INT_ID         NUMBER(11) not null
        constraint PAYMENT_PK
            primary key,
    PAYMENT_NUMBER         VARCHAR2(20),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    EFT_INFO               NUMBER(11),
    PAYMENT_DATE           VARCHAR2(20),
    PAYMENT_METHOD         NUMBER(4),
    PAYEE                  NUMBER(8)
        constraint PAYMENT_PAYEE_FK1
            references SKYTEST.PAYEE,
    AMOUNT                 NUMBER(9, 2),
    PAID                   VARCHAR2(1)
)
/

comment on column SKYTEST.PAYMENT.PAYMENT_METHOD is 'USING PAYMENT_TYPE CLASS'
/

-- Cyclic dependencies found

create table SKYTEST.PHONE
(
    PHONE_INT_ID           NUMBER(8) not null,
    PHONE_NUMBER           VARCHAR2(20),
    AREA_CODE              VARCHAR2(4),
    EXTENSION              VARCHAR2(16),
    IS_PRIMARY             VARCHAR2(1),
    TYPE                   NUMBER(8),
    OWNER_INT_ID           NUMBER(8),
    OWNER_TYPE             NUMBER(4),
    SHAREABLE              VARCHAR2(1),
    DO_NOT_CALL            VARCHAR2(1),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    LAST_UPDATE_USER       VARCHAR2(64),
    ONBOARDING             NUMBER(10)
        constraint PHONE_ONBOARDING_FK
            references SKYTEST.ONBOARDING_STATUS
)
/

create index SKYTEST.PHONE_IDX3
    on SKYTEST.PHONE (AREA_CODE, PHONE_NUMBER, EXTENSION)
/

create index SKYTEST.PHONE_IDX1
    on SKYTEST.PHONE (PHONE_INT_ID)
/

create index SKYTEST.PHONE_IDX2
    on SKYTEST.PHONE (PHONE_NUMBER)
/

create index SKYTEST.INDEX14
    on SKYTEST.PHONE (AREA_CODE, PHONE_NUMBER)
/

create index SKYTEST.INDEX13
    on SKYTEST.PHONE (AREA_CODE)
/

alter table SKYTEST.PHONE
    add constraint PHONE_PK
        primary key (PHONE_INT_ID)
/

create table SKYTEST.PRODUCT_SUPPLIER
(
    PRODUCT_SUPPLIER_INT_ID    NUMBER(8) not null,
    PRODUCT_SUPPLIER_TYPE      NUMBER(8) not null
        constraint PRODUCT_SUPPLIER_PRODUCT__FK1
            references SKYTEST.PRODUCT_SUPPLIER_TYPE,
    PRODUCT_SUPPLIER_TYPE_CODE VARCHAR2(3),
    INSTITUTION_NO             VARCHAR2(3),
    CREATION_DATE              DATE,
    LAST_MODIFICATION_DATE     DATE,
    COMPANY_ID                 NUMBER(8)
        constraint COMPANYCONST
            unique,
    ORGANIZATION               NUMBER(8)
        constraint PRODUCT_SUPPLIER_ORG_FK
            references SKYTEST.ORGANIZATION,
    MASTER_CODE                VARCHAR2(16),
    ACTIVE                     VARCHAR2(1),
    ADDRESS                    NUMBER(8)
        constraint PRODUCT_SUPPLIER_FK1
            references SKYTEST.ADDRESS,
    PHONE                      NUMBER(8)
        constraint PRODUCT_SUPPLIER_FK2
            references SKYTEST.PHONE
)
/

create index SKYTEST.PRODUCT_SUPPLIER_IDX1
    on SKYTEST.PRODUCT_SUPPLIER (PRODUCT_SUPPLIER_INT_ID)
/

create index SKYTEST.PRODUCT_SUPPLIER_IDX3
    on SKYTEST.PRODUCT_SUPPLIER (PRODUCT_SUPPLIER_TYPE)
/

create index SKYTEST.PRODUCT_SUPPLIER_INDEX1
    on SKYTEST.PRODUCT_SUPPLIER (MASTER_CODE)
/

alter table SKYTEST.PRODUCT_SUPPLIER
    add constraint PRODUCT_SUPPLIER_PK
        primary key (PRODUCT_SUPPLIER_INT_ID)
/

create table SKYTEST.AGENCY_PRODUCT_SUPPLIER
(
    AGENCY           NUMBER(8) not null
        constraint AGENCY_PRODUCT_SUPPLIER_FK1
            references SKYTEST.AGENCY,
    PRODUCT_SUPPLIER NUMBER(8) not null
        constraint AGENCY_PRODUCT_SUPPLIER_FK2
            references SKYTEST.PRODUCT_SUPPLIER,
    constraint AGENCY_PRODUCT_SUPPLIER_PK
        primary key (AGENCY, PRODUCT_SUPPLIER)
)
/

create table SKYTEST.PRODUCT_PRODUCT_SUPPLIER
(
    PRODUCT_SUPPLIER NUMBER(8) not null
        constraint PRODUCT_PRODUCT_SUPPLIER_FK
            references SKYTEST.PRODUCT_SUPPLIER,
    PRODUCT          NUMBER(8) not null
        constraint PRODUCT_SUPPLIER_PRODUCT_FK
            references SKYTEST.PRODUCT,
    constraint PRODUCT_PRODUCT_SUPPLIER_PK
        primary key (PRODUCT_SUPPLIER, PRODUCT)
)
/

create index SKYTEST.PRODUCT_PRODUCT_SUPPLIER_IDX1
    on SKYTEST.PRODUCT_PRODUCT_SUPPLIER (PRODUCT_SUPPLIER)
/

create index SKYTEST.PRODUCT_PRODUCT_SUPPLIER_IDX2
    on SKYTEST.PRODUCT_PRODUCT_SUPPLIER (PRODUCT)
/

create table SKYTEST.MANUFACTURER
(
    MANUFACTURER_INT_ID    NUMBER(8)   not null,
    PRODUCT_SUPPLIER       NUMBER(8)   not null
        constraint MANUFACTURER_PROD_SUPP_FK
            references SKYTEST.PRODUCT_SUPPLIER,
    MGMT_COMPANY_CODE      VARCHAR2(3) not null,
    MANUFACTURER_DESC      VARCHAR2(128),
    MANUFACTURER_URL       VARCHAR2(128),
    ACTIVE                 CHAR        not null,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    AT_FILE                VARCHAR2(1),
    AF_FILE                VARCHAR2(1),
    AW_FILE                VARCHAR2(1),
    RS_FILE                VARCHAR2(1),
    NS_FILE                VARCHAR2(1),
    PS_FILE                VARCHAR2(1),
    AS_FILE                VARCHAR2(1),
    AH_FILE                VARCHAR2(1)
)
/

create table SKYTEST.ACCOUNT
(
    ACCOUNT_INT_ID         NUMBER(8)    not null,
    ACCOUNT_NUMBER         VARCHAR2(15) not null,
    ACCOUNT_TYPE           NUMBER(8)    not null
        constraint ACCOUNT_ACCOUNT_TYPE_FK
            references SKYTEST.ACCOUNT_TYPE,
    STATUS                 VARCHAR2(1)  not null,
    CREATION_DATE          DATE         not null,
    LAST_MODIFICATION_DATE DATE,
    SPOUSAL_ACCOUNT_FLAG   VARCHAR2(1),
    DEALER_CODE            VARCHAR2(4),
    SALESREP_CODE          VARCHAR2(5),
    CLOSE_DATE             DATE,
    MGMT_COMPANY_CODE      VARCHAR2(3),
    ACCOUNT_DESIGNATION    VARCHAR2(1),
    TAX_CODE               VARCHAR2(3),
    MARKET_VALUE           NUMBER(11, 2),
    VALUED_ON              DATE,
    PRODUCT_SUPPLIER       NUMBER(8)
        constraint ACCOUNT_PRODUCT_SUPPLIER_FK1
            references SKYTEST.PRODUCT_SUPPLIER,
    BOOK_VALUE             NUMBER(11, 4),
    LAST_UPDATE_USER       VARCHAR2(64),
    INITIAL_AMOUNT         NUMBER(9, 2)
)
/

comment on column SKYTEST.ACCOUNT.CREATION_DATE is 'Creation date is either from the PS file at month end or from the first Account/Fund appearance in an AT file. The later uses the trade_date as creation date.'
/

create table SKYTEST.SEG_FUND
(
    SEG_FUND_INT_ID        NUMBER(8)   not null,
    FUND_ID                VARCHAR2(5) not null,
    MGMT_COMPANY_CODE      VARCHAR2(3) not null,
    CREATION_DATE          DATE        not null,
    LAST_MODIFICATION_DATE DATE,
    TOTAL_UNASSIGNED       NUMBER(22, 9),
    TOTAL_ASSIGNED         NUMBER(22, 9),
    PRODUCT                NUMBER(8)
        constraint SEG_FUND_PRODUCT_FK
            references SKYTEST.PRODUCT,
    ACCOUNT                NUMBER(8)
        constraint SEG_FUND_ACCOUNT_FK
            references SKYTEST.ACCOUNT,
    DEALER_CODE            VARCHAR2(4),
    SALESREP_CODE          VARCHAR2(5),
    MARKET_VALUE           NUMBER(11, 4),
    VALUED_ON              DATE,
    UNIT_PRICE             NUMBER(11, 4),
    BOOK_VALUE             NUMBER(11, 4),
    LAST_UPDATE_USER       VARCHAR2(20)
)
/

comment on column SKYTEST.SEG_FUND.CREATION_DATE is 'Creation date is set by using either frist appearance in an AT file as trade date or from the PS file at month end reconciliation.'
/

create index SKYTEST.SEG_FUND_IDX2
    on SKYTEST.SEG_FUND (SEG_FUND_INT_ID)
/

alter table SKYTEST.SEG_FUND
    add constraint SEGREGATED_FUND_PK
        primary key (SEG_FUND_INT_ID)
/

create table SKYTEST.SEG_FUND_TRANSACTION
(
    SEG_FUND_TRANS_INT_ID        NUMBER(8)    not null,
    TRANSACTION_ID               VARCHAR2(15),
    TRANSACTION_TYPE             VARCHAR2(1)  not null,
    SEG_FUND                     NUMBER(8)
        constraint SEG_FUND_TRANSACTION_FK
            references SKYTEST.SEG_FUND,
    MGMT_COMPANY_CODE            VARCHAR2(3)  not null,
    ACCOUNT_NUMBER               VARCHAR2(15) not null,
    FUND_ID                      VARCHAR2(5)  not null,
    TRANSACTION_TYPE_DETAIL      VARCHAR2(1),
    UNIT_TRANSACTED              NUMBER(15, 5),
    UNIT_PRICE                   NUMBER(15, 5),
    GROSS_AMOUNT                 NUMBER(15, 5),
    NET_AMOUNT                   NUMBER(15, 5),
    TOTAL_ASSIGNED               NUMBER(15, 5),
    TOTAL_UNASSIGNED             NUMBER(15, 5),
    SETTLEMENT_STATUS            VARCHAR2(1),
    SETTLEMENT_DATE              DATE         not null,
    TRADE_DATE                   DATE         not null,
    CREATION_DATE                DATE         not null,
    LAST_MODIFICATION_DATE       DATE,
    SEQUENCE_NUMBER              NUMBER(8),
    BENEFICIAL_OWNER_LASTNAME    VARCHAR2(20),
    BENEFICIAL_OWNER_FIRSTNAME   VARCHAR2(20),
    BENEFICIAL_OWNER_SIN         VARCHAR2(9),
    AT_FILE_NAME                 VARCHAR2(128),
    SALESREP_CODE                VARCHAR2(5),
    DEALER_CODE                  VARCHAR2(4),
    SPOUSAL_ACCOUNT_FLAG         VARCHAR2(1),
    DESIGNATION                  VARCHAR2(1),
    ACCOUNT_TYPE                 VARCHAR2(2),
    LOCKED_IN_CODE               VARCHAR2(1),
    TAX_CODE                     VARCHAR2(3),
    ACCOUNT_STATUS               VARCHAR2(1),
    POS_NEG_IND                  VARCHAR2(1),
    CLIENT_PAID_COMM             NUMBER(15, 5),
    DEALER_COM_CLIENT_PAID       NUMBER(15, 5),
    FUND_PARTNERSHIP_PAID_COMM   NUMBER(15, 5),
    FEES                         NUMBER(15, 5),
    RETURN_CODE                  VARCHAR2(2),
    RETURN_CODE_DETAIL           VARCHAR2(3),
    NET_GROSS_INDICATOR          VARCHAR2(1),
    DEALER_COMM_FUND_PARTNERSHIP NUMBER(15, 5),
    SOURCE_ID                    VARCHAR2(20),
    ORDER_SOURCE                 VARCHAR2(1),
    ORDER_TYPE                   VARCHAR2(1),
    FUND_PERCENT_COMM            NUMBER(9, 2),
    DIV_OPT                      NUMBER(4),
    SETTLE_AMOUNT                NUMBER(9, 4),
    AVG_COST                     NUMBER(9, 4),
    SETTLE_METHOD                NUMBER(4),
    PROCESSING_STATUS            VARCHAR2(1),
    PROCESS_DATE                 DATE
)
/

create index SKYTEST.SEG_FUND_TRANSACTION_IDX1
    on SKYTEST.SEG_FUND_TRANSACTION (TRANSACTION_TYPE)
/

create index SKYTEST.SEG_FUND_TRANSACTION_IDX2
    on SKYTEST.SEG_FUND_TRANSACTION (SEG_FUND)
/

create index SKYTEST.SEG_FUND_TRANSACTION_IDX3
    on SKYTEST.SEG_FUND_TRANSACTION (SEG_FUND_TRANS_INT_ID)
/

alter table SKYTEST.SEG_FUND_TRANSACTION
    add constraint SEG_FUND_TRANSACTION_PK
        primary key (SEG_FUND_TRANS_INT_ID)
/

create table SKYTEST.SEG_FUND_TRANS_DETAILS
(
    SEG_FUND_TRANS_DETAIL_INT_ID NUMBER(8) not null,
    SEG_FUND_TRANSACTION         NUMBER(8) not null
        constraint SEG_FUND_TRANS_DETAILS_FK
            references SKYTEST.SEG_FUND_TRANSACTION,
    TRANSACTION_TYPE_DETAIL      VARCHAR2(1),
    RAW_RECORD                   VARCHAR2(700)
)
/

create index SKYTEST.SEG_FUND_TRANS_DETAILS_IDX1
    on SKYTEST.SEG_FUND_TRANS_DETAILS (SEG_FUND_TRANSACTION)
/

create index SKYTEST.SEG_FUND_TRANS_DETAILS_IDX2
    on SKYTEST.SEG_FUND_TRANS_DETAILS (SEG_FUND_TRANS_DETAIL_INT_ID)
/

alter table SKYTEST.SEG_FUND_TRANS_DETAILS
    add constraint SEG_FUND_TRANS_DET_PK
        primary key (SEG_FUND_TRANS_DETAIL_INT_ID)
/

create table SKYTEST.COMMISSION_SOURCE
(
    COMMISSION_SOURCE_INT_ID NUMBER(11) not null
        constraint COMMISSION_SOURCE_PK
            primary key,
    CREATION_DATE            DATE,
    LAST_MODIFICATION_DATE   DATE,
    START_DATE               DATE,
    END_DATE                 DATE,
    PRODUCT_SUPPLIER         NUMBER(11)
        constraint COMMISSION_SOURCE_PROD_SUPP_FK
            references SKYTEST.PRODUCT_SUPPLIER,
    FILENAME                 VARCHAR2(256),
    MGMT_COMPANY_CODE        VARCHAR2(3),
    DEALER                   VARCHAR2(4),
    SALESREP_CODE            VARCHAR2(5),
    SETTLE_DATE              DATE,
    PAYMENT_ID               VARCHAR2(128),
    INCOME_TYPE              VARCHAR2(2),
    ACCOUNT                  NUMBER(11)
        constraint COMMISSION_SOURCE_ACCOUNT_FK
            references SKYTEST.ACCOUNT,
    ACCOUNT_DETAIL           VARCHAR2(128),
    TRANSACTION_DATE         DATE,
    TRANSACTION_TYPE         VARCHAR2(2),
    TRANSACTION_ID           VARCHAR2(64),
    TRANSACTION_SOURCE       VARCHAR2(32),
    TRANSACTION_SOURCE_ID    VARCHAR2(32),
    GROSS_AMOUNT             NUMBER(12, 2),
    NET_AMOUNT               NUMBER(12, 2),
    GROSS_NET_INDICATOR      VARCHAR2(1),
    TRANSACTION_PERCENT_COMM NUMBER(9, 4),
    SEQUENCE_NUMBER          NUMBER(8),
    SPLIT_LIST               VARCHAR2(128),
    ACCOUNT_NUMBER           VARCHAR2(64),
    COMMISSION_LEVEL         NUMBER(5),
    DURATION                 NUMBER(5),
    COMMISSION_EARNED        NUMBER(12, 2),
    COMMISSION_PAID          NUMBER(12, 2),
    COMMISSION_TYPE          VARCHAR2(1),
    ADVISOR_ID               NUMBER(8),
    PROCESSED                VARCHAR2(1),
    PROCESSING_DATE          DATE,
    HOLD                     VARCHAR2(1),
    PROCESSING_STATUS        NVARCHAR2(1)
)
/

comment on column SKYTEST.COMMISSION_SOURCE.PRODUCT_SUPPLIER is 'Link to Product Supplier'
/

comment on column SKYTEST.COMMISSION_SOURCE.ACCOUNT is 'Link to Account'
/

comment on column SKYTEST.COMMISSION_SOURCE.COMMISSION_TYPE is 'B = BONUS, F =FYC, T= Trailer and R = Renewal'
/

create table SKYTEST.POLICY
(
    POLICY_INT_ID     NUMBER(8)    not null,
    POLICY_STATUS     NUMBER(4),
    PRODUCT           NUMBER(8)    not null
        constraint POLICY_PRODUCT_FK
            references SKYTEST.PRODUCT,
    ACCOUNT           NUMBER(8)    not null
        constraint POLICY_ACCOUNT_FK
            references SKYTEST.ACCOUNT,
    REQ_ISSUE_DATE    DATE,
    ISSUE_DATE        DATE,
    COMMENTS          VARCHAR2(200),
    LAST_UPDATE_DATE  DATE         not null,
    LAST_UPDATE_USER  VARCHAR2(64) not null,
    POLICY_NUMBER     VARCHAR2(15),
    POLICY_TYPE       NUMBER(4),
    AMOUNT_PAID_APP   NUMBER(9, 2),
    APP_NUMBER        VARCHAR2(64),
    LAST_CHANGED_UPLD DATE
)
/

create index SKYTEST.POLICY_IDX1
    on SKYTEST.POLICY (POLICY_INT_ID)
/

create index SKYTEST.POLICY_IDX2
    on SKYTEST.POLICY (ACCOUNT)
/

create index SKYTEST.POLICY_IDX3
    on SKYTEST.POLICY (PRODUCT)
/

alter table SKYTEST.POLICY
    add constraint POLICY_PK
        primary key (POLICY_INT_ID)
/

create table SKYTEST.POLICY_SETTLING
(
    POLICY_SETTLING_INT_ID NUMBER(8) not null
        constraint POLICY_SETTLING_POLICY_FK
            references SKYTEST.POLICY,
    FACEAMT                NUMBER(19, 4),
    ANNPREM                NUMBER(19, 4),
    MOD_PREM               NUMBER(19, 4),
    PAYF_REQ               NUMBER(4),
    SMOKER                 VARCHAR2(1),
    SMOKER_CLASS           NUMBER(11),
    ISSUE_DATE             DATE,
    COMMENTS               VARCHAR2(2000),
    STATUS_DATE            DATE,
    RATED                  VARCHAR2(1),
    LAST_USER              VARCHAR2(35),
    LAST_UPDATE            DATE,
    CLOSE_DATE             DATE,
    RECEIVED_DATE          DATE,
    PLANNED_PREMIUM        NUMBER(19, 4),
    CONVEXP_DATE           DATE,
    SETTLE_DATE            DATE,
    TERMEXP_DATE           DATE,
    TOT_ANNRID_PREM        NUMBER(19, 4),
    DEATH_DATE             DATE,
    LAPSE_DATE             DATE,
    ORIGINAL_ISSUE_DATE    DATE,
    PAID_TO_DATE           DATE,
    LAST_CHANGED_UPLD      DATE
)
/

create index SKYTEST.POLICY_SETTLING_POLICY_FK
    on SKYTEST.POLICY_SETTLING (POLICY_SETTLING_INT_ID)
/

alter table SKYTEST.POLICY_SETTLING
    add constraint POLICYIDSETT_PK
        primary key (POLICY_SETTLING_INT_ID)
/

create table SKYTEST.POLICY_TRAVEL
(
    POLICY_TRAVEL_INT_ID NUMBER(8)  not null
        constraint POLICY_TRAVEL_FK
            references SKYTEST.POLICY,
    COMPANY_ID           NUMBER(3)  not null,
    PRODUCT_ID           NUMBER(11) not null,
    POLICY_NUMBER        VARCHAR2(15),
    APP_NUMBER           VARCHAR2(10),
    POLICY_STATUS        NUMBER(4),
    PROVINCE             NUMBER(5)  not null,
    FROM_DATE            DATE,
    TO_DATE              DATE,
    NUMBER_OF_DAYS       NUMBER(5),
    ISSUE_AGE            NUMBER(2),
    APP_DATE             DATE       not null,
    APPENTERED_BY        VARCHAR2(35),
    PRICE_PER_DAY        NUMBER(9, 4),
    TOTAL_PRICE          NUMBER(9, 4),
    NOTE_ID              NUMBER(11),
    OFFICE               VARCHAR2(1),
    COMMENTS             VARCHAR2(200),
    POLICY_TYPE          VARCHAR2(1),
    DEPART_COUNTRY       NUMBER(4),
    DESTIN_COUNTRY       NUMBER(4),
    DEPARTCITY           VARCHAR2(50),
    DESTIN_CITY          VARCHAR2(50),
    DISCOUNT_RATE        NUMBER(9, 2),
    LAST_UPDATE_DATE     DATE,
    LAST_UPDATE_USER     DATE,
    CANCELLED            VARCHAR2(1)
)
/

create index SKYTEST.POLICY_TRAVEL_INDX1
    on SKYTEST.POLICY_TRAVEL (POLICY_TRAVEL_INT_ID)
/

alter table SKYTEST.POLICY_TRAVEL
    add constraint TRAVEL_POLICY_PK
        primary key (POLICY_TRAVEL_INT_ID)
/

create table SKYTEST.POLICY_UNDERWRITING
(
    POLICY_UNDERWRITING_INT_ID NUMBER(8)   not null,
    POLICY                     NUMBER(8)   not null
        constraint POLICY_UNDERWRITING_POLICY_FK
            references SKYTEST.POLICY,
    REQUIREMENT_CODE           VARCHAR2(4) not null,
    ORDERED_DATE               DATE,
    RECEIVED_DATE              DATE,
    FOLLOWUP_DATE              DATE,
    TBAR                       VARCHAR2(15),
    REFERENCE_NO               VARCHAR2(10),
    PRODUCT_CLASS              VARCHAR2(10),
    PARA_COMPANY               NUMBER(4),
    ORDERED_BY                 NUMBER(1),
    CLIENT_ID                  NUMBER(11),
    ORDER_GROUP_ID             NUMBER(11),
    LAST_UPDATE                DATE,
    LAST_UPDATE_USER           VARCHAR2(35),
    OLI_LU_REQSTAT             NUMBER(15),
    STATUS_DATE                DATE,
    REQ_CODE                   NUMBER(4),
    HAS_APPOINTMENT            VARCHAR2(1),
    HAS_COMPLET_EDATE          VARCHAR2(1),
    REQ_DETAILS_ID             NUMBER(4),
    PARTICIPANT_ID             NUMBER(11),
    REQ_TYPE                   VARCHAR2(1),
    APPOINTMENT_DATE           DATE,
    COMPLETED_DATE             DATE,
    LAPSE_DATE                 DATE,
    COMPANY_ID                 NUMBER(3),
    UNDERWRITING_REQUIREMENT   NUMBER(9)
        constraint POLICY_UNDERWRITING_UNDER_FK1
            references SKYTEST.UNDERWRITING_REQUIREMENT
)
/

create index SKYTEST.POLICY_UNDERWRITING_FK
    on SKYTEST.POLICY_UNDERWRITING (POLICY)
/

create index SKYTEST.POLICY_UNDERWRITING_IDX2
    on SKYTEST.POLICY_UNDERWRITING (POLICY_UNDERWRITING_INT_ID)
/

alter table SKYTEST.POLICY_UNDERWRITING
    add constraint UNDERWRITINGID_PK
        primary key (POLICY_UNDERWRITING_INT_ID)
/

create table SKYTEST.POLICY_DI
(
    POLICY_DI_INT_ID    NUMBER(8)    not null
        constraint POLICY_DI_FK
            references SKYTEST.POLICY,
    COMPANY_ID          NUMBER(3)    not null,
    PRODUCT_ID          NUMBER(11)   not null,
    DATE_SENT_TO_HO     DATE         not null,
    APPENTERED_BY       VARCHAR2(35) not null,
    PROVINCE            NUMBER(5)    not null,
    POLICY_STATUS       NUMBER(4),
    POLICY_TYPE         NUMBER(4),
    APP_NUMBER          VARCHAR2(20),
    CERT_NUMBER         VARCHAR2(20),
    WB_COMBO            NUMBER(11),
    TAXABLE             VARCHAR2(1),
    MODAL_PREM          NUMBER(10, 2),
    PAY_MODE            NUMBER(2),
    INCOME_CVRD         NUMBER(10, 2),
    BENEFIT_PMT         VARCHAR2(1),
    BEN_AMNT            NUMBER(10, 2),
    REQ_ISSUE_DATE      DATE,
    ISSUE_DATE          DATE,
    TEMPORARY_INSURANCE VARCHAR2(1),
    DISCOUNT_RATE       NUMBER(10, 2),
    OFFICE              VARCHAR2(1),
    COMMENTS            VARCHAR2(200),
    LAST_UPDATE_DATE    DATE,
    LAST_UPDATE_USER    VARCHAR2(35),
    FOLLOWUP_DATE       DATE,
    AMNT_WITH_APP       NUMBER(10, 2),
    ANNUAL_PREMIUM      NUMBER(19, 4),
    PAID_BY_EMPL_OYER   VARCHAR2(1),
    TOT_ANNRID_PREM     NUMBER(19, 4),
    AMORTIZPER          NUMBER(3),
    LOAN_TERM           NUMBER(3),
    WAS_JOINT           VARCHAR2(1),
    CLARICA_REF_NUM     VARCHAR2(15)
)
/

create index SKYTEST.POLICY_DI_IDX1
    on SKYTEST.POLICY_DI (POLICY_DI_INT_ID)
/

alter table SKYTEST.POLICY_DI
    add constraint POLICY_DI_PK
        primary key (POLICY_DI_INT_ID)
/

create table SKYTEST.POLICY_RIDER
(
    POLICY_RIDER_INT_ID NUMBER(8) not null
        constraint POLICY_RIDER_PK
            primary key,
    POLICY              NUMBER(8)
        constraint POLICY_RIDER_POLICY_FK
            references SKYTEST.POLICY,
    RIDER               NUMBER(8)
        constraint POLICY_RIDER_RIDER_FK
            references SKYTEST.RIDER,
    CLIENT              NUMBER(8)
        constraint POLICY_RIDER_CLIENT_FK
            references SKYTEST.CLIENT,
    RIDER_STATUS        NUMBER(4),
    AMOUNT              NUMBER(11, 2),
    PREMIUM             NUMBER(10, 2),
    MODAL_PREMIUM       NUMBER(10, 2),
    WBCOMBO             NUMBER(11),
    SETTLED_PREMIUM     NUMBER(10, 2),
    SETTLED_MODAL       NUMBER(10, 2),
    PRODUCT_CLASS       VARCHAR2(20)
)
/

create table SKYTEST.POLICY_BENEFICIARY_RIDER
(
    POLICY_BENEFICIARY_RID_INT_ID NUMBER(8)    not null,
    POLICY                        NUMBER(8)    not null
        constraint POLICY_BENEFICIARY_RIDER1_FK
            references SKYTEST.POLICY,
    RIDER                         NUMBER(8)    not null
        constraint POLICY_BENEFICIARY_RIDER2_FK
            references SKYTEST.RIDER,
    BENEFICIARY_ID                NUMBER(11)   not null,
    BENEFICIARY_TYPE              NUMBER(1)    not null,
    NAME                          VARCHAR2(60) not null,
    RELATIONSHIP                  NUMBER(4)    not null,
    PERCENTAGE                    NUMBER(7, 4) not null,
    IRREVOCABLE                   VARCHAR2(1)  not null,
    LAST_UPDATE_USER              VARCHAR2(30),
    LAST_UPDATE_DATE              DATE,
    CLIENT_ID                     NUMBER(11),
    POLICY_RIDER                  NUMBER(8)    not null
        constraint POL_BEN_RID_POLICY_RIDER_FK
            references SKYTEST.POLICY_RIDER
)
/

create index SKYTEST.POLICY_BENEFICIARY_RIDER_IDX1
    on SKYTEST.POLICY_BENEFICIARY_RIDER (POLICY)
/

create index SKYTEST.POLICY_BENEFICIARY_RIDER_IDX2
    on SKYTEST.POLICY_BENEFICIARY_RIDER (POLICY_BENEFICIARY_RID_INT_ID)
/

create index SKYTEST.POLICY_BENEFICIARY_RIDER_IDX3
    on SKYTEST.POLICY_BENEFICIARY_RIDER (RIDER)
/

alter table SKYTEST.POLICY_BENEFICIARY_RIDER
    add constraint POLICY_BENEFICIARY_RIDER_PK
        primary key (POLICY_BENEFICIARY_RID_INT_ID)
/

create table SKYTEST.POLICY_LIFE
(
    POLICY_LIFE_INT_ID   NUMBER(8)     not null
        constraint POLICY_LIFE_POLICY_FK
            references SKYTEST.POLICY,
    COMPANY_ID           NUMBER(3)     not null,
    PRODUCT_ID           NUMBER(11)    not null,
    FACE_AMOUNT          NUMBER(19, 4) not null,
    DATE_SENT_TO_HO      DATE          not null,
    PROVINCE             NUMBER(5)     not null,
    BACK_DATE            VARCHAR2(1),
    POLICY_STATUS        NUMBER(4),
    APP_STATE            NUMBER(4),
    APPENTERED_BY        VARCHAR2(35)  not null,
    FOLLOWUP_DATE        DATE,
    APP_NUMBER           VARCHAR2(15),
    TEMPORARY_INSURANCE  VARCHAR2(1),
    REQ_ISSUE_DATE       DATE,
    ISSUE_DATE           DATE,
    REPLACE_INS          VARCHAR2(1),
    PRODUCT_CLASS        VARCHAR2(10),
    POLICY_TYPE          NUMBER(4),
    OFFICE               VARCHAR2(1),
    COMMENTS             VARCHAR2(200),
    WAS_JOINT            VARCHAR2(1),
    LAST_UPDATE_DATE     DATE,
    LAST_UPDATE_USER     VARCHAR2(35),
    OLI_LU_POLSTAT       NUMBER(15),
    CASE_COORDINATOR_ID  NUMBER(11),
    UNDERWRITER_ID       NUMBER(11),
    CLARICAREFNUM        VARCHAR2(15),
    NON_MEDICAL_ANSWERED VARCHAR2(1),
    HAS_VOID_CHEQUE      VARCHAR2(1),
    PAC_AUTHORIZATION    VARCHAR2(1)
)
/

create index SKYTEST.POLICY_LIFE_INDX1
    on SKYTEST.POLICY_LIFE (POLICY_LIFE_INT_ID)
/

alter table SKYTEST.POLICY_LIFE
    add constraint LIFEPOLICY_PK
        primary key (POLICY_LIFE_INT_ID)
/

create table SKYTEST.POLICY_LIFE_RATING
(
    POLICY_LIFE_RATING_INT_ID NUMBER(8) not null,
    POLICY_LIFE               NUMBER(8) not null
        constraint POLICY_LIFE_RATING_FK
            references SKYTEST.POLICY_LIFE,
    POLICY                    NUMBER(8) not null,
    PERCENT                   NUMBER(5, 2),
    PER_THOUSAND              NUMBER(5, 2),
    LNGTH_DAYS                NUMBER(3),
    LNGTH_MONTHS              NUMBER(3),
    LNGTH_YEARS               NUMBER(3),
    LAST_UPDATE_USER          VARCHAR2(35),
    LAST_UPDATE_DATE          DATE,
    COMMENTS                  VARCHAR2(200),
    LIFE_POLICY_POLICY        NUMBER(8) not null
)
/

create index SKYTEST.POLICY_LIFE_RATING_IDX2
    on SKYTEST.POLICY_LIFE_RATING (POLICY)
/

create index SKYTEST.POLICY_LIFE_RATING_IDX3
    on SKYTEST.POLICY_LIFE_RATING (POLICY_LIFE)
/

create index SKYTEST.POLICY_LIFE_RATING_FK
    on SKYTEST.POLICY_LIFE_RATING (POLICY_LIFE_RATING_INT_ID)
/

alter table SKYTEST.POLICY_LIFE_RATING
    add constraint POLICY_LIFE_RATING_PK
        primary key (POLICY_LIFE_RATING_INT_ID)
/

create table SKYTEST.CLIENT_POLICY
(
    POLICY NUMBER(8) not null
        constraint CLIENT_POLICY_POLICY_FK
            references SKYTEST.POLICY,
    CLIENT NUMBER(8) not null
        constraint CLIENT_POLICY_CLIENT_FK
            references SKYTEST.CLIENT,
    constraint CLIENT_POLICY_PK
        primary key (POLICY, CLIENT)
)
/

create table SKYTEST.POLICY_PREMIUM
(
    PREMIUM_INT_ID       NUMBER(8)     not null,
    POLICY               NUMBER(8)     not null
        constraint POLICY_PREMIUM_POLICY_FK
            references SKYTEST.POLICY,
    PRODUCT_CLASS        VARCHAR2(10),
    PRODUCT_TYPE         VARCHAR2(10),
    ANNUAL_PREMIUM       NUMBER(19, 4) not null,
    MODAL_PREMIUM        NUMBER(19, 4),
    STANDARD_PREMIUM     NUMBER(19, 4),
    ANNUALIZED_PREMIUM   NUMBER(19, 4),
    PAYMENT_MODE         NUMBER(4)     not null,
    PREMIUM_BALANCE_DUE  NUMBER(19, 4),
    CASH_WITH_APP        NUMBER(19, 4),
    PROD_PREM_AMT        NUMBER(19, 4),
    CURRENT_PREMIUM_DUE  DATE,
    LAST_RECEIVED_DATE   DATE,
    BANK_NAME            VARCHAR2(30),
    BANK_ACCOUNT         VARCHAR2(20),
    YTD_PREMIUM_PAID     DATE,
    TARGET_PREMIUM       NUMBER(19, 4),
    SUPPLE_PREMIUM       NUMBER(19, 4),
    MINI_MUM_PREMIUM_DUE NUMBER(19, 4),
    PLANNED_PREMIUM      NUMBER(19, 4),
    PREMIUM_REFUND       NUMBER(19, 4),
    BILLING_FORM         NUMBER(2),
    LAST_UPDATE_USER     VARCHAR2(35),
    TOT_ANN_RID_PREMIUM  NUMBER(19, 4)
)
/

create index SKYTEST.POLICY_PREMIUM_IDX1
    on SKYTEST.POLICY_PREMIUM (PREMIUM_INT_ID)
/

create index SKYTEST.POLICY_PREMIUM_IDX2
    on SKYTEST.POLICY_PREMIUM (POLICY)
/

alter table SKYTEST.POLICY_PREMIUM
    add constraint PREMIUM_PK
        primary key (PREMIUM_INT_ID)
/

create table SKYTEST.POLICY_GROUP_BEN
(
    POLICY_GROUP_BEN_INT_ID NUMBER(8)    not null
        constraint POLICY_GROUP_BEN_FK
            references SKYTEST.POLICY,
    COMPANY_ID              NUMBER(3)    not null,
    PRODUCT_ID              NUMBER(11)   not null,
    DATE_SENT_TO_HO         DATE         not null,
    APPENTERED_BY           VARCHAR2(35) not null,
    PROVINCE                NUMBER(5)    not null,
    POLICY_STATUS           NUMBER(4),
    POLICY_TYPE             NUMBER(4),
    APP_NUMBER              VARCHAR2(20),
    CERT_NUMBER             VARCHAR2(20),
    TAXABLE                 VARCHAR2(1),
    INCOME_CVRD             NUMBER(10, 2),
    BENEFIT_PMT             VARCHAR2(1),
    BEN_AMNT                NUMBER(10, 2),
    MODAL_PREM              NUMBER(10, 2),
    PAY_MODE                NUMBER(2),
    ANNUAL_PREMIUM          NUMBER(19, 4),
    TOT_ANNRID_PREM         NUMBER(19, 4),
    AMNT_WITH_APP           NUMBER(10, 2),
    REQ_ISSUE_DATE          DATE,
    ISSUE_DATE              DATE,
    TEMPORARY_INSURANCE     VARCHAR2(1),
    DISCOUNT_RATE           NUMBER(10, 2),
    OFFICE                  VARCHAR2(1),
    COMMENTS                VARCHAR2(200),
    LAST_UPDATE_DATE        DATE,
    LAST_UPDATE_USER        VARCHAR2(35),
    PAID_BY_EMPLOYER        VARCHAR2(1)
)
/

create index SKYTEST.POLICY_GROUP_BEN_INDX
    on SKYTEST.POLICY_GROUP_BEN (POLICY_GROUP_BEN_INT_ID)
/

alter table SKYTEST.POLICY_GROUP_BEN
    add constraint GROUP_BEN_POLICY_PK
        primary key (POLICY_GROUP_BEN_INT_ID)
/

create table SKYTEST.POLICY_RIDER_INSURED
(
    POLICY_RIDER_INS_INT_ID NUMBER(8)    not null,
    POLICY                  NUMBER(8)    not null
        constraint POLICY_RIDER_INSURED_POLICY_FK
            references SKYTEST.POLICY,
    RIDER                   NUMBER(8)    not null
        constraint POLICY_RIDER_INSURED_RIDER_FK
            references SKYTEST.RIDER,
    CLIENT_ID               NUMBER(11),
    INSURED_ID              NUMBER(11),
    INSURED_NAME            VARCHAR2(70) not null,
    INSURED_BIRTH_DAY       DATE,
    INSURED_AGE             NUMBER(2),
    INSURED_GENDER          VARCHAR2(1),
    POLICY_RIDER            NUMBER(8)    not null
        constraint POL_RID_INS_POLICY_RIDER_FK
            references SKYTEST.POLICY_RIDER
)
/

create index SKYTEST.POLICY_RIDER_INSURED_IDX1
    on SKYTEST.POLICY_RIDER_INSURED (POLICY_RIDER_INS_INT_ID)
/

create index SKYTEST.POLICY_RIDER_INSURED_IDX2
    on SKYTEST.POLICY_RIDER_INSURED (POLICY)
/

create index SKYTEST.POLICY_RIDER_INSURED_IDX3
    on SKYTEST.POLICY_RIDER_INSURED (RIDER)
/

alter table SKYTEST.POLICY_RIDER_INSURED
    add constraint POLICY_RIDER_INSURED_PK
        primary key (POLICY_RIDER_INS_INT_ID)
/

create table SKYTEST.POLICY_BENEFICIARY
(
    BENEFICIARY_INT_ID NUMBER(8) not null
        constraint POLICY_BENEFICIARY_PK
            primary key,
    POLICY             NUMBER(8) not null
        constraint POLICY_BENEFICIARY_FK
            references SKYTEST.POLICY,
    LEVEL_SEQ          NUMBER(2) not null,
    IRREVOCABLE        VARCHAR2(1),
    RELATIONSHIP       NUMBER(4) not null,
    PERCENTAGE         NUMBER(7, 4),
    PRIMARY_CONTINGENT VARCHAR2(1),
    PRODUCT_CLASS      VARCHAR2(10),
    NAME               VARCHAR2(60),
    BENEFICIARY_ID     NUMBER(11),
    LAST_UPDATE_USER   VARCHAR2(30),
    LAST_UPDATE_DATE   DATE,
    BIRTH_DATE         DATE,
    GENDER             VARCHAR2(1),
    FD_PROFILE         NUMBER(11),
    BENEFICIARY_TYPE   NUMBER(4),
    RELATIONSHIP_TYPE  NUMBER(4)
)
/

create index SKYTEST.POLICY_BENEFICIARY_POLICY_FK
    on SKYTEST.POLICY_BENEFICIARY (POLICY)
/

create table SKYTEST.AGENCY_PROFILE
(
    AGENCY_PROFILE         NUMBER(8) not null
        constraint AGENCY_PROFILE_PK
            primary key,
    AGENCY                 NUMBER(8) not null
        constraint AGENCY_PROFILE_FK1
            references SKYTEST.AGENCY,
    PRODUCT_SUPPLIER       NUMBER(8) not null
        constraint AGENCY_PROFILE_FK2
            references SKYTEST.PRODUCT_SUPPLIER,
    ALBERTA                VARCHAR2(1),
    BRITISH_COLUMBIA       VARCHAR2(1),
    MANITOBA               VARCHAR2(1),
    NEW_BRUNSWICK          VARCHAR2(1),
    NEWFOUNDLAND           VARCHAR2(1),
    NORTHWEST_TERRITORIES  VARCHAR2(1),
    NOVA_SCOTIA            VARCHAR2(1),
    NUNAVUT                VARCHAR2(1),
    ONTARIO                VARCHAR2(1),
    PRINCE_EDWARD_ISLAND   VARCHAR2(1),
    QUEBEC                 VARCHAR2(1),
    SASKATCHEWAN           VARCHAR2(1),
    YUKON_TERRITORY        VARCHAR2(1),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE
)
/

create table SKYTEST.ACCOUNT_CLIENT_OWNERSHIP
(
    CLIENT_ACC_OWNERSHIP_INT_ID NUMBER(8) not null
        constraint ACCOUNT_CLIENT_OWNERSHIP_PK
            primary key,
    ACCOUNT                     NUMBER(8)
        constraint ACC_CLIENT_OWNER_ACCOUNT_FK
            references SKYTEST.ACCOUNT,
    CLIENT                      NUMBER(8)
        constraint ACC_CLIENT_OWNER_CLIENT_FK
            references SKYTEST.CLIENT,
    CREATION_DATE               DATE,
    LAST_MODIFICATION_DATE      DATE,
    OWNERSHIP_TYPE              NUMBER(4),
    POLICY                      NUMBER(8)
        constraint ACCOUNT_CLIENT_OWNER_POLICY_FK
            references SKYTEST.POLICY,
    ISSUE_AGE                   NUMBER(3),
    SMOKER                      VARCHAR2(1),
    SMOKER_CLASS                NUMBER(11)
)
/

create table SKYTEST.POLICY_UW_COMMENTS
(
    POLICY_UW_COMMENTS_INT_ID NUMBER(8)  not null,
    POLICY_UNDERWRITING       NUMBER(8)  not null
        constraint POLICY_UW_COMMENTS_FK
            references SKYTEST.POLICY_UNDERWRITING,
    POLICY                    NUMBER(8)  not null,
    COMMENT_ID                NUMBER(11) not null,
    COMMENT_DATE              DATE       not null,
    COMMENTS                  VARCHAR2(2500),
    USER_NAME                 VARCHAR2(35),
    LAST_UPDATE               DATE,
    LAST_UPDATE_USER          VARCHAR2(35),
    IS_PRIVATE                VARCHAR2(1),
    STATUS_EVENT_CODE         NUMBER(11),
    REQUIREMENT_FILE          VARCHAR2(100)
)
/

create index SKYTEST.POLICY_UW_COMMENTS_IDX1
    on SKYTEST.POLICY_UW_COMMENTS (POLICY_UW_COMMENTS_INT_ID)
/

create index SKYTEST.POLICY_UW_COMMENTS_IDX2
    on SKYTEST.POLICY_UW_COMMENTS (POLICY_UNDERWRITING)
/

create index SKYTEST.POLICY_UW_COMMENTS_IDX3
    on SKYTEST.POLICY_UW_COMMENTS (POLICY)
/

alter table SKYTEST.POLICY_UW_COMMENTS
    add constraint IMUWCOMMENTS_PK
        primary key (POLICY_UW_COMMENTS_INT_ID)
/

create table SKYTEST.ANNOUNCEMENT
(
    PRODUCT_SUPPLIER    NUMBER(8)
        constraint PRODUCT_SUPLIER2_FK
            references SKYTEST.PRODUCT_SUPPLIER,
    ANNOUNCEMENT_INT_ID NUMBER(8) not null
        primary key,
    CATEGORY_TYPE       NUMBER(4),
    START_DATE          DATE,
    END_DATE            DATE,
    PROFILE_TYPES       NUMBER(4),
    IS_HTML             VARCHAR2(1),
    TITLE_EN            VARCHAR2(255),
    TITLE_RF            VARCHAR2(255),
    CONTENT_EN          BLOB,
    CONTENT_FR          BLOB,
    CONTENT_EN_HTML     BLOB,
    CONTENT_FR_HTML     BLOB,
    URL_EN              VARCHAR2(255),
    URL_FR              VARCHAR2(255),
    MOBILE_ACCESS       VARCHAR2(1),
    VIP                 NUMBER(1)
)
/

comment on column SKYTEST.ANNOUNCEMENT.CATEGORY_TYPE is ' ie: product news, sales & promotions ...'
/

comment on column SKYTEST.ANNOUNCEMENT.PROFILE_TYPES is 'this says to who is allow'
/

comment on column SKYTEST.ANNOUNCEMENT.IS_HTML is 'Y = html ,  N = pdf, V = video, U = url'
/

comment on column SKYTEST.ANNOUNCEMENT.MOBILE_ACCESS is 'Allow access or not to the mobile'
/

create table SKYTEST.PRODUCTSUPPLIER_FORMS
(
    PRODUCTSUPPLIER_FORMS_INT_ID NUMBER(11)  not null
        constraint PRODUCTSUPPLIER_FORMS_PK
            primary key,
    PRODUCT_SUPPLIER             NUMBER(11)  not null
        constraint PRODUCTSUPPLIER_FORMS_FK1
            references SKYTEST.PRODUCT_SUPPLIER,
    DASHBOARD_TYPE               NUMBER(4)   not null,
    FORM_TYPE                    NUMBER(4)   not null,
    FORM_TITLE_ENG               VARCHAR2(100),
    FORM_TITLE_FRE               VARCHAR2(100),
    FORM_ENG                     BLOB        not null,
    FORM_FRE                     BLOB        not null,
    WEBSITE_TYPE                 VARCHAR2(2) not null,
    AGENCY                       NUMBER(11)
        constraint PRODUCTSUPPLIER_FORMS_AGE_FK1
            references SKYTEST.AGENCY,
    START_DATE                   DATE,
    END_DATE                     DATE
)
/

create index SKYTEST.INDEX20
    on SKYTEST.PRODUCTSUPPLIER_FORMS (AGENCY, DASHBOARD_TYPE, WEBSITE_TYPE)
/

create index SKYTEST.INDEX21
    on SKYTEST.PRODUCTSUPPLIER_FORMS (AGENCY)
/

create index SKYTEST.INDEX22
    on SKYTEST.PRODUCTSUPPLIER_FORMS (DASHBOARD_TYPE)
/

create index SKYTEST.INDEX23
    on SKYTEST.PRODUCTSUPPLIER_FORMS (WEBSITE_TYPE)
/

create table SKYTEST.EAPPLICATION
(
    EAPPLICATION_INT_ID    NUMBER(9) not null
        constraint EAPPLICATION_PK
            primary key,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    APPLICATION_NUMBER     VARCHAR2(64),
    POLICY_NUMBER          VARCHAR2(64),
    PRODUCT_SUPPLIER       NUMBER(9) not null
        constraint EAPP_PRODUCT_SUPPLIER_FK
            references SKYTEST.PRODUCT_SUPPLIER,
    FILENAME               VARCHAR2(128),
    PROCESSED              VARCHAR2(1),
    MGA                    VARCHAR2(20),
    EAPPLICATION_ID        VARCHAR2(20),
    PDF_FILE               BLOB,
    LAST_UPDATE_USER       VARCHAR2(64),
    EFFECTIVE_DATE         DATE
)
/

create table SKYTEST.PROMOTIONS
(
    PROMOTION_INT_ID NUMBER(8) not null
        primary key,
    START_DATE       DATE,
    END_DATE         DATE,
    PROMO_CODE       VARCHAR2(50),
    COMPANY_ID       NUMBER(8),
    TERM_OR_PERM     VARCHAR2(2),
    PROMO_PERCENT    NUMBER(3),
    ITERATIONS       NUMBER(3),
    RIDERS_APPLY     VARCHAR2(2),
    RENEWALS_APPLY   VARCHAR2(2),
    ANNOUNCEMENT     NUMBER(8)
        constraint ANNOUNCEMENT_FK
            references SKYTEST.ANNOUNCEMENT,
    DESC_EN          VARCHAR2(1000),
    DESC_FR          VARCHAR2(1000),
    BUILT_IN         VARCHAR2(2),
    YEARS            NUMBER(2),
    MIN_PREMIUM      NUMBER(8),
    RATES_APPLY      VARCHAR2(2),
    MAX_AGE          NUMBER(2),
    PERIOD_MAX       NUMBER(2),
    TEST_ZONE        VARCHAR2(2)
)
/

comment on column SKYTEST.PROMOTIONS.START_DATE is 'date when start the promotion'
/

comment on column SKYTEST.PROMOTIONS.END_DATE is 'date when ends the promotion'
/

comment on column SKYTEST.PROMOTIONS.PROMO_CODE is 'if promotion is activated by a code'
/

comment on column SKYTEST.PROMOTIONS.TERM_OR_PERM is 'wich product is apply for(think will not be used)'
/

comment on column SKYTEST.PROMOTIONS.PROMO_PERCENT is 'percent to apply the promo'
/

comment on column SKYTEST.PROMOTIONS.ITERATIONS is 'iteration for renewals'
/

comment on column SKYTEST.PROMOTIONS.RIDERS_APPLY is 'If riders renewals apply'
/

comment on column SKYTEST.PROMOTIONS.RENEWALS_APPLY is 'If renewals apply'
/

comment on column SKYTEST.PROMOTIONS.ANNOUNCEMENT is 'announcement linked'
/

comment on column SKYTEST.PROMOTIONS.BUILT_IN is 'if is a build in (allways apply)'
/

comment on column SKYTEST.PROMOTIONS.YEARS is '(not in use for now )'
/

comment on column SKYTEST.PROMOTIONS.MIN_PREMIUM is 'minimun premium to apply the promo'
/

comment on column SKYTEST.PROMOTIONS.RATES_APPLY is 'If rates apply (this goes to rate in selft)'
/

comment on column SKYTEST.PROMOTIONS.MAX_AGE is 'maximun age where will be apply the promotion'
/

comment on column SKYTEST.PROMOTIONS.PERIOD_MAX is 'the top period where will be applyed'
/

comment on column SKYTEST.PROMOTIONS.TEST_ZONE is 'if the promotion is able to try on test zone'
/

create table SKYTEST.CONTACT_ACTION_PRODUCT_SUPPLIER
(
    CONTACT_ACTION   NUMBER(8)
        constraint CONTACT_ACTION_FK_FK
            references SKYTEST.CONTACT_ACTION,
    PRODUCT_SUPPLIER NUMBER(8)
        constraint PRODUCT_SUPPLIER_FK_FK
            references SKYTEST.PRODUCT_SUPPLIER
)
/

create table SKYTEST.PROMOTIONS_DESCRIPTION
(
    PROMOTIONS_DESCRIPTION_INT_ID NUMBER(8) not null
        primary key,
    MIN_AGE                       NUMBER(3),
    MAX_AGE                       NUMBER(3),
    MIN_FACE_AMOUNT               NUMBER(10),
    MAX_FACE_AMOUNT               NUMBER(10),
    PROMOTIONS                    NUMBER(8)
        constraint PROMOTION_DESC_PROMOTION_FK
            references SKYTEST.PROMOTIONS,
    PROMO_PERCENT                 NUMBER(3)
)
/

comment on column SKYTEST.PROMOTIONS_DESCRIPTION.PROMO_PERCENT is 'percent to apply the promo(if not set or 0 means it will use global)'
/

create table SKYTEST.PRODUCT_SUPPLIER_TREE
(
    PRODUCT_SUPPLIER_IC          NUMBER(8)
        constraint PRODUCT_SUP_IC_FK
            references SKYTEST.PRODUCT_SUPPLIER,
    PRODUCT_SUPPLIER_MGA         NUMBER(8)
        constraint PRODUCT_SUP_MGA_FK
            references SKYTEST.PRODUCT_SUPPLIER,
    PRODUCT_SUPPLIER_MGA_AGA     NUMBER(8)
        constraint PRODUCT_SUP_MGA_AGA_FK
            references SKYTEST.PRODUCT_SUPPLIER,
    PRODUCT_SUPPLIER_MGA_AGA_AGA NUMBER(8)
        constraint PRODUCT_SUP_MGA_AGA_AGA_FK
            references SKYTEST.PRODUCT_SUPPLIER
)
/

-- Cyclic dependencies found

create table SKYTEST.ACCOUNT_CONTRACT_SETUP
(
    ACCOUNT        NUMBER not null
        constraint ACCT_CONTRACT_SETUP_FK
            references SKYTEST.ACCOUNT,
    CONTRACT_SETUP NUMBER not null
        constraint ACCOUNT_CONTRACT_SETUP_FK1
            references SKYTEST.CONTRACT_SETUP,
    constraint ACCOUNT_CONTRACT_SETUP_PK
        primary key (ACCOUNT, CONTRACT_SETUP)
)
/

comment on table SKYTEST.ACCOUNT_CONTRACT_SETUP is 'This table is used for keeping track of the Account/Contract relationship.  An  shared Account (aka a split) will result in two entries in this joint table where ContractSetup point to two seperate contract.'
/

-- Cyclic dependencies found

create table SKYTEST.ADVISOR_BUSINESS_STAT
(
    ADVISOR_BIZ_STAT_INT_ID NUMBER not null
        constraint ADVISOR_BUSINESS_STAT_PK
            primary key,
    CREATION_DATE           DATE,
    LAST_MODIFICATION_DATE  DATE,
    ADVISOR                 NUMBER(8)
        constraint ADVISOR_BUSINESS_STAT_ADV_FK1
            references SKYTEST.ADVISOR,
    PRODUCT_CLASS           NUMBER(8)
        constraint ADVISOR_BUSINESS_STAT_PRO_FK1
            references SKYTEST.PRODUCT_CLASS,
    LAST_NEW_BUSINESS       DATE,
    TOTAL_ANNUAL_ACCOUNT    NUMBER(6),
    TOTAL_ANNUAL_BUSINESS   NUMBER(16, 4),
    PROVINCE                VARCHAR2(2),
    PRODUCT_SUPPLIER        NUMBER(8)
        constraint ADVISOR_BUSINESS_STAT_FK1
            references SKYTEST.PRODUCT_SUPPLIER,
    STATUS                  NUMBER(4),
    START_DATE              DATE,
    END_DATE                DATE,
    TOTAL_NUMBER            NUMBER(8),
    TOTAL_AMOUNT            NUMBER(16, 2),
    TYPE                    NUMBER(4),
    TOTAL_NUMBER2           NUMBER(8),
    TOTAL_NUMBER3           NUMBER,
    AGENCY                  NUMBER(8)
        constraint ADVISOR_BUSINESS_STAT_FK2
            references SKYTEST.AGENCY,
    TOTAL_NUMBER4           NUMBER(8),
    TOTAL_NUMBER5           NUMBER(8),
    TOTAL_NUMBER6           NUMBER(8)
)
/

comment on column SKYTEST.ADVISOR_BUSINESS_STAT.ADVISOR is 'Owning Advisor or AGA'
/

comment on column SKYTEST.ADVISOR_BUSINESS_STAT.STATUS is '1= INFORCE, 2 = PENDING 11 = Active, 12 = Inactive, 13=Expired, 14 = Lapse, 15 = Renewed'
/

comment on column SKYTEST.ADVISOR_BUSINESS_STAT.TYPE is '1 = POLICY, 2=Client, 10=CONTRACT, 11=LICENSE, 12=LIABILITY'
/

comment on column SKYTEST.ADVISOR_BUSINESS_STAT.AGENCY is 'Owning Agency'
/

-- Cyclic dependencies found

create table SKYTEST.ADVISOR_POLICY_PROFILE
(
    ADVISOR_POLICY_PROF_INT_ID NUMBER(8) not null
        constraint ADVISOR_POLICY_PROFILE_PK
            primary key,
    ADVISOR                    NUMBER(8) not null
        constraint ADVISOR_POLICY_PROFILE_AD_FK
            references SKYTEST.ADVISOR,
    POLICY                     NUMBER(8)
        constraint ADVISOR_POLICY_PROFILE_PO_FK
            references SKYTEST.POLICY,
    PERCENTAGE                 NUMBER(5, 2),
    CONTRACT                   NUMBER(11),
    ACCOUNT                    NUMBER(8)
        constraint ADVISOR_POLICY_PROFILE_AC_FK1
            references SKYTEST.ACCOUNT,
    ADVISOR_ROLE               NUMBER(4)
)
/

comment on table SKYTEST.ADVISOR_POLICY_PROFILE is 'This table is used for keeping track of the percentage split between Advisor/Account.  No split means 100% on percentage'
/

comment on column SKYTEST.ADVISOR_POLICY_PROFILE.ADVISOR_ROLE is 'TYPE_CLASS = 66557 '
/

-- Cyclic dependencies found

create table SKYTEST.BRANCH
(
    BRANCH_INT_ID           NUMBER(8) not null
        constraint BRANCH_PK
            primary key,
    CREATION_DATE           DATE,
    LAST_MODIFICATION_DATE  DATE,
    BRANCH_CODE             VARCHAR2(20),
    BRANCH_PRIMARY_NAME     VARCHAR2(64),
    BRANCH_OTHER_NAME       VARCHAR2(64),
    ORGANIZATION            NUMBER(8)
        constraint BRANCH_FK1
            references SKYTEST.ORGANIZATION,
    ADDRESS                 NUMBER(8)
        constraint BRANCH_FK2
            references SKYTEST.ADDRESS,
    PHONE                   NUMBER(8)
        constraint BRANCH_FK4
            references SKYTEST.PHONE,
    EMAIL                   NUMBER(8)
        constraint BRANCH_FK3
            references SKYTEST.EMAIL,
    BRANCH_NUMBER           NUMBER(8),
    MARKETING_REGION        NUMBER(8),
    DISTRICT_OFFICE         VARCHAR2(10),
    DISTRICT_NUMBER         NUMBER(10)
        constraint BRANCH_DISTRICT
            references SKYTEST.DISTRICT (NUMBER_CODE),
    MARKETING_REGION_OFFICE VARCHAR2(10),
    MAIN_CONTACT            NUMBER(10)
        constraint BRANCH_MAIN_CONTACT
            references SKYTEST.CONTACT,
    MANAGER                 NUMBER(10)
        constraint BRANCH_MANAGER
            references SKYTEST.CONTACT
)
/

-- Cyclic dependencies found

create table SKYTEST.HEIGHT_WEIGHT
(
    HEIGHT_WEIGHT_INT_ID NUMBER(8) not null
        primary key,
    PUBLIC1              NUMBER(1),
    NAME                 VARCHAR2(255),
    DESCRIPTION          VARCHAR2(255),
    OWNER                NUMBER(8)
        constraint HEIGHT_WEIGHT_OWNER_FK
            references SKYTEST.PROFILE,
    NAME_FR              VARCHAR2(255),
    DESCRIPTION_FR       VARCHAR2(255),
    PRODUCT_SUPPLIER     NUMBER(8)
        constraint PRODUCT_SUPPLIER_HEIGHT_FK
            references SKYTEST.PRODUCT_SUPPLIER
)
/

comment on column SKYTEST.HEIGHT_WEIGHT.PUBLIC1 is '0 --- PUBLIC (DEFAULT)
1--- PRIVATE (COMPANIES)'
/

create table SKYTEST.SURVEY_QUESTION
(
    SURVEY_QUESTION_INT_ID NUMBER(11) not null
        constraint SURVEY_QUESTION_PK
            primary key,
    SEQUENCE               NUMBER(4),
    TEXT                   VARCHAR2(1024),
    TEXT_FR                VARCHAR2(1024),
    SUB_QUESTION           VARCHAR2(1),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    MODIFIED_BY            VARCHAR2(128),
    SURVEY_SECTION         NUMBER(11)
        constraint SURVEY_QUESTION_FK1
            references SKYTEST.SURVEY_SECTION,
    PARENT_QUESTION        NUMBER(11),
    TYPE                   NUMBER(4),
    POST_TEXT              VARCHAR2(4000),
    POST_TEXT_FR           VARCHAR2(4000),
    SUB_QUESTION_LABEL     VARCHAR2(20),
    NO_ANSWER              VARCHAR2(1),
    QUESTION_LABEL         VARCHAR2(20),
    CHOICE_LABEL_01        VARCHAR2(20),
    CHOICE_LABEL_02        VARCHAR2(20),
    CHOICE_LABEL_03        VARCHAR2(20),
    CHOICE_LABEL_04        VARCHAR2(20),
    CHOICE_LABEL_05        VARCHAR2(20),
    CHOICE_TEXT_01         VARCHAR2(256),
    CHOICE_TEXT_02         VARCHAR2(256),
    CHOICE_TEXT_03         VARCHAR2(256),
    CHOICE_TEXT_04         VARCHAR2(256),
    CHOICE_TEXT_05         VARCHAR2(256),
    CHOICE_TEXT_FR_01      VARCHAR2(256),
    CHOICE_TEXT_FR_02      VARCHAR2(256),
    CHOICE_TEXT_FR_03      VARCHAR2(256),
    CHOICE_TEXT_FR_04      VARCHAR2(256),
    CHOICE_TEXT_FR_05      VARCHAR2(256),
    CHOICE_SCORE_01        NUMBER(4),
    CHOICE_SCORE_02        NUMBER(4),
    CHOICE_SCORE_03        NUMBER(4),
    CHOICE_SCORE_04        NUMBER(4),
    CHOICE_SCORE_05        NUMBER(4),
    HEIGHT_WEIGHT          NUMBER(8)
        constraint SURVEY_QUESTION_FK2
            references SKYTEST.HEIGHT_WEIGHT
)
/

comment on column SKYTEST.SURVEY_QUESTION.SURVEY_SECTION is 'FK TO SURVEY_QUESTION'
/

comment on column SKYTEST.SURVEY_QUESTION.PARENT_QUESTION is 'SOFT LINK TO PARENT QUESTION'
/

comment on column SKYTEST.SURVEY_QUESTION.TYPE is '1= YES/NO  2 = Multiple Choice'
/

create table SKYTEST.HEIGHT_WEIGHT_PRODUCT_SUPPLIER
(
    HEIGHT_WEIGHT    NUMBER(8)
        constraint HEIGHT_WEIGHT_SUPPLIER_FK
            references SKYTEST.HEIGHT_WEIGHT,
    PRODUCT_SUPPLIER NUMBER(8)
        constraint PRODUCT_SUPPLIER_HEIGHT_WEIGHT_FK
            references SKYTEST.PRODUCT_SUPPLIER
)
/

-- Cyclic dependencies found

create table SKYTEST.ONBOARDING
(
    ONBOARDING_INT_ID NUMBER(10) not null
        constraint SYS_C0025665
            primary key,
    PRODUCT_SUPPLIER  NUMBER(10)
        constraint CHECK_PRODUCT_SUPPLIER_FK
            references SKYTEST.PRODUCT_SUPPLIER,
    CREATIONT_DATE    DATE,
    DESCRIPTION       VARCHAR2(500),
    NAME              VARCHAR2(100),
    SYSTEM_CREATED    NUMBER(1),
    FOR_TO            NUMBER(2),
    NAME_FR           VARCHAR2(100),
    DESCRIPTION_FR    VARCHAR2(500),
    ORDER_NO          NUMBER(2),
    NEEDS_REPLY       NUMBER(1),
    STORED_FILE       NUMBER(10)
        constraint ONBOARDING_STORED_FILE_FK
            references SKYTEST.STORED_FILE
)
/

comment on column SKYTEST.ONBOARDING.FOR_TO is '1 - Address , 2 - Advisor , 3 - Licensee , 4 - Contact , 5 - Contract , 6 - Contract EFT , 7 - Contract Setup , 8 - Email , 9 - License , 10 - License Liability , 11 - Phone , 12 - File , 13 - User'
/

comment on column SKYTEST.ONBOARDING.NEEDS_REPLY is '0 if no - 1 if yes'
/

comment on column SKYTEST.ONBOARDING.STORED_FILE is 'if exist will means that it will sent to the user and the user will need to read it and reply/sign it or fill it if needed'
/

-- Cyclic dependencies found

create table SKYTEST.ONBOARDING_STATUS
(
    ONBOARDING_STATUS_INT_ID NUMBER(10) not null
        primary key,
    ONBOARDING               NUMBER(10) not null
        constraint ONBOARDING_STATUS_FK
            references SKYTEST.ONBOARDING,
    STATUS                   NUMBER(2)  not null,
    CONTACT                  NUMBER(10)
        constraint ONBOARDING_CONTACT_FK
            references SKYTEST.CONTACT
)
/

comment on column SKYTEST.ONBOARDING_STATUS.STATUS is '66577 from type_class'
/

create table SKYTEST.STORED_FILE
(
    STORED_FILE_INT_ID NUMBER(8) not null
        constraint SYS_C0064213
            primary key,
    TYPE_              NUMBER(4),
    TYPE_ID            NUMBER(8),
    CREATION_DATE      DATE,
    NAME               VARCHAR2(255),
    CREATED_FROM       VARCHAR2(255),
    FILE_TYPE          NUMBER(8),
    AMOUNT             NUMBER(10),
    AVAILABLE_DATE     DATE,
    NOTE               VARCHAR2(500),
    NOTE_FR            VARCHAR2(500),
    CREATED_BY         NUMBER(8),
    EXTENSION          VARCHAR2(8),
    ULR                VARCHAR2(255),
    VIP                NUMBER(2),
    NAME_FR            VARCHAR2(255),
    ULR_FR             VARCHAR2(255),
    UNAVAILABLE_DATE   DATE,
    PRODUCT_SUPPLIER   NUMBER(8)
        constraint STORED_FILE_PRODUCT_SUPPLIER_FK
            references SKYTEST.PRODUCT_SUPPLIER,
    POLICY             NUMBER(8)
        constraint STORED_FILE_POLICY_FK
            references SKYTEST.POLICY,
    FILE_TYPE_CLASS    NUMBER(8),
    POLICY_NUMBER      VARCHAR2(16),
    ONBOARDING         NUMBER(10)
        constraint STORED_FILE_ONBOARDING_FK
            references SKYTEST.ONBOARDING_STATUS,
    TYPE_CODE_HIGHER   NUMBER(8)
)
/

comment on column SKYTEST.STORED_FILE.TYPE_ is '1 ---- opportunity

2 ---- report
***********cloud documents*******

3 ---- Company

4 ---- Lisense

5 ---- EandO Policy

6 ---- Banking

7 ---- Contracts
 
8 ---- Activity
9------DOCUMENTS (Product Supplier)
10 ---- DASHBOARD (Product Supplier) then type_class =                   66377
11---- Advisor
12----Contact
13----Lead
14----product supplier for onboarding
15----onboarding
16----onboarding_status'
/

comment on column SKYTEST.STORED_FILE.FILE_TYPE is '*****TYPE_CLASS*** 
TYPE_INT_ID(TYPES)


3 ---- 66037

4 ---- 66038

5 ---- 66039

6 ---- 66040

7 ---- 66041 (already not in use)

8 ---- 66046 (temporal - permanent)


*****for the document view this will store the types_id

**** for contract_eft we will store if they are check or the other type'
/

comment on column SKYTEST.STORED_FILE.VIP is '0 --- private   1----public'
/

comment on column SKYTEST.STORED_FILE.POLICY is 'only filled when are contact file for gabinet tab'
/

comment on column SKYTEST.STORED_FILE.FILE_TYPE_CLASS is 'typeclass for the gabinet and then on file_type is the type'
/

comment on column SKYTEST.STORED_FILE.POLICY_NUMBER is 'this will fill if policy exist or not (gabinet)'
/

comment on column SKYTEST.STORED_FILE.ONBOARDING is 'this is the status'
/

comment on column SKYTEST.STORED_FILE.TYPE_CODE_HIGHER is 'this is for the new insert in my account for store business or personal 66660 or 66719'
/

create table SKYTEST.STORED_FILE_NOTE
(
    STORED_FILES_NOTE_INT_ID NUMBER(8) not null
        constraint SYS_C0024677
            primary key,
    CREATION_DATE            DATE,
    NOTE_EN                  VARCHAR2(1000),
    NOTE_FR                  VARCHAR2(1000),
    STORED_FILE              NUMBER(8)
        constraint STORED_FILE_NOTES_STORED_FILE_FK
            references SKYTEST.STORED_FILE,
    FILE_TYPE                NUMBER(8)
)
/

-- Cyclic dependencies found

create table SKYTEST.ACTIVITY_ATTACHMENT
(
    ACTIVITY    NUMBER(8)
        constraint ACTIVITY_ATTACHMENT_FK
            references SKYTEST.ACTIVITY,
    STORED_FILE NUMBER(8)
        constraint ATTACHMENT_ACTIVITY_FK
            references SKYTEST.STORED_FILE
)
/

-- Cyclic dependencies found

create table SKYTEST.ADVISOR
(
    ADVISOR_INT_ID          NUMBER(8)     not null
        constraint ADVISOR_CONTACT
            references SKYTEST.CONTACT,
    ADVISOR_ID              NUMBER(8)     not null,
    NAME                    VARCHAR2(256) not null,
    STATUS                  NUMBER(4),
    CREATION_DATE           DATE,
    LAST_MODIFICATION_DATE  DATE,
    SIN                     VARCHAR2(11),
    INSURFACT_CUSTOMER      VARCHAR2(1),
    ADVISOR_NUMBER          VARCHAR2(15),
    AIRMILES_NUM            VARCHAR2(15),
    AIRMILES_NAME_ON_CARD   VARCHAR2(70),
    SUN_TERMINATED          VARCHAR2(1),
    SUN_DEL_DATE            DATE,
    FIRST_CONTACT           DATE,
    BLKS_OLD                VARCHAR2(1),
    BLKS_OLD_DATE           DATE,
    BLKS_OLD_AGENT_ID       NUMBER(11),
    DRIVERS_LICENSE         VARCHAR2(30),
    SUN_TERM_DATE           DATE,
    TRANS_DATE              DATE,
    TRANS_AGENT_ID          NUMBER(11),
    SUNLIFE_BONUS           NUMBER(5, 2),
    SUNLIFE_BONUS_DATE      DATE,
    SUN_VIP                 VARCHAR2(1),
    COPOADMIN               NUMBER(11),
    SALES_DIR_PCNT          NUMBER(5, 2),
    SUNLIFE_ADVISOR         VARCHAR2(20),
    NOTE                    VARCHAR2(1024),
    CORPORATE_NAME          VARCHAR2(256),
    ADVISOR_TYPE            NUMBER(4),
    SALES_DIRECTOR          VARCHAR2(1),
    INVEST_PERCENT_SHARE    NUMBER(5, 2),
    LAST_UPDATE_USER        VARCHAR2(64),
    MASTER_CODE             VARCHAR2(16),
    MASTER_GROUP            NUMBER(8)
        constraint ADVISOR_FK1
            references SKYTEST.MASTER_GROUP,
    USER_HOME               VARCHAR2(256),
    TOTAL_STORAGE_GIGABYTES NUMBER(5, 2),
    OLD_AGA_ID              NUMBER(8),
    OTHER_NAME              VARCHAR2(256),
    AGA_TYPE                NUMBER(4),
    EXCLUSIVE_ASSOCIATION   VARCHAR2(1),
    SHARE_ADDRESS           VARCHAR2(1),
    SHARE_LIABILITY         VARCHAR2(1),
    AGA_AGENCY_ID           NUMBER(8),
    SHARED_LIABILITY_ID     NUMBER(8),
    ONBOARDING              NUMBER(10)
        constraint ADVISOR_ONBOARDING_FK
            references SKYTEST.ONBOARDING_STATUS
)
/

comment on column SKYTEST.ADVISOR.CORPORATE_NAME is 'to be delete'
/

comment on column SKYTEST.ADVISOR.AGA_TYPE is 'AGA = 1, AGENCY=2'
/

create table SKYTEST.POLICY_COMMISSION
(
    POLICY_COMMISSION_INT_ID NUMBER(8) not null,
    POLICY                   NUMBER(8) not null
        constraint POLICY_COMMISSION_POLICY_FK
            references SKYTEST.POLICY,
    ADVISOR                  NUMBER(8) not null
        constraint POLICY_COMMISSION_ADVISOR_FK
            references SKYTEST.ADVISOR,
    PAY_MODE                 VARCHAR2(1),
    PAID_BY_TYPE             NUMBER(2),
    COMMISSION_TYPE          NUMBER(3),
    COMMISSION_AMNT          NUMBER(19, 4),
    STATEMENT_DATE           DATE,
    PAID                     VARCHAR2(1),
    CHARGE_BACK              VARCHAR2(1),
    CHGBCK_ON_COMM           NUMBER(19, 4),
    HOLD                     VARCHAR2(1),
    PRINT_DATE               DATE,
    AGENT_ID_SUB_COMM        NUMBER(11),
    RELEASE_DATE             DATE,
    RIDER_ID                 NUMBER(3),
    LAST_USER                VARCHAR2(35),
    LAST_UPDATE              DATE,
    MANUAL_ENTRY             VARCHAR2(1),
    PREM_USED_FOR_CALC       NUMBER(19, 4),
    PAYMENT_DUE_DATE         DATE,
    STATEMENT_ID             NUMBER(11),
    DEBIT_NOTIF              DATE,
    COMMENTS                 VARCHAR2(2000),
    ENTITY_TYPE              NUMBER(4) not null
)
/

create index SKYTEST.POLICY_COMMISSION_IDX1
    on SKYTEST.POLICY_COMMISSION (POLICY_COMMISSION_INT_ID)
/

create index SKYTEST.POLICY_COMMISSION_IDX2
    on SKYTEST.POLICY_COMMISSION (ADVISOR)
/

create index SKYTEST.POLICY_COMMISSION_IDX3
    on SKYTEST.POLICY_COMMISSION (POLICY)
/

alter table SKYTEST.POLICY_COMMISSION
    add constraint POLICY_COMMISSION_PK
        primary key (POLICY_COMMISSION_INT_ID)
/

create table SKYTEST.SUNLIFE_BONUS
(
    SUNLIFE_BONUS_INT_ID NUMBER(11)   not null
        constraint SUNLIFE_BONUS_PK
            primary key,
    ADVISOR_NO           VARCHAR2(20) not null,
    YEAR                 NUMBER(4)    not null,
    BONUS_PCNT           NUMBER(5)    not null,
    START_DATE           DATE,
    ADVISOR_TYPE         VARCHAR2(4),
    ADVISOR              NUMBER(11)
        constraint SUNLIFE_BONUS_ADVISOR_FK1
            references SKYTEST.ADVISOR
)
/

create table SKYTEST.POLICY_HISTORY_OWNERSHIP
(
    POLICY_HISTORY_INT_ID NUMBER(8) not null,
    POLICY                NUMBER(8) not null
        constraint POLICY_HISTORY_POLICY_FK
            references SKYTEST.POLICY,
    ADVISOR               NUMBER(8) not null
        constraint POLICY_HISTORY_ADVISOR_FK
            references SKYTEST.ADVISOR,
    EFFECTIVE_DATE        DATE,
    END_DATE              DATE,
    PERCENTAGE_OWNED      NUMBER(5, 2)
)
/

create index SKYTEST.POLICY_HISTORY_OWNERSHIP_IDX3
    on SKYTEST.POLICY_HISTORY_OWNERSHIP (ADVISOR)
/

create index SKYTEST.POLICY_HISTORY_OWNERSHIP_IDX1
    on SKYTEST.POLICY_HISTORY_OWNERSHIP (POLICY_HISTORY_INT_ID)
/

create index SKYTEST.POLICY_HISTORY_OWNERSHIP_IDX2
    on SKYTEST.POLICY_HISTORY_OWNERSHIP (POLICY)
/

alter table SKYTEST.POLICY_HISTORY_OWNERSHIP
    add constraint POLICY_HISTORY_PK
        primary key (POLICY_HISTORY_INT_ID)
/

create table SKYTEST.PRODUCT_COMMISSION_BONUS
(
    PRODUCT_COMM_BONUS_INT_ID NUMBER(8)    not null,
    ADVISOR                   NUMBER(8)
        constraint PRODUCT_COMM_BONUS_ADVISOR_FK
            references SKYTEST.ADVISOR,
    ENTITY_TYPE               NUMBER(4)    not null,
    PRODUCT                   NUMBER(11)   not null
        constraint PRODUCT_COMM_BONUS_PRODUCT_FK
            references SKYTEST.PRODUCT,
    CALC_BY_PREM_OR_COMM      VARCHAR2(2),
    COMMISSION_PERCENT        NUMBER(5, 2) not null,
    FROM_YEAR                 NUMBER(2),
    TO_YEAR                   NUMBER(2),
    FROM_DOLLAR               NUMBER(9),
    TO_DOLLAR                 NUMBER(9),
    MINAGE                    NUMBER(3),
    MAXAGE                    NUMBER(3),
    MIN_FACE                  NUMBER(10, 2),
    MAX_FACE                  NUMBER(10, 2),
    POLICY_FEE                NUMBER(5, 2),
    LAST_MODIFICATION_DATE    DATE,
    LAST_UPDATE_USER          VARCHAR2(30),
    PAID_BY_TYPE              NUMBER(2),
    START_DATE                DATE,
    END_DATE                  DATE,
    CREATION_DATE             DATE,
    AGENCY                    NUMBER(8)
        constraint PRODUCT_COMM_BONUS_AGENCY_FK
            references SKYTEST.AGENCY
)
/

create index SKYTEST.PRODUCTCOMMISSION_BONUS_IDX1
    on SKYTEST.PRODUCT_COMMISSION_BONUS (PRODUCT_COMM_BONUS_INT_ID)
/

create index SKYTEST.PRODUCTCOMMISSION_BONUS_IDX2
    on SKYTEST.PRODUCT_COMMISSION_BONUS (ADVISOR)
/

alter table SKYTEST.PRODUCT_COMMISSION_BONUS
    add constraint PRODCOMMISSIONID_PK
        primary key (PRODUCT_COMM_BONUS_INT_ID)
/

create table SKYTEST.RIDER_COMMISSION_BONUS
(
    RIDER_COMMISSION_BONUS_INT_ID NUMBER(11)   not null,
    RIDER_COMMISSION              NUMBER(11)   not null
        constraint RIDER_COMM_BONUS_FK
            references SKYTEST.RIDER_COMMISSION,
    ADVISOR                       NUMBER(8)    not null
        constraint RIDER_COMM_BONUS_ADVISOR_FK
            references SKYTEST.ADVISOR,
    PROD_COMMISSION_ID            NUMBER(11)   not null,
    CALC_BY_PREMOR_COMM           VARCHAR2(2)  not null,
    COMMISSION_PERCENT            NUMBER(5, 2) not null,
    FROM_YEAR                     NUMBER(2),
    TO_YEAR                       NUMBER(2),
    FROM_DOLLAR                   NUMBER(9),
    TO_DOLLAR                     NUMBER(9),
    MINAGE                        NUMBER(3),
    MAXAGE                        NUMBER(3),
    MAXAGE_1                      NUMBER(10, 2),
    MAXFACE                       NUMBER(10, 2),
    MAXFACE_1                     NUMBER(5, 2),
    LAST_UPDATE_DATE              DATE         not null,
    LAST_UPDATE_USER              VARCHAR2(30) not null,
    PAID_BY_TYPE                  NUMBER(2),
    START_DATE                    DATE,
    END_DATE                      DATE
)
/

create index SKYTEST.RIDER_COMMISSION_BONUS_IDX1
    on SKYTEST.RIDER_COMMISSION_BONUS (ADVISOR)
/

create index SKYTEST.RIDER_COMMISSION_BONUS_IDX2
    on SKYTEST.RIDER_COMMISSION_BONUS (RIDER_COMMISSION)
/

create index SKYTEST.RIDER_COMMISSION_BONUS_IDX3
    on SKYTEST.RIDER_COMMISSION_BONUS (RIDER_COMMISSION_BONUS_INT_ID)
/

alter table SKYTEST.RIDER_COMMISSION_BONUS
    add constraint RIDER_COMMISSION_BONUS_PK
        primary key (RIDER_COMMISSION_BONUS_INT_ID)
/

-- Cyclic dependencies found

create table SKYTEST.ADVISOR_LICENSE_LIABILITY
(
    ADVISOR           NUMBER not null
        constraint ADVISOR_LICENSE_LIABILITY_FK1
            references SKYTEST.ADVISOR,
    LICENSE_LIABILITY NUMBER not null
        constraint ADVISOR_LICENSE_LIABILITY_FK2
            references SKYTEST.LICENSE_LIABILITY,
    constraint ADVISOR_LICENSE_LIABILITY_PK
        primary key (ADVISOR, LICENSE_LIABILITY)
)
/

-- Cyclic dependencies found

create table SKYTEST.CONTRACT_EFT
(
    CONTRACT_EFT_INT_ID    NUMBER(8) not null
        constraint CONTRACT_EFT_PK
            primary key,
    SENT_DATE              DATE,
    BANK_HOLDER            VARCHAR2(75),
    BANK_ADDRESS           VARCHAR2(75),
    TRANSIT                VARCHAR2(15),
    ACCOUNT_NUMBER         VARCHAR2(15),
    COPO_EFT               VARCHAR2(1),
    STMTS_BY_EMAIL         VARCHAR2(1),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    BANK                   VARCHAR2(3),
    EFT_INFO_ID            NUMBER(8),
    ADVISOR                NUMBER(8)
        constraint CONTRACT_EFT_ADV_FK
            references SKYTEST.ADVISOR,
    COMPANY                NUMBER(8)
        constraint CONTRACT_EFT_CIE_FK
            references SKYTEST.COMPANY,
    AGENCY                 NUMBER(8)
        constraint CONTRACT_EFT_AGENCY_FK
            references SKYTEST.AGENCY,
    DELETED                VARCHAR2(1),
    DELETED_DATE           DATE,
    FILE_NAME              VARCHAR2(256),
    ONBOARDING             NUMBER(10)
        constraint CONTRACT_EFT_ONBOARDING_FK
            references SKYTEST.ONBOARDING_STATUS
)
/

-- Cyclic dependencies found

create table SKYTEST.COMPANY
(
    COMPANY_INT_ID         NUMBER(8) not null
        constraint COMPANY_PK
            primary key,
    COMPANY_ID             NUMBER(8),
    NAME_EN                VARCHAR2(128),
    NAME_FR                VARCHAR2(128),
    CONTACT                NUMBER(8)
        constraint COMPANY_CONTACT_FK
            references SKYTEST.CONTACT,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    ACTIVE                 VARCHAR2(1),
    COMPANY_TYPE           NUMBER(4),
    ASSIGNABLE_COMMISSIONS VARCHAR2(1),
    PROV_BUSINESS_NUMBER   VARCHAR2(64),
    CONTRACT_EFT           NUMBER(9)
        constraint COMPANY_CONTRACT_EFT_FK
            references SKYTEST.CONTRACT_EFT,
    BUSINESS_START_DATE    DATE,
    ADVISOR                NUMBER(8),
    AGENCY                 NUMBER(8)
        constraint COMPANY_AGENCY_FK
            references SKYTEST.AGENCY,
    DELETED                VARCHAR2(1),
    DELETED_DATE           DATE,
    FILE_NAME              VARCHAR2(256),
    PRIMARY_NAME           VARCHAR2(256),
    OTHER_NAME             VARCHAR2(256),
    BUILDING_TYPE          NUMBER(4),
    ONBOARDING             NUMBER(10)
        constraint COMPANY_ONBOARDING_FK
            references SKYTEST.ONBOARDING_STATUS
)
/

comment on column SKYTEST.COMPANY.NAME_EN is 'to be deleted'
/

comment on column SKYTEST.COMPANY.NAME_FR is 'to be deleted'
/

comment on column SKYTEST.COMPANY.CONTACT is 'if is a building is the manager fk'
/

comment on column SKYTEST.COMPANY.PROV_BUSINESS_NUMBER is '
'
/

comment on column SKYTEST.COMPANY.CONTRACT_EFT is 'change  to 8'
/

create table SKYTEST.LICENSE_LIABILITY
(
    LICENSE_LIABILITY_INT_ID NUMBER(8)    not null
        constraint LICENSE_LIABILITY_PK
            primary key,
    LIABILITY_NUMBER         VARCHAR2(25) not null,
    START_DATE               DATE,
    END_DATE                 DATE,
    CREATION_DATE            DATE,
    LAST_MODIFICATION_DATE   DATE,
    NOTE                     VARCHAR2(1024),
    REQUEST_DATE             DATE,
    SENT_DATE                DATE,
    OWNER_INT_ID             NUMBER(8),
    OWNER_TYPE               NUMBER(4),
    SHAREABLE                VARCHAR2(1),
    OLD_LIABILITY_ID         NUMBER(8),
    COMPANY_ID               NUMBER(8),
    PROVINCE                 VARCHAR2(2),
    INSURANCE_COMPANY        VARCHAR2(128),
    NAME                     VARCHAR2(256),
    DELETED                  VARCHAR2(1),
    DELETED_DATE             DATE,
    FILE_NAME                VARCHAR2(256),
    COMPANY                  NUMBER(8)
        constraint LICENSE_LIABILITY_FK1
            references SKYTEST.COMPANY,
    STATUS                   NUMBER(4),
    ONBOARDING               NUMBER(10)
        constraint LIC_LIA_ONBOARDING_FK
            references SKYTEST.ONBOARDING_STATUS
)
/

create table SKYTEST.LICENSE_LIABILITY_COMPANY
(
    LICENSE_LIABILITY NUMBER(8) not null
        constraint LICENSE_LIABILITY_COMPANY_FK1
            references SKYTEST.LICENSE_LIABILITY,
    COMPANY           NUMBER(8) not null
        constraint LICENSE_LIABILITY_COMPANY_FK2
            references SKYTEST.COMPANY,
    constraint LICENSE_LIABILITY_COMPANY_PK
        primary key (LICENSE_LIABILITY, COMPANY)
)
/

create table SKYTEST.LICENSE
(
    LICENSE_INT_ID         NUMBER(8)    not null
        constraint LICENSE_PK
            primary key,
    LICENSE_NUMBER         VARCHAR2(25) not null,
    PROVINCE               VARCHAR2(2)  not null,
    START_DATE             DATE,
    END_DATE               DATE,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    AGENCY                 NUMBER(8)
        constraint LICENSE_AGENCY_FK
            references SKYTEST.AGENCY,
    REQUEST_DATE           DATE,
    SENT_DATE              DATE,
    LICENSE_LIABILITY      NUMBER(8)
        constraint LICENSE_LICENSE_LIABILITY_FK
            references SKYTEST.LICENSE_LIABILITY,
    OLD_LICENSE_ID         NUMBER(8),
    LICENSE_DESC           VARCHAR2(30),
    CLIENT_NUMBER          NUMBER(15),
    ADVISOR                NUMBER(8)
        constraint LICENSE_ADVISOR_FK
            references SKYTEST.ADVISOR,
    COMPANY                NUMBER(8)
        constraint LICENSE_COMPANY_FK
            references SKYTEST.COMPANY,
    DELETED                VARCHAR2(1),
    DELETED_DATE           DATE,
    FILE_NAME              VARCHAR2(256),
    STATUS                 NUMBER(4),
    SELECTED               NUMBER(1),
    LICENSE_CATEGORY       NUMBER(8)
        constraint LICENSE_LICENSE_CATEGORY_FK
            references SKYTEST.LICENSE_CATEGORY,
    ONBOARDING             NUMBER(10)
        constraint LICENSE_ONBOARDING_FK
            references SKYTEST.ONBOARDING_STATUS
)
/

create table SKYTEST.CONTRACT_SETUP
(
    CONTRACT_SETUP_INT_ID  NUMBER(8) not null
        constraint CONTRACT_SETUP_PK
            primary key,
    PRODUCT_SUPPLIER       NUMBER(8)
        constraint CONT_SETUP_PROD_SUPP_FK
            references SKYTEST.PRODUCT_SUPPLIER,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    CONTRACT_SETUP         NUMBER(8),
    AGENCY                 NUMBER(8)
        constraint CONTRACT_SETUP_FK3
            references SKYTEST.AGENCY,
    COMPANY                NUMBER(8)
        constraint CONTRACT_SETUP_FK1
            references SKYTEST.COMPANY,
    ADVISOR                NUMBER(8)
        constraint CONTRACT_SETUP_FK2
            references SKYTEST.ADVISOR,
    AGENCY_NAME            VARCHAR2(128),
    DELETED                VARCHAR2(1),
    DELETED_DATE           DATE,
    SERVICING_AGENCY       NUMBER(8)
        constraint CONTRACT_SETUP_FK4
            references SKYTEST.AGENCY,
    AGA_ADVISOR            NUMBER(8)
        constraint CONTRACT_SETUP_FK5
            references SKYTEST.ADVISOR,
    MGA_PRODUCT_SUPPLIER   NUMBER(8)
        constraint CONT_SETUP_PROD_SUPP_MGA_FK
            references SKYTEST.PRODUCT_SUPPLIER,
    AGA_PRODUCT_SUPPLIER   NUMBER(8)
        constraint CONT_SETUP_PROD_SUPP_MGA_AGA_FK
            references SKYTEST.PRODUCT_SUPPLIER,
    AGA2_PRODUCT_SUPPLIER  NUMBER(8)
        constraint CONT_SETUP_PROD_SUPP_MGA_AGA_AGA_FK
            references SKYTEST.PRODUCT_SUPPLIER,
    ONBOARDING             NUMBER(10)
        constraint CONT_SETUP_ONBOARDING_FK
            references SKYTEST.ONBOARDING_STATUS
)
/

comment on column SKYTEST.CONTRACT_SETUP.CONTRACT_SETUP is 'ID to another CS - used for redirect'
/

comment on column SKYTEST.CONTRACT_SETUP.AGENCY is 'Owning Agency'
/

comment on column SKYTEST.CONTRACT_SETUP.COMPANY is 'Owning Licensee'
/

comment on column SKYTEST.CONTRACT_SETUP.ADVISOR is 'Owning Advisor'
/

comment on column SKYTEST.CONTRACT_SETUP.AGENCY_NAME is 'AGA Name'
/

comment on column SKYTEST.CONTRACT_SETUP.SERVICING_AGENCY is 'Servicing Agency'
/

comment on column SKYTEST.CONTRACT_SETUP.AGA_ADVISOR is 'AGA for this  Contract Setup'
/

comment on column SKYTEST.CONTRACT_SETUP.MGA_PRODUCT_SUPPLIER is 'link to mga is there is agency name saved'
/

comment on column SKYTEST.CONTRACT_SETUP.AGA_PRODUCT_SUPPLIER is 'link to mga/aga is there is agency name saved'
/

comment on column SKYTEST.CONTRACT_SETUP.AGA2_PRODUCT_SUPPLIER is 'link toa second mga/aga is there is agency name saved'
/

create table SKYTEST.CONTRACT
(
    CONTRACT_INT_ID         NUMBER(8) not null
        constraint CONTRACT_PK
            primary key
        constraint CONTRACT_CONTRACT_SETUP_FK
            references SKYTEST.CONTRACT_SETUP,
    CONTRACT_TYPE           NUMBER(4) not null,
    CONTRACT_NUMBER         VARCHAR2(25),
    CREATION_DATE           DATE,
    LAST_MODIFICATION_DATE  DATE,
    CONTRACT_STATUS         NUMBER(4),
    EFFECTIVE_DATE          DATE,
    INACTIVE_DATE           DATE,
    CONTRACT_EFT            NUMBER(8)
        constraint CONTRACT_CONTRACT_EFT_FK
            references SKYTEST.CONTRACT_EFT,
    SALESREP_CODE           VARCHAR2(5),
    OLD_CONTRACT_ID         NUMBER(8),
    COMMISSION_PAY_TO       VARCHAR2(128),
    MFCODE                  VARCHAR2(20),
    DEALER_CODE             VARCHAR2(4),
    DISTRICT                VARCHAR2(10),
    IS_PRIMARY              CHAR,
    DIRECT_DEPOSIT          CHAR,
    EFT_FORM_MINCL          CHAR,
    EFT_SENT_DATE           DATE,
    HEALTH_CODE             VARCHAR2(20),
    TRANSFER_IN_PRICE       NUMBER(10, 2),
    TRANSFER_OUT_PRICE      NUMBER(10, 2),
    TRANSFER_FROM           NUMBER(6)
        constraint CONTRACT_FROM_PRODUCT_SUPPLIER_FK
            references SKYTEST.PRODUCT_SUPPLIER,
    TRANSFER_TO             NUMBER(6)
        constraint CONTRACT_TO_PRODUCT_SUPPLIER_FK
            references SKYTEST.PRODUCT_SUPPLIER,
    TRANSFER_IN_DATE        DATE,
    TRANSFER_OUT_DATE       DATE,
    COMPANY_ASSOCIATION     NUMBER(8),
    TRANS_PAID_DATE         DATE,
    PAYSCALE                CHAR,
    CUSTOM_FIELD1_ID        NUMBER(8),
    CUSTOM_FIELD1_VALUE     VARCHAR2(128),
    CUSTOM_FIELD2_ID        NUMBER(8),
    CUSTOM_FIELD2_VALUE     VARCHAR2(128),
    CUSTOM_FIELD3_ID        NUMBER(8),
    CUSTOM_FIELD3_VALUE     VARCHAR2(128),
    CUSTOM_FIELD4_ID        NUMBER(8),
    CUSTOM_FIELD4_VALUE     VARCHAR2(128),
    REFERENCE_ONLY          NUMBER(11),
    CORRESPOND_WITH         NUMBER(4),
    CARE_OF_COMPANY_ID      NUMBER(11),
    FYC_PAID_BY_TYPE        NUMBER(4),
    INVEST_FYC_RATE         NUMBER(9, 4),
    INVEST_FEE_RATE         NUMBER(9, 4),
    INSURANCE_FYC_RATE      NUMBER(9, 4),
    INSURANCE_RENEWAL_RATE  NUMBER(9, 4),
    TRANSFER_ID             VARCHAR2(25),
    BROKER_CONTRACT_TYPE    NUMBER(4),
    COMMISSION_PAYMENT_TYPE NUMBER(5),
    COMMISSION_PAYER_TYPE   NUMBER(5),
    INVEST_FYC_CALC         VARCHAR2(1),
    INVEST_FEE_CALC         VARCHAR2(1),
    INSURANCE_FYC_CALC      VARCHAR2(1),
    INSURANCE_RENEWAL_CALC  VARCHAR2(1),
    LAST_UPDATE_USER        VARCHAR2(64),
    INSURANCE_BONUS_RATE    NUMBER(9, 4),
    INSURANCE_BONUS_CALC    VARCHAR2(1),
    COMMISSIONS_ASSIGNEMENT VARCHAR2(1),
    RETIRED_CONTRACT_NUMBER VARCHAR2(25),
    TRANSFER_FEES           NUMBER(10, 2),
    FILE_NAME               VARCHAR2(256),
    ONBOARDING              NUMBER(10)
        constraint CONTRACT_ONBOARDING_FK
            references SKYTEST.ONBOARDING_STATUS
)
/

create table SKYTEST.COMPANY_CONTRACT_SETUP
(
    COMPANY        NUMBER(10) not null
        constraint COMPANY_CONTRACT_SETUP_COMPANY
            references SKYTEST.COMPANY,
    CONTRACT_SETUP NUMBER(10) not null
        constraint CMPNYCONTRACTSETUPCNTRACTSETUP
            references SKYTEST.CONTRACT_SETUP,
    primary key (COMPANY, CONTRACT_SETUP)
)
/

create table SKYTEST.PAYEE
(
    PAYEE_INT_ID           NUMBER(9) not null
        constraint PAYEE_PK
            primary key,
    NAME                   VARCHAR2(256),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    ACCOUNT_MANAGER        NUMBER(9)
        constraint PAYEE_ACCOUNT_MANAGER_FK
            references SKYTEST.ACCOUNT_MANAGER,
    AGENCY                 NUMBER(9)
        constraint PAYEE_AGENCY_FK
            references SKYTEST.AGENCY,
    ADVISOR                NUMBER(9)
        constraint PAYEE_ADVISOR_FK
            references SKYTEST.ADVISOR,
    CONTRACT_SETUP         NUMBER(9)
        constraint PAYEE_CONTRACT_SETUP_FK
            references SKYTEST.CONTRACT_SETUP,
    PAYTO                  VARCHAR2(256),
    FOR_SUBAGENCY          VARCHAR2(1),
    FOR_MGA                VARCHAR2(1),
    TRANSFER_ID            VARCHAR2(1),
    COMMISSION_LEVEL       NUMBER(4)
)
/

create table SKYTEST.STATEMENT
(
    STATEMENT_INT_ID       NUMBER(11) not null
        constraint STATEMENT_PK
            primary key,
    STATEMENT_NUMBER       VARCHAR2(16),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    AMOUNT                 NUMBER(9, 2),
    EFFECTIVE_DATE         DATE,
    NOTE                   VARCHAR2(1024),
    PRODUCER               VARCHAR2(32),
    LOCKED                 VARCHAR2(1),
    PAYMENT_DUE_DATE       DATE,
    PAYMENT                NUMBER(11)
        constraint STATEMENT_PAYMENT_FK1
            references SKYTEST.PAYMENT,
    PAYEE                  NUMBER(9)
        constraint STATEMENT_PAYEE_FK1
            references SKYTEST.PAYEE,
    START_PERIOD           DATE,
    END_PERIOD             DATE,
    TYPE                   VARCHAR2(1),
    PRODUCT_SUPPLIER       NUMBER(8)
        constraint STATEMENT_PS_FK1
            references SKYTEST.PRODUCT_SUPPLIER,
    PRINT_COUNT            NUMBER(9),
    PAYEE_TYPE             NUMBER(4)
)
/

comment on column SKYTEST.STATEMENT.PRODUCER is 'STR REPRENSENTING CREATION SOURCE'
/

comment on column SKYTEST.STATEMENT.LOCKED is 'BOOLEAN INDICATOR FOR LOCKING EDITION'
/

create table SKYTEST.COMMISSION_PAYABLE
(
    COMMISSION_PAYABLE_INT_ID NUMBER(11) not null
        constraint COMMISSION_PAYABLE_PK
            primary key,
    CREATION_DATE             DATE,
    LAST_MODIFICATION_DATE    DATE,
    PRODUCT_COMMISSION_BONUS  NUMBER(11)
        constraint COMMISSION_PAYABLE_PRODUC_FK1
            references SKYTEST.PRODUCT_COMMISSION_BONUS,
    PRODUCT_COMMISSION        NUMBER(11)
        constraint COMMISSION_PAYABLE_PRODUC_FK2
            references SKYTEST.PRODUCT_COMMISSION,
    COMMISSION_PAYABLE_TYPE   NUMBER(11) not null
        constraint COMMISSION_PAYABLE_COMMIS_FK1
            references SKYTEST.COMMISSION_PAYABLE_TYPE,
    ACCOUNT_DETAIL            VARCHAR2(64),
    INIT_AMOUNT               NUMBER(9, 2),
    STATEMENT                 NUMBER(11)
        constraint COMMISSION_PAYABLE_STATEM_FK1
            references SKYTEST.STATEMENT,
    HOLD                      VARCHAR2(1),
    MANUAL_ENTRY              VARCHAR2(1),
    CHARGE_BACK               VARCHAR2(1),
    CHARGE_BACK_COMM          NUMBER(9, 2),
    PAYMENT_DUE_DATE          DATE,
    PREM_USED_FOR_CALC        NUMBER(9, 2),
    NOTE                      VARCHAR2(1024),
    INIT_RATE                 NUMBER(9, 4),
    CALC_RATE                 NUMBER(9, 4),
    CALC_AMOUNT               NUMBER(9, 2),
    COMMISSION_LEVEL          NUMBER(4),
    COMMISSION_SOURCE         NUMBER(11)
        constraint COMMISSION_PAYABLE_COMMIS_FK2
            references SKYTEST.COMMISSION_SOURCE,
    PAYEE                     NUMBER(11)
        constraint COMMISSION_PAYABLE_PAYEE_FK1
            references SKYTEST.PAYEE,
    START_DATE                DATE,
    END_DATE                  DATE,
    COMMISSION_BASE           NUMBER(7, 2),
    EFFECTIVE_DATE            DATE,
    FORMULA_TYPE              VARCHAR2(1)
)
/

comment on column SKYTEST.COMMISSION_PAYABLE.PRODUCT_COMMISSION_BONUS is 'LINK BACK TO THE ACTUAL PROD COMM'
/

comment on column SKYTEST.COMMISSION_PAYABLE.PRODUCT_COMMISSION is 'LINK TO THE ACTUAL PROD COMM BONUS'
/

comment on column SKYTEST.COMMISSION_PAYABLE.COMMISSION_PAYABLE_TYPE is 'TYPE OF PAYABLE COMMISSION'
/

comment on column SKYTEST.COMMISSION_PAYABLE.ACCOUNT_DETAIL is 'CAN BE USED TO SPECIFY A SEG_FUND UNDER INVESTMENT OR OTHER SUB-ACCOUNT-TYPE LATER ON'
/

create table SKYTEST.SALESREP
(
    SALESREP_INT_ID        NUMBER(8)   not null,
    SALESREP_CODE          VARCHAR2(5) not null,
    NAME                   VARCHAR2(256),
    ACTIVE                 CHAR        not null,
    ADVISOR                NUMBER(8)
        constraint SALESREP_ADVISOR_FK1
            references SKYTEST.ADVISOR,
    COMMENTS               VARCHAR2(200),
    DEALER_CODE            VARCHAR2(4),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    COMPANY                NUMBER(8)
        constraint SALESREP_COMPANY_FK1
            references SKYTEST.COMPANY,
    TYPE                   NUMBER(4)
)
/

create index SKYTEST.SALESREP_IDX2
    on SKYTEST.SALESREP (SALESREP_CODE)
/

create unique index SKYTEST.SALESREP_IDX1
    on SKYTEST.SALESREP (SALESREP_INT_ID)
/

create index SKYTEST.SALESREP_IDX3
    on SKYTEST.SALESREP (SALESREP_CODE, DEALER_CODE)
/

alter table SKYTEST.SALESREP
    add constraint SALESREP_PK
        primary key (SALESREP_INT_ID)
/

create table SKYTEST.STORAGE_SPACE
(
    STORAGE_SPACE_INT_ID   NUMBER(8) not null
        constraint SYS_C0016007
            primary key,
    BUILDING               NUMBER(8)
        constraint BUILDING_FK2
            references SKYTEST.COMPANY,
    DOOR                   NUMBER(8)
        constraint DOOR_FK2
            references SKYTEST.DOOR,
    STORAGE_TYPE           NUMBER(8),
    STORAGE_SPACE_NUMBER   VARCHAR2(60),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    FREE                   VARCHAR2(2)
)
/

-- Cyclic dependencies found

create table SKYTEST.CONTACT_BUILDING
(
    COMPANY NUMBER(8)
        constraint COMPANY_FK
            references SKYTEST.COMPANY,
    CONTACT NUMBER(8)
        constraint CONTACT_FK
            references SKYTEST.CONTACT
)
/

-- Cyclic dependencies found

create table SKYTEST.PROFILE
(
    PROFILE_INT_ID         NUMBER(8) not null
        constraint PROFILE_PK
            primary key,
    APP_NAME               VARCHAR2(128),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    ACTIVE                 VARCHAR2(1),
    SPECIAL_NOTE_INT_ID    NUMBER(8)
        constraint SPECIAL_NOTE_ID
            references SKYTEST.SPECIAL_NOTE_PROD,
    PRODUCT_SUPPLIER       NUMBER(8)
        constraint PROFILE_PRODUCT_SUPPLIER_FK
            references SKYTEST.PRODUCT_SUPPLIER
)
/

create table SKYTEST.PROFILE_SELECTION
(
    SELECTION_INT_ID NUMBER(8) not null
        constraint PROFILE_SELECTION_PK
            primary key,
    PROFILE          NUMBER(8) not null
        constraint PROFILE_SELECTION_PROFILE_FK1
            references SKYTEST.PROFILE,
    COMPANY_ID       NUMBER(8),
    PRODUCT_ID       NUMBER(8),
    PRODUCT_TYPE     VARCHAR2(8),
    IQ4              VARCHAR2(1),
    SHOP4RATES       VARCHAR2(1),
    SHOP4FUNDS       VARCHAR2(1),
    TRAVEL           VARCHAR2(1),
    TESTZONE         VARCHAR2(1)
)
/

create trigger SKYTEST.PROFILE_SELECTION_INCREMENT
    before insert
    on SKYTEST.PROFILE_SELECTION
    for each row
    when (new.SELECTION_INT_ID IS NULL)
BEGIN
:new.SELECTION_INT_ID := PROFILE_SELECTION_SEQ.NEXTVAL;
END;
/

create table SKYTEST.PROFILE_ANNOUNCEMENT
(
    PROFILE      NUMBER(8) not null
        constraint PROFILE_ANNOUNCEMENT_FK
            references SKYTEST.PROFILE,
    ANNOUNCEMENT NUMBER(8) not null
        constraint ANNOUNCEMENT_PROFILE_FK
            references SKYTEST.ANNOUNCEMENT,
    primary key (PROFILE, ANNOUNCEMENT)
)
/

create table SKYTEST.SPECIAL_NOTE_PROD
(
    SPECIAL_NOTE_INT_ID NUMBER(8) not null
        primary key,
    SHORT_ENGLISH_DESC  VARCHAR2(1500),
    SHORT_FRENCH_DESC   VARCHAR2(1500),
    LAST_UPDATE         DATE,
    PROFILE_INT_ID      NUMBER(5)
        constraint PROFILE_ID
            references SKYTEST.PROFILE,
    PRODUCT_INT_ID      NUMBER(5)
)
/

create table SKYTEST.STORED_FILE_PROFILE
(
    STORED_FILE NUMBER(8)
        constraint STORED_FILE_FK2
            references SKYTEST.STORED_FILE,
    PROFILE     NUMBER(8)
        constraint PROFILE_FK_STORED_FILE
            references SKYTEST.PROFILE
)
/

create table SKYTEST.WEBTOOL
(
    WEBTOOL_INT_ID NUMBER not null
        primary key,
    WELCOME_EN     VARCHAR2(255),
    DASH_FIRST_EN  VARCHAR2(1000),
    DASH_SECOND_EN VARCHAR2(4000),
    WELCOME_FR     VARCHAR2(255),
    DASH_FIRST_FR  VARCHAR2(1000),
    DASH_SECOND_FR VARCHAR2(4000),
    PROFILE        NUMBER(8)
        constraint PROFILE_WEBTOOL_FK
            references SKYTEST.PROFILE
                on delete cascade,
    ACTIVE         NUMBER(2),
    IMG_EN         BLOB,
    IMG_FR         BLOB
)
/

create table SKYTEST.PROFILE_PROFILE
(
    PROFILE_SON    NUMBER(8)
        constraint PROFILE_PROFILE_SON_FK
            references SKYTEST.PROFILE,
    PROFILE_FATHER NUMBER(8)
        constraint PROFILE_PROFILE_FATHER_FK
            references SKYTEST.PROFILE
)
/

create table SKYTEST.PROFILE_SELECTION_INVESTMENT
(
    SELECTION_INT_ID NUMBER(8) not null
        primary key,
    PROFILE          NUMBER(8) not null
        references SKYTEST.PROFILE,
    COMPANY_ID       NUMBER(8),
    PRODUCT_ID       NUMBER(8),
    PRODUCT_TYPE     VARCHAR2(8),
    IQ4              VARCHAR2(1),
    SHOP4RATES       VARCHAR2(1),
    SHOP4FUNDS       VARCHAR2(1),
    TRAVEL           VARCHAR2(1),
    TESTZONE         VARCHAR2(1)
)
/

create trigger SKYTEST."PROFILE_SELECTION_INCREMENT_copy1"
    before insert
    on SKYTEST.PROFILE_SELECTION_INVESTMENT
    for each row
    when (new.SELECTION_INT_ID IS NULL)
BEGIN
:new.SELECTION_INT_ID := PROFILE_SELECTION_SEQ.NEXTVAL;
END;
/

-- Cyclic dependencies found

create table SKYTEST.STORAGE_SPACE_CONTACT
(
    CONTACT       NUMBER(8)
        constraint CONTACTSTORAGE_FK
            references SKYTEST.CONTACT,
    STORAGE_SPACE NUMBER(8)
        constraint STORAGECONTACT_FK
            references SKYTEST.STORAGE_SPACE
)
/

-- Cyclic dependencies found

create table SKYTEST.SUNLIFE_DECLINED
(
    DECL_INT_ID         NUMBER(11) not null
        constraint SUNLIFE_DECLINED_PK
            primary key,
    CONTRACTNO          VARCHAR2(10),
    CLIENT_FIRST_NAME   VARCHAR2(35),
    BASE_PLAN           VARCHAR2(20),
    ADVISOR_NO          VARCHAR2(20),
    CREATE_DATE         DATE,
    CLIENT_LAST_NAME    VARCHAR2(35),
    ADVISOR             NUMBER(11)
        constraint SUNLIFE_DECLINED_FK1
            references SKYTEST.ADVISOR,
    BRANCH              NUMBER(11)
        constraint SUNLIFE_DECLINED_FK2
            references SKYTEST.BRANCH,
    MARKETING_REG       VARCHAR2(35),
    MARKETING_REG_CODE  NUMBER(10),
    ORIGINAL_ADVISOR_ID NUMBER(11),
    OPPORTUNITY         NUMBER(11)
        constraint SUNLIFE_DECLINED_OPPORTUN_FK1
            references SKYTEST.OPPORTUNITY,
    DATE_OF_BIRTH       DATE,
    SEX                 VARCHAR2(2),
    PREMIUM             NUMBER(15),
    FACE_AMOUNT         NUMBER(15),
    SMOKER              VARCHAR2(2)
)
/

-- Cyclic dependencies found

create table SKYTEST.USERS
(
    USER_INT_ID             NUMBER(8)     not null
        constraint USERS_CONTACT_FK
            references SKYTEST.CONTACT,
    USERNAME                VARCHAR2(128) not null,
    PASSWORD                VARCHAR2(256),
    CREATION_DATE           DATE,
    LAST_MODIFICATION_DATE  DATE,
    ACTIVE                  VARCHAR2(1)   not null,
    USER_TYPE               NUMBER(4),
    TESTING_MODE            VARCHAR2(1),
    USER_HOME               VARCHAR2(256),
    FIRST_TIME_LOGIN        VARCHAR2(1),
    MASTER_CODE             VARCHAR2(16),
    ADVISOR_LOCK            NUMBER(8),
    AGENCY_LOCK             NUMBER(8),
    EFFECTIVE_DATE          DATE,
    VALID_UNTIL             DATE,
    AUTH_METHOD             NUMBER(4),
    LAST_PASSWORD_RESET     DATE,
    SENT_INTRO_EMAIL        VARCHAR2(1),
    SENT_INTRO_DATE         DATE,
    SENT_USERNAME_EMAIL     VARCHAR2(1),
    SENT_USERNAME_DATE      DATE,
    SENT_PASSWORD_EMAIL     VARCHAR2(1),
    SENT_PASSWORD_DATE      DATE,
    PROFILE_TYPE            NUMBER(4),
    IQ_SERVICE              VARCHAR2(1),
    FUND_SERVICE            VARCHAR2(1),
    RATE_SERVICE            VARCHAR2(1),
    TRAVEL_SERVICE          VARCHAR2(1),
    TEST_PLATFORM           VARCHAR2(1),
    NEED_CONFIRMATION       VARCHAR2(1),
    CONFIRMATION_KEY        VARCHAR2(256),
    SENT_CONFIRMATION_DATE  DATE,
    TOTAL_STORAGE_GIGABYTES NUMBER(5, 2),
    CFILES_SERVICE          VARCHAR2(1),
    VIDEO_SERVICE           VARCHAR2(1),
    RANKING_SERVICE         VARCHAR2(1),
    LEADS                   VARCHAR2(1),
    SAVED_QUOTES            VARCHAR2(1),
    POLICIES                VARCHAR2(1),
    LAST_UPDATE_USER        VARCHAR2(64),
    MGA_PROFILE             VARCHAR2(1),
    ADVISOR_PROFILE         VARCHAR2(1),
    AGA_PROFILE             VARCHAR2(1),
    BRANCH_PROFILE          VARCHAR2(1),
    REGIONAL_PROFILE        VARCHAR2(1),
    HEAD_OFFICE_PROFILE     VARCHAR2(1),
    AGA_LINK_ID             NUMBER(11),
    BRANCH_ID               NUMBER(11),
    MARKETING_REGION_ID     NUMBER(11),
    PROFILE                 NUMBER(8)
        constraint USERS_PROFILE_FK
            references SKYTEST.PROFILE,
    DESACTIVATION_DATE      DATE,
    FORGOT_CODE             VARCHAR2(255),
    ONBOARDING              NUMBER(10)
        constraint USERS_ONBOARDING_FK
            references SKYTEST.ONBOARDING_STATUS
)
/

comment on column SKYTEST.USERS.PROFILE is 'NOT IN USE'
/

comment on column SKYTEST.USERS.FORGOT_CODE is 'WILL STORE A MD5 WITH THE FORGOT CODE TO CHECK'
/

create index SKYTEST.USERS_IDX1
    on SKYTEST.USERS (USER_INT_ID)
/

create index SKYTEST.USERS_IDX2
    on SKYTEST.USERS (USERNAME)
/

alter table SKYTEST.USERS
    add constraint USERS_PK
        primary key (USER_INT_ID)
/

create table SKYTEST.CAMPAIGN
(
    CAMPAIGN_INT_ID        NUMBER(8) not null,
    NAME                   VARCHAR2(128),
    USERS                  NUMBER(8)
        constraint CAMPAIGN_FK1
            references SKYTEST.USERS,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    START_DATE             DATE,
    END_DATE               DATE,
    CAMPAIGN_ID            VARCHAR2(128),
    GOAL_AMOUNT            NUMBER(11, 2),
    GOAL_NUMBER            NUMBER(8),
    STATUS                 NUMBER(4)
)
/

comment on column SKYTEST.CAMPAIGN.USERS is 'FK to Users ''owner'''
/

create table SKYTEST.FD_FUND_FAVORITE_PY
(
    FUND_FAVORITE_INT_ID   NUMBER(11) not null
        constraint FD_FUND_FAVORITE_PY_PK
            primary key,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    FD_FUND_FLEX_SEARCH_PY NUMBER(11)
        constraint FD_FUND_FAVORITE_PY_FK1
            references SKYTEST.FD_FUND_FLEX_SEARCH_PY (FUND_FUNDATAKEY),
    USERS                  NUMBER(11)
        constraint FD_FUND_FAVORITE_PY_FK2
            references SKYTEST.USERS,
    INITIAL_VALUE          NUMBER(11, 3),
    PURCHASE_DATE          DATE,
    INITIAL_UNITS          NUMBER(8)
)
/

create table SKYTEST.LOGIN_ACCESS
(
    LOGIN_ACCESS_INT_ID NUMBER(8) not null
        constraint LOGIN_ACCESS_PK
            primary key,
    USERS               NUMBER(8)
        constraint LOGIN_ACCESS_USERS_FK
            references SKYTEST.USERS,
    TIMESTAMP           DATE,
    IP_ADDRESS          VARCHAR2(35),
    HOSTNAME            VARCHAR2(128),
    BROWSER_DETAIL      VARCHAR2(640),
    APPLICATION_NAME    VARCHAR2(32),
    STATUS              VARCHAR2(32),
    MESSAGE             VARCHAR2(512),
    LOGOUT              DATE,
    EXIT_PAGE           VARCHAR2(256),
    SESSION_ID          VARCHAR2(256)
)
/

create table SKYTEST.PROFILE_USERS
(
    USERS   NUMBER(8)
        constraint USERS_FK
            references SKYTEST.USERS,
    PROFILE NUMBER(8)
        constraint PROFILE_FK
            references SKYTEST.PROFILE
)
/

create table SKYTEST.USERS_SERVICE
(
    USERS_SERVICE_INT_ID        NUMBER(8)   not null
        constraint USERS_SERVICE_PK
            primary key,
    USERS                       NUMBER(8)   not null
        constraint USERS_SERVICE_USERS_FK1
            references SKYTEST.USERS,
    SERVICE_PROFILE             NUMBER(8)   not null
        constraint USERS_SERVICE_SERVICE_PRO_FK1
            references SKYTEST.SERVICE_PROFILE,
    START_DATE                  DATE        not null,
    END_DATE                    DATE,
    PAYMENT_TYPE                VARCHAR2(1) not null,
    AUTOMATIC_RECURRING_BILLING VARCHAR2(1) not null
)
/

comment on column SKYTEST.USERS_SERVICE.PAYMENT_TYPE is 'A(annual), M(onthly), S(emi-annual), Q(uarterly)'
/

comment on column SKYTEST.USERS_SERVICE.AUTOMATIC_RECURRING_BILLING is 'Y(es), N(o)'
/

create table SKYTEST.MARKET_PLACE
(
    MARKET_PLACE_INT_ID NUMBER(8) not null,
    ADVISOR             NUMBER(8)
        constraint MARKET_PLACE_FK1
            references SKYTEST.ADVISOR,
    TYPES_ID            NUMBER(4) not null,
    CLASS_NAME          VARCHAR2(64),
    CATEGORY            VARCHAR2(64),
    USERS               NUMBER(8)
        constraint MARKET_PLACE_FK2
            references SKYTEST.USERS,
    DESCRIPTION_EN      VARCHAR2(255),
    START_DATE          DATE,
    END_DATE            DATE,
    DESCRIPTION_FR      VARCHAR2(255)
)
/

create table SKYTEST.AVAILABILITY
(
    AVAILABILITY_INT_ID    NUMBER(8) not null
        constraint AVAILABILITY_PK
            primary key,
    ADVISOR                NUMBER(8)
        constraint AVAILABILITY_ADVISDOR_FR
            references SKYTEST.ADVISOR,
    CLIENT                 NUMBER(8),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    MON_START              DATE,
    MON_END                DATE,
    TUE_START              DATE,
    TUE_END                DATE,
    WED_START              DATE,
    WED_END                DATE,
    THU_START              DATE,
    THU_END                DATE,
    FRI_START              DATE,
    FRI_END                DATE,
    SAT_START              DATE,
    SAT_END                DATE,
    SUN_START              DATE,
    SUN_END                DATE,
    MON_EMAIL              VARCHAR2(1),
    MON_PHONE              VARCHAR2(1),
    TUE_EMAIL              VARCHAR2(1),
    TUE_PHONE              VARCHAR2(1),
    WED_EMAIL              VARCHAR2(1),
    WED_PHONE              VARCHAR2(1),
    THU_EMAIL              VARCHAR2(1),
    THU_PHONE              VARCHAR2(1),
    FRI_EMAIL              VARCHAR2(1),
    FRI_PHONE              VARCHAR2(1),
    SAT_EMAIL              VARCHAR2(1),
    SAT_PHONE              VARCHAR2(1),
    SUN_EMAIL              VARCHAR2(1),
    SUN_PHONE              VARCHAR2(1),
    MON_AVAILABLE          VARCHAR2(1),
    TUE_AVAILABLE          VARCHAR2(1),
    WED_AVAILABLE          VARCHAR2(1),
    THU_AVAILABLE          VARCHAR2(1),
    FRI_AVAILABLE          VARCHAR2(1),
    SAT_AVAILABLE          VARCHAR2(1),
    SUN_AVAILABLE          VARCHAR2(1),
    USERS                  NUMBER(8)
        constraint AVAILABILITY_USERS_FK
            references SKYTEST.USERS
)
/

create table SKYTEST.NOTIFICATIONS
(
    NOTIFICATIONS_INT_ID   NUMBER(8) not null,
    SEVERITY               VARCHAR2(1),
    MESSAGE                VARCHAR2(1024),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    ACK_TIMESTAMP          DATE,
    REQ_ACK                VARCHAR2(1),
    SOURCE                 VARCHAR2(128),
    USERS                  NUMBER(8)
        constraint NOTIFICATIONS_USERS_FK
            references SKYTEST.USERS,
    HAS_ATTACHMENT         VARCHAR2(1),
    OWNER                  NUMBER(8)
)
/

create table SKYTEST.PRODUCT_SELECTION
(
    PRODUCT_SELECTION_INT_ID NUMBER(8) not null
        constraint PRODUCT_SELECTION_PK
            primary key,
    USERS                    NUMBER(8) not null
        constraint PRODUCT_SELECTION_FK1
            references SKYTEST.USERS,
    COMPANY_ID               NUMBER(8) not null,
    PRODUCT_ID               NUMBER(8) not null,
    CREATION_DATE            DATE,
    IQ4                      VARCHAR2(1),
    TRAVEL                   VARCHAR2(1),
    PROFILE_TYPE             NUMBER(2)
)
/

create table SKYTEST.REPORT
(
    REPORT_INT_ID NUMBER(8) not null
        primary key
        constraint "pk_not_null"
            check ("REPORT_INT_ID" IS NOT NULL),
    USERS         NUMBER(8)
        constraint REPORT_USERS_FK
            references SKYTEST.USERS,
    SQL_STR       VARCHAR2(1000),
    NAME          VARCHAR2(50)
)
/

create table SKYTEST.KNOWLEDGE_RATING
(
    KNOWLEDGE_RATING_INT_ID NUMBER(11) not null
        constraint KNOWLEDGE_RATING_PK
            primary key,
    CREATION_DATE           DATE,
    RATING                  NUMBER(4),
    KNOWLEDGE_BASE          NUMBER(11)
        constraint KNOWLEDGE_RATING_FK1
            references SKYTEST.KNOWLEDGE_BASE,
    USERS                   NUMBER(11)
        constraint KNOWLEDGE_RATING_FK2
            references SKYTEST.USERS
)
/

create table SKYTEST.CONTACT
(
    CONTACT_INT_ID           NUMBER(11) not null,
    FIRSTNAME                VARCHAR2(35),
    MIDDLENAME               VARCHAR2(35),
    LASTNAME                 VARCHAR2(55),
    JOB_TITLE                VARCHAR2(100),
    JOB_ROLE                 VARCHAR2(100),
    DEPARTMENT               NUMBER(8),
    DO_NOT_CALL              CHAR(1 char),
    DEATH_DATE               DATE,
    ASSIGNED_TO              VARCHAR2(30),
    RETIREMENT_DATE          DATE,
    MARITAL_STATUS           NUMBER(8),
    BIRTH_PLACE              VARCHAR2(128),
    EMPLOYER                 VARCHAR2(128),
    CREATION_DATE            DATE,
    LAST_MODIFICATION_DATE   DATE,
    INCOME                   NUMBER(19, 4),
    LENGTH_OF_EMPLOYMENT     NUMBER(8),
    BIRTH_DATE               DATE,
    FACEBOOK                 VARCHAR2(128),
    TWITTER                  VARCHAR2(128),
    SALUTATION               NUMBER,
    CUSTOM_FIELD1_NAME       VARCHAR2(128),
    CUSTOM_FIELD1_VALUE      VARCHAR2(128),
    MASTER_CODE              VARCHAR2(32),
    GENDER                   NUMBER(4),
    PREFERRED_LANGUAGE       NUMBER(4),
    CONTACT_TYPE             NUMBER(4),
    PUBLIC_NAME              VARCHAR2(128),
    CUSTOM_FIELD2_VALUE      VARCHAR2(128),
    CUSTOM_FIELD3_VALUE      VARCHAR2(128),
    CONTACT_SOURCE           NUMBER(4),
    ORGANIZATION             NUMBER(8)
        constraint CONTACT_ORGANIZATION_FK
            references SKYTEST.ORGANIZATION,
    WEBSITE                  VARCHAR2(128),
    CUSTOM_FIELD1_ID         NUMBER(4),
    CUSTOM_FIELD2_ID         NUMBER(4),
    CUSTOM_FIELD3_ID         NUMBER(4),
    CUSTOM_FIELD0_VALUE      VARCHAR2(128),
    AGENCY_CONTACT           VARCHAR2(1),
    IMPORT_FLAG              VARCHAR2(1),
    MAIDENNAME               VARCHAR2(35),
    CONTACT_LATER            VARCHAR2(1),
    FIRST_CONTACTED          DATE,
    AGENCY_PRIMARY_CONTACT   VARCHAR2(1),
    LAST_UPDATE_USER         VARCHAR2(64),
    ADVISOR_PROFILE          NUMBER(8)
        constraint CONTACT_FK1
            references SKYTEST.ADVISOR_PROFILE,
    SKILL_1                  NUMBER(4),
    SKILL_2                  NUMBER(4),
    SKILL_3                  NUMBER(4),
    SKILL_4                  NUMBER(4),
    SKILL_5                  NUMBER(4),
    SKILL_6                  NUMBER(4),
    SKILL_7                  NUMBER(4),
    SKILL_8                  NUMBER(4),
    FRENCH_ENGLISH_BILINGUAL VARCHAR2(1),
    ABOUT_ME_ENGLISH         VARCHAR2(1596),
    ABOUT_ME_FRENCH          VARCHAR2(1596),
    MARKET_PLACE             VARCHAR2(1),
    BRANCH                   NUMBER(8)
        constraint CONTACT_FK2
            references SKYTEST.BRANCH,
    TITLE                    NUMBER(4),
    OLD_CLIENT_ID            NUMBER(11),
    OLD_AGENT_ID             NUMBER(11),
    REFERRED_BY              VARCHAR2(128),
    TYPE                     NUMBER(4),
    HOUSEHOLD_TYPE           NUMBER(4)  not null,
    EXCLUSIVE_ONLY           VARCHAR2(2),
    PROVIDER_TYPE            NUMBER(4),
    USER_CREATOR             NUMBER(8)
        constraint CREATOR_FK
            references SKYTEST.USERS,
    ONBOARDING               NUMBER(10)
        constraint CONTACT_ONBOARDING_FK
            references SKYTEST.ONBOARDING_STATUS
)
/

comment on column SKYTEST.CONTACT.TYPE is 'SOURCE "WHERE IT CAME FROM"'
/

comment on column SKYTEST.CONTACT.HOUSEHOLD_TYPE is '0 IF IS PRIMARY OR  SECONDARY (1)'
/

create table SKYTEST.COMPLIANCE_DOCUMENT
(
    COMPLIANCE_DOC_INT_ID  NUMBER(8) not null
        constraint COMPLIANCE_DOCUMENT_PK
            primary key,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    OWNER                  NUMBER(8)
        constraint COMPLIANCE_DOCUMENT_FK2
            references SKYTEST.USERS,
    CONTACT                NUMBER(8)
        constraint COMPLIANCE_DOCUMENT_FK1
            references SKYTEST.CONTACT,
    PRINT_DATE             DATE,
    SIGNATURE_DATE         DATE,
    TYPE                   NUMBER(4),
    FILE_PATH              VARCHAR2(512),
    TITLE_EN               VARCHAR2(256),
    TITLE_FR               VARCHAR2(256)
)
/

comment on column SKYTEST.COMPLIANCE_DOCUMENT.OWNER is 'FK to USERS Owner'
/

comment on column SKYTEST.COMPLIANCE_DOCUMENT.CONTACT is 'FK to CLIENT'
/

comment on column SKYTEST.COMPLIANCE_DOCUMENT.TYPE is '1= Life, 2 = Crit, 3 = Invest, 4 = DI'
/

create table SKYTEST.SURVEY_ANSWER
(
    SURVEY_ANSWER_INT_ID NUMBER(8) not null
        constraint SURVEY_ANSWER_PK
            primary key,
    SURVEY_QUESTION      NUMBER(8)
        constraint SURVEY_ANSWER_FK1
            references SKYTEST.SURVEY_QUESTION,
    TYPE                 NUMBER(4),
    CONTACT              NUMBER(8)
        constraint SURVEY_ANSWER_FK2
            references SKYTEST.CONTACT,
    BOOLEAN_VALUE        VARCHAR2(1),
    TEXT_SELECTION_VALUE VARCHAR2(16),
    COMPLIANCE_DOCUMENT  NUMBER(8)
        constraint SURVEY_ANSWER_FK3
            references SKYTEST.COMPLIANCE_DOCUMENT,
    CREATION_DATE        DATE
)
/

comment on column SKYTEST.SURVEY_ANSWER.SURVEY_QUESTION is 'FK to Question'
/

comment on column SKYTEST.SURVEY_ANSWER.TYPE is '1 = YES/NO  2 = multiple choice'
/

comment on column SKYTEST.SURVEY_ANSWER.CONTACT is 'FK to contact, client, advisor, etc'
/

create table SKYTEST.COMPANY_SELECTION
(
    COMPANY_SELECTION_INT_ID NUMBER(8) not null
        constraint COMPANY_SELECTION_PK
            primary key,
    USERS                    NUMBER(8) not null
        constraint COMPANY_SELECTION_FK1
            references SKYTEST.USERS,
    COMPANY_ID               NUMBER(8) not null,
    CREATION_DATE            DATE,
    IQ4                      VARCHAR2(1),
    TEST_PLATFORM            VARCHAR2(1),
    RANKING_INDEX            VARCHAR2(1),
    SHOP4RATES               VARCHAR2(1),
    SHOP4FUNDS               VARCHAR2(1),
    TRAVEL                   VARCHAR2(1),
    PROFILE_TYPE             NUMBER(2)
)
/

create table SKYTEST.FUND_FAVORITE2
(
    FUND_FAVORITE_INT_ID   NUMBER(11) not null
        constraint FUND_FAVORITE2_PK
            primary key,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    FD_FUND                NUMBER(11)
        constraint FUND_FAVORITE2_FD_FUND2_FK1
            references SKYTEST.FD_FUND_FLEX_SEARCH_PY,
    USERS                  NUMBER(11)
        constraint FUND_FAVORITE2_USERS_FK1
            references SKYTEST.USERS,
    INITIAL_VALUE          NUMBER(11, 3),
    PURCHASE_DATE          DATE,
    INITIAL_UNITS          NUMBER(8)
)
/

create table SKYTEST.USERS_PROFILE
(
    USERS_PROFILE_INT_ID   NUMBER(8) not null
        constraint USERS_PROFILE_PK
            primary key,
    USERS                  NUMBER(8)
        constraint USERS_PROFILE_FK1
            references SKYTEST.USERS,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    RANKING_COMPANY_ID     NUMBER(4)
)
/

create table SKYTEST.USERS_COMPANY_SELECTION
(
    USERS_COMPANY_SELECTION_INT_ID NUMBER(8) not null
        constraint USERS_COMPANY_SELECTION_PK
            primary key,
    USERS_PROFILE                  NUMBER(8)
        constraint USERS_COMPANY_SELECTION_FK1
            references SKYTEST.USERS_PROFILE,
    COMPANY_ID                     NUMBER(4),
    IQ4                            VARCHAR2(1),
    TESTING_ZONE                   VARCHAR2(1),
    RATES                          VARCHAR2(1),
    FUNDS                          VARCHAR2(1),
    MORTGAGE                       VARCHAR2(1),
    CREATION_DATE                  DATE,
    LAST_MODIFICATION_DATE         DATE,
    PRODUCT_ID                     NUMBER(8)
)
/

create table SKYTEST.NEWS_COMMUNIQUE
(
    NEWS_COM_INT_ID        NUMBER(8) not null
        constraint NEWS_COMMUNIQUE_PK
            primary key,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    TITLE_ENGLISH          VARCHAR2(128),
    TITLE_FRENCH           VARCHAR2(128),
    DESCRIPTION_ENGLISH    VARCHAR2(2000),
    DESCRIPTION_FRENCH     VARCHAR2(2000),
    EFFECTIVE_DATE         DATE,
    ENDING_DATE            DATE,
    USERS                  NUMBER(8)
        constraint NEWS_COMMUNIQUE_USERS_FK
            references SKYTEST.USERS,
    ADVISOR                NUMBER(8)
        constraint NEWS_COMMUNIQUE_ADVISOR_FK
            references SKYTEST.ADVISOR,
    AGENCY                 NUMBER(8)
        constraint NEWS_COMMUNIQUE_AGENCY_FK
            references SKYTEST.AGENCY
)
/

create table SKYTEST.FUND_FAVORITE
(
    FUND_FAVORITE_INT_ID   NUMBER(11) not null
        constraint FUNDFAVORITE_PK
            primary key,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    FD_FUND                NUMBER(11)
        constraint FUNDFAVORITE_FK2
            references SKYTEST.FD_FUND,
    USERS                  NUMBER(11)
        constraint FUNDFAVORITE_FK1
            references SKYTEST.USERS,
    INITIAL_VALUE          NUMBER(11, 3),
    PURCHASE_DATE          DATE,
    INITIAL_UNITS          NUMBER(8)
)
/

create table SKYTEST.LEAD
(
    LEAD_INT_ID            NUMBER(8) not null,
    LEAD_SOURCE            VARCHAR2(256),
    LEAD_SOURCE_DESC       VARCHAR2(256),
    CAMPAIGN               NUMBER(8)
        constraint LEAD_CAMPAIGN_FK
            references SKYTEST.CAMPAIGN,
    REFERRED_BY            VARCHAR2(256),
    STATUS                 NUMBER(4),
    FIRSTNAME              VARCHAR2(128),
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    DO_NOT_CALL            VARCHAR2(1),
    USERS                  NUMBER(8)
        constraint LEAD_USERS_FK
            references SKYTEST.USERS,
    CONTACT                NUMBER(8),
    BIRTHDATE              DATE,
    EMAIL                  VARCHAR2(128),
    PHONE                  VARCHAR2(32),
    REFERRED_BY_CONTACT_ID NUMBER(8),
    LASTNAME               VARCHAR2(128),
    AFFILIATE_ID           VARCHAR2(128),
    CAMPAIGN_ID            VARCHAR2(128),
    TYPE                   VARCHAR2(8),
    GENDER                 NUMBER(4),
    ADVISOR                NUMBER(8)
        constraint LEAD_ADVISOR_FK
            references SKYTEST.ADVISOR,
    AREA_CODE              VARCHAR2(4),
    MASTER_CODE            VARCHAR2(16),
    HOUSEHOLD_TYPE         NUMBER(4) not null,
    PREFERRED_LANGUAGE     NUMBER(4)
)
/

comment on column SKYTEST.LEAD.CAMPAIGN is 'FK to Campaign'
/

comment on column SKYTEST.LEAD.USERS is 'FK to Users ''owner'''
/

comment on column SKYTEST.LEAD.CONTACT is 'FK to Contact'
/

comment on column SKYTEST.LEAD.REFERRED_BY_CONTACT_ID is 'soft link to Contact'
/

comment on column SKYTEST.LEAD.AFFILIATE_ID is 'from Lead site'
/

comment on column SKYTEST.LEAD.CAMPAIGN_ID is 'from Lead site'
/

comment on column SKYTEST.LEAD.HOUSEHOLD_TYPE is 'IF IS PRIMARY OR SECONDARY'
/

create table SKYTEST.FAST_TEXT_SEARCH
(
    FAST_TEXT_INT_ID       NUMBER(11) not null
        constraint FAST_TEXT_SEARCH_PK
            primary key,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    TYPE                   NUMBER(4),
    OBJECT_ID              NUMBER(11),
    MASTER_GROUP           NUMBER(11)
        constraint FAST_TEXT_SEARCH_FK1
            references SKYTEST.MASTER_GROUP,
    USERS                  NUMBER(11)
        constraint FAST_TEXT_SEARCH_FK2
            references SKYTEST.USERS,
    TAGS                   VARCHAR2(1024)
)
/

create table SKYTEST.USER_SEARCH
(
    USER_SEARCH_INT_ID NUMBER(10) not null
        constraint USER_SEARCH_PK
            primary key,
    SEARCH_NAME        VARCHAR2(64),
    CREATION_DATE      DATE,
    SEARCH_PARAMS      VARCHAR2(512),
    USERS              NUMBER(11) not null
        constraint USER_SEARCH_FK1
            references SKYTEST.USERS,
    TYPE               NUMBER(4)
)
/

comment on column SKYTEST.USER_SEARCH.TYPE is '1-2 = Term, 3 =RRIF, 4 = Mort '
/

create table SKYTEST.USERS_NOTIFICATIONS
(
    USERS         NUMBER(8) not null
        constraint USERS_NOTIFICATIONS_USERS_FK
            references SKYTEST.USERS,
    NOTIFICATIONS NUMBER(8) not null
        constraint USERS_NOTIFICATIONS_FK
            references SKYTEST.NOTIFICATIONS,
    constraint USERS_NOTIFICATIONS_PK
        primary key (USERS, NOTIFICATIONS)
)
/

create index SKYTEST.USERS_NOTIFICATIONS_IDX1
    on SKYTEST.USERS_NOTIFICATIONS (USERS)
/

create index SKYTEST.USERS_NOTIFICATIONS_IDX2
    on SKYTEST.USERS_NOTIFICATIONS (NOTIFICATIONS)
/

create table SKYTEST.REPORT_SAVED
(
    REPORT_SAVED_INT_ID NUMBER(8) not null
        primary key,
    USERS               NUMBER(8)
        constraint REPORT_SAVED_USERS_INT_ID_FK
            references SKYTEST.USERS,
    TIMESTAMP           DATE,
    FILESAVED           BLOB,
    NAME                VARCHAR2(255),
    CREATED_FROM        VARCHAR2(255)
)
/

create table SKYTEST.OPPORTUNITY
(
    OPPORTUNITY_AMOUNT         NUMBER(11, 2),
    LEAD                       NUMBER(8)
        constraint OPPORTUNITY_LEAD_FK
            references SKYTEST.LEAD,
    CREATION_DATE              DATE,
    LAST_MODIFICATION_DATE     DATE,
    TYPE                       NUMBER(4),
    CAMPAIGN                   NUMBER(8)
        constraint OPPORTUNITY_CAMPAIGN_FK
            references SKYTEST.CAMPAIGN,
    STATUS                     NUMBER(4),
    USERS                      NUMBER(8)
        constraint OPPORTUNITY_USERS_FK
            references SKYTEST.USERS,
    DESCRIPTION                VARCHAR2(256),
    OPPORTUNITY_INT_ID         NUMBER(8) not null
        constraint OPPORTUNITY_PK
            primary key,
    COMMISSION_AMOUNT          NUMBER(11, 2),
    CLIENT                     NUMBER(8)
        constraint OPPORTUNITY_CLIENT_FK
            references SKYTEST.CLIENT,
    ADVISOR                    NUMBER(8)
        constraint OPPORTUNITY_FK1
            references SKYTEST.ADVISOR,
    PROBABILITY                NUMBER,
    PROJECTED_CLOSE_DATE       DATE,
    NOTE                       VARCHAR2(512),
    COMPANY_ID                 NUMBER(8),
    PRODUCT_ID                 NUMBER(8),
    FACE_AMOUNT                NUMBER(8),
    HEALTH_CLASS               VARCHAR2(128),
    FOLLOWUP_DATE              DATE,
    FIRST_ALERT                VARCHAR2(3),
    OPPORTUNITY_AMOUNT_MONTHLY NUMBER(11, 2),
    QUOTE_TYPE                 VARCHAR2(1),
    QUOTE_DETAIL               VARCHAR2(256),
    CONTACTS_RELATIONSHIP      NUMBER(11)
        constraint OPPORTUNITY_CONTACTS_REL_FK
            references SKYTEST.CONTACTS_RELATIONSHIP,
    JOINT_TYPE                 VARCHAR2(1),
    PROVINCE_ID                NUMBER(4),
    INSURANCE_TYPE             VARCHAR2(20),
    PLAN_TYPE                  VARCHAR2(1),
    POLICY_NUMBER              VARCHAR2(20),
    LEADS_RELATIONSHIP         NUMBER(11)
        constraint OPPORTUNITY_LEADS_REL_FK
            references SKYTEST.LEADS_RELATIONSHIP
                on delete set null,
    PRODUCT_TYPE               NUMBER(4),
    COMPANY_STRING             VARCHAR2(255),
    PRODUCT_STRING             VARCHAR2(255),
    PREMIUM                    NUMBER(11),
    DECLINED                   NUMBER(8)
        constraint OPORTUNITY_DECLINED_FK
            references SKYTEST.SUNLIFE_DECLINED
)
/

comment on column SKYTEST.OPPORTUNITY.LEAD is 'NOT USED ANYMORE'
/

comment on column SKYTEST.OPPORTUNITY.CAMPAIGN is 'FK to Campaign'
/

comment on column SKYTEST.OPPORTUNITY.USERS is 'FK to ''owner'''
/

comment on column SKYTEST.OPPORTUNITY.CLIENT is 'FK to Client ''conversion'''
/

comment on column SKYTEST.OPPORTUNITY.ADVISOR is 'FK to Advisor'
/

comment on column SKYTEST.OPPORTUNITY.QUOTE_TYPE is 'S=single, M=multi, J=joint, N=Multi-joint  R=Mortgage'
/

comment on column SKYTEST.OPPORTUNITY.INSURANCE_TYPE is 'LIFE or CRIT or DI'
/

comment on column SKYTEST.OPPORTUNITY.PLAN_TYPE is 'T or P   Only for Multi Quotes'
/

comment on column SKYTEST.OPPORTUNITY.PRODUCT_TYPE is 'ProductType       Only for Multi Quotes'
/

comment on column SKYTEST.OPPORTUNITY.COMPANY_STRING is 'Company name for manual entry if the company is not on the system'
/

comment on column SKYTEST.OPPORTUNITY.PRODUCT_STRING is 'Product name for manual entry if the product is not on the system'
/

comment on column SKYTEST.OPPORTUNITY.DECLINED is 'FK to sunlife_declined in case is a oportunity from a declined'
/

create table SKYTEST.ACTIVITY
(
    ACTIVITY_INT_ID        NUMBER(8) not null
        constraint ACTIVITY_PK
            primary key,
    ACTIVITY_TYPE          NUMBER(4) not null,
    CREATION_DATE          DATE,
    EFFECTIVE_DATE         DATE,
    DUE_DATE               DATE,
    OWNER                  NUMBER(8)
        constraint ACTIVITY_OWNER_FK
            references SKYTEST.USERS,
    REFERENCE_INT_ID       NUMBER(8),
    REFERENCE_TYPE         NUMBER(4),
    HAS_ATTACHMENT         VARCHAR2(1),
    CATEGORY               NUMBER(4),
    STATUS                 NUMBER(4) not null,
    CLOSED_ON_DATE         DATE,
    ASSIGNED_BY            NUMBER(8)
        constraint ACTIVITY_ASSIGNED_BY_FK
            references SKYTEST.USERS,
    LAST_MODIFICATION_DATE DATE,
    TITLE                  VARCHAR2(128),
    ACCEPTED               VARCHAR2(1),
    PARENT_ACTIVITY_ID     NUMBER(8)
        constraint FATHER_FK
            references SKYTEST.ACTIVITY,
    ATTACHMENTS            VARCHAR2(512),
    ALL_DAY                VARCHAR2(1),
    STYLE_CLASS            VARCHAR2(64),
    EDITABLE               VARCHAR2(1),
    FIRST_ALERT            VARCHAR2(3),
    OPPORTUNITY            NUMBER(8)
        constraint ACTIVITY_OPP_FK
            references SKYTEST.OPPORTUNITY,
    CONTACT                NUMBER(8)
        constraint ACTIVITY_CONTACT
            references SKYTEST.CONTACT,
    ACCOUNT                NUMBER(8)
        constraint ACTIVITY_ACCOUNT_FK
            references SKYTEST.ACCOUNT,
    ACTIVITY_RESULT        NUMBER(4),
    RECIPIENT              VARCHAR2(256),
    ACTIVITY_GROUP         NUMBER(8),
    TYPE_ID                NUMBER(8),
    TYPE                   NUMBER(4),
    TITLE_TYPE             NUMBER(4),
    STATE                  NUMBER(4),
    RECIPIENT_FULLNAME     VARCHAR2(255),
    VIEWED                 NUMBER(1),
    OTP_VALUE              VARCHAR2(6),
    OTP_CREATION_DATE      DATE,
    OTP_USER               VARCHAR2(255),
    DESCRIPTION            LONG,
    LEAD                   NUMBER(8)
        constraint ACTIVITY_LEAD_FK
            references SKYTEST.LEAD
)
/

comment on column SKYTEST.ACTIVITY.OWNER is 'FK to USERS ''owner'''
/

comment on column SKYTEST.ACTIVITY.CATEGORY is 'actual Reason from activity_reason type'
/

comment on column SKYTEST.ACTIVITY.ASSIGNED_BY is 'FK to USERS ASSIGNED BY'
/

comment on column SKYTEST.ACTIVITY.PARENT_ACTIVITY_ID is 'If is null the activity is the main if not is the id of the father'
/

comment on column SKYTEST.ACTIVITY.FIRST_ALERT is '5m,15m,30m,1h,2h,1d,2d,1w'
/

comment on column SKYTEST.ACTIVITY.OPPORTUNITY is 'FK to OPPORTUNITY'
/

comment on column SKYTEST.ACTIVITY.CONTACT is 'FK to CONTACT'
/

comment on column SKYTEST.ACTIVITY.ACCOUNT is 'FK to ACCOUNT'
/

comment on column SKYTEST.ACTIVITY.TYPE_ID is 'Depending of the type, is the "FK" to type selected'
/

comment on column SKYTEST.ACTIVITY.TYPE is 'Type of activity ex: 
1 -> Opportunity,
2 -> Lead, (Keep adding more),
3 -> Contact,
4 -> License,
5 -> EandO,

'
/

comment on column SKYTEST.ACTIVITY.TITLE_TYPE is 'New way to set the title'
/

comment on column SKYTEST.ACTIVITY.STATE is 'same as status but for the advisor'
/

comment on column SKYTEST.ACTIVITY.VIEWED is '0 - no
1 - yes'
/

comment on column SKYTEST.ACTIVITY.OTP_VALUE is '6 values one time authenticator'
/

comment on column SKYTEST.ACTIVITY.OTP_CREATION_DATE is 'OTP creation date'
/

comment on column SKYTEST.ACTIVITY.LEAD is 'FK to LEAD'
/

create table SKYTEST.USERS_ACTIVITY
(
    USERS    NUMBER(8) not null
        constraint USERS_ACTIVITY_USERS_FK
            references SKYTEST.USERS,
    ACTIVITY NUMBER(8) not null
        constraint USERS_ACTIVITY_ACTIVITY_FK
            references SKYTEST.ACTIVITY,
    constraint USERS_ACTIVITY_PK
        primary key (USERS, ACTIVITY)
)
/

create table SKYTEST.USERS_INVOICE
(
    USERS_INVOICE_INT_ID NUMBER(8)  not null
        constraint USERS_INVOICE_PK
            primary key,
    USERS                NUMBER(8)  not null
        constraint USERS_INVOICE_USERS_FK1
            references SKYTEST.USERS,
    INVOICENO            NUMBER(11) not null,
    PRINTSTATUS          NUMBER(1),
    PRINTDATE            DATE,
    PAID                 VARCHAR2(1),
    INVOICE_AMOUNT       NUMBER(8, 2),
    PDF_STORED           BLOB,
    NOTES                VARCHAR2(255),
    PAID_BY              NUMBER(1),
    FREQUENCE            VARCHAR2(1),
    PAYMENT_DUE_DATE     DATE,
    PAYMENT_DATE         DATE,
    PAYMENT_START_DATE   DATE,
    TRIAL                NUMBER(1)
)
/

comment on column SKYTEST.USERS_INVOICE.TRIAL is '1 is trial 0 normal'
/

create table SKYTEST.USERS_INVOICE_DETAIL
(
    USERS_INVOICE_DETAIL_INT_ID NUMBER(8) not null
        constraint USERS_INVOICE_DETAIL_PK
            primary key,
    USERS_INVOICE               NUMBER(8) not null
        constraint USERS_INVOICE_DETAIL_USER_FK1
            references SKYTEST.USERS_INVOICE,
    SERVICE_PROFILE             NUMBER(8) not null
        constraint USERS_INVOICE_DETAIL_SERV_FK1
            references SKYTEST.SERVICE_PROFILE,
    QTY                         NUMBER(3) not null,
    PAYMENT_TYPE                VARCHAR2(1),
    GST_TAX                     NUMBER(9, 2),
    PST_TAX                     NUMBER(9, 2),
    CUSTOM_MSG                  VARCHAR2(350),
    PAYMENT_DUE_DATE            DATE,
    PAYMENT_DATE                DATE,
    PAYMENT_START_DATE          DATE,
    HST_TAX                     NUMBER(9, 2),
    QST_TAX                     NUMBER(9, 2),
    INVOICE_AMOUNT              NUMBER(8, 2)
)
/

comment on column SKYTEST.USERS_INVOICE_DETAIL.SERVICE_PROFILE is 'dropdown choices from service_profile display. Save service_int_id, but show in dropdwon name_eng or name_fre'
/

comment on column SKYTEST.USERS_INVOICE_DETAIL.PAYMENT_TYPE is 'Year or Montly'
/

create table SKYTEST.WEB_SERVICE_USERS
(
    WEB_SERVICE_INT_ID NUMBER(11) not null
        constraint WEB_SERVICE_USERS_PK
            primary key,
    USERS              NUMBER(11) not null
        constraint WEB_SERVICE_USERS_USERS_FK1
            references SKYTEST.USERS,
    TOKEN              VARCHAR2(255),
    IP                 VARCHAR2(20),
    SERVICE            NUMBER(11)
        constraint WEB_SERVICE_USERS_SERVICE_FK1
            references SKYTEST.SERVICE_PROFILE,
    CREATED_DATE       DATE,
    EXPIRE_DATE        DATE,
    SECRET_KEY         VARCHAR2(100),
    SUBJECT            VARCHAR2(50),
    INVOICE            NUMBER(11)
        constraint TOKEN_INVOICE_FK
            references SKYTEST.USERS_INVOICE
)
/

comment on column SKYTEST.WEB_SERVICE_USERS.EXPIRE_DATE is 'expire date of the token, also used to build the token'
/

comment on column SKYTEST.WEB_SERVICE_USERS.SECRET_KEY is 'information used to build the token'
/

comment on column SKYTEST.WEB_SERVICE_USERS.SUBJECT is 'information used to build the token'
/

create table SKYTEST.WS_QUOTES
(
    WS_QUOTES_INT_ID  NUMBER(11) not null
        constraint WS_QUOTES_PK
            primary key,
    QUOTED_DATE       DATE       not null,
    IP_ADDRESS        VARCHAR2(20),
    WEB_SERVICE_USERS NUMBER(11)
        constraint WS_QUOTES_WEB_SERVICE_USE_FK1
            references SKYTEST.WEB_SERVICE_USERS,
    LINK_NAME         VARCHAR2(75),
    FACE_AMOUNT       NUMBER(11),
    DATE_OF_BIRTH     DATE,
    GENDER            VARCHAR2(1),
    SMOKER            VARCHAR2(1),
    PROVINCE_CODE     VARCHAR2(3),
    COMPANY_ID        VARCHAR2(20),
    PRODUCT_ID        VARCHAR2(20),
    PRODUCT_TYPE      VARCHAR2(20),
    QUOTE_TYPE        NUMBER(2),
    MORT_TERM         VARCHAR2(20),
    OPEN_CLOSED       VARCHAR2(20),
    FIXED_VAR         VARCHAR2(20),
    AGE               NUMBER(3),
    REQUEST_SITE      VARCHAR2(75)
)
/

comment on column SKYTEST.WS_QUOTES.WS_QUOTES_INT_ID is 'WS_QUOTES_SEQ'
/

comment on column SKYTEST.WS_QUOTES.WEB_SERVICE_USERS is 'FOREIGN KEY TO TABLE WEB_SERVICE_USERS'
/

comment on column SKYTEST.WS_QUOTES.QUOTE_TYPE is '1=INS 2=MORT'
/

create table SKYTEST.USERS_NATIONAL_GEOGRAPHY
(
    USERS              NUMBER(8)
        constraint USERS_FK4
            references SKYTEST.USERS,
    NATIONAL_GEOGRAPHY NUMBER(8)
        constraint NATIONAL_GEOGRAPHY
            references SKYTEST.NATIONAL_GEOGRAPHY
)
/

create table SKYTEST.USERS_MARKETING_REGION
(
    USERS            NUMBER(8)
        constraint USERS_FK5
            references SKYTEST.USERS,
    MARKETING_REGION NUMBER(8)
        constraint MARKETING_REGION_FK
            references SKYTEST.MARKETING_REGION
)
/

create table SKYTEST.USERS_DISTRICT
(
    USERS    NUMBER(8)
        constraint USERS_FK6
            references SKYTEST.USERS,
    DISTRICT NUMBER(8)
        constraint DISTRICT_FK
            references SKYTEST.DISTRICT
)
/

create table SKYTEST.USERS_FINANCIAL_CENTER
(
    USERS            NUMBER(8)
        constraint USERS_FK7
            references SKYTEST.USERS,
    FINANCIAL_CENTER NUMBER(8)
        constraint FINANCIAL_CENTER_FK
            references SKYTEST.BRANCH
)
/

create table SKYTEST.USERS_WEB_CONTENT
(
    USERS       NUMBER(8)
        constraint USERS_WEB_CONTENT_FK
            references SKYTEST.USERS,
    WEB_CONTENT NUMBER(8)
        constraint WEB_CONTENT_USERS_FK
            references SKYTEST.WEB_CONTENT
)
/

create table SKYTEST.COMMENT_OBJECT
(
    COMMENT_INT_ID    NUMBER(10) not null
        primary key,
    CREATION_DATE     DATE,
    ONBOARDING_STATUS NUMBER(11)
        constraint ONBOARDING_COMMENT_FK
            references SKYTEST.ONBOARDING_STATUS,
    COMMENT_DESC      VARCHAR2(500),
    OWNER             NUMBER(11)
        constraint OWNER_STATUS_FK
            references SKYTEST.USERS
)
/

create table SKYTEST.CALENDAR_EVENT
(
    CALENDAR_EVENT_INT_ID NUMBER(8)    default "SKYTEST"."ISEQ$$_124963".nextval generated as identity
		primary key,
    EVENT_TITLE           VARCHAR2(255) not null,
    EVENT_DESC_EN         VARCHAR2(1000),
    EVENT_DESC_FR         VARCHAR2(1000),
    START_DATE            TIMESTAMP(6)  not null,
    END_DATE              TIMESTAMP(6)  not null,
    LOCATION              VARCHAR2(255),
    IS_RECURRING          CHAR(5)      default 'N'
        check (IS_RECURRING IN ('Y', 'N')),
    RECURRING_PATTERN     VARCHAR2(50),
    CREATED_AT            TIMESTAMP(6) default CURRENT_TIMESTAMP,
    UPDATED_AT            TIMESTAMP(6) default CURRENT_TIMESTAMP,
    OWNER                 NUMBER(8)     not null
        constraint CALENDAR_EVENT_OWNER_FK
            references SKYTEST.USERS,
    CALENDAR_CATEGORY     NUMBER(1),
    FILE_NOTE             NUMBER(8)
        constraint CALENDAR_EVENT_FILE_NOTE_FK
            references SKYTEST.STORED_FILE_NOTE,
    BACKGROUND_COLOR      VARCHAR2(20),
    LEAD                  NUMBER(8)
        constraint CALENDAR_EVENT_LEAD_FK
            references SKYTEST.LEAD,
    CONTACT               NUMBER(8)
        constraint CALENDAR_EVENT_CONTACT_FK
            references SKYTEST.CONTACT
)
/

comment on column SKYTEST.CALENDAR_EVENT.CALENDAR_CATEGORY is 'this will be just a number to match a css and will be inserted by the user in a slection'
/

comment on column SKYTEST.CALENDAR_EVENT.FILE_NOTE is 'a one to one to activity, we will need to add more of those FK for the rest of incoming stuff'
/

comment on column SKYTEST.CALENDAR_EVENT.LEAD is 'for a new lead default event'
/

comment on column SKYTEST.CALENDAR_EVENT.CONTACT is 'for a new contact default event'
/

create table SKYTEST.ALERT
(
    ALERT_INT_ID           NUMBER(11) not null
        constraint ALERT_PK
            primary key,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    OWNER                  NUMBER(11)
        constraint ALERT_FK1
            references SKYTEST.USERS,
    ACTIVITY               NUMBER(11)
        constraint ALERT_FK2
            references SKYTEST.ACTIVITY,
    START_DATE             DATE,
    END_DATE               DATE,
    ACKNOWLEDGE            VARCHAR2(1),
    ACK_DATE               DATE,
    FROM_WHOM              VARCHAR2(128),
    TITLE                  VARCHAR2(128),
    MESSAGE                LONG,
    CALENDAR_EVENT         NUMBER(11)
        constraint ALERT_CALENDAR_EVENT_FK
            references SKYTEST.CALENDAR_EVENT
)
/

comment on column SKYTEST.ALERT.OWNER is 'USERS''S'
/

comment on column SKYTEST.ALERT.ACTIVITY is 'ACTIVITY'
/

create table SKYTEST."ALERT_copy1"
(
    ALERT_INT_ID           NUMBER(11) not null
        primary key,
    CREATION_DATE          DATE,
    LAST_MODIFICATION_DATE DATE,
    OWNER                  NUMBER(11)
        references SKYTEST.USERS,
    ACTIVITY               NUMBER(11)
        references SKYTEST.ACTIVITY,
    START_DATE             DATE,
    END_DATE               DATE,
    ACKNOWLEDGE            VARCHAR2(1),
    ACK_DATE               DATE,
    FROM_WHOM              VARCHAR2(128),
    TITLE                  VARCHAR2(128),
    MESSAGE                LONG,
    CALENDAR_EVENT         NUMBER(11)
        references SKYTEST.CALENDAR_EVENT
)
/

comment on column SKYTEST."ALERT_copy1".OWNER is 'USERS''S'
/

comment on column SKYTEST."ALERT_copy1".ACTIVITY is 'ACTIVITY'
/

