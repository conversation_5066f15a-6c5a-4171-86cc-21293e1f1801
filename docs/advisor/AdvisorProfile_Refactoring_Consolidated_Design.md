# Advisor Profile Refactoring - Consolidated Design Considerations

This document outlines the overarching design principles and strategies for the refactoring of the Advisor Profile functionality into a Spring Boot-based microservice architecture.

## 1. Consolidated API Design Principles

### 1.1. Versioning
-   Strategy: (e.g., URI Path Versioning - `/api/v1/...`, `/api/v2/...`)
-   Rationale:

### 1.2. Naming Conventions
-   Endpoints: (e.g., Kebab-case for paths: `/advisor-profiles`, `/advisor-notes`)
-   JSON Properties: (e.g., Camel-case: `advisorId`, `firstName`)
-   DTOs: (e.g., Suffix with `DTO`: `AdvisorProfileDTO`, `NoteDTO`)
-   Rationale:

### 1.3. Pagination
-   Strategy: (e.g., Offset-based: `?page=0&size=20`, or Cursor-based)
-   Default Size:
-   Max Size:
-   Response Structure: (e.g., Including total elements, total pages)
-   Rationale:

### 1.4. Sorting
-   Strategy: (e.g., `?sort=fieldName,asc` or `?sort=fieldName,desc`)
-   Multiple Fields:
-   Default Sort Order:
-   Rationale:

### 1.5. Filtering
-   Strategy: (e.g., Query parameters for specific fields: `?status=ACTIVE&province=QC`)
-   Complex Filtering: (e.g., Support for operators like `gt`, `lt`, `like`)
-   Rationale:

### 1.6. Request/Response Standards
-   Successful Responses: (e.g., `200 OK`, `201 Created`, `204 No Content`)
-   Error Responses: (Consistent error DTO, use of appropriate HTTP status codes - see Global Error Handling)
-   Date/Time Format: (e.g., ISO 8601 for all date/timestamps: `YYYY-MM-DDTHH:mm:ss.sssZ`)

## 2. Overall Service Layer Architecture

### 2.1. Design Patterns
-   **Service Facade:** Each primary business capability or sub-domain within the Advisor Profile (e.g., `AdvisorProfileService`, `AdvisorNoteService`, `AdvisorDocumentService`) will act as a facade. This simplifies the API for controllers and other consumers.
-   **Domain-Driven Design (DDD) Principles (Lightweight):**
    *   **Aggregates:** `Advisor` will be the primary aggregate root. Operations on related entities (e.g., `Contact`, `License`, `Company`) will typically go through the `Advisor` aggregate to ensure consistency.
    *   **Entities & Value Objects:** Clearly distinguish between entities (with identity, e.g., `Advisor`, `License`) and value objects (immutable, e.g., `Address`, `Money`).
    *   **Services:** Business logic that doesn't naturally fit within an entity will reside in service classes.
-   **DTOs (Data Transfer Objects):** Used extensively for API request/response and for transferring data between layers to decouple API contracts from internal domain models. MapStruct will be considered for mapping between DTOs and entities.
-   **Command/Query Separation (Conceptual):** While not strictly CQRS, service methods will generally be either commands (mutating state, e.g., `updateAdvisorProfile`) or queries (retrieving data, e.g., `getAdvisorProfile`). This promotes clarity.
-   Rationale:
    *   **Maintainability & Readability:** Clear separation of concerns and well-defined patterns make the codebase easier to understand and maintain.
    *   **Testability:** Decoupled components are easier to test in isolation.
    *   **Scalability:** Clear boundaries can help in identifying areas for future microservice extraction if needed.
    *   **Consistency:** DDD principles help in modeling the domain accurately and consistently.

### 2.2. Inter-Service Communication (if applicable)
-   Strategy: Initially, this is a single service. If future microservices are introduced (e.g., a dedicated `DocumentManagementService` or `NotificationService`), communication will primarily be:
    *   **Asynchronous Messaging (Preferred for decoupling):** Using Kafka or RabbitMQ for events (e.g., `AdvisorUpdatedEvent`, `DocumentUploadedEvent`). This enhances resilience and allows services to evolve independently.
    *   **Synchronous REST calls (For immediate needs):** For direct queries or commands where an immediate response is required.
-   Resilience:
    *   **Retries:** Implement retry mechanisms for transient failures in synchronous calls.
    *   **Circuit Breakers (e.g., Resilience4j):** To prevent cascading failures if a downstream service is unavailable.
    *   **Idempotent Consumers:** Ensure message consumers can handle duplicate messages if using asynchronous communication.
-   Rationale:
    *   **Decoupling:** Asynchronous communication reduces tight coupling between services.
    *   **Resilience:** Patterns like circuit breakers and retries improve the overall system's fault tolerance.
    *   **Scalability:** Asynchronous systems can often scale more effectively.

### 2.3. Service Granularity
-   **Bounded Context:** The `AdvisorProfileService` will manage the "Advisor Profile" bounded context. This includes all core information directly related to an advisor (personal, business, licenses, contracts, EFT, notes, status).
-   **Potential Future Breakdown:**
    *   **Document Management:** If document handling becomes very complex (versioning, advanced security, large volumes), it could be extracted into a separate `DocumentManagementService`.
    *   **Workflow Management:** Onboarding/offboarding workflows, if they involve complex orchestration beyond simple state changes, might warrant a dedicated `WorkflowService` (potentially integrating with an engine like Camunda).
    *   **Audit Trail:** A highly detailed and generic audit trail mechanism could become a shared `AuditService`.
-   Rationale:
    *   **Cohesion:** Keep related functionalities together within a bounded context.
    *   **Manageable Size:** Avoid creating a "god service" by identifying sub-domains that could be independent.
    *   **Team Autonomy:** Smaller, focused services can potentially be managed by different teams in the future.

### 2.4. Idempotency
-   Strategy for `PUT`, `PATCH`, `DELETE` operations: These HTTP methods are expected to be idempotent by definition. The service layer will ensure that retrying these operations multiple times with the same input yields the same result without unintended side effects (e.g., updating a resource multiple times with the same data results in the same final state as a single update).
-   Strategy for `POST` operations:
    *   `POST` is not inherently idempotent. For critical resource creation endpoints where clients might retry (e.g., creating a new advisor, submitting a critical document), consider supporting an `Idempotency-Key` header.
    *   The server would store the `Idempotency-Key` for a limited time and if a subsequent `POST` request arrives with the same key, the server can return the original response without re-processing the request.
-   Rationale:
    *   **Reliability:** Ensures that network glitches or client retries do not lead to inconsistent data or duplicate resources.
    *   **Client Confidence:** Provides a more robust API for clients, especially in distributed systems.

## 3. Repository Layer Strategy

### 3.1. JPA Best Practices
-   **Entity Relationships:**
    *   **Default to Lazy Fetching:** Use `FetchType.LAZY` for `@OneToMany`, `@ManyToMany`, and `@OneToOne` (on the non-owning side or where appropriate) relationships to avoid loading unnecessary data and prevent N+1 problems by default. Use `FetchType.EAGER` sparingly and only when the related entity is almost always needed with the parent.
    *   **Bidirectional Relationships:** Carefully manage both sides of bidirectional relationships (e.g., using `mappedBy` on the inverse side, and ensuring `hashCode()`/`equals()` are implemented correctly and do not cause infinite loops or rely on mutable fields involved in relationships).
    *   **Cascade Types:** Use cascade operations (e.g., `CascadeType.ALL`, `PERSIST`, `MERGE`, `REMOVE`) judiciously. `CascadeType.ALL` can be convenient but also dangerous if not fully understood. Prefer more specific cascade types. `orphanRemoval=true` is useful for child entities that should not exist without their parent (e.g., an `Address` specific to an `Advisor`).
    *   **Primary Keys:** Use `Long` with sequence generation (e.g., `@GeneratedValue(strategy = GenerationType.SEQUENCE)`) for most entities.
-   **Transaction Management:** (Detailed in Section 6). Transactions will be managed at the service layer. Repositories themselves will operate within these transactions.
-   **Use of Projections (DTO Projections):**
    *   For read-only queries, especially for lists or search results, use Spring Data JPA projections (interface-based or class-based DTO projections) to fetch only the required fields. This reduces data transfer and can improve query performance.
    *   Example: `List<AdvisorSummaryDTO> findByStatus(AdvisorStatus status);`
-   **Repository Interfaces:** Extend `JpaRepository` or `PagingAndSortingRepository`. Custom query methods will follow Spring Data naming conventions or use `@Query` for more complex JPQL/SQL.
-   Rationale:
    *   **Performance:** Lazy loading and projections help in fetching only necessary data.
    *   **Maintainability:** Clear relationship definitions and adherence to JPA best practices make entities easier to manage.
    *   **Data Integrity:** Correct use of cascade types and `orphanRemoval` helps maintain data consistency.

### 3.2. Query Optimization
-   **Strategy:**
    *   **Spring Data JPA Query Methods:** Leverage derived query methods for simple queries.
    *   **`@Query` Annotation:** Use for custom JPQL or native SQL queries when derived queries are insufficient or for optimized queries (e.g., complex joins, specific database functions).
    *   **Entity Graphs (`@EntityGraph`):** Use to solve N+1 problems by defining which associations to fetch eagerly for specific queries, overriding default lazy fetching.
    *   **Batching:** Configure JDBC batching for bulk inserts/updates if performance becomes an issue in specific scenarios (e.g., data migration, large batch operations).
-   **Indexing:**
    *   Identify and create database indexes on columns frequently used in `WHERE` clauses, `JOIN` conditions, and `ORDER BY` clauses.
    *   Analyze query execution plans to verify index usage.
    *   Key fields for indexing will include: `advisor.status`, `advisor.lastName`, `advisor.advisorCode`, foreign keys, and date fields used in range queries.
-   **N+1 Problem Mitigation:**
    *   **Entity Graphs:** Preferred JPA solution for selectively fetching related entities.
    *   **JPQL `JOIN FETCH`:** Explicitly fetch related collections/entities in a single query.
    *   **Projections:** Avoid loading entire child entities if only a few fields are needed.
    *   **Batch Fetching (`@BatchSize`):** Hibernate-specific annotation to fetch collections or entities in batches, reducing the number of subsequent queries.
-   Rationale:
    *   **Performance:** Optimized queries and proper indexing are crucial for application responsiveness, especially with growing data volumes.
    *   **Resource Efficiency:** Avoids unnecessary database load and network traffic.

### 3.3. Potential Use of Querydsl or Specifications (JPA Criteria API)
-   **When to use:** For building dynamic queries where conditions are determined at runtime, often based on user input (e.g., complex search filters in the Advisor Search functionality).
-   **Benefits:**
    *   **Type Safety:** Querydsl offers strong type safety, catching errors at compile time rather than runtime (compared to string-based JPQL).
    *   **Readability & Maintainability:** Can make complex query logic more readable and easier to refactor than concatenating JPQL strings.
    *   **Dynamic Queries:** Simplifies the construction of queries with optional criteria, multiple conditions, and dynamic sorting.
-   **Considerations:**
    *   **Learning Curve:** Introduces a new API/tool for the team.
    *   **Setup:** Requires initial setup for annotation processing (for Querydsl).
    *   **Complexity:** For very simple dynamic queries, it might be overkill. Spring Data JPA Specifications (based on Criteria API) offer a JPA-native way to achieve similar results with less setup but can be more verbose than Querydsl.
-   **Recommendation:** Start with Spring Data JPA Specifications for dynamic queries. If queries become extremely complex and type safety becomes a major concern, evaluate Querydsl.

## 4. Comprehensive Security Design

### 4.1. Authentication
-   Mechanism: (e.g., OAuth 2.0 / OpenID Connect with Keycloak/Okta)
-   Token Type: (e.g., JWT)
-   Token Validation:
-   Rationale:

### 4.2. Authorization Model
-   Strategy: (e.g., Role-Based Access Control (RBAC), Attribute-Based Access Control (ABAC))
-   Granularity: (e.g., Endpoint-level, Method-level, Field-level if necessary)
-   Implementation: (e.g., Spring Security annotations: `@PreAuthorize`, `@PostAuthorize`)
-   Rationale:

### 4.3. Data Protection
-   Sensitive Data Handling: (e.g., Encryption at rest for PII, EFT details; Masking in logs/UI for non-privileged users)
-   Input Validation: (To prevent injection attacks - covered by Bean Validation and service-level checks)
-   Output Encoding: (To prevent XSS if data is rendered directly by any downstream client, though less critical for pure REST APIs)
-   Rationale:

### 4.4. Token Management
-   Issuance:
-   Storage (Client-side): (e.g., Secure HTTPOnly cookies, browser local storage - with caveats)
-   Expiration & Refresh:
-   Revocation:
-   Rationale:

### 4.5. API Gateway
-   Role: (e.g., Centralized SSL termination, rate limiting, request routing, coarse-grained security checks)
-   Consideration: (e.g., Spring Cloud Gateway, Apigee)

## 5. Global Error Handling Strategy

### 5.1. Consistent Error Responses
-   Standard Error DTO:
    ```json
    {
      "timestamp": "YYYY-MM-DDTHH:mm:ss.sssZ",
      "status": 400,
      "error": "Bad Request",
      "message": "Validation failed for object='createAdvisorRequest'. Error count: 2",
      "path": "/api/v1/advisors",
      "details": [
        {
          "field": "email",
          "message": "must be a well-formed email address"
        },
        {
          "object": "addressDTO",
          "field": "postalCode",
          "message": "must not be blank"
        }
      ]
    }
    ```
-   Rationale:

### 5.2. Exception Hierarchy
-   Custom Exceptions: (e.g., `ResourceNotFoundException`, `ValidationException`, `UnauthorizedOperationException`)
-   Mapping to HTTP Status Codes:
    -   `400 Bad Request`: Validation errors, malformed requests.
    -   `401 Unauthorized`: Authentication failures.
    -   `403 Forbidden`: Authorization failures.
    -   `404 Not Found`: Resource not found.
    -   `409 Conflict`: Business rule violation (e.g., duplicate resource).
    -   `500 Internal Server Error`: Unhandled exceptions.
-   Use of `@ControllerAdvice` and `@ExceptionHandler`.
-   Rationale:

## 6. Transaction Management Approach

### 6.1. Boundaries
-   Where to define: (Typically at the Service layer method level using `@Transactional`)
-   Rationale:

### 6.2. Propagation
-   Default: (`Propagation.REQUIRED`)
-   Specific Needs: (e.g., `Propagation.REQUIRES_NEW` for independent sub-operations, `Propagation.NESTED` if underlying DB supports savepoints)
-   Rationale:

### 6.3. Rollback Strategies
-   Default: (Rollback on any `RuntimeException` and `Error`)
-   Custom: (Specify `rollbackFor` or `noRollbackFor` specific checked exceptions if necessary)
-   Rationale:

### 6.4. Read-Only Transactions
-   Use of `@Transactional(readOnly = true)` for query methods to optimize performance.
-   Rationale:

## 7. Data Migration Strategy (If Applicable)

### 7.1. Scope
-   Identify data to be migrated from the existing JSF system's database to the new service's database schema.
-   (e.g., Advisor core profile, contacts, companies, licenses, notes, EFT, etc.)

### 7.2. Approach
-   (e.g., Big Bang vs. Phased, ETL scripts, use of tools like Flyway/Liquibase for schema management and potentially for migration scripts)
-   Downtime considerations.

### 7.3. Data Transformation & Validation
-   Mapping old schema to new schema.
-   Data cleansing requirements.
-   Post-migration validation checks.

### 7.4. Rollback Plan
-   Strategy if migration fails.

## 8. Deployment Considerations

### 8.1. Environment Configuration
-   Strategy: (e.g., Spring Profiles, externalized configuration using Spring Cloud Config Server, HashiCorp Vault for secrets)
-   Rationale:

### 8.2. CI/CD Pipeline
-   Tools: (e.g., Jenkins, GitLab CI, GitHub Actions)
-   Stages: (e.g., Build, Unit Test, Integration Test, SonarQube Analysis, Package, Deploy to Dev/QA/Prod)
-   Rationale:

### 8.3. Containerization & Orchestration
-   Strategy: (e.g., Docker for containerization, Kubernetes for orchestration)
-   Rationale:

### 8.4. Logging & Monitoring
-   Logging: (e.g., SLF4J with Logback/Log4j2, structured logging, centralized logging with ELK stack or Splunk)
-   Monitoring: (e.g., Spring Boot Actuator, Prometheus, Grafana for metrics and dashboards; Distributed tracing with OpenTelemetry/Jaeger)
-   Alerting:
-   Rationale:

## 9. Testing Strategy

### 9.1. Unit Tests
-   Scope: (Test individual classes and methods in isolation - Services, Controllers, Utility classes)
-   Tools: (e.g., JUnit 5, Mockito)
-   Coverage Goals:
-   Rationale:

### 9.2. Integration Tests
-   Scope: (Test interactions between components - Service to Repository, Controller to Service)
-   Tools: (e.g., Spring Boot's `@SpringBootTest`, Testcontainers for database/external dependencies)
-   In-memory vs. Real Dependencies:
-   Rationale:

### 9.3. API/Contract Tests
-   Scope: (Test API endpoints against their defined contracts - request/response formats, status codes)
-   Tools: (e.g., RestAssured, Postman/Newman, Spring Cloud Contract)
-   Consumer-Driven Contract Testing (if applicable):
-   Rationale:

### 9.4. Performance Tests
-   Scope: (Assess responsiveness, stability, and scalability under load)
-   Tools: (e.g., JMeter, Gatling, k6)
-   Key Scenarios & Metrics:
-   Rationale:

### 9.5. Security Tests
-   Scope: (Vulnerability scanning, penetration testing, DAST/SAST tools)
-   Tools: (e.g., OWASP ZAP, SonarQube security hotspots)
-   Frequency:
-   Rationale:

## 10. Non-Functional Requirements (NFRs) Summary
-   **Performance:** (e.g., Average API response time < 200ms for 95th percentile)
-   **Scalability:** (e.g., System should handle X concurrent users or Y requests per second)
-   **Availability:** (e.g., 99.9% uptime)
-   **Reliability:** (e.g., Error rates, data consistency)
-   **Maintainability:** (Code complexity, documentation, ease of updates)
-   **Security:** (Compliance with specific standards, e.g., OWASP Top 10)
-   **Usability (for API consumers):** (Clarity of API documentation, ease of integration)

*(This document will be updated iteratively as design decisions are finalized.)*
