## Advisor ProfileU.xhtml – 运行时依赖的后端 Bean 与核心数据表

下面的梳理只关注 **Advisor ProfileU.xhtml** 直接或间接会用到的 Bean 以及这些 Bean 操作的持久化实体（= 数据库表）。  
代码行、具体实现细节均已省略，仅说明“谁→用什么→干什么”。

---

### 1. 后端 Bean 之间的分工

| Bean | 典型 EL 前缀 | 在页面中的角色 | 主要职责 |
|------|--------------|---------------|-----------|
| `AdvisorNavigationBean` | `#{advisorNavigationBean…}` | **主视图 Bean**（读/改个人资料各 Tab） | 把选中的 `Advisor` 及其关联对象（Company、License、Liability、EFT、Contract 等）准备成页面可用的 POJO；同时控制 Tab 切换、按钮可见性等 |
| `AdvisorManagementBean` | `#{advisorManagementBean…}` | **新增/编辑 Bean**（保存、发送确认邮件等操作） | 组装/验证输入并调用 *Facade* 持久化；生成确认邮件、活动日志等 |
| `AGANavigationBean` | `#{agaNavigationBean…}` | **AGA 场景下的代理 Bean** | 当用户以 AGA 身份浏览/修改 Advisor 时接管导航与数据加载；页面与 `AdvisorNavigationBean` 共用大部分组件 |
| `ContractNavigationBean` | `#{contractNavigationBean…}` | **合同子 Tab Bean** | 载入/筛选 `ContractSetup` 列表、控制合同 Tab、下载附件等 |
| （其他注入 Bean）`ApplicationMainBean`、`OnboardingBean`、`NetStorageBean`、`DocumentCentreBean`、`CommonContractingBean` … | 被上面四个主要 Bean 调用 | 通用函数、语言切换、文件存储、文档中心等 |


### 2. Facade / 实体 = 关键数据库表

下表列出四个 Bean 直接注入的 *Facade*，即页面最终会碰到的持久化实体（JPA→表）。只列最核心的，辅助表如日志、验证码等忽略。

| 实体 (JPA) | 说明 | 与 Advisor 的关系 |
|------------|------|-------------------|
| `Advisor` | 顾问主表 | 1 (根对象) |
| `Contact` / `Address` / `Phone` / `Email` | 联系方式 | 1 : 1/多 |
| `AddressBook` | CRM / 名录 | 1 : 1 |
| `Company` | 业务公司（LLC/Corp 等） | 1 : N |
| `License` | 牌照 | 1 : N |
| `LicenseLiability` | 责任保险 | 1 : N |
| `ContractSetup` | 合同设置 | 1 : N |
| `ContractEft` | EFT/银行信息 | 1 : 1 |
| `ProductSupplier` / `ProductSupplierType` | MGA/AGA/供应商 | 多对多（通过合同、AGA 关系） |
| `Province` / `Country` | 地理信息 | License / Address 外键 |
| `OnboardingStatus` | 入职进度 | 1 : N (按 Contact) |
| `Users` | 登录账号 | 0 / 1 : 1 (可无账户) |
| `Activity` / `Alert` | 活动日志、提醒 | 触发邮件/日志 |
| 其他：`Organization`、`CRM`、`StoredFile`、`LicenseCategory`, `IMCompany`(银行) … | 被局部功能使用 | — |

这些表之间的典型关系（简化）：

```
Advisor
 ├── 1─1  Contact
 │        ├── 1─1 Address
 │        ├── 1─n Phone
 │        └── 1─n Email
 ├── 1─n  Company
 ├── 1─n  License ── n─1 Province
 │                 └── n─m LicenseCategory
 ├── 1─n  LicenseLiability
 ├── 1─n  ContractSetup ── n─1 ProductSupplier
 │                         └── 1─1 ContractEft
 └── 0─1  Users
```


### 3. Advisor ProfileU.xhtml 与 Bean/表的映射

1. **个人信息 Tab**  
   * EL: `advisorNavigationBean.advisor.firstName`, `advisorManagementBean.username`  
   * 表: `Advisor`, `Contact`, `Users`

2. **地址/联系方式**  
   * EL: `advisorManagementBean.address.*`, `advisorManagementBean.phone.*`  
   * 表: `Address`, `Phone`, `Email`, `Province`, `Country`

3. **公司 (Business/Company) Tab**  
   * EL: `advisorNavigationBean.filteredCompanyList`, `advisorNavigationBean.selectedCompany`  
   * 表: `Company`

4. **执照 (License) Tab**  
   * EL: `advisorNavigationBean.selectedLicense`, `advisorNavigationBean.licensedProvinces`  
   * 表: `License`, `Province`, `LicenseCategory`

5. **责任险 (Liability) Tab**  
   * EL: `advisorNavigationBean.selectedLiability`  
   * 表: `LicenseLiability`

6. **银行 / EFT Tab**  
   * EL: `advisorNavigationBean.selectedContractEft`, `agaNavigationBean.bankList`  
   * 表: `ContractEft`, `IMCompany`(银行列表)

7. **合同 (Contract) Tab**  
   * EL: `contractNavigationBean.selectedContractSetup`, `contractNavigationBean.allMga`  
   * 表: `ContractSetup`, `ProductSupplier`, 可能还有 `StoredFile`

8. **入职进度 / 云图**  
   * EL: `advisorNavigationBean.selectedOnboardingStatus`, `onboardingBean.*`  
   * 表: `OnboardingStatus`, `Activity`

### 4. 重构为 REST 时可拆分的资源

基于上面的实体关系，可以把现有视图操作映射成 REST 资源（示例路径，仅供规划）:

```
/advisors
/advisors/{id}
/advisors/{id}/contact
/advisors/{id}/companies
/advisors/{id}/licenses
/advisors/{id}/licenses/{licId}/liabilities
/advisors/{id}/contracts
/advisors/{id}/eft
/advisors/{id}/onboarding-status
```


* 前端（Vue/React）再按 Tab 拉取/提交对应资源。
* Bean 中的大量 Facade 调用可移入 `@Stateless`/`@ApplicationScoped` 服务类，在 REST 端点里直接注入。
* 邮件、日志 (`Activity`, `Alert`) 等副作用可通过 CDI 事件或异步任务解耦。

---

### 5. 小结

Advisor ProfileU.xhtml 实际依赖 **四大 Bean**（`AdvisorNavigationBean`, `AdvisorManagementBean`, `AGANavigationBean`, `ContractNavigationBean`），  
而这些 Bean 又通过 **二十余个 Facade** 操作大致 **15 张核心业务表**。  
把这些表按“顾问→联系方式/执照/公司/合同/银行/入职进度”六大聚合建 REST 资源，即可在保持业务逻辑的同时完成前后端分离的重构。