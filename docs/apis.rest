@host={{$dotenv TEST_HOST}}
@token={{$dotenv TEST_TOKEN}}

### GET: Single Life Quote from a single carrier for all products
### Endpoint:
### GET https://{{host}}/webresources/lifeSingleCompAll/{face_amount}/{date_of_birth}/{gender}/{smoking}/{province_code}/{carrier_id}/{healthClass}
###
### Path Parameters:
### - face_amount     (e.g. 500000)        → Coverage amount in canadian dollars
### - date_of_birth   (e.g. 1979-01-01)    → Date of birth in yyyy-MM-dd format
### - gender          (M / F)              → Gender: M = Male, F = Female
### - smoking         (Y / N)              → Smoking status: Y = Yes, N = No
### - province_code   (e.g. ON)            → Province code (Canada)
### - carrier_id      (e.g. 29)            → Carrier/company ID
### - healthClass     (S / P / A)          → Health class: S = Standard, P = Preferred, A = All
###
### token: {{token}}                     → Authorization token
###
### Sample Request:
GET https://{{host}}/webresources/lifeSingleCompAll/500000/1979-01-01/F/N/ON/82/S
token: {{token}}


### GET: Single Life Quote from a single carrier for Specific Product Type
### Endpoint:
### GET https://{{host}}/webresources/lifeSingleComp/{product_type_id}/{face_amount}/{date_of_birth}/{gender}/{smoking}/{province_code}/{carrier_id}
###
### Path Parameters:
### - product_type_id (e.g. 22)            → Product type ID
### - face_amount     (e.g. 500000)        → Coverage amount in canadian dollars
### - date_of_birth   (e.g. 1979-01-01)    → Date of birth in yyyy-MM-dd format
### - gender          (M / F)              → Gender: M = Male, F = Female
### - smoking         (Y / N)              → Smoking status: Y = Yes, N = No
### - province_code   (e.g. QC)            → Province code (Canada)
### - carrier_id      (e.g. 485)           → Carrier/company ID
###
### token: {{token}}                     → Authorization token
###
### Sample Request:
GET https://{{host}}/webresources/lifeSingleComp/22/500000/1979-01-01/M/N/QC/485
token: {{token}}

### GET: Single Life Quote from a single carrier for a Specific Product
### Endpoint:
### GET https://{{host}}/webresources/lifeSingleProd/{face_amount}/{date_of_birth}/{gender}/{smoking}/{province_code}/{carrier_id}/{prod_id}
###
### Path Parameters:
### - face_amount     (e.g. 500000)        → Coverage amount in canadian dollars
### - date_of_birth   (e.g. 1979-01-01)    → Date of birth in yyyy-MM-dd format
### - gender          (M / F)              → Gender: M = Male, F = Female
### - smoking         (Y / N)              → Smoking status: Y = Yes, N = No
### - province_code   (e.g. QC)            → Province code (Canada)
### - carrier_id      (e.g. 485)           → Carrier/company ID
### - prod_id         (e.g. 7451)          → Product ID
###
### Headers:
### - token: {{token}}                     → Authorization token
###
### Sample Request:
GET https://{{host}}/webresources/lifeSingleProd/500000/1979-01-01/M/N/QC/485/7451
token: {{token}}


### GET: All insurance company names
GET https://{{host}}/webresources/info/insurancecompanies
token: {{token}}