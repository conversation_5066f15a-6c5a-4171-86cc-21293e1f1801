# Setup dundas for development

## Prerequisites

- Payara Application Server
- JDK 21

## Setup

* unzip domain2.zip into <PERSON><PERSON><PERSON><PERSON>'s `domains` directory ( if you use brew installed, the path is `/opt/homebrew/opt/payara/libexec/glassfish/domains`)

```
<network-listener protocol="http-listener-1" port="8082" name="http-listener-1" thread-pool="http-thread-pool" transport="tcp"></network-listener>
<network-listener protocol="http-listener-2" port="8282" name="http-listener-2" thread-pool="http-thread-pool" transport="tcp"></network-listener>
<network-listener protocol="admin-listener" port="4949" name="admin-listener" thread-pool="admin-thread-pool" transport="tcp"></network-listener>
```

* in IDEA, setup Run/Debug Configurations:
  * set the Server Domain to `domain2`
  * set the URL to `http://localhost:8082/ws_dundas-1.2/`