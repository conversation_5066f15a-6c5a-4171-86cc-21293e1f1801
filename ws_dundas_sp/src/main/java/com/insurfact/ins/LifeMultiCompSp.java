/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2019 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.ins;
 
import com.insurfact.iq4.ejb.EnhancedProductFacade;
import com.insurfact.iq4.ejb.IQ4EngineFacade;
import com.insurfact.iq4.ejb.IQ4ProductFacade;
import com.insurfact.iq4.ejb.PromotionsHelper;
import com.insurfact.sdk.utils.InsurfactPrivateException;
 
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.insurfact.websecuring.UserValidationDundas;

import jakarta.ejb.EJB;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * REST Web Service for Life Insurance quotes: multi-company, single-product.
 * Life is for complete json output parameters; not all clients will be using
 * this, most will be using subsets such as life1, life2, etc. Life1 and life,
 * both include percentages in the json.
 *
 * <p>
 * The origin ip is the identifier of the widget user (an insurfact client).
 * </p>
 *
 * <p>
 * For testing:
 * http://localhost:8080/WS_INS/webresources/life/6/500000/1979-01-01/M/N/QC
 * </p>
 *
 * <p>
 * For production:
 * http://webservices.insurfact.com/some-path/WS_INS/webresources/life/6/500000/1979-01-01/M/N/QC
 * </p>
 *
 * <p>
 * <code><pre>
 * &#64;PathParam("product_type_id") String productType = Master product type
 * &#64;PathParam("face_amount") String faceAmount
 * &#64;PathParam("date_of_birth") String dateOfBirth
 * &#64;PathParam("gender") String gender
 * &#64;PathParam("smoking") String smoking
 * &#64;PathParam("province_code") String provCode
 * </pre></code>
 * </p>
 *
 * <p>
 * Location of war file: /Users/<USER>/NBProjs/WS_INS/dist/WS_INS.war
 * </p>
 * **** QuoteMngrImpl.multiProductQuote(qp,user):
 * *prodMngr.multiProductQuote(qp, user)* returned List<Product> = size 5]]
 *
 * <AUTHOR>
 */
//http://***********:28081/WS_INS_WIDGET/webresources/lifeMultiComp/9/150000/1979-01-01/M/N/QC/S12/T
//http://***********:28081/WS_INS_WIDGET/webresources/lifeMultiComp/9/275000/1986-7-24/M/N/QC/S12/P                                                                                                   
//https://wsinsurance.insurfact.com/WS_INS_WIDGET/webresources/lifeMultiComp/9/150000/1979-01-01/M/N/QC/S/T/L/L/2
//http://***********:8080/WS_INS_WIDGET/webresources/lifeMultiCompSp/9/150000/1979-01-01/M/N/QC/S/L/L/F
//http://***********:8080/WS_INS_WIDGET/webresources/lifeMultiCompSp/9/150000/1979-01-01/M/N/QC/S/L/L/1
//http://***********:8080/WS_INS_WIDGET/webresources/lifeMultiCompSp/9/150000/1979-01-01/M/N/QC/S/L/L/S
//http://***********:8080/WS_INS_WIDGET/webresources/lifeMultiCompSp/9/150000/1979-01-01/M/N/QC/S/L/L/F
//uwtype in the table im_lifeproductdetail     F= Full underwriting, S = Simplified underwriting, G = Guaranteed issue
@RestController
@RequestMapping("/webresources/lifeMultiCompSp") // Base path for the controller
public class LifeMultiCompSp {
 
 

	@SuppressWarnings("unused")
	private static boolean DEBUG = false;

	private boolean emptyValue = false;

	@EJB
	private IQ4EngineFacade quoteFacade;

	@EJB
	private EnhancedProductFacade enhancedProductFacade;

	@EJB
	private IQ4ProductFacade productFacade;

	@EJB
	private PromotionsHelper promotionsHelper; 
	
	@EJB
	private UserValidationDundas userValidation;
  

	/**
	 * Retrieves representation of an instance of com.insurfact.ins.Life
	 *
	 * @param productTypeId
	 * @param faceAmount
	 * @param dateOfBirth
	 * @param gender
	 * @param smoking
	 * @param healthClass
	 * @param levelOrDecr
	 * @param prodClass
	 * @param UW
	 * @param provCode
	 *
	 * @return an instance of java.lang.String
	 */
	@GetMapping(value ="/{product_type_id}/{face_amount}/{date_of_birth}/{gender}/{smoking}/{province_code}/{healthClass}/{levelOrDecr}/{prodClass}/{UW}", produces = MediaType.APPLICATION_JSON_VALUE)
	@ResponseBody
	public Object getJson(HttpServletRequest request, HttpServletResponse response,
			@PathVariable("product_type_id") String productTypeId, @PathVariable("face_amount") String faceAmount,
			@PathVariable("date_of_birth") String dateOfBirth, @PathVariable("gender") String gender,
			@PathVariable("smoking") String smoking, @PathVariable("province_code") String provCode,
			@PathVariable("healthClass") String healthClass, @PathVariable("levelOrDecr") String levelOrDecr,
			@PathVariable("prodClass") String prodClass, @PathVariable("UW") String uw,
			@RequestHeader("token") String tokenCli) {

		// get debug flag from properties
		DEBUG = false;

		String PRODUCT_CLASS = "LIFE";

		if (prodClass.equalsIgnoreCase("C")) {
			PRODUCT_CLASS = "CRIT";
		}

		String responseAll = "{\"response\": token does not match }";
		String ipAddress = request.getRemoteAddr();

		String permOrTerm = "A";

		String urlOrigin = request.getHeader("origin");// changed to origin from Referer Jan 17th, 2022 aa
		if (urlOrigin != null) {
			if (urlOrigin.equals("")) {
				urlOrigin = "noOrigin";
			}
		}

		/*
		 * UserValidation_1 userVal = new UserValidation_1(); int wsUserID = 0; int
		 * usersID = 0;
		 * 
		 * try { wsUserID = userVal.getWSUserID(urlOrigin, ipAddress, true); usersID =
		 * userVal.getUsersID(urlOrigin, ipAddress, true); } catch
		 * (InsurfactPrivateException ex) {
		 * System.out.println("* LifeMultiCompSp ERROR originIP="+ipAddress+" token="
		 * +urlOrigin);
		 * Logger.getLogger(LifeMultiCompSp.class.getName()).log(Level.SEVERE, null,
		 * ex); }
		 */ 
		int wsUserID = 0;
		try {
			wsUserID = userValidation.getWSUserID(tokenCli, ipAddress);
		} catch (InsurfactPrivateException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		if (wsUserID != 0) {

			Generic generic = new Generic(PRODUCT_CLASS, "ALL", quoteFacade, enhancedProductFacade, productFacade,
					promotionsHelper);
			String responseValue = generic.getNoExceptionMulti(productTypeId, faceAmount, dateOfBirth, gender, smoking,
					provCode, healthClass, permOrTerm, levelOrDecr, uw, "");
			if (responseValue.equals("")) {
				emptyValue = true;
			}

			responseAll = "{\"response\": " + responseValue + " }";

		}

		if (emptyValue) {
			responseAll = "";
			return ResponseEntity.status(HttpStatus.NO_CONTENT).contentType(MediaType.APPLICATION_JSON).body("");
		} else {
			return responseAll;
		}

	}

}
