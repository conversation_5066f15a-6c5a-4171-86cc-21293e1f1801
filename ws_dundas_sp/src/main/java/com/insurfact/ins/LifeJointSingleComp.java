/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.insurfact.ins;
 
import com.insurfact.iq4.ejb.EnhancedProductFacade;
import com.insurfact.iq4.ejb.IQ4EngineFacade;
import com.insurfact.iq4.ejb.IQ4JointSEAUtilsFacade;
import com.insurfact.iq4.ejb.IQ4ProductFacade;
import com.insurfact.iq4.ejb.PromotionsHelper;
import com.insurfact.sdk.properties.InsurfactSdkProperties;
import com.insurfact.sdk.utils.InsurfactPrivateException;
import java.io.IOException;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

import jakarta.ejb.EJB;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.insurfact.websecuring.UserValidationDundas;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/webresources/lifeJointSingleComp") // Base path for the controller
public class LifeJointSingleComp {
//http://10.10.88.22:28081/WS_INS_JNT/webresources/lifeJointSingleComp/9/250000/QC/1985-01-06/M/N/1985-01-06/F/N/F/63   

	private static final String TAG = LifeSingleComp.class.getName();

	private static boolean DEBUG = false;

	private static final String PRODUCT_CLASS = "LIFE";

	public static final String dobFormat = "yyyy-MM-dd";

	@EJB
	private IQ4EngineFacade quoteFacade;

	@EJB
	private IQ4JointSEAUtilsFacade jointESAUtils;

	@EJB
	private IQ4ProductFacade productFacade;

	@EJB
	private EnhancedProductFacade enhancedProductFacade;

	@EJB
	private PromotionsHelper promotionsHelper; 

	@Autowired
	private final UserValidationDundas userValidation;

    public LifeJointSingleComp(UserValidationDundas userValidation) {
        this.userValidation = userValidation;
    }

    @GetMapping(value ="/{product_type_id}/{face_amount}/{province_code}/{client1DOB}/{client1Gender}/{client1Smoking}/{client2DOB}/{client2Gender}/{client2Smoking}/{jointType}/{compID}", produces = MediaType.APPLICATION_JSON_VALUE)
	@ResponseBody
	public String getJson(HttpServletRequest request, HttpServletResponse response,
			@PathVariable("product_type_id") String productTypeId, @PathVariable("face_amount") String faceAmount,
			@PathVariable("province_code") String provCode, @PathVariable("client1DOB") String dateOfBirth1,
			@PathVariable("client1Gender") String gender1, @PathVariable("client1Smoking") String smoking1,
			@PathVariable("client2DOB") String dateOfBirth2, @PathVariable("client2Gender") String gender2,
			@PathVariable("client2Smoking") String smoking2, @PathVariable("jointType") String jointType, // F=first to
																											// die
																											// L=last to
																											// die
			@PathVariable("compID") String compID, // F=first to die L=last to die
			@RequestHeader("token") String tokenCli) {

		DEBUG = InsurfactSdkProperties.getBoolean("debug.enabled", true);
		String responseAll = "{\"response\": token does not match }";
		String ipAddress = request.getRemoteAddr();

		String urlOrigin = request.getHeader("origin");// changed to origin from Referer Jan 17th, 2022 aa
		if (urlOrigin != null) {
			if (urlOrigin.equals("")) {
				urlOrigin = "noOrigin";
			}
		} else {
			urlOrigin = "noOrigin";
		}

		int wsUserID = 0;
		try {
			wsUserID = userValidation.getWSUserID(tokenCli, ipAddress);
		} catch (InsurfactPrivateException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		if (wsUserID != 0) {

			DateFormat df = new SimpleDateFormat(dobFormat);
			df.getCalendar().setLenient(false);
			Date date1 = Calendar.getInstance().getTime();
			Date date2 = Calendar.getInstance().getTime();
			try {
				date1 = df.parse(dateOfBirth1);
				date2 = df.parse(dateOfBirth2);
			} catch (Exception ex) {
				@SuppressWarnings("unused")
				final String s = TAG + ".startMulti: *df.parse(dob)* with dobFormat {" + dobFormat + "} raised: " + ex;
			}

			String newDOB = dateOfBirth2;
			String newGend = gender2;
			String newSmok = smoking2;

			if (date1.after(date2)) {
				dateOfBirth2 = dateOfBirth1;
				gender2 = gender1;
				smoking2 = smoking1;

				dateOfBirth1 = newDOB;
				gender1 = newGend;
				smoking1 = newSmok;

			}

			GenericJoint generic = new GenericJoint(PRODUCT_CLASS, "ALL", quoteFacade, jointESAUtils, productFacade,
					enhancedProductFacade, promotionsHelper);
			String responseValue = generic.getNoExceptionJoint(productTypeId, faceAmount, provCode, dateOfBirth1,
					gender1, smoking1, dateOfBirth2, gender2, smoking2, jointType, compID, "A", "A", "");

			try {
				generic.saveQuote(ipAddress, wsUserID, "lifeJointSingleComp", faceAmount, dateOfBirth1, gender1,
						smoking1, provCode, compID, "", productTypeId, urlOrigin);
			} catch (IOException e) {
				System.out.println("103 lifeJointSingleComp IOException e=" + e.getMessage());
			} catch (InsurfactPrivateException e) {
				System.out.println("106 lifeJointSingleComp InsurfactPrivateException e=" + e.getMessage());
			}

			responseAll = "{\"response\": " + responseValue + " }";

			if (DEBUG) {
				System.out.println(TAG + ".getJson: returning $$" + responseAll + "$$");
			}
		}

		return responseAll;
	}

}
