package com.insurfact.ins;

import com.insurfact.sdk.utils.InsurfactPrivateException;
import com.insurfact.skynet.entity.Lead;
import com.insurfact.skynet.entity.LeadsRelationship;
import com.insurfact.skynet.entity.Opportunity;
import com.insurfact.skynet.entity.Users;

import com.insurfact.wsutil.PhoneNumberUtil;
import com.insurfact.wsutil.CommonUtil;
import jakarta.annotation.Resource;
import jakarta.ejb.Stateless;
import jakarta.persistence.NoResultException;

import java.io.IOException;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Map;
import java.util.function.Supplier;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import javax.sql.DataSource;

import org.springframework.stereotype.Service;

/**
 *  Insurance lead management service class,
 *  responsible for processing and saving insurance-related lead information to database
 *
 * <AUTHOR>
 */
@Stateless
@Service
public class GenericLeads {

    private static final Logger LOGGER = Logger.getLogger(GenericLeads.class.getName());
    private static final boolean DEBUG = false;

    // database table name constants
    private static final String LEAD_TABLE = "LEAD";
    private static final String LEADS_RELATIONSHIP_TABLE = "LEADS_RELATIONSHIP";
    private static final String OPPORTUNITY_TABLE = "OPPORTUNITY";
    private static final String ACTIVITY_TABLE = "ACTIVITY";
    private static final String ALERT_TABLE = "ALERT";

    // statuses and types constants
    private static final int STATUS_ACTIVE = 1;
    private static final int TYPE_NEW = 1;
    private static final int STATUS_ONGOING = 3;
    private static final int TYPE_LIFE_INSURANCE = 17;
    private static final int PREFERRED_LANG_ENGLISH = 9;
    private static final int PREFERRED_LANG_FRENCH = 12;
    private static final int GENDER_MALE = 1;
    private static final int GENDER_FEMALE = 2;
    private static final String SMOKER_STATUS_YES = "Smoker";
    private static final String SMOKER_STATUS_NO =  "Non-smoker";
    private static final String QUOTE_TYPE_R = "R";
    private static final String LEADS_RELATIONSHIP_TYPE_S = "S";
    private static final String OPPORTUNITY_TYPE_J = "J";
    private static final String OPPORTUNITY_TYPE_S = "S";

    // date format constants
    private static final String DATE_FORMAT = "yyyy-MM-dd";
    private static final int FOLLOWUP_DAYS = 7;
    // source constants
    private static final String LEAD_SOURCE_DESC = "Agent AI";
    private static final String LEAD_SOURCE = "116";
    private static final String REFERRED_BY = "Agent AI";

    private static final String RESPONSE_SUCCESS = "success";
    private static final String RESPONSE_FAILURE = "failure";
    // database connection constants
    @Resource(lookup = "jdbc/Skytest")
    private DataSource dataSource;

    /**
     * Save life insurance leads information to a database
     *
     * @param urlOrigin     Source URL
     * @param ipAddress     IP Address
     * @param usersID       User ID
     * @param compID        Company ID
     * @param prodID        Product ID
     * @param insAmount     Insurance Amount
     * @param annualPrem    Annual Premium
     * @param monthPrem     Monthly Premium
     * @param firstName     First Name
     * @param lastName      Last Name
     * @param gender        Gender
     * @param dob           Date of Birth
     * @param smoker        Smoker Status
     * @param firstName2    Second Insured First Name
     * @param lastName2     Second Insured Last Name
     * @param gender2       Second Insured Gender
     * @param dob2         Second Insured Date of Birth
     * @param smoker2      Second Insured Smoker Status
     * @param prov         Province
     * @param email        Email
     * @param phoneNumber  Phone Number
     * @param lang         Language Preference
     * @param message      Message Content
     * @return            Processing Result
     * @throws IOException                  IO Exception
     * @throws InsurfactPrivateException    Business Exception
     */
    public String saveLeadLife(
            String urlOrigin, String ipAddress, int usersID, String compID, String prodID,
            String insAmount, String annualPrem, String monthPrem, String firstName, String lastName, String gender,
            String dob, String smoker, String firstName2, String lastName2, String gender2, String dob2, String smoker2,
            String prov, String email, String phoneNumber, String lang, String message)
            throws IOException, InsurfactPrivateException {

        Connection connection = null;

        try {
            // prepare data for database
            Calendar today = Calendar.getInstance();
            PhoneNumberUtil phoneNumberUtil = new PhoneNumberUtil(phoneNumber);
            String areaCode = phoneNumberUtil.getAreaCode();
            String phoneNumberOnly = phoneNumberUtil.getNumber();


            // create lead entity object
            Lead lead = createLeadEntity(firstName, lastName, email,
                    gender, dob, phoneNumberOnly,
                    LEAD_SOURCE, LEAD_SOURCE_DESC, REFERRED_BY
            );

            // setting leads description text
            String secondLifeDetails = createSecondLifeDetails(firstName2, lastName2, gender2, dob2, smoker2);

            // process premium amounts
            double annualPremium = CommonUtil.parseAmount(annualPrem, Double.class);
            double monthlyPremium = CommonUtil.parseAmount(monthPrem, Double.class);
            int faceAmount = CommonUtil.parseAmount(insAmount, Integer
                    .class);

            // setting preferred language
            int preferredLanguage = determineLanguagePreference(lang);

            // create opportunity entity object
            Opportunity opportunity = createOpportunityEntity(faceAmount, today, annualPremium, monthlyPremium);

            // create leads relationship entity object
            LeadsRelationship leadsRelationship = createLeadsRelationship(lead, today);
            opportunity.setLeadsRelationship(leadsRelationship);

            // setting health class
            String healthClass = smoker.equalsIgnoreCase("Y") ? SMOKER_STATUS_YES :SMOKER_STATUS_NO;

            // get database connection
            connection = getConnection();

            // generate SQL statements
            String[] sqlStatements = generateSqlStatements(
                    connection, usersID, Integer.parseInt(compID), Integer.parseInt(prodID),
                    lead, opportunity, faceAmount, areaCode, phoneNumberOnly,
                    preferredLanguage, prov, annualPremium, monthlyPremium,
                    dob, healthClass, lastName2, message, urlOrigin, insAmount,
                    smoker, secondLifeDetails);

            LOGGER.log(Level.INFO, "Generated SQL statements: " + Arrays.toString(sqlStatements));
            executeStatements(connection, sqlStatements);

            return RESPONSE_SUCCESS;

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error occurred while saving insurance lead", e);
            throw new InsurfactPrivateException("Failed to save insurance lead: " + e.getMessage());
        } finally {
            closeResources(connection, null);
        }
    }

    /**
     * create Lead entity object
     */
    private Lead createLeadEntity(String firstName, String lastName,
                                  String email, String gender, String dob, String phoneNumber,
                                  String LeadSource,
                                  String LeadSourceDesc,
                                  String ReferredBy
    ) {
        Lead lead = new Lead();
        lead.setFirstname(firstName);
        lead.setLastname(lastName);
        lead.setPhone(phoneNumber);
        lead.setCreationDate(Calendar.getInstance().getTime());
        lead.setEmail(email);

        int genderCode = GENDER_FEMALE;
        if (gender != null && gender.equalsIgnoreCase("m")) {
            genderCode = GENDER_MALE;
        }
        lead.setGender(genderCode);

        lead.setLeadSource(LeadSource);
        lead.setLeadSourceDesc(LeadSourceDesc);
        lead.setReferredBy(ReferredBy);
        lead.setStatus(STATUS_ACTIVE);
        lead.setType(TYPE_LIFE_INSURANCE);

        return lead;
    }

    /**
     * create Opportunity entity object
     */
    private Opportunity createOpportunityEntity(int faceAmount, Calendar today, double annualPremium, double monthlyPremium) {
        Opportunity opportunity = new Opportunity();
        opportunity.setCreationDate(today.getTime());
        opportunity.setFaceAmount(faceAmount);

        // set follow-up days to 7 days
        Calendar followUpDate = (Calendar) today.clone();
        followUpDate.add(Calendar.DATE, FOLLOWUP_DAYS);
        opportunity.setFollowupDate(followUpDate.getTime());

        opportunity.setOpportunityAmount((double) faceAmount);
        opportunity.setQuoteType(QUOTE_TYPE_R);
        opportunity.setStatus(STATUS_ONGOING);
        opportunity.setType(TYPE_NEW);

        return opportunity;
    }

    /**
     * create LeadsRelationship entity object
     */
    private LeadsRelationship createLeadsRelationship(Lead lead, Calendar today) {
        LeadsRelationship leadsRelationship = new LeadsRelationship();
        leadsRelationship.setCreationDate(today.getTime());
        leadsRelationship.setType(LEADS_RELATIONSHIP_TYPE_S);
        leadsRelationship.setFirst(lead);
        return leadsRelationship;
    }



  

    /**
     * determine the language preference
     */
    private int determineLanguagePreference(String lang) {
        return (lang != null && lang.contains("English")) ? PREFERRED_LANG_ENGLISH : PREFERRED_LANG_FRENCH;
    }

    /**
     * create second life details
     */
    private String createSecondLifeDetails(String firstName2, String lastName2, String gender2, String dob2, String smoker2) {
        Map<String, Supplier<String>> fieldsMap = Map.of(
                "First name of 2nd life insured", () -> firstName2,
                "Last name of 2nd life insured", () -> lastName2,
                "Gender of 2nd life insured", () -> gender2 != null ? (gender2.equalsIgnoreCase("m") ? "Male" : "Female") : "",
                "DOB of 2nd life insured", () -> dob2,
                "2nd life insured smoker", () -> smoker2
        );

        return fieldsMap.entrySet().stream()
                .map(entry -> {
                    String value = entry.getValue().get();
                    return value != null && !value.isEmpty() ?
                            entry.getKey() + ": " + value : "";
                })
                .filter(s -> !s.isEmpty())
                .collect(Collectors.joining("; "));
    }



    /**
     * generate SQL statements
     */
    private String[] generateSqlStatements(Connection connection, int usersID, int companyId, int productId,
                                           Lead lead, Opportunity opportunity, int faceAmount,
                                           String areaCode, String phoneNumber, int preferredLanguage,
                                           String prov, double annualPremium, double monthlyPremium,
                                           String dob, String healthClass, String lastName2,
                                           String message, String urlOrigin, String insAmount,
                                           String smoker, String secondLifeDetails) throws SQLException {

        // clean the message content
        String cleanedMessage = sanitizeMessage(message);

        // get province info
        ProvinceInfo provinceInfo = getProvinceInfo(connection, prov, preferredLanguage);

        // create description text for the activity record
        String description = createDescription(urlOrigin, insAmount, smoker, secondLifeDetails, provinceInfo.provinceName);
        if (description.length() > 200) {
            description = description.substring(0, 150);
        }

        // get opportunity type
        String opportunityType = lastName2 != null && !lastName2.isEmpty() ? OPPORTUNITY_TYPE_J : OPPORTUNITY_TYPE_S;

        // get activity id
        int activityId = getNextActivityId(connection);

        SimpleDateFormat dateFormat = new SimpleDateFormat(DATE_FORMAT);
        String birthDateFormatted = "";
        try {
            Calendar dobCalendar = Calendar.getInstance();
            dobCalendar.setTime(dateFormat.parse(dob));
            birthDateFormatted = dateFormat.format(dobCalendar.getTime());
        } catch (ParseException e) {
            LOGGER.log(Level.WARNING, "日期解析失败: " + dob, e);
        }

        // get lead fields from the lead entity object
        String firstName = lead.getFirstname();
        String lastName = lead.getLastname();
        String email = lead.getEmail();

        String sqlLead = generateLeadSql(usersID, firstName, lastName, areaCode, phoneNumber,
                email, birthDateFormatted, preferredLanguage);
        LOGGER.log(Level.WARNING, "Generated SQL statement: " + sqlLead);
        String sqlLeadRel = generateLeadRelationshipSql();

        String sqlOpp = generateOpportunitySql(companyId, productId, annualPremium, monthlyPremium,
                healthClass, faceAmount, usersID, description, opportunityType, provinceInfo.provinceId);

        String sqlActivity = generateActivitySql(activityId, usersID, cleanedMessage);

        String sqlAlert = generateAlertSql(usersID, activityId, preferredLanguage);

        return new String[] {sqlLead, sqlLeadRel, sqlOpp, sqlActivity, sqlAlert};
    }

    /**
     * clean the message content
     */
    private String sanitizeMessage(String message) {
        if (message == null) {
            return "";
        }

        return message.replaceAll("'", "''")
                .replaceAll("&", "and")
                .replaceAll("%", "percent")
                .trim();
    }

    /**
     * create description text for the activity record
     */
    private String createDescription(String urlOrigin, String insAmount, String smoker,
                                     String secondLifeDetails, String provinceName) {

        return "Life widget contact request from : " + urlOrigin + " " +
                "Amount of insurance needed: " + insAmount + ". " +
                "Smoker: " + smoker + ". " +
                secondLifeDetails;
    }

    /**
     * get province info
     */
    private ProvinceInfo getProvinceInfo(Connection connection, String provinceCode, int preferredLanguage)
            throws SQLException {
        ProvinceInfo info = new ProvinceInfo();
        info.provinceId = 90; // default province id as Quebec if not found

        if (connection == null) {
            return info;
        }

        String provinceNameField = preferredLanguage == PREFERRED_LANG_FRENCH ? "NAME_FR" : "NAME_EN";

        try (Statement stmt = connection.createStatement()) {
            String query = " SELECT " + provinceNameField + " pName, OLD_CODE pID " +
                    " FROM PROVINCE " +
                    " WHERE PROVINCE_CODE='" + provinceCode + "' ";

            try (ResultSet rs = stmt.executeQuery(query)) {
                if (rs.next()) {
                    info.provinceName = rs.getString("pName");
                    info.provinceId = rs.getInt("pID");
                }
            }
        }

        return info;
    }

    /**
     * get next activity id
     */
    private int getNextActivityId(Connection connection) throws SQLException {
        int activityId = 0;

        if (connection == null) {
            return activityId;
        }

        try (Statement stmt = connection.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT ACTIVITY_SEQ.NEXTVAL from dual")) {
            if (rs.next()) {
                activityId = rs.getInt("NEXTVAL");
            }
        }

        return activityId;
    }

    /**
     * generate lead sql
     */
    private String generateLeadSql(int usersID, String firstName, String lastName,
                                   String areaCode, String phoneNumber, String email,
                                   String birthDate, int preferredLanguage) {
        return " INSERT INTO " + LEAD_TABLE + " " +
                " (LEAD_INT_ID, LEAD_SOURCE, LEAD_SOURCE_DESC, " +
                " STATUS, BIRTHDATE, " +
                " FIRSTNAME, LASTNAME, " +
                " CREATION_DATE, LAST_MODIFICATION_DATE, " +
                " USERS, ADVISOR, " +
                " EMAIL, AREA_CODE, PHONE, " +
                " TYPE, GENDER, HOUSEHOLD_TYPE, PREFERRED_LANGUAGE) " +
                " VALUES (LEAD_SEQ.NEXTVAL, 'Life widget - Contact Form'," +
                " 'widget', " + STATUS_ACTIVE + ", TO_DATE('" + birthDate + "','yyyy-mm-dd'), " +
                " '" + firstName + "', '" + lastName + "', " +
                " SYSDATE, SYSDATE, " + usersID + "," + usersID + ", " +
                " '" + email + "','" + areaCode + "','" + phoneNumber + "', " + TYPE_LIFE_INSURANCE + ", " +
                " 1, 0, " + preferredLanguage + ") ";
    }

    /**
     * generate leads relationship sql
     */
    private String generateLeadRelationshipSql() {
        return " INSERT INTO " + LEADS_RELATIONSHIP_TABLE + " " +
                " (LEADS_RELATIONSHIP_INT_ID, " +
                " CREATION_DATE, TYPE, FIRST_LEAD_INT_ID) " +
                " VALUES (LEADS_RELATIONSHIP_SEQ.NEXTVAL, " +
                " SYSDATE, 'S', LEAD_SEQ.CURRVAL) ";
    }

    /**
     * generate opportunity sql
     */
    private String generateOpportunitySql(int companyId, int productId, double annualPremium,
                                          double monthlyPremium, String healthClass, int faceAmount,
                                          int usersID, String description, String opportunityType,
                                          int provinceId) {
        return " INSERT INTO " + OPPORTUNITY_TABLE + " " +
                " (OPPORTUNITY_INT_ID, OPPORTUNITY_AMOUNT, " +
                " OPPORTUNITY_AMOUNT_MONTHLY, " +
                " HEALTH_CLASS, LEADS_RELATIONSHIP, " +
                " COMPANY_ID, PRODUCT_ID, " +
                " LEAD, CREATION_DATE, LAST_MODIFICATION_DATE," +
                " TYPE, STATUS, USERS, DESCRIPTION, " +
                " FACE_AMOUNT, FOLLOWUP_DATE, QUOTE_TYPE, " +
                " PROVINCE_ID) " +
                " VALUES (OPPORTUNITY_SEQ.NEXTVAL, " + annualPremium + ", " + monthlyPremium + "," +
                " '" + healthClass + "', LEADS_RELATIONSHIP_SEQ.CURRVAL," +
                " " + companyId + ", " + productId + ", " +
                " LEAD_SEQ.CURRVAL, SYSDATE, SYSDATE, " +
                " " + TYPE_NEW + ", " + STATUS_ONGOING + ", " + usersID + ", " +
                " '" + description + "', " + faceAmount + ",SYSDATE+7, '" + opportunityType + "', " +
                " " + provinceId + ") ";
    }

    /**
     * generate activity sql
     */
    private String generateActivitySql(int activityId, int usersID, String message) {
        return " INSERT INTO " + ACTIVITY_TABLE + " " +
                " (ACTIVITY_INT_ID, ACTIVITY_TYPE, " +
                " CREATION_DATE, OWNER, OPPORTUNITY, CATEGORY, " +
                " DESCRIPTION, STATUS, TYPE_ID, " +
                " TYPE, TITLE_TYPE) " +
                " VALUES (" + activityId + ", 8, SYSDATE, " + usersID + " , OPPORTUNITY_SEQ.CURRVAL , 57, " +
                " '" + message + "', 2, " +
                " OPPORTUNITY_SEQ.CURRVAL, 1, 58)";
    }

    /**
     * generate alert sql
     */
    private String generateAlertSql(int usersID, int activityId, int preferredLanguage) {
        StringBuilder sql = new StringBuilder();
        sql.append(" INSERT INTO " + ALERT_TABLE + " ");
        sql.append(" VALUES (ALERT_SEQ.NEXTVAL,SYSDATE, SYSDATE, ")
                .append(usersID).append(", ")
                .append(activityId).append(" , SYSDATE , SYSDATE +7 , 'N' ,null, null,");

        if (preferredLanguage == PREFERRED_LANG_ENGLISH) {
            sql.append(" 'Insertion of New Lead', 'New Lead insertion from Widget',null )");
        } else {
            sql.append(" 'Insertion d''un nouveau fil', 'Insertion d''un nouveau fil depuis le widget',null )");
        }
        return sql.toString();
    }

    /**
     * execute SQL statements
     */
    private void executeStatements(Connection connection, String[] sqlStatements) throws SQLException {
        if (connection == null || sqlStatements == null) {
            return;
        }

        try (Statement stmt = connection.createStatement()) {
            for (String sql : sqlStatements) {
                if (sql != null && !sql.isEmpty()) {
                    stmt.executeUpdate(sql);
                }
            }
        }
    }

    /**
     * get database connection
     */
    private Connection getConnection() {
        try {
            return dataSource.getConnection();
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "获取数据库连接失败", e);
            return null;
        }
    }

    /**
     * close database resources
     */
    private void closeResources(Connection connection, Statement statement) {
        try {
            if (statement != null) {
                statement.close();
            }
            if (connection != null) {
                connection.close();
            }
        } catch (SQLException e) {
            LOGGER.log(Level.WARNING, "关闭数据库资源失败", e);
        }
    }

    /**
     * find user by user id
     */
    public Users findByUserID(Integer userID) {
        Users user = new Users();
        try {
            // TODO: 实现用户查询逻辑
        } catch (NoResultException e) {
            return null;
        }
        return user;
    }

    /**
     * 省份信息内部类
     */
    private static class ProvinceInfo {
        String provinceName;
        int provinceId;
    }
}