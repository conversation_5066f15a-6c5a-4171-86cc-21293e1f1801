package com.insurfact.ins.exception;

/**
 * Custom exception thrown when a requested resource is not found.
 * This exception is typically used when an entity (like an Advisor) 
 * cannot be found by its identifier.
 * 
 * <AUTHOR> zhu
 * @version 1.0
 * @since 2025-01-01
 */
public class ResourceNotFoundException extends RuntimeException {

    /**
     * Constructs a new ResourceNotFoundException with the specified detail message.
     *
     * @param message the detail message explaining what resource was not found
     */
    public ResourceNotFoundException(String message) {
        super(message);
    }

    /**
     * Constructs a new ResourceNotFoundException with the specified detail message and cause.
     *
     * @param message the detail message explaining what resource was not found
     * @param cause the cause of the exception
     */
    public ResourceNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * Constructs a new ResourceNotFoundException for a specific resource type and ID.
     *
     * @param resourceType the type of resource that was not found (e.g., "Advisor")
     * @param resourceId the ID of the resource that was not found
     */
    public ResourceNotFoundException(String resourceType, Object resourceId) {
        super(String.format("%s with ID '%s' not found", resourceType, resourceId));
    }
}
