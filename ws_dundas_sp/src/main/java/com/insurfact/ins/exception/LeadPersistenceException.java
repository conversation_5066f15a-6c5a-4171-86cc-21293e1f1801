package com.insurfact.ins.exception;

/**
 * Custom exception for errors occurring during lead persistence operations.
 */
public class LeadPersistenceException extends RuntimeException {

    /**
     * Constructs a new LeadPersistenceException with the specified detail message.
     *
     * @param message the detail message.
     */
    public LeadPersistenceException(String message) {
        super(message);
    }

    /**
     * Constructs a new LeadPersistenceException with the specified detail message and cause.
     *
     * @param message the detail message.
     * @param cause   the cause (which is saved for later retrieval by the {@link #getCause()} method).
     *                (A {@code null} value is permitted, and indicates that the cause is nonexistent or unknown.)
     */
    public LeadPersistenceException(String message, Throwable cause) {
        super(message, cause);
    }
}