/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2019 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.ins;

import com.insurfact.im.IMProduct;
import com.insurfact.iq.domain.PremiumRenewal;
import com.insurfact.iq.domain.ProductType;
import com.insurfact.iq.domain.Product;
import com.insurfact.iq.domain.WBCombo;
import com.insurfact.skynet.entity.AboutMe;
import com.insurfact.skynet.entity.Province;
import com.insurfact.skynet.entity.Types;
import com.insurfact.wsutil.JsonUtil;
import com.insurfact.wsutil.Util;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Iterator;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class JsonGenerator {
    
    
    
    /*
 "{\"response\":\""+responseString+"\"}"

responseString example = [ {"fundatakey": key, "f2": v2, "f3": v3 ...}, {  }, {  } ]
     */
    
    /**
     * Input product data: company, product name, 
     * yearly premium, monthly premium, short desc en, short desc fr
     * 
     * <p>
     * company name en
     * company name fr
     * product.getName(),
     * product.getNameFr(),
        province
        product.getMonthlyBaseSumPremium(),
        product.getAnnualBaseSumPremium(),
        product.getDescription(),
        product.getDescriptionFr()
     * </p> 
     * 
     * @return String formatted for JSON; example = {"rank": key, "f2": v2,
     * "f3": v3 ...}, { }, { }
     */

            
    //http://gitlab.insurfact.com/aaspiotis/ws_ins2.git
    static String getResponseValuesLIFEFineo(
        int rank,
        String companyNameEn,
        String companyNameFr,
        String productNameEn,
        String productNameFr,
        String productTypeCode,
        String productTypeLabel,
        String province,
        String monthlyBaseSumPremium,
        String annualBaseSumPremium,
        String percentage,
        String prodDescEn,
        String prodDescFr,
        String premRenew,
        String uw,
        String uwFr,
        String convertibleAge,
        String payableAge,
        String healthClass,
        String healthClassFr,
        String smoking,
        String faceAmount,
        int tmpAge,
        Product newProduct

    ) {
        StringBuilder buf = new StringBuilder(" ");

        buf.append(" { "); //open response
        
        buf.append("\"info\"").append(" : ").append("{ ");         

        buf.append("\"carrierId\"").append(" : \"").append(newProduct.getCompanyID()).append("\", ");
        buf.append("\"productId\"").append(" : \"").append(newProduct.getID()).append("\", ");
        buf.append("\"productCode\"").append(" : \"").append(newProduct.getProductType().getID()).append("\", ");
        buf.append("\"productType\"").append(" : \"").append("LIFE").append("\", ");
        buf.append("\"productSubType\"").append(" : \"").append(newProduct.getProductType().getTermOrPerm()).append("\", ");
        
        buf.append("\"masterDescProductId\"").append(" : \"").append(newProduct.getMasterDescProductID()).append("\", ");
     //   master_desc_productid
        
        buf.append("\"rank\"").append(" : \"").append(rank).append("\", ");

        String guar = "N";
        if(newProduct.isGuaranteed())
            guar="Y";
        buf.append("\"guaranteed\"").append(" : \"").append(guar).append("\", ");

        SimpleDateFormat formatter = new SimpleDateFormat("dd-MMM-yyyy");
        String actDte = "";
        if(newProduct.getActiveDate() != null)
          actDte = formatter.format(newProduct.getActiveDate());
        buf.append("\"activeDate\"").append(" : \"").append(actDte).append("\", ");
        
        buf.append("\"productGroupId\"").append(" : \"").append(newProduct.getProductType().getMasterID()).append("\", ");
                
        buf.append("\"productTypeLabel\"").append(" : \"").append(JsonUtil.cleanStringForJson(productTypeLabel)).append("\" ");
        
        buf.append(" }, ");   //close info:{      
        
        
        buf.append("\"quoteSpecs\"").append(" : ").append("{ ");      
        buf.append("\"province\"").append(" : \"").append(JsonUtil.cleanStringForJson(province)).append("\", ");
        
        buf.append("\"healthClass\"").append(" : \"").append(JsonUtil.cleanStringForJson(healthClass)).append("\", ");
        buf.append("\"healthClassFr\"").append(" : \"").append(JsonUtil.cleanStringForJson(healthClassFr)).append("\", ");
        
        buf.append("\"smoking\"").append(" : \"").append(JsonUtil.cleanStringForJson(smoking)).append("\", ");
        buf.append("\"faceAmount\"").append(" : \"").append(JsonUtil.cleanStringForJson(faceAmount)).append("\" ");
        
        buf.append(" }, ");    //close quoteSpecs:{     
        
        buf.append("\"quotes\"").append(" : ").append("{ ");      
        buf.append("\"monthlyPremium\"").append(" : \"").append(JsonUtil.cleanStringForJson(monthlyBaseSumPremium)).append("\", ");
        buf.append("\"yearlyPremium\"").append(" : \"").append(JsonUtil.cleanStringForJson(annualBaseSumPremium)).append("\", ");
        buf.append("\"percentage\"").append(" : \"").append(JsonUtil.cleanStringForJson(percentage)).append("\", ");
        
        buf.append("\"premiumRenewals\"").append(" : ").append(" [ {");      
        boolean isFirst = true;
        int lowAge = 0;
        int highAge = 0;
        
        int payAge = Integer.parseInt(payableAge);
        for (PremiumRenewal pr : newProduct.getPremiumRenewals()) {
           lowAge =   tmpAge+pr.getLowBound();
           highAge = tmpAge+pr.getHighBound();
           if(highAge > payAge)
               highAge= payAge;
          if(!isFirst)
              buf.append(" }, {");
          buf.append("\"startAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(lowAge+"")).append("\", ");
          buf.append("\"endAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(highAge+"")).append("\", ");
          buf.append("\"monthlyPremium\"").append(" : \"").append(JsonUtil.cleanStringForJson(Util.getStringFromDouble(pr.getTotalMonthly()))).append("\", ");
          buf.append("\"annualPremium\"").append(" : \"").append(JsonUtil.cleanStringForJson(Util.getStringFromDouble(pr.getTotalAnnual()))).append("\" ");
          isFirst = false;
        }

       
        buf.append(" } ] "); //close premiumRenewals:{             
        buf.append(" }, ");  //close quotes:{           
        
        buf.append("\"details\"").append(" : ").append("{ ");      
        buf.append("\"en\"").append(" : ").append("{ ");      
        buf.append("\"product\"").append(" : ").append("{ ");  
        buf.append("\"productName\"").append(" : \"").append(JsonUtil.cleanStringForJson(productNameEn)).append("\", ");
        buf.append("\"companyName\"").append(" : \"").append(JsonUtil.cleanStringForJson(companyNameEn)).append("\", ");
        buf.append("\"description\"").append(" : \"").append(JsonUtil.cleanStringForJson(prodDescEn)).append("\", ");
        buf.append("\"productGroup\"").append(" : \"").append(newProduct.getProductType().getName()).append("\" ");
        
        buf.append(" }, ");   //close product:{          
        
        buf.append("\"underwriting\"").append(" : ").append(" [ {");      

        if(newProduct.getUnderwriting() != null){
           Iterator<String> iterator = newProduct.getUnderwriting().iterator();
           String val ="";
           boolean first = true;
           while (iterator.hasNext()) {
             val = iterator.next();
             if(!first)
               buf.append("}, {");
             buf.append("\"name\"").append(" : \"").append(JsonUtil.cleanStringForJson(val)).append("\", ");                   
             buf.append("\"value\"").append(" : \"").append(JsonUtil.cleanStringForJson(val)).append("\"  ");                   
             
             first = false;
           }
        }
        buf.append(" } ");
        buf.append("  ] ,");    //close underwriting:{       
//System.out.println(" line 173 JsonGenerator ");        
        buf.append("\"features\"").append(" : ").append("{ ");      
        buf.append("\"convertibleAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(convertibleAge)).append("\", ");
        buf.append("\"payableAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(payableAge)).append("\", ");
        buf.append("\"payableYears\"").append(" : \"").append(newProduct.getYearsPayable()).append("\" ");
        buf.append(" }  "); //close features:{               
        buf.append(" }, "); //close en:{       
        

        buf.append("\"fr\"").append(" : ").append("{ ");      
        buf.append("\"product\"").append(" : ").append("{ ");      
        buf.append("\"companyName\"").append(" : \"").append(JsonUtil.cleanStringForJson(companyNameFr)).append("\", ");
        buf.append("\"productName\"").append(" : \"").append(JsonUtil.cleanStringForJson(productNameFr)).append("\", ");
        buf.append("\"description\"").append(" : \"").append(JsonUtil.cleanStringForJson(prodDescFr)).append("\", ");
        buf.append("\"productGroup\"").append(" : \"").append(newProduct.getProductType().getNameFr()).append("\" ");
        
        buf.append(" }, ");     //close product:{        
        
        buf.append("\"underwriting\"").append(" : ").append(" [ {");      
        if(newProduct.getUnderwriting() != null){
           Iterator<String> iterator = newProduct.getUnderwritingFr().iterator();
           String val ="";
           boolean first = true;
           while (iterator.hasNext()) {
             val = iterator.next();
             if(!first)
               buf.append("}, {");
             buf.append("\"name\"").append(" : \"").append(JsonUtil.cleanStringForJson(val)).append("\", ");                   
             buf.append("\"value\"").append(" : \"").append(JsonUtil.cleanStringForJson(val)).append("\"  ");                   
             first = false;
           }
        }
        buf.append(" } ");
        buf.append("  ] ,"); //close underwriting:{       
        buf.append("\"features\"").append(" : ").append("{ ");      
        buf.append("\"convertibleAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(convertibleAge)).append("\", ");
        buf.append("\"payableAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(payableAge)).append("\", ");
        buf.append("\"payableYears\"").append(" : \"").append(newProduct.getYearsPayable()).append("\" ");
        buf.append(" } ");   //close features:{          


        buf.append(" }"); //close fr:{       
        buf.append(" }"); //close details:{       

        buf.append(" }"); //close response:{       
        return buf.toString();
    }

    
    static String getResponseValuesCRITFineo(
        int rank,
        String companyNameEn,
        String companyNameFr,
        String productNameEn,
        String productNameFr,
        String productTypeCode,
        String productTypeLabel,
        String province,
        String monthlyBaseSumPremium,
        String annualBaseSumPremium,
        String percentage,
        String numIll,
        String prodDescEn,
        String prodDescFr,
        String premRenew,
        String uw,
        String uwFr,
        String convertibleAge,
        String payableAge,
        String healthClass,
        String healthClassFr,
        String smoking,
        String faceAmount,
        int tmpAge,
        Product newProduct

    ) {
        StringBuilder buf = new StringBuilder(" ");

        buf.append(" { "); //open response
        
        buf.append("\"info\"").append(" : ").append("{ ");         

        buf.append("\"carrierId\"").append(" : \"").append(newProduct.getCompanyID()).append("\", ");
        buf.append("\"productId\"").append(" : \"").append(newProduct.getID()).append("\", ");
        buf.append("\"productCode\"").append(" : \"").append(newProduct.getProductType().getID()).append("\", ");
        buf.append("\"productType\"").append(" : \"").append("CRIT").append("\", ");
        buf.append("\"productSubType\"").append(" : \"").append(newProduct.getProductType().getTermOrPerm()).append("\", ");
        
        buf.append("\"masterDescProductId\"").append(" : \"").append(newProduct.getMasterDescProductID()).append("\", ");
     //   master_desc_productid
        
        buf.append("\"rank\"").append(" : \"").append(rank).append("\", ");

        String guar = "N";
        if(newProduct.isGuaranteed())
            guar="Y";
        buf.append("\"guaranteed\"").append(" : \"").append(guar).append("\", ");

        SimpleDateFormat formatter = new SimpleDateFormat("dd-MMM-yyyy");
        String actDte = "";
        if(newProduct.getActiveDate() != null)
          actDte = formatter.format(newProduct.getActiveDate());
        buf.append("\"activeDate\"").append(" : \"").append(actDte).append("\", ");
        
        buf.append("\"productGroupId\"").append(" : \"").append(newProduct.getProductType().getMasterID()).append("\", ");
        buf.append("\"productTypeLabel\"").append(" : \"").append(JsonUtil.cleanStringForJson(productTypeLabel)).append("\" ");
        
        buf.append(" }, ");   //close info:{      
        
        
        buf.append("\"quoteSpecs\"").append(" : ").append("{ ");      
        buf.append("\"province\"").append(" : \"").append(JsonUtil.cleanStringForJson(province)).append("\", ");
        buf.append("\"numIll\"").append(" : \"").append(JsonUtil.cleanStringForJson(numIll)).append("\", ");
        
        buf.append("\"healthClass\"").append(" : \"").append(JsonUtil.cleanStringForJson(healthClass)).append("\", ");
        buf.append("\"healthClassFr\"").append(" : \"").append(JsonUtil.cleanStringForJson(healthClassFr)).append("\", ");
        
        buf.append("\"smoking\"").append(" : \"").append(JsonUtil.cleanStringForJson(smoking)).append("\", ");
        buf.append("\"faceAmount\"").append(" : \"").append(JsonUtil.cleanStringForJson(faceAmount)).append("\" ");
        
        buf.append(" }, ");    //close quoteSpecs:{     
        
        buf.append("\"quotes\"").append(" : ").append("{ ");      
        buf.append("\"monthlyPremium\"").append(" : \"").append(JsonUtil.cleanStringForJson(monthlyBaseSumPremium)).append("\", ");
        buf.append("\"yearlyPremium\"").append(" : \"").append(JsonUtil.cleanStringForJson(annualBaseSumPremium)).append("\", ");
        buf.append("\"percentage\"").append(" : \"").append(JsonUtil.cleanStringForJson(percentage)).append("\", ");
        
        buf.append("\"premiumRenewals\"").append(" : ").append(" [ {");      
        boolean isFirst = true;
        int lowAge = 0;
        int highAge = 0;
        
        int payAge = Integer.parseInt(payableAge);
        for (PremiumRenewal pr : newProduct.getPremiumRenewals()) {
           lowAge =   tmpAge+pr.getLowBound();
           highAge = tmpAge+pr.getHighBound();
           if(highAge > payAge)
               highAge= payAge;
          if(!isFirst)
              buf.append(" }, {");
          buf.append("\"startAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(lowAge+"")).append("\", ");
          buf.append("\"endAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(highAge+"")).append("\", ");
          buf.append("\"monthlyPremium\"").append(" : \"").append(JsonUtil.cleanStringForJson(Util.getStringFromDouble(pr.getTotalMonthly()))).append("\", ");
          buf.append("\"annualPremium\"").append(" : \"").append(JsonUtil.cleanStringForJson(Util.getStringFromDouble(pr.getTotalAnnual()))).append("\" ");
          isFirst = false;
        }

       
        buf.append(" } ] "); //close premiumRenewals:{             
        buf.append(" }, ");  //close quotes:{           
        
        buf.append("\"details\"").append(" : ").append("{ ");      
        buf.append("\"en\"").append(" : ").append("{ ");      
        buf.append("\"product\"").append(" : ").append("{ ");  
        buf.append("\"companyName\"").append(" : \"").append(JsonUtil.cleanStringForJson(companyNameEn)).append("\", ");
        buf.append("\"productName\"").append(" : \"").append(JsonUtil.cleanStringForJson(productNameEn)).append("\", ");
        buf.append("\"description\"").append(" : \"").append(JsonUtil.cleanStringForJson(prodDescEn)).append("\", ");
        buf.append("\"productGroup\"").append(" : \"").append(newProduct.getProductType().getName()).append("\" ");
        buf.append(" }, ");   //close product:{          
        
        buf.append("\"underwriting\"").append(" : ").append(" [ {");      

        if(newProduct.getUnderwriting() != null){
           Iterator<String> iterator = newProduct.getUnderwriting().iterator();
           String val ="";
           boolean first = true;
           while (iterator.hasNext()) {
             val = iterator.next();
             if(!first)
               buf.append("}, {");
             buf.append("\"name\"").append(" : \"").append(JsonUtil.cleanStringForJson(val)).append("\", ");                   
             buf.append("\"value\"").append(" : \"").append(JsonUtil.cleanStringForJson(val)).append("\"  ");                   
             
             first = false;
           }
        }
        buf.append(" } ");
        buf.append("  ] ,");    //close underwriting:{       
  
        buf.append("\"features\"").append(" : ").append("{ ");      
        buf.append("\"convertibleAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(convertibleAge)).append("\", ");
        buf.append("\"payableAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(payableAge)).append("\", ");
        buf.append("\"payableYears\"").append(" : \"").append(newProduct.getYearsPayable()).append("\" ");

        buf.append(" }  "); //close features:{               
        buf.append(" }, "); //close en:{       
        

        buf.append("\"fr\"").append(" : ").append("{ ");      
        buf.append("\"product\"").append(" : ").append("{ ");      
        buf.append("\"companyName\"").append(" : \"").append(JsonUtil.cleanStringForJson(companyNameFr)).append("\", ");
        buf.append("\"productName\"").append(" : \"").append(JsonUtil.cleanStringForJson(productNameFr)).append("\", ");
        buf.append("\"description\"").append(" : \"").append(JsonUtil.cleanStringForJson(prodDescFr)).append("\", ");
        buf.append("\"productGroup\"").append(" : \"").append(newProduct.getProductType().getNameFr()).append("\" ");
        buf.append(" }, ");     //close product:{        
        
        buf.append("\"underwriting\"").append(" : ").append(" [ {");      
        if(newProduct.getUnderwriting() != null){
           Iterator<String> iterator = newProduct.getUnderwritingFr().iterator();
           String val ="";
           boolean first = true;
           while (iterator.hasNext()) {
             val = iterator.next();
             if(!first)
               buf.append("}, {");
             buf.append("\"name\"").append(" : \"").append(JsonUtil.cleanStringForJson(val)).append("\", ");                   
             buf.append("\"value\"").append(" : \"").append(JsonUtil.cleanStringForJson(val)).append("\"  ");                   
             first = false;
           }
        }
        buf.append(" } ");
        buf.append("  ] ,"); //close underwriting:{       
        buf.append("\"features\"").append(" : ").append("{ ");      
        buf.append("\"convertibleAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(convertibleAge)).append("\", ");
        buf.append("\"payableAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(payableAge)).append("\", ");
        buf.append("\"payableYears\"").append(" : \"").append(newProduct.getYearsPayable()).append("\" ");
        buf.append(" } ");   //close features:{          


        buf.append(" }"); //close fr:{       
        buf.append(" }"); //close details:{       

        buf.append(" }"); //close response:{       
        return buf.toString();
    }

    
    
    static String getResponseValuesInJsonForOneResult(
        int rank,
        String companyNameEn,
        String companyNameFr,
        String productNameEn,
        String productNameFr,
        String productTypeCode,
        String productTypeLabel,
        String province,
        String monthlyBaseSumPremium,
        String annualBaseSumPremium,
        String percentage,
        String numIll,
        String descEn,
        String descFr,
        String premRenew,
        String uw,
        String uwFr,
        String convertibleAge,
        String payableAge,
        String healthClass,
        String healthClassFr,
        String smoking

    ) {
        StringBuilder buf = new StringBuilder("{ ");

        buf.append("\"rank\"").append(" : \"").append(rank).append("\", ");
        buf.append("\"company_name_en\"").append(" : \"").append(JsonUtil.cleanStringForJson(companyNameEn)).append("\", ");
        buf.append("\"company_name_fr\"").append(" : \"").append(JsonUtil.cleanStringForJson(companyNameFr)).append("\", ");
        buf.append("\"product_name_en\"").append(" : \"").append(JsonUtil.cleanStringForJson(productNameEn)).append("\", ");
        buf.append("\"product_name_fr\"").append(" : \"").append(JsonUtil.cleanStringForJson(productNameFr)).append("\", ");
        
        buf.append("\"product_type_code\"").append(" : \"").append(JsonUtil.cleanStringForJson(productTypeCode)).append("\", ");
        buf.append("\"product_type_label\"").append(" : \"").append(JsonUtil.cleanStringForJson(productTypeLabel)).append("\", ");
       
        buf.append("\"province\"").append(" : \"").append(JsonUtil.cleanStringForJson(province)).append("\", ");
        
        buf.append("\"monthly_premium\"").append(" : \"").append(JsonUtil.cleanStringForJson(monthlyBaseSumPremium)).append("\", ");
        buf.append("\"yearly_premium\"").append(" : \"").append(JsonUtil.cleanStringForJson(annualBaseSumPremium)).append("\", ");
        buf.append("\"percentage\"").append(" : \"").append(JsonUtil.cleanStringForJson(percentage)).append("\", ");
        buf.append("\"num_ill\"").append(" : \"").append(JsonUtil.cleanStringForJson(numIll)).append("\", ");
        buf.append("\"desc_en\"").append(" : \"").append(JsonUtil.cleanStringForJson(descEn)).append("\", ");
        buf.append("\"desc_fr\"").append(" : \"").append(JsonUtil.cleanStringForJson(descFr)).append("\", ");
        buf.append("\"premRenew\"").append(" : \"").append(JsonUtil.cleanStringForJson(premRenew)).append("\", ");
        buf.append("\"uw\"").append(" : \"").append(JsonUtil.cleanStringForJson(uw)).append("\", ");
        buf.append("\"uwFr\"").append(" : \"").append(JsonUtil.cleanStringForJson(uwFr)).append("\", ");
        buf.append("\"convertibleAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(convertibleAge)).append("\", ");
        buf.append("\"payableAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(payableAge)).append("\", ");
        buf.append("\"healthClass\"").append(" : \"").append(JsonUtil.cleanStringForJson(healthClass)).append("\", ");
        buf.append("\"healthClassFr\"").append(" : \"").append(JsonUtil.cleanStringForJson(healthClassFr)).append("\", ");
        buf.append("\"smoking\"").append(" : \"").append(JsonUtil.cleanStringForJson(smoking)).append("\"");
        
        buf.append(" }");
     
        
        return buf.toString();
    }

    
    static String getResponseValuesInJsonForOneResult(
        int rank,
        String companyNameEn,
        String companyNameFr,
        String productNameEn,
        String productNameFr,
        String productTypeCode,
        String productTypeLabel,
        String province,
        String monthlyBaseSumPremium,
        String annualBaseSumPremium,
        String percentage,
        //String numIll,
        String descEn,
        String descFr,
        String premRenew,
        String uw,
        String uwFr,
        String convertibleAge,
        String payableAge,
        String healthClass,
        String healthClassFr,
        String smoking


    ) {
        StringBuilder buf = new StringBuilder("{ ");

        buf.append("\"rank\"").append(" : \"").append(rank).append("\", ");
        buf.append("\"company_name_en\"").append(" : \"").append(JsonUtil.cleanStringForJson(companyNameEn)).append("\", ");
        buf.append("\"company_name_fr\"").append(" : \"").append(JsonUtil.cleanStringForJson(companyNameFr)).append("\", ");
        buf.append("\"product_name_en\"").append(" : \"").append(JsonUtil.cleanStringForJson(productNameEn)).append("\", ");
        buf.append("\"product_name_fr\"").append(" : \"").append(JsonUtil.cleanStringForJson(productNameFr)).append("\", ");
        
        buf.append("\"product_type_code\"").append(" : \"").append(JsonUtil.cleanStringForJson(productTypeCode)).append("\", ");
        buf.append("\"product_type_label\"").append(" : \"").append(JsonUtil.cleanStringForJson(productTypeLabel)).append("\", ");
        
        buf.append("\"province\"").append(" : \"").append(JsonUtil.cleanStringForJson(province)).append("\", ");
        buf.append("\"monthly_premium\"").append(" : \"").append(JsonUtil.cleanStringForJson(monthlyBaseSumPremium)).append("\", ");
        buf.append("\"yearly_premium\"").append(" : \"").append(JsonUtil.cleanStringForJson(annualBaseSumPremium)).append("\", ");
        
        buf.append("\"percentage\"").append(" : \"").append(JsonUtil.cleanStringForJson(percentage)).append("\", ");
        
        //buf.append("\"num_ill\"").append(" : \"").append(JsonUtil.cleanStringForJson(numIll)).append("\", ");
        buf.append("\"desc_en\"").append(" : \"").append(JsonUtil.cleanStringForJson(descEn)).append("\", ");
        buf.append("\"desc_fr\"").append(" : \"").append(JsonUtil.cleanStringForJson(descFr)).append("\", ");
      
        buf.append("\"premRenew\"").append(" : \"").append(JsonUtil.cleanStringForJson(premRenew)).append("\", ");
        buf.append("\"uw\"").append(" : \"").append(JsonUtil.cleanStringForJson(uw)).append("\", ");
        buf.append("\"uwFr\"").append(" : \"").append(JsonUtil.cleanStringForJson(uwFr)).append("\", ");
        buf.append("\"convertibleAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(convertibleAge)).append("\", ");
        buf.append("\"payableAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(payableAge)).append("\", ");

        buf.append("\"healthClass\"").append(" : \"").append(JsonUtil.cleanStringForJson(healthClass)).append("\", ");
        buf.append("\"healthClassFr\"").append(" : \"").append(JsonUtil.cleanStringForJson(healthClassFr)).append("\", ");
        buf.append("\"smoking\"").append(" : \"").append(JsonUtil.cleanStringForJson(smoking)).append("\"");

        
        buf.append(" }");

        return buf.toString();
    }
    
    static String getResponseValuesInJsonForOneResultForPercentage(
        int rank,
        String monthlyBaseSumPremium,
        String annualBaseSumPremium,
        String numIll,
        String percentage,
        String productTypeCode,
        String productTypeLabel
    ) {
        StringBuilder buf = new StringBuilder("{ ");

        buf.append("\"rank\"").append(" : \"").append(rank).append("\", ");
        buf.append("\"monthly_premium\"").append(" : \"").append(JsonUtil.cleanStringForJson(monthlyBaseSumPremium)).append("\", ");
        buf.append("\"yearly_premium\"").append(" : \"").append(JsonUtil.cleanStringForJson(annualBaseSumPremium)).append("\", ");
        buf.append("\"num_ill\"").append(" : \"").append(JsonUtil.cleanStringForJson(numIll)).append("\", ");
        buf.append("\"percentage\"").append(" : \"").append(JsonUtil.cleanStringForJson(percentage)).append("\", ");
        
        buf.append("\"product_type_code\"").append(" : \"").append(JsonUtil.cleanStringForJson(productTypeCode)).append("\", ");
        buf.append("\"product_type_label\"").append(" : \"").append(JsonUtil.cleanStringForJson(productTypeLabel)).append("\"");
        
        buf.append(" }");

        return buf.toString();
    }
    
    static String getResponseValuesInJsonForOneResultForPercentage(
        int rank,
        String monthlyBaseSumPremium,
        String annualBaseSumPremium,
        //String numIll,
        String percentage,
        String productTypeCode,
        String productTypeLabel 
    ) {
        StringBuilder buf = new StringBuilder("{ ");

        buf.append("\"rank\"").append(" : \"").append(rank).append("\", ");
        buf.append("\"monthly_premium\"").append(" : \"").append(JsonUtil.cleanStringForJson(monthlyBaseSumPremium)).append("\", ");
        buf.append("\"yearly_premium\"").append(" : \"").append(JsonUtil.cleanStringForJson(annualBaseSumPremium)).append("\", ");
        //buf.append("\"num_ill\"").append(" : \"").append(JsonUtil.cleanStringForJson(numIll)).append("\", ");
        buf.append("\"percentage\"").append(" : \"").append(JsonUtil.cleanStringForJson(percentage)).append("\", ");
        
        buf.append("\"product_type_code\"").append(" : \"").append(JsonUtil.cleanStringForJson(productTypeCode)).append("\", ");
        buf.append("\"product_type_label\"").append(" : \"").append(JsonUtil.cleanStringForJson(productTypeLabel)).append("\"");
        
        buf.append(" }");

        return buf.toString();
    }




        static String getResponseValuesLIFEWidget(
        int rank,
        String companyNameEn,
        String companyNameFr,
        String productNameEn,
        String productNameFr,
        String productTypeCode,
        String productTypeLabel,
        String province,
        String monthlyBaseSumPremium,
        String annualBaseSumPremium,
        String percentage,
        String prodDescEn,
        String prodDescFr,
        String premRenew,
        String uw,
        String uwFr,
        String convertibleAge,
        String payableAge,
        String healthClass,
        String healthClassFr,
        String smoking,
        String faceAmount,
        int tmpAge,
        Product newProduct

    ) {
        StringBuilder buf = new StringBuilder(" ");

        buf.append(" { "); //open response
        
        buf.append("\"info\"").append(" : ").append("{ ");         

        buf.append("\"carrierId\"").append(" : \"").append(newProduct.getCompanyID()).append("\", ");
        buf.append("\"productId\"").append(" : \"").append(newProduct.getID()).append("\", ");
        buf.append("\"productCode\"").append(" : \"").append(newProduct.getProductType().getID()).append("\", ");
        buf.append("\"productType\"").append(" : \"").append("LIFE").append("\", ");
        buf.append("\"productSubType\"").append(" : \"").append(newProduct.getProductType().getTermOrPerm()).append("\", ");
        
        buf.append("\"masterDescProductId\"").append(" : \"").append(newProduct.getMasterDescProductID()).append("\", ");
     //   master_desc_productid
        
        buf.append("\"rank\"").append(" : \"").append(rank).append("\", ");

        String guar = "N";
        if(newProduct.isGuaranteed())
            guar="Y";
        buf.append("\"guaranteed\"").append(" : \"").append(guar).append("\", ");

        SimpleDateFormat formatter = new SimpleDateFormat("dd-MMM-yyyy");
        String actDte = "";
        if(newProduct.getActiveDate() != null)
          actDte = formatter.format(newProduct.getActiveDate());
        buf.append("\"activeDate\"").append(" : \"").append(actDte).append("\", ");

        
        buf.append("\"productGroupId\"").append(" : \"").append(newProduct.getProductType().getMasterID()).append("\", ");
                
        buf.append("\"productTypeLabel\"").append(" : \"").append(JsonUtil.cleanStringForJson(productTypeLabel)).append("\" ");
        
        buf.append(" }, ");   //close info:{      
        
        
        buf.append("\"quoteSpecs\"").append(" : ").append("{ ");      
        buf.append("\"province\"").append(" : \"").append(JsonUtil.cleanStringForJson(province)).append("\", ");
        
        buf.append("\"healthClass\"").append(" : \"").append(JsonUtil.cleanStringForJson(healthClass)).append("\", ");
        buf.append("\"healthClassFr\"").append(" : \"").append(JsonUtil.cleanStringForJson(healthClassFr)).append("\", ");
        
        buf.append("\"smoking\"").append(" : \"").append(JsonUtil.cleanStringForJson(smoking)).append("\", ");
        buf.append("\"faceAmount\"").append(" : \"").append(JsonUtil.cleanStringForJson(faceAmount)).append("\" ");
        
        buf.append(" }, ");    //close quoteSpecs:{     
        
        buf.append("\"quotes\"").append(" : ").append("{ ");      
        buf.append("\"monthlyPremium\"").append(" : \"").append(JsonUtil.cleanStringForJson(monthlyBaseSumPremium)).append("\", ");
        buf.append("\"yearlyPremium\"").append(" : \"").append(JsonUtil.cleanStringForJson(annualBaseSumPremium)).append("\", ");
        buf.append("\"percentage\"").append(" : \"").append(JsonUtil.cleanStringForJson(percentage)).append("\", ");
        
        buf.append("\"premiumRenewals\"").append(" : ").append(" [ {");      
        boolean isFirst = true;
        int lowAge = 0;
        int highAge = 0;
        
        int payAge = Integer.parseInt(payableAge);
        for (PremiumRenewal pr : newProduct.getPremiumRenewals()) {
           lowAge =   tmpAge+pr.getLowBound();
           highAge = tmpAge+pr.getHighBound();
           if(highAge > payAge)
               highAge= payAge;
          if(!isFirst)
              buf.append(" }, {");
          buf.append("\"startAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(lowAge+"")).append("\", ");
          buf.append("\"endAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(highAge+"")).append("\", ");
          buf.append("\"monthlyPremium\"").append(" : \"").append(JsonUtil.cleanStringForJson(Util.getStringFromDouble(pr.getTotalMonthly()))).append("\", ");
          buf.append("\"annualPremium\"").append(" : \"").append(JsonUtil.cleanStringForJson(Util.getStringFromDouble(pr.getTotalAnnual()))).append("\" ");
          isFirst = false;
        }

       
        buf.append(" } ] "); //close premiumRenewals:{             
        buf.append(" }, ");  //close quotes:{           
        
        buf.append("\"details\"").append(" : ").append("{ ");      
        buf.append("\"en\"").append(" : ").append("{ ");      
        buf.append("\"product\"").append(" : ").append("{ ");  
        buf.append("\"companyName\"").append(" : \"").append(JsonUtil.cleanStringForJson(companyNameEn)).append("\", ");
        buf.append("\"productName\"").append(" : \"").append(JsonUtil.cleanStringForJson(productNameEn)).append("\", ");
        buf.append("\"description\"").append(" : \"").append(JsonUtil.cleanStringForJson(prodDescEn)).append("\", ");
        buf.append("\"productGroup\"").append(" : \"").append(newProduct.getProductType().getName()).append("\" ");
        
        buf.append(" }, ");   //close product:{          
        
        buf.append("\"underwriting\"").append(" : ").append(" [ {");      

        if(newProduct.getUnderwriting() != null){
           Iterator<String> iterator = newProduct.getUnderwriting().iterator();
           String val ="";
           boolean first = true;
           while (iterator.hasNext()) {
             val = iterator.next();
             if(!first)
               buf.append("}, {");
             buf.append("\"name\"").append(" : \"").append(JsonUtil.cleanStringForJson(val)).append("\", ");                   
             buf.append("\"value\"").append(" : \"").append(JsonUtil.cleanStringForJson(val)).append("\"  ");                   
             
             first = false;
           }
        }
        buf.append(" } ");
        buf.append("  ] ,");    //close underwriting:{       
//System.out.println(" line 173 JsonGenerator ");        
        buf.append("\"features\"").append(" : ").append("{ ");      
        buf.append("\"convertibleAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(convertibleAge)).append("\", ");
        buf.append("\"payableAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(payableAge)).append("\", ");
        buf.append("\"payableYears\"").append(" : \"").append(newProduct.getYearsPayable()).append("\" ");
        buf.append(" }  "); //close features:{               
        buf.append(" }, "); //close en:{       
        

        buf.append("\"fr\"").append(" : ").append("{ ");      
        buf.append("\"product\"").append(" : ").append("{ ");      
        buf.append("\"companyName\"").append(" : \"").append(JsonUtil.cleanStringForJson(companyNameFr)).append("\", ");
        buf.append("\"productName\"").append(" : \"").append(JsonUtil.cleanStringForJson(productNameFr)).append("\", ");
        buf.append("\"description\"").append(" : \"").append(JsonUtil.cleanStringForJson(prodDescFr)).append("\", ");
        buf.append("\"productGroup\"").append(" : \"").append(newProduct.getProductType().getNameFr()).append("\" ");
        
        buf.append(" }, ");     //close product:{        
        
        buf.append("\"underwriting\"").append(" : ").append(" [ {");      
        if(newProduct.getUnderwriting() != null){
           Iterator<String> iterator = newProduct.getUnderwritingFr().iterator();
           String val ="";
           boolean first = true;
           while (iterator.hasNext()) {
             val = iterator.next();
             if(!first)
               buf.append("}, {");
             buf.append("\"name\"").append(" : \"").append(JsonUtil.cleanStringForJson(val)).append("\", ");                   
             buf.append("\"value\"").append(" : \"").append(JsonUtil.cleanStringForJson(val)).append("\"  ");                   
             first = false;
           }
        }
        buf.append(" } ");
        buf.append("  ] ,"); //close underwriting:{       
        buf.append("\"features\"").append(" : ").append("{ ");      
        buf.append("\"convertibleAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(convertibleAge)).append("\", ");
        buf.append("\"payableAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(payableAge)).append("\", ");
        buf.append("\"payableYears\"").append(" : \"").append(newProduct.getYearsPayable()).append("\" ");
        buf.append(" } ");   //close features:{          


        buf.append(" }"); //close fr:{       
        buf.append(" }"); //close details:{       

        buf.append(" }"); //close response:{       
        return buf.toString();
    }

    
    static String getResponseValuesCRITWidget(
        int rank,
        String companyNameEn,
        String companyNameFr,
        String productNameEn,
        String productNameFr,
        String productTypeCode,
        String productTypeLabel,
        String province,
        String monthlyBaseSumPremium,
        String annualBaseSumPremium,
        String percentage,
        String numIll,
        String prodDescEn,
        String prodDescFr,
        String premRenew,
        String uw,
        String uwFr,
        String convertibleAge,
        String payableAge,
        String healthClass,
        String healthClassFr,
        String smoking,
        String faceAmount,
        int tmpAge,
        Product newProduct

    ) {
        StringBuilder buf = new StringBuilder(" ");

        buf.append(" { "); //open response
        
        buf.append("\"info\"").append(" : ").append("{ ");         

        buf.append("\"carrierId\"").append(" : \"").append(newProduct.getCompanyID()).append("\", ");
        buf.append("\"productId\"").append(" : \"").append(newProduct.getID()).append("\", ");
        buf.append("\"productCode\"").append(" : \"").append(newProduct.getProductType().getID()).append("\", ");
        buf.append("\"productType\"").append(" : \"").append("CRIT").append("\", ");
        buf.append("\"productSubType\"").append(" : \"").append(newProduct.getProductType().getTermOrPerm()).append("\", ");
        
        buf.append("\"masterDescProductId\"").append(" : \"").append(newProduct.getMasterDescProductID()).append("\", ");
     //   master_desc_productid
        
        buf.append("\"rank\"").append(" : \"").append(rank).append("\", ");

        String guar = "N";
        if(newProduct.isGuaranteed())
            guar="Y";
        buf.append("\"guaranteed\"").append(" : \"").append(guar).append("\", ");

        SimpleDateFormat formatter = new SimpleDateFormat("dd-MMM-yyyy");
        String actDte = "";
        if(newProduct.getActiveDate() != null)
          actDte = formatter.format(newProduct.getActiveDate());
        buf.append("\"activeDate\"").append(" : \"").append(actDte).append("\", ");

        
        buf.append("\"productGroupId\"").append(" : \"").append(newProduct.getProductType().getMasterID()).append("\", ");
        buf.append("\"productTypeLabel\"").append(" : \"").append(JsonUtil.cleanStringForJson(productTypeLabel)).append("\" ");
        
        buf.append(" }, ");   //close info:{      
        
        
        buf.append("\"quoteSpecs\"").append(" : ").append("{ ");      
        buf.append("\"province\"").append(" : \"").append(JsonUtil.cleanStringForJson(province)).append("\", ");
        buf.append("\"numIll\"").append(" : \"").append(JsonUtil.cleanStringForJson(numIll)).append("\", ");
        
        buf.append("\"healthClass\"").append(" : \"").append(JsonUtil.cleanStringForJson(healthClass)).append("\", ");
        buf.append("\"healthClassFr\"").append(" : \"").append(JsonUtil.cleanStringForJson(healthClassFr)).append("\", ");
        
        buf.append("\"smoking\"").append(" : \"").append(JsonUtil.cleanStringForJson(smoking)).append("\", ");
        buf.append("\"faceAmount\"").append(" : \"").append(JsonUtil.cleanStringForJson(faceAmount)).append("\" ");
        
        buf.append(" }, ");    //close quoteSpecs:{     
        
        buf.append("\"quotes\"").append(" : ").append("{ ");      
        buf.append("\"monthlyPremium\"").append(" : \"").append(JsonUtil.cleanStringForJson(monthlyBaseSumPremium)).append("\", ");
        buf.append("\"yearlyPremium\"").append(" : \"").append(JsonUtil.cleanStringForJson(annualBaseSumPremium)).append("\", ");
        buf.append("\"percentage\"").append(" : \"").append(JsonUtil.cleanStringForJson(percentage)).append("\", ");
        
        buf.append("\"premiumRenewals\"").append(" : ").append(" [ {");      
        boolean isFirst = true;
        int lowAge = 0;
        int highAge = 0;
        
        int payAge = Integer.parseInt(payableAge);
        for (PremiumRenewal pr : newProduct.getPremiumRenewals()) {
           lowAge =   tmpAge+pr.getLowBound();
           highAge = tmpAge+pr.getHighBound();
           if(highAge > payAge)
               highAge= payAge;
          if(!isFirst)
              buf.append(" }, {");
          buf.append("\"startAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(lowAge+"")).append("\", ");
          buf.append("\"endAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(highAge+"")).append("\", ");
          buf.append("\"monthlyPremium\"").append(" : \"").append(JsonUtil.cleanStringForJson(Util.getStringFromDouble(pr.getTotalMonthly()))).append("\", ");
          buf.append("\"annualPremium\"").append(" : \"").append(JsonUtil.cleanStringForJson(Util.getStringFromDouble(pr.getTotalAnnual()))).append("\" ");
          isFirst = false;
        }

       
        buf.append(" } ] "); //close premiumRenewals:{             
        buf.append(" }, ");  //close quotes:{           
        
        buf.append("\"details\"").append(" : ").append("{ ");      
        buf.append("\"en\"").append(" : ").append("{ ");      
        buf.append("\"product\"").append(" : ").append("{ ");  
        buf.append("\"companyName\"").append(" : \"").append(JsonUtil.cleanStringForJson(companyNameEn)).append("\", ");
        buf.append("\"productName\"").append(" : \"").append(JsonUtil.cleanStringForJson(productNameEn)).append("\", ");
        buf.append("\"description\"").append(" : \"").append(JsonUtil.cleanStringForJson(prodDescEn)).append("\", ");
        buf.append("\"productGroup\"").append(" : \"").append(newProduct.getProductType().getName()).append("\" ");
        buf.append(" }, ");   //close product:{          
        
        buf.append("\"underwriting\"").append(" : ").append(" [ {");      

        if(newProduct.getUnderwriting() != null){
           Iterator<String> iterator = newProduct.getUnderwriting().iterator();
           String val ="";
           boolean first = true;
           while (iterator.hasNext()) {
             val = iterator.next();
             if(!first)
               buf.append("}, {");
             buf.append("\"name\"").append(" : \"").append(JsonUtil.cleanStringForJson(val)).append("\", ");                   
             buf.append("\"value\"").append(" : \"").append(JsonUtil.cleanStringForJson(val)).append("\"  ");                   
             
             first = false;
           }
        }
        buf.append(" } ");
        buf.append("  ] ,");    //close underwriting:{       
  
        buf.append("\"features\"").append(" : ").append("{ ");      
        buf.append("\"convertibleAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(convertibleAge)).append("\", ");
        buf.append("\"payableAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(payableAge)).append("\", ");
        buf.append("\"payableYears\"").append(" : \"").append(newProduct.getYearsPayable()).append("\" ");

        buf.append(" }  "); //close features:{               
        buf.append(" }, "); //close en:{       
        

        buf.append("\"fr\"").append(" : ").append("{ ");      
        buf.append("\"product\"").append(" : ").append("{ ");      
        buf.append("\"companyName\"").append(" : \"").append(JsonUtil.cleanStringForJson(companyNameFr)).append("\", ");
        buf.append("\"productName\"").append(" : \"").append(JsonUtil.cleanStringForJson(productNameFr)).append("\", ");
        buf.append("\"description\"").append(" : \"").append(JsonUtil.cleanStringForJson(prodDescFr)).append("\", ");
        buf.append("\"productGroup\"").append(" : \"").append(newProduct.getProductType().getNameFr()).append("\" ");
        buf.append(" }, ");     //close product:{        
        
        buf.append("\"underwriting\"").append(" : ").append(" [ {");      
        if(newProduct.getUnderwriting() != null){
           Iterator<String> iterator = newProduct.getUnderwritingFr().iterator();
           String val ="";
           boolean first = true;
           while (iterator.hasNext()) {
             val = iterator.next();
             if(!first)
               buf.append("}, {");
             buf.append("\"name\"").append(" : \"").append(JsonUtil.cleanStringForJson(val)).append("\", ");                   
             buf.append("\"value\"").append(" : \"").append(JsonUtil.cleanStringForJson(val)).append("\"  ");                   
             first = false;
           }
        }
        buf.append(" } ");
        buf.append("  ] ,"); //close underwriting:{       
        buf.append("\"features\"").append(" : ").append("{ ");      
        buf.append("\"convertibleAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(convertibleAge)).append("\", ");
        buf.append("\"payableAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(payableAge)).append("\", ");
        buf.append("\"payableYears\"").append(" : \"").append(newProduct.getYearsPayable()).append("\" ");
        buf.append(" } ");   //close features:{          


        buf.append(" }"); //close fr:{       
        buf.append(" }"); //close details:{       

        buf.append(" }"); //close response:{       
        return buf.toString();
    }

    static String getResponseValues(
        int rank,
        String companyNameEn,
        String companyNameFr,
        String productNameEn,
        String productNameFr,
        String productTypeCode,
        String productTypeLabel,
        String province,
        String monthlyBaseSumPremium,
        String annualBaseSumPremium,
     //   String percentage,
        String prodDescEn,
        String prodDescFr,
        String convertibleAge,
        String payableAge,
        String healthClass,
        String healthClassFr,
        String classFlag,
        String smoking,
        String faceAmount,
        int tmpAge,
        Product newProduct,
        String coverageType,
        String productClass//Level or Decreasing added sept 20, 2021 aa

    ) {
        StringBuilder buf = new StringBuilder(" ");

        buf.append(" { "); //open response
        
        buf.append("\"info\"").append(" : ").append("{ ");         

        buf.append("\"carrierId\"").append(" : \"").append(newProduct.getCompanyID()).append("\", ");
        buf.append("\"productId\"").append(" : \"").append(newProduct.getID()).append("\", ");
        buf.append("\"productCode\"").append(" : \"").append(newProduct.getProductType().getID()).append("\", ");
        buf.append("\"productType\"").append(" : \"").append(productClass).append("\", ");
        
        buf.append("\"productSubType\"").append(" : \"").append(newProduct.getProductType().getTermOrPerm()).append("\", ");
        
        buf.append("\"masterDescProductId\"").append(" : \"").append(newProduct.getMasterDescProductID()).append("\", ");
     //   master_desc_productid
     
        buf.append("\"rank\"").append(" : \"").append(rank).append("\", ");

        String guar = "N";
        if(newProduct.isGuaranteed())
            guar="Y";
        buf.append("\"guaranteed\"").append(" : \"").append(guar).append("\", ");

        SimpleDateFormat formatter = new SimpleDateFormat("dd-MMM-yyyy");
        String actDte = "";
        if(newProduct.getActiveDate() != null)
          actDte = formatter.format(newProduct.getActiveDate());
        buf.append("\"activeDate\"").append(" : \"").append(actDte).append("\", ");

        buf.append("\"productGroupId\"").append(" : \"").append(newProduct.getProductType().getMasterID()).append("\", ");
                
        buf.append("\"productTypeLabel\"").append(" : \"").append(JsonUtil.cleanStringForJson(productTypeLabel)).append("\" ");
        
        buf.append(" }, ");   //close info:{      
        
        
        buf.append("\"quoteSpecs\"").append(" : ").append("{ ");      
        buf.append("\"province\"").append(" : \"").append(JsonUtil.cleanStringForJson(province)).append("\", ");

        if(productClass.equalsIgnoreCase("CRIT"))
          buf.append("\"numIll\"").append(" : \"").append(newProduct.getNumIllnesses()).append("\", ");
        
        if(productClass.equalsIgnoreCase("DI")){
          buf.append("\"WBCombos\"").append(" : ").append(" [ {");      
          boolean first = true;                     
          for (WBCombo wb : newProduct.getWbcombos()) {
            if(!first)
              buf.append(" }, {");
            buf.append("\"benefitDescEN\"").append(" : \"").append(JsonUtil.cleanStringForJson(wb.getBenefitDescEN())).append("\", ");
            buf.append("\"classDescEN()\"").append(" : \"").append(JsonUtil.cleanStringForJson(wb.getClassDescEN())).append("\", ");
            buf.append("\"waitingPeriod()\"").append(" : \"").append(JsonUtil.cleanStringForJson(wb.getWaitingPeriod()+"")).append("\", ");
            buf.append("\"benefitPeriod()\"").append(" : \"").append(JsonUtil.cleanStringForJson(wb.getBenefitPeriod()+"")).append("\", ");
            buf.append("\"waitingDescEN()\"").append(" : \"").append(JsonUtil.cleanStringForJson(wb.getWaitingDescEN())).append("\" ");
            first = false;
          }
          
          buf.append(" } ] "); //close premiumRenewals:{                       
        }
        
        
        buf.append("\"healthClass\"").append(" : \"").append(JsonUtil.cleanStringForJson(healthClass)).append("\", ");
        buf.append("\"healthClassFr\"").append(" : \"").append(JsonUtil.cleanStringForJson(healthClassFr)).append("\", ");
        
        buf.append("\"classFlag\"").append(" : \"").append(JsonUtil.cleanStringForJson(classFlag)).append("\", ");//NEW!!  jan 13 2021 -aa

        buf.append("\"smoking\"").append(" : \"").append(JsonUtil.cleanStringForJson(smoking)).append("\", ");

        buf.append("\"lowMonths\"").append(" : \"").append(JsonUtil.cleanStringForJson(newProduct.getHealthClass().getNonSmokerMonths()+"")).append("\", ");

        String cigs = "N";
        if(newProduct.getHealthClass().isCigarretSmoker()){
          cigs = "Y";    
        }
        
        buf.append("\"cigarettes\"").append(" : \"").append(JsonUtil.cleanStringForJson(cigs+"")).append("\", ");//NEW!!  jan 13 2021 -aa
        
        String famHist = "N";  //Family History  -- added jan 13 2021 -aa   
        if(newProduct.getHealthClass().isFamilyHistory()){
          famHist = "Y";    
        }
        
        buf.append("\"familyHistory\"").append(" : \"").append(JsonUtil.cleanStringForJson(famHist+"")).append("\", ");//NEW!!  jan 13 2021 -aa
        
        buf.append("\"coverageType\"").append(" : \"").append(JsonUtil.cleanStringForJson(coverageType)).append("\", ");//Level or Decreasing added sept 20, 2021 aa
 
        buf.append("\"faceAmount\"").append(" : \"").append(JsonUtil.cleanStringForJson(faceAmount)).append("\" ");
        
        buf.append(" }, ");    //close quoteSpecs:{     
        
        buf.append("\"quotes\"").append(" : ").append("{ ");     

        buf.append("\"monthlyPremium\"").append(" : \"").append(JsonUtil.cleanStringForJson(monthlyBaseSumPremium)).append("\", ");
        buf.append("\"yearlyPremium\"").append(" : \"").append(JsonUtil.cleanStringForJson(annualBaseSumPremium)).append("\", ");
    //    buf.append("\"percentage\"").append(" : \"").append(JsonUtil.cleanStringForJson(percentage)).append("\", ");
        
        buf.append("\"premiumRenewals\"").append(" : ").append(" [ {");      
        boolean isFirst = true;
        int lowAge = 0;
        int highAge = 0;

        int payAge = Integer.parseInt(payableAge);
        for (PremiumRenewal pr : newProduct.getPremiumRenewals()) {
           lowAge =   tmpAge+pr.getLowBound();
           highAge = tmpAge+pr.getHighBound();
           if(highAge > payAge)
               highAge= payAge;
          if(!isFirst)
              buf.append(" }, {");
          buf.append("\"startAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(lowAge+"")).append("\", ");
          buf.append("\"endAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(highAge+"")).append("\", ");
          buf.append("\"monthlyPremium\"").append(" : \"").append(JsonUtil.cleanStringForJson(Util.getStringFromDouble(pr.getTotalMonthly()))).append("\", ");
          buf.append("\"annualPremium\"").append(" : \"").append(JsonUtil.cleanStringForJson(Util.getStringFromDouble(pr.getTotalAnnual()))).append("\" ");
          isFirst = false;
        }

       
        buf.append(" } ] "); //close premiumRenewals:{             
        buf.append(" }, ");  //close quotes:{           
        
        buf.append("\"details\"").append(" : ").append("{ ");      
        buf.append("\"en-us\"").append(" : ").append("{ ");      
        buf.append("\"product\"").append(" : ").append("{ ");  
        buf.append("\"productName\"").append(" : \"").append(JsonUtil.cleanStringForJson(productNameEn)).append("\", ");
        buf.append("\"companyName\"").append(" : \"").append(JsonUtil.cleanStringForJson(companyNameEn)).append("\", ");
        buf.append("\"description\"").append(" : \"").append(JsonUtil.cleanStringForJson(prodDescEn)).append("\", ");
        buf.append("\"productGroup\"").append(" : \"").append(newProduct.getProductType().getName()).append("\" ");
        
        buf.append(" }, ");   //close product:{          
        
        buf.append("\"underwriting\"").append(" : ").append(" [ {");      

        if(newProduct.getUnderwriting() != null){
           Iterator<String> iterator = newProduct.getUnderwriting().iterator();
           String val ="";
           boolean first = true;
           while (iterator.hasNext()) {
             val = iterator.next();
             if(!first)
               buf.append("}, {");
             buf.append("\"name\"").append(" : \"").append(JsonUtil.cleanStringForJson(val)).append("\", ");                   
             buf.append("\"value\"").append(" : \"").append(JsonUtil.cleanStringForJson(val)).append("\"  ");                   
             
             first = false;
           }
        }
        buf.append(" } ");
        buf.append("  ] ,");    //close underwriting:{       
//System.out.println(" line 173 JsonGenerator ");        
        buf.append("\"features\"").append(" : ").append("{ ");      
        buf.append("\"convertibleAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(convertibleAge)).append("\", ");
        buf.append("\"payableAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(payableAge)).append("\", ");
        buf.append("\"payableYears\"").append(" : \"").append(newProduct.getYearsPayable()).append("\" ");
        buf.append(" }  "); //close features:{               
        buf.append(" }, "); //close en-us:{       
        

        buf.append("\"fr\"").append(" : ").append("{ ");      
        buf.append("\"product\"").append(" : ").append("{ ");      
        buf.append("\"companyName\"").append(" : \"").append(JsonUtil.cleanStringForJson(companyNameFr)).append("\", ");
        buf.append("\"productName\"").append(" : \"").append(JsonUtil.cleanStringForJson(productNameFr)).append("\", ");
        buf.append("\"description\"").append(" : \"").append(JsonUtil.cleanStringForJson(prodDescFr)).append("\", ");
        buf.append("\"productGroup\"").append(" : \"").append(newProduct.getProductType().getNameFr()).append("\" ");
        
        buf.append(" }, ");     //close product:{        
        
        buf.append("\"underwriting\"").append(" : ").append(" [ {");      
        if(newProduct.getUnderwriting() != null){
           Iterator<String> iterator = newProduct.getUnderwritingFr().iterator();
           String val ="";
           boolean first = true;
           while (iterator.hasNext()) {
             val = iterator.next();
             if(!first)
               buf.append("}, {");
             buf.append("\"name\"").append(" : \"").append(JsonUtil.cleanStringForJson(val)).append("\", ");                   
             buf.append("\"value\"").append(" : \"").append(JsonUtil.cleanStringForJson(val)).append("\"  ");                   
             first = false;
           }
        }
        buf.append(" } ");
        buf.append("  ] ,"); //close underwriting:{       

        buf.append("\"features\"").append(" : ").append("{ ");      
        buf.append("\"convertibleAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(convertibleAge)).append("\", ");
        buf.append("\"payableAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(payableAge)).append("\", ");
        buf.append("\"payableYears\"").append(" : \"").append(newProduct.getYearsPayable()).append("\" ");
        buf.append(" } ");   //close features:{          


        buf.append(" }"); //close fr:{       
        buf.append(" }"); //close details:{       

        buf.append(" }"); //close response:{       
        return buf.toString();
    }


    
    static String getLocalesValues(
        List<ProductType> productTypes, List<ProductType> productTypesFr, 
            List<Province> provs, List<Types> types, List<IMProduct> products,
            List<Types> titleTypes, Province defaultProv, AboutMe aboutMe) {  
        StringBuilder buf = new StringBuilder(" ");
   
        buf.append(" { "); //open response
        
        buf.append("\"en-us\"").append(" : ").append("{ ");      

        boolean isFirst = true;
        String tmp = "";
        @SuppressWarnings("unused")
		boolean same = false;
        @SuppressWarnings("unused")
		boolean putABrack = false;

// count of options set in - getTypeIntId
        for(Types type: types) {
            
          if(type.getAcordName().equals(tmp))
            same= true;
          
          else{
            if(!isFirst){
              buf.append(" } ] }, ");  
              putABrack = false;
            }
            isFirst=true;
            same= false;
            tmp = type.getAcordName();
          }
          
          if(type.getCategory().equalsIgnoreCase("label")){
            if(type.getTypeIntId() == 0){
              buf.append("\"").append(JsonUtil.cleanStringForJson(type.getAcordName())).append("\" : \"").append(JsonUtil.cleanStringForJson(type.getDescEn())).append("\", ");
            }
            else{  
              buf.append("\"").append(JsonUtil.cleanStringForJson(type.getAcordName())).append("\" : ").append(" { ");    
              buf.append("\"label\"").append(" : \"").append(JsonUtil.cleanStringForJson(type.getDescEn())).append("\", ");
              buf.append("\"options\"").append(" : [").append("{");      
            }
          }
          else{
            if(!isFirst)
                buf.append(" }, {");
            buf.append("\"value\"").append(" : \"").append(type.getOldSystemCode()).append("\", ");
            buf.append("\"label\"").append(" : \"").append(JsonUtil.cleanStringForJson(type.getDescEn())).append("\" ");
            isFirst=false;
          }
          
        }
        
        if(!isFirst){
          buf.append(" } ] "); 
          buf.append(" }, "); 
        }
        
         buf.append("\"").append("title").append("\" : \"").append(JsonUtil.cleanStringForJson(aboutMe.getAboutMeEnglish())).append("\", ");
        
// so we have the label ( acord field) , the title (DESC_EN) and then the Text1 is the DEFINITION_EN field
/*
        
        for(Types typeT: titleTypes) {
           buf.append("\"").append(JsonUtil.cleanStringForJson(typeT.getAcordName())).append("\" : \"").append(JsonUtil.cleanStringForJson(typeT.getDescEn())).append("\", ");
           buf.append("\"").append(JsonUtil.cleanStringForJson(typeT.getCategory())).append("\" : \"").append(JsonUtil.cleanStringForJson(typeT.getDefinitionEn())).append("\", ");           
        }
*/

        isFirst = true;
        for(Types type: titleTypes) {
          buf.append("\"").append(JsonUtil.cleanStringForJson(type.getAcordName())).append("\" : \"").append(JsonUtil.cleanStringForJson(type.getDescEn())).append("\", ");
          buf.append("\"").append(JsonUtil.cleanStringForJson(type.getCategory())).append("\" : \"").append(JsonUtil.cleanStringForJson(type.getDefinitionEn())).append("\", ");           
          /*
          buf.append("\"").append(JsonUtil.cleanStringForJson(type.getAcordName())).append("\" : ").append(" { ");    
          buf.append("\"label\"").append(" : \"").append(JsonUtil.cleanStringForJson(type.getDescEn())).append("\", ");
          buf.append("\"options\"").append(" : [").append("{");      
          buf.append("\"value\"").append(" : \"").append(type.getDescEn()).append("\", ");
          buf.append("\"label\"").append(" : \"").append(JsonUtil.cleanStringForJson(type.getDefinitionEn())).append("\" ");
          isFirst=false;
          buf.append(" }, ");   
  */
        }
        
        
        
        buf.append("\"defaultProvinceValue\"").append(" : \"").append(JsonUtil.cleanStringForJson(defaultProv.getProvinceCode())).append("\", ");
        buf.append("\"defaultProvinceLabel\"").append(" : \"").append(JsonUtil.cleanStringForJson(defaultProv.getNameEn())).append("\", ");
        
         
        buf.append("\"datePicker\"").append(" : ").append("{ ");      
        buf.append("\"title\"").append(" : \"").append("Select date").append("\", ");

        buf.append("\"monthsFull\"").append(" : [ ");
        buf.append("\"January\"").append(", ");
        buf.append("\"February\"").append(", ");
        buf.append("\"March\"").append(", ");
        buf.append("\"April\"").append(", ");
        buf.append("\"May\"").append(", ");
        buf.append("\"June\"").append(", ");
        buf.append("\"July\"").append(", ");
        buf.append("\"August\"").append(", ");
        buf.append("\"September\"").append(", ");
        buf.append("\"October\"").append(", ");
        buf.append("\"November\"").append(", ");
        buf.append("\"December\"");
        buf.append(" ], "); 

        buf.append("\"monthsShort\"").append(" : [ ");
        buf.append("\"Jan\"").append(", ");
        buf.append("\"Feb\"").append(", ");
        buf.append("\"Mar\"").append(", ");
        buf.append("\"Apr\"").append(", ");
        buf.append("\"May\"").append(", ");
        buf.append("\"June\"").append(", ");
        buf.append("\"July\"").append(", ");
        buf.append("\"Aug\"").append(", ");
        buf.append("\"Sept\"").append(", ");
        buf.append("\"Oct\"").append(", ");
        buf.append("\"Nov\"").append(", ");
        buf.append("\"Dec\"");
        buf.append(" ], "); 

        buf.append("\"weekdaysFull\"").append(" : [ ");
        buf.append("\"Sunday\"").append(", ");
        buf.append("\"Monday\"").append(", ");
        buf.append("\"Tuesday\"").append(", ");
        buf.append("\"Wednesday\"").append(", ");
        buf.append("\"Thursday\"").append(", ");
        buf.append("\"Friday\"").append(", ");
        buf.append("\"Saturday\"");
        buf.append(" ], "); 

        buf.append("\"weekdaysShort\"").append(" : [ ");
        buf.append("\"Sun\"").append(", ");
        buf.append("\"Mon\"").append(", ");
        buf.append("\"Tue\"").append(", ");
        buf.append("\"Wed\"").append(", ");
        buf.append("\"Thu\"").append(", ");
        buf.append("\"Fri\"").append(", ");
        buf.append("\"Sat\"");
        buf.append(" ], "); 

        buf.append("\"weekdaysNarrow\"").append(" : [ ");
        buf.append("\"S\"").append(", ");
        buf.append("\"M\"").append(", ");
        buf.append("\"T\"").append(", ");
        buf.append("\"W\"").append(", ");
        buf.append("\"T\"").append(", ");
        buf.append("\"F\"").append(", ");
        buf.append("\"S\"");
        buf.append(" ], "); 
        
        buf.append("\"okBtnText\"").append(" : \"").append("Ok").append("\", ");
        buf.append("\"clearBtnText\"").append(" : \"").append("Clear").append("\", ");
        buf.append("\"cancelBtnText\"").append(" : \"").append("Cancel").append("\" ");
        buf.append(" }, "); 
 

        buf.append("\"province\"").append(" : ").append("{ ");    
        buf.append("\"label\"").append(" : \"").append("Current province/territory of residence").append("\", ");
        buf.append("\"options\"").append(" : [").append("{");      

        isFirst = true;
        for(Province prov: provs) {
            if(!isFirst)
              buf.append(" }, {");
            buf.append("\"value\"").append(" : \"").append(prov.getProvinceCode()).append("\", ");
            buf.append("\"label\"").append(" : \"").append(JsonUtil.cleanStringForJson(prov.getNameEn())).append("\" ");
            isFirst=false;
        }
      
        
        buf.append(" } ] "); 
        buf.append(" }, "); 
        
        buf.append("\"product\"").append(" : ").append("{ ");
        buf.append("\"label\"").append(" : \"").append("Product").append("\", ");
        buf.append("\"options\"").append(" : ").append(" [ {");      

        isFirst = true;
        for(IMProduct prod: products) {
            if(!isFirst)
              buf.append(" }, {");
          
            buf.append("\"label\"").append(" : \"").append(JsonUtil.cleanStringForJson(prod.getEnglish_name())).append("\", ");
            buf.append("\"value\"").append(" : \"").append(prod.getProductid()).append("\", ");
            buf.append("\"company\"").append(" : \"").append(prod.getCompanyid()).append("\", ");
            
            if(prod.getProductclass().equals("CRIT")){
              buf.append("\"insuranceType\"").append(" : \"").append("C").append("\", ");
            }
            
            if(prod.getProductclass().equals("LIFE")){
              buf.append("\"insuranceType\"").append(" : \"").append("L").append("\", ");
            }
            
            buf.append("\"productDesc\"").append(" : \"").append(JsonUtil.cleanStringForJson(prod.getShort_english_desc())).append("\", ");
    
            buf.append("\"maleLowAge\"").append(" : \"").append(prod.getIm_lifeProductDetail().getMLOW_ISSUE_AGE()).append("\", ");
            buf.append("\"maleHighAge\"").append(" : \"").append(prod.getIm_lifeProductDetail().getMHIGH_ISSUE_AGE()).append("\", ");
            buf.append("\"femaleLowAge\"").append(" : \"").append(prod.getIm_lifeProductDetail().getFLOW_ISSUE_AGE()).append("\", ");
            buf.append("\"femaleHighAge\"").append(" : \"").append(prod.getIm_lifeProductDetail().getFHIGH_ISSUE_AGE()).append("\", ");

            buf.append("\"maleLowAgeSmoker\"").append(" : \"").append(prod.getIm_lifeProductDetail().getMLOW_ISSUE_AGE_SMOKER()).append("\", ");
            buf.append("\"maleHighAgeSmoker\"").append(" : \"").append(prod.getIm_lifeProductDetail().getMHIGH_ISSUE_AGE_SMOKER()).append("\", ");
            buf.append("\"femaleLowAgeSmoker\"").append(" : \"").append(prod.getIm_lifeProductDetail().getFLOW_ISSUE_AGE_SMOKER()).append("\", ");
            buf.append("\"femaleHighAgeSmoker\"").append(" : \"").append(prod.getIm_lifeProductDetail().getFHIGH_ISSUE_AGE_SMOKER()).append("\", ");
        
            DecimalFormat df2 = new DecimalFormat("#########.00");
            String minFace = df2.format(prod.getIm_lifeProductDetail().getMIN_BUS_FACE_AMOUNT());
            String maxFace = df2.format(prod.getIm_lifeProductDetail().getMAX_BUS_FACE_AMOUNT());
            
            buf.append("\"minimumFaceAmount\"").append(" : \"").append(minFace).append("\", ");
            buf.append("\"maximumFaceAmount\"").append(" : \"").append(maxFace).append("\", ");

            int sfl = prod.getIm_lifeProductDetail().getSING_JO1_JOLAST();

            boolean hasSing = false;
            boolean hasFirst = false;
            boolean hasLast = false;
                       
            if(sfl ==1 || sfl == 3 || sfl == 5 || sfl == 7)
              hasSing = true; //"S"
            
            if(sfl ==2 || sfl == 3 || sfl == 6 || sfl == 7)
              hasFirst = true;//"F"; 
            
            if(sfl ==4 || sfl == 5 || sfl == 6 || sfl == 7)
              hasLast = true; //"L"; 

            buf.append("\"jointModes\"").append(" : ").append(" [");      
            boolean addCom = false;
            if(hasSing){
              buf.append("\"").append("S").append("\" ");                 
              addCom = true;
            }
            if(hasFirst){
              if(addCom)
                buf.append(", ");  
              buf.append("\"").append("F").append("\" ");                 
              addCom = true;
            }

            if(hasLast){
              if(addCom)
                buf.append(", ");  
              buf.append("\"").append("L").append("\" ");                 
            }

            buf.append("] "); 
            
            isFirst=false;
        }
        
        buf.append(" } ] "); 
        
        
        buf.append(" },  "); 

        buf.append("\"productTypes\"").append(" : ").append("{ ");
        buf.append("\"label\"").append(" : \"").append("Product Type").append("\", ");
        buf.append("\"options\"").append(" : ").append(" [ {");      
        isFirst = true;
        for(ProductType prodType: productTypes) {
            if(!isFirst)
              buf.append(" }, {");
          
            buf.append("\"value\"").append(" : \"").append(prodType.getID()).append("\", ");
            buf.append("\"label\"").append(" : \"").append(JsonUtil.cleanStringForJson(prodType.getName())).append("\", ");
            
            if(prodType.getProductClass().equals("BOTH")){
              buf.append("\"insuranceType\"").append(" : \"").append("B").append("\", ");
            }

            if(prodType.getProductClass().equals("CRIT")){
              buf.append("\"insuranceType\"").append(" : \"").append("C").append("\", ");
            }
            
            if(prodType.getProductClass().equals("LIFE")){
              buf.append("\"insuranceType\"").append(" : \"").append("L").append("\", ");
            }

            buf.append("\"coverageType\"").append(" : \"").append(JsonUtil.cleanStringForJson(prodType.getTermOrPerm())).append("\" ");
            isFirst=false;
          }
        
        buf.append(" } ] "); 
        
        
        buf.append(" }  "); 
        buf.append(" }, "); 

  
        /////////////   FRENCH  start //////////////////////
        
        buf.append("\"fr\"").append(" : ").append("{ ");      
       
        isFirst = true;
        tmp = "";
        same = false;
        putABrack = false;
        // count of options set in - getTypeIntId
        // count of options set in - getTypeIntId
        for(Types type: types) {
            
          if(type.getAcordName().equals(tmp))
            same= true;
          
          else{
            if(!isFirst){
              buf.append(" } ] }, ");  
              putABrack = false;
            }
            isFirst=true;
            same= false;
            tmp = type.getAcordName();
          }

          if(type.getCategory().equalsIgnoreCase("label")){
            if(type.getTypeIntId() == 0){
              buf.append("\"").append(JsonUtil.cleanStringForJson(type.getAcordName())).append("\" : \"").append(JsonUtil.cleanStringForJson(type.getDescFr())).append("\", ");
            }
            else{  
              buf.append("\"").append(JsonUtil.cleanStringForJson(type.getAcordName())).append("\" : ").append(" { ");    
              buf.append("\"label\"").append(" : \"").append(JsonUtil.cleanStringForJson(type.getDescFr())).append("\", ");
              buf.append("\"options\"").append(" : [").append("{");      
            }
          }
          else{
            if(!isFirst)
                buf.append(" }, {");
            buf.append("\"value\"").append(" : \"").append(type.getOldSystemCode()).append("\", ");
            buf.append("\"label\"").append(" : \"").append(JsonUtil.cleanStringForJson(type.getDescFr())).append("\" ");
            isFirst=false;
          }
          
        }
        
        if(!isFirst){
          buf.append(" } ] "); 
          buf.append(" }, "); 
        }

        buf.append("\"").append("title").append("\" : \"").append(JsonUtil.cleanStringForJson(aboutMe.getAboutMeFrench())).append("\", ");

        isFirst = true;
        tmp = "";
        same = false;
        putABrack = false;
        
        for(Types type: titleTypes) {
            buf.append("\"").append(JsonUtil.cleanStringForJson(type.getAcordName())).append("\" : \"").append(JsonUtil.cleanStringForJson(type.getDescFr())).append("\", ");
            buf.append("\"").append(JsonUtil.cleanStringForJson(type.getCategory())).append("\" : \"").append(JsonUtil.cleanStringForJson(type.getDefinitionFr())).append("\", ");           
        }


        buf.append("\"defaultProvinceValue\"").append(" : \"").append(JsonUtil.cleanStringForJson(defaultProv.getProvinceCode())).append("\", ");
        buf.append("\"defaultProvinceLabel\"").append(" : \"").append(JsonUtil.cleanStringForJson(defaultProv.getNameFr())).append("\", ");

        
        buf.append("\"datePicker\"").append(" : ").append("{ ");      
        buf.append("\"title\"").append(" : \"").append("Choisir la date").append("\", ");

        buf.append("\"monthsFull\"").append(" : [ ");
        buf.append("\"janvier\"").append(", ");
        buf.append("\"février\"").append(", ");
        buf.append("\"mars\"").append(", ");
        buf.append("\"avril\"").append(", ");
        buf.append("\"mai\"").append(", ");
        buf.append("\"juin\"").append(", ");
        buf.append("\"juillet\"").append(", ");
        buf.append("\"août\"").append(", ");
        buf.append("\"septembre\"").append(", ");
        buf.append("\"octobre\"").append(", ");
        buf.append("\"novembre\"").append(", ");
        buf.append("\"décembre\"");
        buf.append(" ], "); 
        
        buf.append("\"monthsShort\"").append(" : [ ");
        buf.append("\"janv.\"").append(", ");
        buf.append("\"févr.\"").append(", ");
        buf.append("\"mars\"").append(", ");
        buf.append("\"avr.\"").append(", ");
        buf.append("\"mai\"").append(", ");
        buf.append("\"juin\"").append(", ");
        buf.append("\"juil.\"").append(", ");
        buf.append("\"août\"").append(", ");
        buf.append("\"sept.\"").append(", ");
        buf.append("\"oct.\"").append(", ");
        buf.append("\"nov.\"").append(", ");
        buf.append("\"déc.\"");
        buf.append(" ], "); 

        buf.append("\"weekdaysFull\"").append(" : [ ");
        buf.append("\"dimanche\"").append(", ");
        buf.append("\"lundi\"").append(", ");
        buf.append("\"mardi\"").append(", ");
        buf.append("\"mercredi\"").append(", ");
        buf.append("\"jeudi\"").append(", ");
        buf.append("\"vendredi\"").append(", ");
        buf.append("\"samedi\"");
        buf.append(" ], "); 

        buf.append("\"weekdaysShort\"").append(" : [ ");
        buf.append("\"dim\"").append(", ");
        buf.append("\"lun\"").append(", ");
        buf.append("\"mar\"").append(", ");
        buf.append("\"mer\"").append(", ");
        buf.append("\"jeu\"").append(", ");
        buf.append("\"ven\"").append(", ");
        buf.append("\"sam\"");
        buf.append(" ], "); 

        buf.append("\"weekdaysNarrow\"").append(" : [ ");
        buf.append("\"d\"").append(", ");
        buf.append("\"l\"").append(", ");
        buf.append("\"m\"").append(", ");
        buf.append("\"m\"").append(", ");
        buf.append("\"j\"").append(", ");
        buf.append("\"v\"").append(", ");
        buf.append("\"s\"");
        buf.append(" ], "); 
        
        buf.append("\"okBtnText\"").append(" : \"").append("Ok").append("\", ");
        buf.append("\"clearBtnText\"").append(" : \"").append("réinitialiser").append("\", ");
        buf.append("\"cancelBtnText\"").append(" : \"").append("Annuler").append("\" ");
        buf.append(" }, "); 
 
        
        
        buf.append("\"province\"").append(" : ").append("{ ");    
        buf.append("\"label\"").append(" : \"").append("Province/territoire de résidence actuel").append("\", ");
        buf.append("\"options\"").append(" : [").append("{");      

        isFirst = true;
        for(Province prov: provs) {
            if(!isFirst)
              buf.append(" }, {");
            buf.append("\"value\"").append(" : \"").append(prov.getProvinceCode()).append("\", ");
            buf.append("\"label\"").append(" : \"").append(JsonUtil.cleanStringForJson(prov.getNameFr())).append("\" ");
            isFirst=false;
        }
      
        
        buf.append(" } ] "); 
        buf.append(" }, "); 

        
        buf.append("\"product\"").append(" : ").append("{ ");
        buf.append("\"label\"").append(" : \"").append("Produit").append("\", ");
        buf.append("\"options\"").append(" : ").append(" [ {");      

        isFirst = true;
        for(IMProduct prod: products) {
            if(!isFirst)
              buf.append(" }, {");

            buf.append("\"label\"").append(" : \"").append(JsonUtil.cleanStringForJson(prod.getFrench_name())).append("\", ");
            buf.append("\"value\"").append(" : \"").append(prod.getProductid()).append("\", ");
            buf.append("\"company\"").append(" : \"").append(prod.getCompanyid()).append("\", ");
          
            if(prod.getProductclass().equals("CRIT")){
              buf.append("\"insuranceType\"").append(" : \"").append("C").append("\", ");
            }
            
            if(prod.getProductclass().equals("LIFE")){
              buf.append("\"insuranceType\"").append(" : \"").append("L").append("\", ");
            }
            
            buf.append("\"productDesc\"").append(" : \"").append(JsonUtil.cleanStringForJson(prod.getShort_french_desc())).append("\", ");
    
            buf.append("\"maleLowAge\"").append(" : \"").append(prod.getIm_lifeProductDetail().getMLOW_ISSUE_AGE()).append("\", ");
            buf.append("\"maleHighAge\"").append(" : \"").append(prod.getIm_lifeProductDetail().getMHIGH_ISSUE_AGE()).append("\", ");
            buf.append("\"femaleLowAge\"").append(" : \"").append(prod.getIm_lifeProductDetail().getFLOW_ISSUE_AGE()).append("\", ");
            buf.append("\"femaleHighAge\"").append(" : \"").append(prod.getIm_lifeProductDetail().getFHIGH_ISSUE_AGE()).append("\", ");

            buf.append("\"maleLowAgeSmoker\"").append(" : \"").append(prod.getIm_lifeProductDetail().getMLOW_ISSUE_AGE_SMOKER()).append("\", ");
            buf.append("\"maleHighAgeSmoker\"").append(" : \"").append(prod.getIm_lifeProductDetail().getMHIGH_ISSUE_AGE_SMOKER()).append("\", ");
            buf.append("\"femaleLowAgeSmoker\"").append(" : \"").append(prod.getIm_lifeProductDetail().getFLOW_ISSUE_AGE_SMOKER()).append("\", ");
            buf.append("\"femaleHighAgeSmoker\"").append(" : \"").append(prod.getIm_lifeProductDetail().getFHIGH_ISSUE_AGE_SMOKER()).append("\", ");

            DecimalFormat df2 = new DecimalFormat("#########.00");
            String minFace = df2.format(prod.getIm_lifeProductDetail().getMIN_BUS_FACE_AMOUNT());
            String maxFace = df2.format(prod.getIm_lifeProductDetail().getMAX_BUS_FACE_AMOUNT());
            
            buf.append("\"minimumFaceAmount\"").append(" : \"").append(minFace).append("\", ");
            buf.append("\"maximumFaceAmount\"").append(" : \"").append(maxFace).append("\", ");

            int sfl = prod.getIm_lifeProductDetail().getSING_JO1_JOLAST();
            
            boolean hasSing = false;
            boolean hasFirst = false;
            boolean hasLast = false;
                       
            if(sfl ==1 || sfl == 3 || sfl == 5 || sfl == 7)
              hasSing = true; //"S"
            
            if(sfl ==2 || sfl == 3 || sfl == 6 || sfl == 7)
              hasFirst = true;//"F"; 
            
            if(sfl ==4 || sfl == 5 || sfl == 6 || sfl == 7)
              hasLast = true; //"L"; 

            buf.append("\"jointModes\"").append(" : ").append(" [");      
            boolean addCom = false;
            if(hasSing){
              buf.append("\"").append("S").append("\" ");                 
              addCom = true;
            }
            if(hasFirst){
              if(addCom)
                buf.append(", ");  
              buf.append("\"").append("F").append("\" ");                 
              addCom = true;
            }

            if(hasLast){
              if(addCom)
                buf.append(", ");  
              buf.append("\"").append("L").append("\" ");                 
            }
          
            buf.append("] "); 
            
            isFirst=false;
        }
        
        buf.append(" } ] "); 
        
        
        buf.append(" },  "); 

        
        buf.append("\"productTypes\"").append(" : ").append("{ ");
        buf.append("\"label\"").append(" : \"").append("Type de Produit").append("\", ");
        buf.append("\"options\"").append(" : ").append(" [ {");      
        isFirst = true;
        for(ProductType prodType: productTypes) {
            if(!isFirst)
              buf.append(" }, {");
          
            buf.append("\"value\"").append(" : \"").append(prodType.getID()).append("\", ");
            buf.append("\"label\"").append(" : \"").append(JsonUtil.cleanStringForJson(prodType.getNameFr())).append("\", ");
            
            if(prodType.getProductClass().equals("BOTH")){
              buf.append("\"insuranceType\"").append(" : \"").append("B").append("\", ");
            }

            if(prodType.getProductClass().equals("CRIT")){
              buf.append("\"insuranceType\"").append(" : \"").append("C").append("\", ");
            }
            
            if(prodType.getProductClass().equals("LIFE")){
              buf.append("\"insuranceType\"").append(" : \"").append("L").append("\", ");
            }

            buf.append("\"coverageType\"").append(" : \"").append(JsonUtil.cleanStringForJson(prodType.getTermOrPerm())).append("\" ");
            isFirst=false;
        }
        
        buf.append(" } ] "); 
        
        buf.append(" } "); 
        buf.append(" }");       

       
        buf.append(" }");

        return buf.toString();
    }
    /*
    we need from IM_PRODUCTTYPE the field GUARANTEED = N, please output No or Non and if Y, output Yes or Oui

from 
IM_COMPANYPRODUCT the field ACTIVE_DATE
    */
  static String getResponseValuesJoint(
        int rank,
        String province,
        String monthlyBaseSumPremium,
        String annualBaseSumPremium,
        String percentage,
        String convertibleAge,
        String payableAge,
        String healthClass1,
        String healthClassFr1,
        String healthClass2,
        String healthClassFr2,
        String clientUW1,
        String clientUWFr1,
        String clientUW2,
        String clientUWFr2,
        String clientDOB1,
        String clientDOB2,
        int clientNearest1,
        int clientActual1,
        int clientNearest2,
        int clientActual2,
        String smoking1,
        String smoking2,
        String faceAmount,
        Product productRates,
        char clientGen1,
        char clientGen2,
        String classFlag1,
        String lowMonths1,
        boolean isCigarretSmoker1,
        boolean isFamilyHistory1,
        String classFlag2,
        String lowMonths2,
        boolean isCigarretSmoker2,
        boolean isFamilyHistory2,
        String coverageType
    ) {
        StringBuilder buf = new StringBuilder(" ");

        buf.append(" { "); //open response

        buf.append("\"info\"").append(" : ").append("{ ");         
        buf.append("\"carrierId\"").append(" : \"").append(productRates.getCompanyID()).append("\", ");
        buf.append("\"productId\"").append(" : \"").append(productRates.getID()).append("\", ");
        buf.append("\"productCode\"").append(" : \"").append(productRates.getProductType().getID()).append("\", ");
        buf.append("\"productType\"").append(" : \"").append("LIFE").append("\", ");
        buf.append("\"productSubType\"").append(" : \"").append(productRates.getProductType().getTermOrPerm()).append("\", ");

        buf.append("\"masterDescProductId\"").append(" : \"").append(productRates.getMasterDescProductID()).append("\", ");
     //   master_desc_productid
        
        buf.append("\"rank\"").append(" : \"").append(rank).append("\", ");
        
        String guar = "N";
        if(productRates.isGuaranteed())
            guar="Y";
        buf.append("\"guaranteed\"").append(" : \"").append(guar).append("\", ");

        SimpleDateFormat formatter = new SimpleDateFormat("dd-MMM-yyyy");
        String actDte = "";
        if(productRates.getActiveDate() != null)
          actDte = formatter.format(productRates.getActiveDate());
        buf.append("\"activeDate\"").append(" : \"").append(actDte).append("\", ");

        
        buf.append("\"productGroupId\"").append(" : \"").append(productRates.getProductType().getMasterID()).append("\", ");
        buf.append("\"productTypeLabel\"").append(" : \"").append(JsonUtil.cleanStringForJson(productRates.getProductTypeLabel())).append("\" ");
        
                
        buf.append(" }, ");   //close info:{      
       
        buf.append("\"quoteSpecs\"").append(" : ").append("{ ");      
        buf.append("\"province\"").append(" : \"").append(JsonUtil.cleanStringForJson(province)).append("\", ");
        buf.append("\"faceAmount\"").append(" : \"").append(JsonUtil.cleanStringForJson(faceAmount)).append("\", ");
        
        buf.append("\"coverageType\"").append(" : \"").append(JsonUtil.cleanStringForJson(coverageType)).append("\", ");//Level or Decreasing added sept 20, 2021 aa
        
        buf.append("\"client1\"").append(" : ").append("{ ");      
        buf.append("\"gender\"").append(" : \"").append(clientGen1).append("\", ");
        buf.append("\"DOB\"").append(" : \"").append(JsonUtil.cleanStringForJson(clientDOB1)).append("\", ");
        buf.append("\"ActualAge\"").append(" : \"").append(clientActual1).append("\", ");
        buf.append("\"NearestAge\"").append(" : \"").append(clientNearest1).append("\", ");
        
        buf.append("\"healthClass\"").append(" : \"").append(JsonUtil.cleanStringForJson(healthClass1)).append("\", ");
        buf.append("\"healthClassFr\"").append(" : \"").append(JsonUtil.cleanStringForJson(healthClassFr1)).append("\", ");

        buf.append("\"classFlag\"").append(" : \"").append(JsonUtil.cleanStringForJson(classFlag1)).append("\", ");//NEW!!  apr 13 2021 -aa

        buf.append("\"smoking\"").append(" : \"").append(JsonUtil.cleanStringForJson(smoking1)).append("\", ");

        buf.append("\"lowMonths\"").append(" : \"").append(JsonUtil.cleanStringForJson(lowMonths1+"")).append("\", ");

        String cigs1 = "N"; 
        if(isCigarretSmoker1){
          cigs1 = "Y";    
        }
        
        buf.append("\"cigarettes\"").append(" : \"").append(JsonUtil.cleanStringForJson(cigs1+"")).append("\", ");//NEW!!  jan 13 2021 -aa
        
        String famHist1 = "N";  //Family History  -- added jan 13 2021 -aa   
        if(isFamilyHistory1){
          famHist1 = "Y";    
        }
        
        buf.append("\"familyHistory\"").append(" : \"").append(JsonUtil.cleanStringForJson(famHist1+"")).append("\", ");//NEW!!  jan 13 2021 -aa
        
        buf.append("\"underwriting\"").append(" : \"").append(JsonUtil.cleanStringForJson(clientUW1)).append("\", ");
        buf.append("\"underwritingFR\"").append(" : \"").append(JsonUtil.cleanStringForJson(clientUWFr1)).append("\" ");
        buf.append(" }, ");    //close client1:{     
        
        buf.append("\"client2\"").append(" : ").append("{ ");      
        buf.append("\"gender\"").append(" : \"").append(clientGen2).append("\", "); 
        buf.append("\"DOB\"").append(" : \"").append(JsonUtil.cleanStringForJson(clientDOB2)).append("\", ");
        buf.append("\"ActualAge\"").append(" : \"").append(clientActual2).append("\", ");
        buf.append("\"NearestAge\"").append(" : \"").append(clientNearest2).append("\", ");

        buf.append("\"healthClass\"").append(" : \"").append(JsonUtil.cleanStringForJson(healthClass2)).append("\", ");
        buf.append("\"healthClassFr\"").append(" : \"").append(JsonUtil.cleanStringForJson(healthClassFr2)).append("\", ");

        
        buf.append("\"classFlag\"").append(" : \"").append(JsonUtil.cleanStringForJson(classFlag2)).append("\", ");//NEW!!  apr 13 2021 -aa
        buf.append("\"smoking\"").append(" : \"").append(JsonUtil.cleanStringForJson(smoking2)).append("\", ");
        buf.append("\"lowMonths\"").append(" : \"").append(JsonUtil.cleanStringForJson(lowMonths2+"")).append("\", ");

        String cigs2 = "N";
        if(isCigarretSmoker2){
          cigs2 = "Y";    
        }

        buf.append("\"cigarettes\"").append(" : \"").append(JsonUtil.cleanStringForJson(cigs2+"")).append("\", ");//NEW!!  jan 13 2021 -aa
        
        String famHist2 = "N";  //Family History  -- added apr 13 2021 -aa   
        if(isFamilyHistory2){
          famHist2 = "Y";    
        }
        
        buf.append("\"familyHistory\"").append(" : \"").append(JsonUtil.cleanStringForJson(famHist2+"")).append("\", ");//NEW!!  jan 13 2021 -aa
        
        
        buf.append("\"underwriting\"").append(" : \"").append(JsonUtil.cleanStringForJson(clientUW2)).append("\", ");
        buf.append("\"underwritingFR\"").append(" : \"").append(JsonUtil.cleanStringForJson(clientUWFr2)).append("\" ");
        buf.append(" } ");    //close client1:{     

        buf.append(" }, ");    //close quoteSpecs:{     
        
        buf.append("\"quotes\"").append(" : ").append("{ ");      
        buf.append("\"monthlyPremium\"").append(" : \"").append(JsonUtil.cleanStringForJson(monthlyBaseSumPremium)).append("\", ");
        buf.append("\"yearlyPremium\"").append(" : \"").append(JsonUtil.cleanStringForJson(annualBaseSumPremium)).append("\", ");
        buf.append("\"percentage\"").append(" : \"").append(JsonUtil.cleanStringForJson(percentage)).append("\", ");
        
        buf.append("\"premiumRenewals\"").append(" : ").append(" [ {");      
        boolean isFirst = true;

       // int payAge = Integer.parseInt(payableAge);
        for (PremiumRenewal pr : productRates.getPremiumRenewals()) {
           if(!isFirst)
              buf.append(" }, {");

           buf.append("\"startAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(pr.getYear()+"")).append("\", ");
          buf.append("\"endAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(pr.getYearEnd()+"")).append("\", ");
          buf.append("\"monthlyPremium\"").append(" : \"").append(JsonUtil.cleanStringForJson(Util.getStringFromDouble(pr.getTotalMonthly()))).append("\", ");
          buf.append("\"annualPremium\"").append(" : \"").append(JsonUtil.cleanStringForJson(Util.getStringFromDouble(pr.getTotalAnnual()))).append("\" ");
          isFirst = false;
        }

        buf.append(" } ] "); //close premiumRenewals:{             
        buf.append(" }, ");  //close quotes:{           
        
        buf.append("\"details\"").append(" : ").append("{ ");      
        buf.append("\"en-us\"").append(" : ").append("{ ");      
        buf.append("\"product\"").append(" : ").append("{ ");  
        buf.append("\"companyName\"").append(" : \"").append(JsonUtil.cleanStringForJson(productRates.getCompany().getName())).append("\", ");
        buf.append("\"productName\"").append(" : \"").append(JsonUtil.cleanStringForJson(productRates.getName())).append("\", ");
        buf.append("\"description\"").append(" : \"").append(JsonUtil.cleanStringForJson(productRates.getDescription())).append("\", ");
        buf.append("\"productGroup\"").append(" : \"").append(productRates.getProductType().getName()).append("\" ");
        
        buf.append(" }, ");   //close product:{          
        
//System.out.println(" line 173 JsonGenerator ");        
        buf.append("\"features\"").append(" : ").append("{ ");      
        buf.append("\"convertibleAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(convertibleAge)).append("\", ");
        buf.append("\"payableAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(payableAge)).append("\", ");
        buf.append("\"payableYears\"").append(" : \"").append(productRates.getYearsPayable()).append("\" ");
        buf.append(" }  "); //close features:{               
        buf.append(" }, "); //close en-us:{       
        

        buf.append("\"fr\"").append(" : ").append("{ ");      
        buf.append("\"product\"").append(" : ").append("{ ");      

        buf.append("\"companyName\"").append(" : \"").append(JsonUtil.cleanStringForJson(productRates.getCompany().getNameFr())).append("\", ");

        buf.append("\"productName\"").append(" : \"").append(JsonUtil.cleanStringForJson(productRates.getNameFr())).append("\", ");

        buf.append("\"description\"").append(" : \"").append(JsonUtil.cleanStringForJson(productRates.getDescriptionFr())).append("\", ");

        buf.append("\"productGroup\"").append(" : \"").append(productRates.getProductType().getName()).append("\" ");

        buf.append(" }, ");     //close product:{        
        buf.append("\"features\"").append(" : ").append("{ ");      
        buf.append("\"convertibleAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(convertibleAge)).append("\", ");
        buf.append("\"payableAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(payableAge)).append("\", ");
        buf.append("\"payableYears\"").append(" : \"").append(productRates.getYearsPayable()).append("\" ");
        buf.append(" } ");   //close features:{          


        buf.append(" }"); //close fr:{       
        buf.append(" }"); //close details:{       

        buf.append(" }"); //close response:{       
        return buf.toString();
    }
            

    
}
