/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2019 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THES
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.ins;
 
import com.insurfact.im.IMUWClass;
import com.insurfact.iq.domain.Band;
import com.insurfact.iq.domain.BandRate;
import com.insurfact.iq.domain.Client;
import com.insurfact.iq.domain.Company;
import com.insurfact.iq.domain.HealthClass;
import com.insurfact.iq.domain.MinMax;
import com.insurfact.iq.domain.Product;
import com.insurfact.iq.domain.ProductIllness;
import com.insurfact.iq.domain.QuoteParams;
import com.insurfact.iq.domain.Rider;
import com.insurfact.iq.domain.util.CoveredIllnessesComparator;
import com.insurfact.iq.domain.util.DateFunctions;
import com.insurfact.iq.domain.util.HighestCashvalue10thYearComparator;
import com.insurfact.iq.domain.util.HighestCashvalue20thYearComparator;
import com.insurfact.iq.domain.util.HighestCashvalueAge65Comparator;
import com.insurfact.iq.domain.util.HighestCashvalueAge75Comparator;
import com.insurfact.iq.domain.util.MinimumBandComparator;
import com.insurfact.iq.domain.util.ProductIllnessComparator;
import com.insurfact.iq.domain.util.TotalAnnComparator;
import com.insurfact.iq.domain.util.TotalMonComparator;
import com.insurfact.iq4.ejb.EnhancedProductFacade;
import com.insurfact.iq4.ejb.IQ4EngineFacade;
import com.insurfact.iq4.ejb.IQ4JointSEAUtilsFacade;
import com.insurfact.iq4.ejb.IQ4ProductFacade;
import com.insurfact.iq4.ejb.PromotionsHelper;
import com.insurfact.sdk.properties.InsurfactSdkProperties;
import com.insurfact.sdk.utils.InsurfactPrivateException;
import com.insurfact.sdk.utils.LogUtil;
import com.insurfact.skynet.entity.Contact;
import com.insurfact.skynet.entity.Promotions;
import com.insurfact.wsutil.CompanyProductComparator;
import com.insurfact.wsutil.IQ4Modes;
import com.insurfact.wsutil.IncludedRidersOrderComparator;
import com.insurfact.wsutil.JsonUtil;
import com.insurfact.wsutil.Util;
//import java.io.BufferedWriter;
///import java.io.File;
///import java.io.FileWriter;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;

//import java.util.logging.Logger;
import jakarta.ejb.EJB;
//import javax.ws.rs.core.Context;
//import javax.ws.rs.core.UriInfo;
//import websecuring.Securing;

/**
 * <AUTHOR>
 */
public class GenericJoint {

	private static final String TAG = GenericJoint.class.getName();

	private static boolean DEBUG = false;

	private static final boolean SEND_EMAIL_FOR_ANOMALIES = false;

	private QuoteParams multiQP;

	private Promotions promo;

	private IQ4Modes iq4Mode;

	boolean queried = false;

	@EJB
	final private IQ4EngineFacade quoteFacade;

	@EJB
	final private IQ4JointSEAUtilsFacade jointESAUtils;

	@EJB
	final private IQ4ProductFacade productFacade;

	@EJB
	final private EnhancedProductFacade enhancedProductFacade;

	@EJB
	final private PromotionsHelper promotionsHelper;
 

	public Connection con = null;

	public final String PRODUCT_CLASS;

	public final String OUTPUT_TYPE;

	private int selectionRiderId = 0;

	public GenericJoint(final String quoteType, final String outputType, final IQ4EngineFacade quoteFacadeGiven,
			final IQ4JointSEAUtilsFacade iq4JointSEAFacadeGiven, final IQ4ProductFacade productFacadeGiven,
			final EnhancedProductFacade enhancedProductFacadeGiven, final PromotionsHelper promotionsHelperGiven ) {

		PRODUCT_CLASS = quoteType;

		OUTPUT_TYPE = outputType;

		quoteFacade = quoteFacadeGiven;

		jointESAUtils = iq4JointSEAFacadeGiven;

		productFacade = productFacadeGiven;

		enhancedProductFacade = enhancedProductFacadeGiven;

		promotionsHelper = promotionsHelperGiven; 

		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.YEAR, 1); // Where n is int

		// createJWT(String id, String issuer, String subject, Date expireDate)
		// String key = Securing.createJWT("44439", "InsurFactConnectInc",
		// "WebServiceQuoting", cal.getTime());
		// System.out.println("line 102 Generic security key for Finaeo= "+key);
	}

	void startJoint(final String dob1, final String genderUp1, final String dob2, final String genderUp2,
			final String compID) throws InsurfactPrivateException {

		if (compID.equals("")) {
			iq4Mode = IQ4Modes.JointMultiCompany;
		} else {
			iq4Mode = IQ4Modes.JointSingleCompany;
		}

		if (multiQP == null) {
			multiQP = new QuoteParams();

			if (compID.equals("")) {
				multiQP.initJointMultiMode();
			} else {
				multiQP.initJointMode();
			}

			multiQP.setTermOrPerm(null);

			// com.insurfact.skynet.entity.Contact
			Contact firstContact = Util.create30YearsOldMaleContact();
			multiQP.setFirstContact(firstContact);

			DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
			df.getCalendar().setLenient(false);
			Date date;
			try {
				date = df.parse(dob1);
			} catch (ParseException ex) {
				final String s = TAG + ".startMulti: *df.parse(dob)* with dobFormat {} raised: " + ex;
				if (DEBUG) {
					System.out.println(s); // Logger.getLogger(Life.class.getName()).log(Level.SEVERE, s, ex);
				}
				throw new InsurfactPrivateException(s, ex);
			}

			if (DEBUG) {
				final String s = TAG + ".startMulti: *df.parse(dob {" + dob1 + "})* with dobFormat {}: " + date;
				System.out.println(s);
			}

			firstContact.setBirthDate(date);

			// com.insurfact.iq.domain.Client client = multiQP.getClient();
			Client client1 = multiQP.getClient();

			client1.setFullBirthDate(date); // TODO washere use the dob from http url

			SimpleDateFormat formatDD = new SimpleDateFormat("dd");
			SimpleDateFormat formatMM = new SimpleDateFormat("MM");
			SimpleDateFormat formatYYYY = new SimpleDateFormat("yyyy");
			int bd_day = Integer.parseInt(formatDD.format(client1.getFullBirthDate()));
			int bd_month = Integer.parseInt(formatMM.format(client1.getFullBirthDate()));
			int bd_year = Integer.parseInt(formatYYYY.format(client1.getFullBirthDate()));

			client1.setActualAge(DateFunctions.calcActualAge(bd_month - 1, bd_day, bd_year));
			client1.setNearestAge(DateFunctions.calcNearestAge(bd_month - 1, bd_day, bd_year));

			client1.setGender(genderUp1.charAt(0)); // firstContact.getGender() == 1 ? 'M' : 'F');
			multiQP.setClient(client1);

			try {
				date = df.parse(dob2);
			} catch (ParseException ex) {
				final String s = TAG + ".startMulti: *df.parse(dob)* with dobFormat {} raised: " + ex;
				if (DEBUG) {
					System.out.println(s); // Logger.getLogger(Life.class.getName()).log(Level.SEVERE, s, ex);
				}
				throw new InsurfactPrivateException(s, ex);
			}

			if (DEBUG) {
				final String s = TAG + ".startMulti: *df.parse(dob {" + dob2 + "})* with dobFormat {}: " + date;
				System.out.println(s);
			}

			// com.insurfact.iq.domain.Client client = multiQP.getClient();
			Client client2 = multiQP.getClient2();

			client2.setFullBirthDate(date); // TODO washere use the dob from http url

			int bd_day2 = Integer.parseInt(formatDD.format(client2.getFullBirthDate()));
			int bd_month2 = Integer.parseInt(formatMM.format(client2.getFullBirthDate()));
			int bd_year2 = Integer.parseInt(formatYYYY.format(client2.getFullBirthDate()));

			client2.setActualAge(DateFunctions.calcActualAge(bd_month2 - 1, bd_day2, bd_year2));
			client2.setNearestAge(DateFunctions.calcNearestAge(bd_month2 - 1, bd_day2, bd_year2));

			client2.setGender(genderUp2.charAt(0)); // firstContact.getGender() == 1 ? 'M' : 'F');

			multiQP.setClient2(client2);

			setMultiQP(multiQP);

//            initializeIQ4();
//            updateAllProductTypes();
		}
	}

	void startMultiSingle(final String dob, final String genderUp, final String carrierID)
			throws InsurfactPrivateException {

		iq4Mode = IQ4Modes.MultiCompany;

		if (multiQP == null) {
			multiQP = new QuoteParams();

			multiQP.initMultiCompanyMode();

			multiQP.setTermOrPerm(null);

			// com.insurfact.skynet.entity.Contact
			Contact firstContact = Util.create30YearsOldMaleContact();
			multiQP.setFirstContact(firstContact);

			multiQP.setCompanyID(Integer.parseInt(carrierID));

			DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
			df.getCalendar().setLenient(false);
			Date date;
			try {
				date = df.parse(dob);
			} catch (ParseException ex) {
				final String s = TAG + ".startMulti: *df.parse(dob)* with dobFormat {} raised: " + ex;
				if (DEBUG) {
					System.out.println(s); // Logger.getLogger(Life.class.getName()).log(Level.SEVERE, s, ex);
				}
				throw new InsurfactPrivateException(s, ex);
			}

			if (DEBUG) {
				final String s = TAG + ".startMulti: *df.parse(dob {" + dob + "})* with dobFormat {}: " + date;
				System.out.println(s);
			}

			firstContact.setBirthDate(date);

			// com.insurfact.iq.domain.Client client = multiQP.getClient();
			Client client = multiQP.getClient();

			client.setFullBirthDate(date); // TODO washere use the dob from http url
			SimpleDateFormat formatDD = new SimpleDateFormat("dd");
			SimpleDateFormat formatMM = new SimpleDateFormat("MM");
			SimpleDateFormat formatYYYY = new SimpleDateFormat("yyyy");
			int bd_day = Integer.parseInt(formatDD.format(client.getFullBirthDate()));
			int bd_month = Integer.parseInt(formatMM.format(client.getFullBirthDate()));
			int bd_year = Integer.parseInt(formatYYYY.format(client.getFullBirthDate()));

			client.setActualAge(DateFunctions.calcActualAge(bd_month - 1, bd_day, bd_year));
			client.setNearestAge(DateFunctions.calcNearestAge(bd_month - 1, bd_day, bd_year));

			client.setGender(genderUp.charAt(0)); // firstContact.getGender() == 1 ? 'M' : 'F');

			multiQP.setClient(client);

			setMultiQP(multiQP);

//            initializeIQ4();
//            updateAllProductTypes();
		}
	}

	@SuppressWarnings("unused")
	private List<Product> getProductsOneType() throws InsurfactPrivateException {
		List<Product> products = null;

		try {
			// ===============================================================
//System.out.println("270 GenericJoint getMultiQP().getProductTypeID()="+getMultiQP().getProductTypeID());
			products = quoteFacade.getAvailableJointProducts(getMultiQP(), 0);
			// products = quoteFacade.multiProductQuote(getMultiQP(), null);
			// ===============================================================
		} catch (Throwable e) {
			StackTraceElement[] stack = e.getStackTrace();
			StringBuilder buf = new StringBuilder();
			for (StackTraceElement st : stack) {
				buf.append("\n").append(st);
			}
			String s = "*product = quoteFacade.multiProductQuote(multiQP, null)* raised {" + e + "} cause: "
					+ e.getCause() + buf;
			if (DEBUG) {
				System.out.println(TAG + ".getProductsOneType: " + s);
			}
			throw new InsurfactPrivateException(s, e);
		}

		return products;
	}

	private List<Product> getProductsCombosNew() throws InsurfactPrivateException {

		List<Product> products = new LinkedList<>();

		multiQP = getMultiQP();

		int[] types = { 9, 10, 6, 22, 15, 38, 16 }; // LIFE

		try {
			int i = 0;
			while (i < 7) {
				multiQP.setProductTypeID(types[i]);
				// =====================================================
				List<Product> products2 = getProductsJoint();
				if (products2.size() != 0) {
					products.addAll(products2);
				}

				i++;
			}
		} catch (InsurfactPrivateException e) {
			throw e;
		} catch (Throwable e) {
			StackTraceElement[] stack = e.getStackTrace();
			StringBuilder buf = new StringBuilder();
			for (StackTraceElement st : stack) {
				buf.append("\n").append(st);
			}
			String s = "*product = quoteFacade.multiProductQuote(multiQP, null)* raised {" + e + "} cause: "
					+ e.getCause() + buf;
			if (DEBUG) {
				System.out.println(TAG + ".getProductsCombos: " + s);
			}
			throw new InsurfactPrivateException(s, e);
		}

		multiQP.setProductTypeID(0);

		return products;
	}

	private List<Product> getProductsJoint() throws InsurfactPrivateException {

		List<Product> products = null;
		getMultiQP().setCompanyProducts(null);

		// disable Riders
		getMultiQP().setRiderSelected(false);

		try {
			// ===============================================================

			products = quoteFacade.getAvailableJointProducts(getMultiQP(), 18);

			// ===============================================================
		} catch (Throwable e) {
			StackTraceElement[] stack = e.getStackTrace();
			StringBuilder buf = new StringBuilder();
			for (StackTraceElement st : stack) {
				buf.append("\n").append(st);
			}
			String s = "*product = quoteFacade.multiProductQuote(multiQP, null)* raised {" + e + "} cause: "
					+ e.getCause() + buf;
			if (DEBUG) {
				System.out.println(TAG + ".getProductsOneType: " + s);
			}
			throw new InsurfactPrivateException(s, e);
		}

		return products;
	}

	/**
	 * <p>
	 * step 1: get largest set of applicable products from IQ4Engine; this is the
	 * given list from the caller.
	 *
	 * <p>
	 * step 2: get the most expensive set of products: with smokerCode N for
	 * non-smoker, or with smokerCode S for smoker; this is the list returned by
	 * this method.
	 *
	 * <p>
	 * step 3: get the lowest annualPremium (for E & P for non-smoker, and for 1 and
	 * P for smoker) for the entire list given by the caller.
	 *
	 * <p>
	 * step 4: go over the list from step 2 and calculate the gain:
	 * <p>
	 * ((annualPremium - lowestPremium) / annualPremium) * 100
	 *
	 *
	 * @param productsGiven
	 * @param multiQP
	 * @return List{Product}
	 */
	protected final List<Product> setPercentageGainsWithAgent(final List<Product> productsGiven,
			final QuoteParams multiQP) {

		List<Product> expensive = getExpensiveProducts(productsGiven, multiQP);

		if (DEBUG) {
			System.out.println(TAG + ".setPercentageGainsWithAgent: " + "*getExpensiveProducts(productsGiven,multiQP)* "
					+ "returned expensive.size {" + expensive.size() + "}");
		}

		BigDecimal lowestAnnualPremium = getLowestPremium(productsGiven, multiQP);

		if (DEBUG) {
			System.out.println(TAG + ".setPercentageGainsWithAgent: " + "*getLowestPremium(productsGiven,multiQP)* "
					+ "returned lowestAnnualPremium {" + lowestAnnualPremium + "}");
		}

		for (Product product : expensive) {

			// ((annualPremium - lowestPremium) / annualPremium) * 100
			product.setPercentageGainWithAgent(
					getPercentageGain(product.getAnnualBaseSumPremium(), lowestAnnualPremium));
		}

		return expensive;
	}

	protected final List<Product> getExpensiveProducts(final List<Product> productsGiven, final QuoteParams multiQP) {

		List<Product> expensiveProducts = new ArrayList<>();

		boolean smoker = multiQP.getClient().isSmoker();

		for (Product product : productsGiven) {

			List<HealthClass> hcs = product.getHealthClasses();

			if (DEBUG) {
				System.out.println(TAG + ".getExpensiveProducts: " + "*product.getHealthClasses()* size = "
						+ (hcs != null ? hcs.size() : "(hcs is null)"));
			}

			if (hcs != null && hcs.size() > 0) {

				for (HealthClass hc : hcs) {
					if (DEBUG) {
						System.out.println(TAG + ".getExpensiveProducts: " + "*hc.getSmokeCode()* {" + hc.getSmokeCode()
								+ "} *hc.getName()* {" + hc.getName() + "}");
					}
					if (smoker) {
						expensiveProducts.add(product);

					} else {

						expensiveProducts.add(product);

					}
				}
			} else {
				if (DEBUG) {
					System.out.println(TAG + ".getExpensiveProducts: " + "*product.getHealthClasses()* is empty");
				}

				HealthClass hc = product.getHealthClass();

				if (DEBUG) {
					System.out.println(TAG + ".getExpensiveProducts: " + "*product.getHealthClass()*: "
							+ "*hc.getSmokeCode()* {" + hc.getSmokeCode() + "} *hc.getName()* {" + hc.getName() + "}");
				}
				if (smoker) {

					expensiveProducts.add(product);

				} else {
					expensiveProducts.add(product);
				}
			}
		}

		if (expensiveProducts.isEmpty()) {
			if (DEBUG) {
				System.out.println(
						TAG + ".getExpensiveProducts: " + "returning productsGiven because expensiveProducts is empty");
			}
			return productsGiven;
		}

		return expensiveProducts;
	}

	/**
	 *
	 * @param productsGiven
	 * @param multiQP
	 * @return BigDecimal lowest annual premium from the given list of products
	 */
	protected final BigDecimal getLowestPremium(final List<Product> productsGiven, final QuoteParams multiQP) {

		double lowest = Double.MAX_VALUE;

		for (Product product : productsGiven) {

			double candidate = product.getAnnualBaseSumPremium();

			if (candidate < lowest) {
				lowest = candidate;
			}
		}

		return BigDecimal.valueOf(lowest);
	}

	/**
	 * ((annualPremium - lowestPremium) / annualPremium) * 100
	 *
	 * @param productPremium
	 * @param lowest
	 * @return ((annualPremium - lowestPremium) / annualPremium) * 100
	 */
	protected BigDecimal getPercentageGain(double productPremium, BigDecimal lowest) {

		BigDecimal premium = BigDecimal.valueOf(productPremium).setScale(5, RoundingMode.HALF_UP);

		BigDecimal diff = premium.subtract(lowest).setScale(5, RoundingMode.HALF_UP);

		return diff.divide(premium, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
	}

	protected final void prepareJointQuoteParams(final int productTypeInt, final int faceAmountInt,
			final String provCodeUp, final String dob1, final String genderUp1, final String smokingUp1,
			final String dob2, final String genderUp2, final String smokingUp2, final String jointTypeUp,
			final String compID, final String newHealthClass1, final String newHealthClass2, final String levelOrDecr)
			throws InsurfactPrivateException {

		startJoint(dob1, genderUp1, dob2, genderUp2, compID); // sets the contact and client
																// com.insurfact.iq.domain.Client

		multiQP.setProductTypeID(productTypeInt);// 6); //TODO ????????????????????????????????

		multiQP.setJointType(jointTypeUp);

		List<Integer> filter = new ArrayList<>();
		filter.add(999);
		multiQP.setCompanyFilter(filter);

		if (compID != null) {
			if (!compID.equals("")) {
				multiQP.setCompanyID(Integer.parseInt(compID));
			}
		}

		Object firstContactObject = multiQP.getFirstContact();

		// com.insurfact.skynet.entity.Contact
		Contact firstContact = null; // TODO confirm ########## needed???

		if (firstContactObject != null && firstContactObject instanceof Contact) {
			firstContact = (Contact) firstContactObject;
			if (DEBUG) {
				System.out.println(TAG + ".prepareQuoteParams: *multiQP.getFirstContact()* returned firstContact = {"
						+ firstContact + "}");
			}
		} else {
			if (DEBUG) {
				System.out.println(
						TAG + ".prepareQuoteParams: *multiQP.getFirstContact()* returned firstContactObject = {"
								+ firstContactObject + "}");
			}

			// TODO washere maybe create Contact in multiQP ???
			throw new InsurfactPrivateException("firstContactObject is null");
		}

		// here we have a Contact in multiQP
		if (DEBUG) {
			System.out.println(TAG + ".prepareQuoteParams: " + "getContactInfo(firstContact) #1 = "
					+ Util.getContactInfo(firstContact));
		}

		// smoking client.isNonSmoker()
		multiQP.getClient().setNonSmoker(!"Y".equals(smokingUp1));

		// com.insurfact.iq.domain.Client
		multiQP.getClient().setProvinceID(Util.getProvinceCodeInt(provCodeUp));

		multiQP.getClient().setClassId(0);// TODO washere ??????????????????

		if (!newHealthClass1.equals("")) {
			multiQP.getClient().setHealthClass(newHealthClass1);
		}

		// smoking client.isNonSmoker()
		multiQP.getClient2().setNonSmoker(!"Y".equals(smokingUp2));

		// com.insurfact.iq.domain.Client
		multiQP.getClient2().setProvinceID(Util.getProvinceCodeInt(provCodeUp));

		multiQP.getClient2().setClassId(0);// TODO washere ??????????????????

		if (!newHealthClass2.equals("")) {
			multiQP.getClient2().setHealthClass(newHealthClass2);
		}

		multiQP.setIssueProvinceID(Util.getProvinceCodeInt(provCodeUp));

		// if(DEBUG) System.out.println(TAG+".prepareQuoteParams:
		// *faceAmountBig.intValue()* returned {"+faceAmountBig.intValue()+"}");
		multiQP.setOriginalFaceAmount(faceAmountInt);

		multiQP.setFaceAmt(faceAmountInt);

		multiQP.setInsuranceTypeAbbr(PRODUCT_CLASS); // or CRIT for another web service

		// from QuoteWizard.goPage2():
		multiQP.setUsingMinReqPremiumFaceAmt(false);

		multiQP.setSelectedRider(null);

		multiQP.setWebService(true);
		multiQP.setNameWS("WIDGET");

		if (!levelOrDecr.equals("")) {
			if (!levelOrDecr.equalsIgnoreCase("A")) {
				if (levelOrDecr.equalsIgnoreCase("L")) {
					multiQP.setInsuranceBen(2);//// 2 or L=level 6 or D=decreasing
				} else {
					multiQP.setInsuranceBen(6);
				}
			}
		}

		multiQP.setDebug(DEBUG);

	}

	protected final void prepareJointQuoteParamsGroup(final int productTypeInt, final int faceAmountInt,
			final String provCodeUp, final String dob1, final String genderUp1, final String smokingUp1,
			final String dob2, final String genderUp2, final String smokingUp2, final String jointTypeUp,
			final String levelOrDecr, String compID) throws InsurfactPrivateException {

		startJoint(dob1, genderUp1, dob2, genderUp2, compID); // sets the contact and client
																// com.insurfact.iq.domain.Client

		multiQP.setProductTypeID(productTypeInt);// 6); //TODO ????????????????????????????????

		multiQP.setJointType(jointTypeUp);

		List<Integer> filter = new ArrayList<>();
		filter.add(999);
		multiQP.setCompanyFilter(filter);

		if (compID != null) {
			if (!compID.equals("")) {
				multiQP.setCompanyID(Integer.parseInt(compID));
			}
		}

		Object firstContactObject = multiQP.getFirstContact();

		// com.insurfact.skynet.entity.Contact
		Contact firstContact = null; // TODO confirm ########## needed???

		if (firstContactObject != null && firstContactObject instanceof Contact) {
			firstContact = (Contact) firstContactObject;
			if (DEBUG) {
				System.out.println(TAG + ".prepareQuoteParams: *multiQP.getFirstContact()* returned firstContact = {"
						+ firstContact + "}");
			}
		} else {
			if (DEBUG) {
				System.out.println(
						TAG + ".prepareQuoteParams: *multiQP.getFirstContact()* returned firstContactObject = {"
								+ firstContactObject + "}");
			}

			// TODO washere maybe create Contact in multiQP ???
			throw new InsurfactPrivateException("firstContactObject is null");
		}

		// here we have a Contact in multiQP
		if (DEBUG) {
			System.out.println(TAG + ".prepareQuoteParams: " + "getContactInfo(firstContact) #1 = "
					+ Util.getContactInfo(firstContact));
		}

		// smoking client.isNonSmoker()
		multiQP.getClient().setNonSmoker(!"Y".equals(smokingUp1));

		// com.insurfact.iq.domain.Client
		multiQP.getClient().setProvinceID(Util.getProvinceCodeInt(provCodeUp));

		multiQP.getClient().setClassId(0);// TODO washere ??????????????????

		// smoking client.isNonSmoker()
		multiQP.getClient2().setNonSmoker(!"Y".equals(smokingUp2));

		// com.insurfact.iq.domain.Client
		multiQP.getClient2().setProvinceID(Util.getProvinceCodeInt(provCodeUp));

		multiQP.getClient2().setClassId(0);// TODO washere ??????????????????

		multiQP.setIssueProvinceID(Util.getProvinceCodeInt(provCodeUp));

		// if(DEBUG) System.out.println(TAG+".prepareQuoteParams:
		// *faceAmountBig.intValue()* returned {"+faceAmountBig.intValue()+"}");
		multiQP.setOriginalFaceAmount(faceAmountInt);

		multiQP.setFaceAmt(faceAmountInt);

		multiQP.setInsuranceTypeAbbr(PRODUCT_CLASS); // or CRIT for another web service

		if (!levelOrDecr.equals("")) {
			if (!levelOrDecr.equalsIgnoreCase("A")) {
				if (levelOrDecr.equalsIgnoreCase("L")) {
					multiQP.setInsuranceBen(2);//// 2 or L=level 6 or D=decreasing
				} else {
					multiQP.setInsuranceBen(6);
				}
			}
		}

		multiQP.setUsingMinReqPremiumFaceAmt(false);

		multiQP.setSelectedRider(null);

		multiQP.setWebService(true);
		multiQP.setNameWS("WIDGET");

		multiQP.setDebug(DEBUG);

	}

	String getNoExceptionJoint(final String productTypeId, final String faceAmount, final String provCode,
			final String dateOfBirth1, final String gender1, final String smoking1, final String dateOfBirth2,
			final String gender2, final String smoking2, final String jointType, final String compID,
			final String newHealthClass1, final String newHealthClass2, final String levelOrDecr) {

		// get debug flag from properties
		DEBUG = false;

		String responseValue = "";

		try {

			responseValue = getJointResponseString(productTypeId, faceAmount, provCode, dateOfBirth1, gender1, smoking1,
					dateOfBirth2, gender2, smoking2, jointType, compID, newHealthClass1, newHealthClass2, levelOrDecr);

		} catch (IllegalArgumentException ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex);
			}
			if (SEND_EMAIL_FOR_ANOMALIES) {
				System.out.println(TAG + " Alert - Probable invalid parameter(s) from client"
						+ "getResponseString raised " + ex.getMessage() + LogUtil.getStackTrace(ex, 10)
						+ "\n\nHttp request parameters:" + " product_type_id {" + productTypeId + "}" + " face_amount {"
						+ faceAmount + "}" + " date_of_birth {" + dateOfBirth1 + "}" + " gender {" + gender1 + "}"
						+ " smoking {" + smoking1 + "}" + " province_code {" + provCode + "}");
			}
			String s = "An anomaly was detected; please review the parameters in your http request"
					+ "; the values that we received:" + " product_type_id {" + productTypeId + "}" + " face_amount {"
					+ faceAmount + "}" + " date_of_birth {" + dateOfBirth1 + "}" + " gender {" + gender1 + "}"
					+ " smoking {" + smoking1 + "}" + " province_code {" + provCode + "}";

			responseValue = "\"" + JsonUtil.cleanStringForJson(s) + "\"";

		} catch (InsurfactPrivateException ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex + "\n" + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10));
			}
			String body = "";
			if (SEND_EMAIL_FOR_ANOMALIES) {
				String subject = TAG + " Alert - Probable Bug";
				body = "\ngetResponseString raised InsurfactPrivateException: " + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10) + "\n\nHttp request parameters:" + " product_type_id {"
						+ productTypeId + "}" + " face_amount {" + faceAmount + "}" + " date_of_birth {" + dateOfBirth1
						+ "}" + " gender {" + gender1 + "}" + " smoking {" + smoking1 + "}" + " province_code {"
						+ provCode + "}";
				if (DEBUG) {
					System.out.println(TAG + ".getJson: alert email; Subject: " + subject + "; email body: " + body);
				}
				System.out.println(body);
			}
			String s = "An anomaly was detected; please contact support and please review the parameters in your http request"
					+ "; the values that we received are:" + " product_type_id {" + productTypeId + "}"
					+ " face_amount {" + faceAmount + "}" + " date_of_birth {" + dateOfBirth1 + "}" + " gender {"
					+ gender1 + "}" + " smoking {" + smoking1 + "}" + " province_code {" + provCode + "}" + "\n" // ==========
			// + body // remove in prod ##################################
			// ==========
			;

			responseValue = "\"" + JsonUtil.cleanStringForJson(s) + "\"";
		} catch (Throwable ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex + "\n" + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10));
			}
			String body = "";
			if (SEND_EMAIL_FOR_ANOMALIES) {
				String subject = TAG + " Alert - Probable Bug";
				body = "\ngetResponseString raised: " + ex.getMessage() + LogUtil.getStackTrace(ex, 10)
						+ "\n\nHttp request parameters:" + " product_type_id {" + productTypeId + "}" + " face_amount {"
						+ faceAmount + "}" + " date_of_birth {" + dateOfBirth1 + "}" + " gender {" + gender1 + "}"
						+ " smoking {" + smoking1 + "}" + " province_code {" + provCode + "}";
				if (DEBUG) {
					System.out.println(TAG + ".getJson: alert email; Subject: " + subject + "; email body: " + body);
				}
				System.out.println(body);
			}
			String s = "An anomaly was detected; "
					+ "please contact support and please review the parameters in your http request"
					+ "; the values that we received are:" + " product_type_id {" + productTypeId + "}"
					+ " face_amount {" + faceAmount + "}" + " date_of_birth {" + dateOfBirth1 + "}" + " gender {"
					+ gender1 + "}" + " smoking {" + smoking1 + "}" + " province_code {" + provCode + "}" + "\n" // ========
			// + body // remove in prod ##################################
			// ========
			;

			responseValue = "\"" + JsonUtil.cleanStringForJson(s) + "\"";
		}

//        String responseAll = "{\"response\": " + responseValue + " }";
//
//        if (DEBUG) {
//            System.out.println(TAG + ".getJson: returning $$" + responseAll + "$$");
//        }
		return responseValue;
	}

	String getNoExceptionJointGroup(final String faceAmount, final String dateOfBirth1, final String gender1,
			final String smoking1, final String dateOfBirth2, final String gender2, final String smoking2,
			final String provCode, final String jointType, final String levelOrDecr) {

		// get debug flag from properties
		DEBUG = false;

		String responseValue = "";

		try {
			// faceAmount, dateOfBirth1, gender1, smoking1, dateOfBirth2, gender2, smoking2,
			// provCode, true,"", "A", levelOrDecr
			System.out.println("877 GenJoint Widget faceAmount=" + faceAmount + " jointType=" + jointType
					+ " levelOrDecr=" + levelOrDecr);
			responseValue = getJointResponseStringGroup(faceAmount, provCode, dateOfBirth1, gender1, smoking1,
					dateOfBirth2, gender2, smoking2, jointType, levelOrDecr);

		} catch (IllegalArgumentException ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex);
			}
			if (SEND_EMAIL_FOR_ANOMALIES) {
				System.out.println(TAG + " Alert - Probable invalid parameter(s) from client"
						+ "getResponseString raised " + ex.getMessage() + LogUtil.getStackTrace(ex, 10)
						+ "\n\nHttp request parameters:" + " face_amount {" + faceAmount + "}" + " date_of_birth {"
						+ dateOfBirth1 + "}" + " gender {" + gender1 + "}" + " smoking {" + smoking1 + "}"
						+ " province_code {" + provCode + "}");
			}
			String s = "An anomaly was detected; please review the parameters in your http request"
					+ "; the values that we received:" + " face_amount {" + faceAmount + "}" + " date_of_birth {"
					+ dateOfBirth1 + "}" + " gender {" + gender1 + "}" + " smoking {" + smoking1 + "}"
					+ " province_code {" + provCode + "}";

			responseValue = "\"" + JsonUtil.cleanStringForJson(s) + "\"";

		} catch (InsurfactPrivateException ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex + "\n" + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10));
			}
			String body = "";
			if (SEND_EMAIL_FOR_ANOMALIES) {
				String subject = TAG + " Alert - Probable Bug";
				body = "\ngetResponseString raised InsurfactPrivateException: " + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10) + "\n\nHttp request parameters:" + " face_amount {" + faceAmount
						+ "}" + " date_of_birth {" + dateOfBirth1 + "}" + " gender {" + gender1 + "}" + " smoking {"
						+ smoking1 + "}" + " province_code {" + provCode + "}";
				if (DEBUG) {
					System.out.println(TAG + ".getJson: alert email; Subject: " + subject + "; email body: " + body);
				}
				System.out.println(body);
			}
			String s = "An anomaly was detected; please contact support and please review the parameters in your http request"
					+ "; the values that we received are:" + " face_amount {" + faceAmount + "}" + " date_of_birth {"
					+ dateOfBirth1 + "}" + " gender {" + gender1 + "}" + " smoking {" + smoking1 + "}"
					+ " province_code {" + provCode + "}" + "\n" // ==========
			// + body // remove in prod ##################################
			// ==========
			;

			responseValue = "\"" + JsonUtil.cleanStringForJson(s) + "\"";
		} catch (Throwable ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex + "\n" + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10));
			}
			String body = "";
			if (SEND_EMAIL_FOR_ANOMALIES) {
				String subject = TAG + " Alert - Probable Bug";
				body = "\ngetResponseString raised: " + ex.getMessage() + LogUtil.getStackTrace(ex, 10)
						+ "\n\nHttp request parameters:" + " face_amount {" + faceAmount + "}" + " date_of_birth {"
						+ dateOfBirth1 + "}" + " gender {" + gender1 + "}" + " smoking {" + smoking1 + "}"
						+ " province_code {" + provCode + "}";
				if (DEBUG) {
					System.out.println(TAG + ".getJson: alert email; Subject: " + subject + "; email body: " + body);
				}
				System.out.println(body);
			}
			String s = "An anomaly was detected; "
					+ "please contact support and please review the parameters in your http request"
					+ "; the values that we received are:" + " face_amount {" + faceAmount + "}" + " date_of_birth {"
					+ dateOfBirth1 + "}" + " gender {" + gender1 + "}" + " smoking {" + smoking1 + "}"
					+ " province_code {" + provCode + "}" + "\n" // ========
			// + body // remove in prod ##################################
			// ========
			;

			responseValue = "\"" + JsonUtil.cleanStringForJson(s) + "\"";
		}

//        String responseAll = "{\"response\": " + responseValue + " }";
//
//        if (DEBUG) {
//            System.out.println(TAG + ".getJson: returning $$" + responseAll + "$$");
//        }
		return responseValue;
	}

	String getNoExceptionJointSingleProd(final String productTypeId, final String faceAmount, final String provCode,
			final String dateOfBirth1, final String gender1, final String smoking1, final String dateOfBirth2,
			final String gender2, final String smoking2, final String jointType, final String compID,
			final String prodID, final String newHealthClass1, final String newHealthClass2, final String levelOrDecr) {

		// get debug flag from properties
		DEBUG = false;

		String responseValue = "";

		try {
//System.out.println("736 GenJoint Widget faceAmount="+ faceAmount +" jointType="+jointType+" productTypeId="+productTypeId+" levelOrDecr="+levelOrDecr);   

			responseValue = getJointResponseStringSingleProd(productTypeId, faceAmount, provCode, dateOfBirth1, gender1,
					smoking1, dateOfBirth2, gender2, smoking2, jointType, compID, prodID, newHealthClass1,
					newHealthClass2, levelOrDecr);

		} catch (IllegalArgumentException ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex);
			}
			if (SEND_EMAIL_FOR_ANOMALIES) {
				System.out.println(TAG + " Alert - Probable invalid parameter(s) from client"
						+ "getResponseString raised " + ex.getMessage() + LogUtil.getStackTrace(ex, 10)
						+ "\n\nHttp request parameters:" + " product_type_id {" + productTypeId + "}" + " face_amount {"
						+ faceAmount + "}" + " date_of_birth {" + dateOfBirth1 + "}" + " gender {" + gender1 + "}"
						+ " smoking {" + smoking1 + "}" + " province_code {" + provCode + "}");
			}
			String s = "An anomaly was detected; please review the parameters in your http request"
					+ "; the values that we received:" + " product_type_id {" + productTypeId + "}" + " face_amount {"
					+ faceAmount + "}" + " date_of_birth {" + dateOfBirth1 + "}" + " gender {" + gender1 + "}"
					+ " smoking {" + smoking1 + "}" + " province_code {" + provCode + "}";

			responseValue = "\"" + JsonUtil.cleanStringForJson(s) + "\"";

		} catch (InsurfactPrivateException ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex + "\n" + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10));
			}
			String body = "";
			if (SEND_EMAIL_FOR_ANOMALIES) {
				String subject = TAG + " Alert - Probable Bug";
				body = "\ngetResponseString raised InsurfactPrivateException: " + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10) + "\n\nHttp request parameters:" + " product_type_id {"
						+ productTypeId + "}" + " face_amount {" + faceAmount + "}" + " date_of_birth {" + dateOfBirth1
						+ "}" + " gender {" + gender1 + "}" + " smoking {" + smoking1 + "}" + " province_code {"
						+ provCode + "}";
				if (DEBUG) {
					System.out.println(TAG + ".getJson: alert email; Subject: " + subject + "; email body: " + body);
				}
				System.out.println(body);
			}
			String s = "An anomaly was detected; please contact support and please review the parameters in your http request"
					+ "; the values that we received are:" + " product_type_id {" + productTypeId + "}"
					+ " face_amount {" + faceAmount + "}" + " date_of_birth {" + dateOfBirth1 + "}" + " gender {"
					+ gender1 + "}" + " smoking {" + smoking1 + "}" + " province_code {" + provCode + "}" + "\n" // ==========
			// + body // remove in prod ##################################
			// ==========
			;

			responseValue = "\"" + JsonUtil.cleanStringForJson(s) + "\"";
		} catch (Throwable ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex + "\n" + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10));
			}
			String body = "";
			if (SEND_EMAIL_FOR_ANOMALIES) {
				String subject = TAG + " Alert - Probable Bug";
				body = "\ngetResponseString raised: " + ex.getMessage() + LogUtil.getStackTrace(ex, 10)
						+ "\n\nHttp request parameters:" + " product_type_id {" + productTypeId + "}" + " face_amount {"
						+ faceAmount + "}" + " date_of_birth {" + dateOfBirth1 + "}" + " gender {" + gender1 + "}"
						+ " smoking {" + smoking1 + "}" + " province_code {" + provCode + "}";
				if (DEBUG) {
					System.out.println(TAG + ".getJson: alert email; Subject: " + subject + "; email body: " + body);
				}
				System.out.println(body);
			}
			String s = "An anomaly was detected; "
					+ "please contact support and please review the parameters in your http request"
					+ "; the values that we received are:" + " product_type_id {" + productTypeId + "}"
					+ " face_amount {" + faceAmount + "}" + " date_of_birth {" + dateOfBirth1 + "}" + " gender {"
					+ gender1 + "}" + " smoking {" + smoking1 + "}" + " province_code {" + provCode + "}" + "\n" // ========
			// + body // remove in prod ##################################
			// ========
			;

			responseValue = "\"" + JsonUtil.cleanStringForJson(s) + "\"";
		}

//        String responseAll = "{\"response\": " + responseValue + " }";
//
//        if (DEBUG) {
//            System.out.println(TAG + ".getJson: returning $$" + responseAll + "$$");
//        }
		return responseValue;
	}

	private String getJointResponseStringSingleProd(final String productTypeId, final String faceAmount,
			final String provCode, final String dateOfBirth1, final String gender1, final String smoking1,
			final String dateOfBirth2, final String gender2, final String smoking2, final String jointType,
			final String compID, final String prodID, final String newHealthClass1, final String newHealthClass2,
			final String levelOrDecr) throws InsurfactPrivateException {

		// step 1: validate the input values from the URL, important for security
		// ----------------------------------------------------------------------
		int productTypeInt = 0;

		int faceAmountInt = 0;
		try {
			faceAmountInt = Integer.parseInt(faceAmount);
		} catch (NumberFormatException e) {
			throw new InsurfactPrivateException("invalid faceAmount input {" + faceAmount + "}");
		}

		if (provCode == null || provCode.length() != 2) {
			throw new InsurfactPrivateException("invalid provCode input {" + provCode + "}");
		}
		String provCodeUp = provCode.toUpperCase();

		if (dateOfBirth1 == null || dateOfBirth1.isEmpty()) {
			throw new InsurfactPrivateException("invalid dateOfBirth input {" + dateOfBirth1 + "}");
		}
		String dob1 = dateOfBirth1;

		// gender M F
		if (gender1 == null || gender1.isEmpty()
				|| (!gender1.equalsIgnoreCase("M") && !gender1.equalsIgnoreCase("F"))) {
			throw new InsurfactPrivateException("invalid gender input {" + gender1 + "}");
		}
		String genderUp1 = gender1.toUpperCase();

		// smoking Y N
		if (smoking1 == null || smoking1.isEmpty()
				|| (!smoking1.equalsIgnoreCase("Y") && !smoking1.equalsIgnoreCase("N"))) {
			throw new InsurfactPrivateException("invalid smoking input {" + smoking1 + "}");
		}
		String smokingUp1 = smoking1.toUpperCase();

		if (dateOfBirth2 == null || dateOfBirth2.isEmpty()) {
			throw new InsurfactPrivateException("invalid dateOfBirth input {" + dateOfBirth2 + "}");
		}
		String dob2 = dateOfBirth2;

		// gender M F
		if (gender2 == null || gender2.isEmpty()
				|| (!gender2.equalsIgnoreCase("M") && !gender2.equalsIgnoreCase("F"))) {
			throw new InsurfactPrivateException("invalid gender input {" + gender2 + "}");
		}
		String genderUp2 = gender2.toUpperCase();

		// smoking Y N
		if (smoking2 == null || smoking2.isEmpty()
				|| (!smoking2.equalsIgnoreCase("Y") && !smoking2.equalsIgnoreCase("N"))) {
			throw new InsurfactPrivateException("invalid smoking input {" + smoking2 + "}");
		}
		String smokingUp2 = smoking2.toUpperCase();

		// jointType F or L
		if (jointType == null || jointType.isEmpty()
				|| (!jointType.equalsIgnoreCase("F") && !jointType.equalsIgnoreCase("L"))) {
			throw new InsurfactPrivateException("invalid smoking input {" + jointType + "}");
		}
		String jointTypeUp = jointType.toUpperCase();

		try {
			// ------------------------------------------------------------------------------------------

			prepareJointQuoteParamsProd(productTypeInt, faceAmountInt, provCodeUp, dob1, genderUp1, smokingUp1, dob2,
					genderUp2, smokingUp2, jointTypeUp, compID, prodID, newHealthClass1, newHealthClass2, levelOrDecr);

			// ------------------------------------------------------------------------------------------
		} catch (InsurfactPrivateException e) {
			String s = "*prepareSingleQuoteParams(smokingUp, provCodeUp, faceAmountInt)* raised {" + e + "}";
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + s);
			}
			throw e;
		} catch (Throwable e) {
			String s = "*prepareSingleQuoteParams(smokingUp, provCodeUp, faceAmountInt)* raised {" + e + "}";
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + s);
			}
			throw new InsurfactPrivateException(s);
		}

		// step 4: run the sql and quote engine logic
		// ------------------------------------------
		List<Product> productsWithPercentage = getProductsJoint();

		List<Product> finalProductList = new ArrayList<>();

		// step 5: prepare results to return to client requester
		// -----------------------------------------------------
		String response = "\"No matching records.\"";

		if (productsWithPercentage == null || productsWithPercentage.isEmpty()) {

			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + "*productsWithPercentage = getProductsCombos()* "
						+ "returned null or empty list.");
			}

			return response;
		}

		Client clientESA = null;

		for (Product product : productsWithPercentage) {

			Product referenceProduct = productFacade.getIQ4LifeProductDetail(product.getID());

			// if(referenceProduct.getMinimumAnnualRequired() <=
			// referenceProduct.getProductAnnualPremiumTotal()){//mininmum premium added
			// april 14 2021 -aa
			referenceProduct = productFacade.getJointProductDetail(referenceProduct, multiQP.getJointType());
			multiQP.setReferenceProduct(referenceProduct);

			clientESA = null;
			multiQP.setClientESA(null);

//System.out.println("1128 GenericJoint getMinimumAnnualRequired "+referenceProduct.getMinimumAnnualRequired());
//System.out.println("1129 GenericJoint getProductAnnualPremiumTotal"+referenceProduct.getProductAnnualPremiumTotal());
			int tmpAge = 0;
			int tmpAge2 = 0;
			if (referenceProduct.getAgeToUse().equalsIgnoreCase("N")) {
				tmpAge = multiQP.getClient().getNearestAge();
				tmpAge2 = multiQP.getClient2().getNearestAge();
			} else {
				tmpAge = multiQP.getClient().getActualAge();
				tmpAge2 = multiQP.getClient2().getActualAge();
			}

			// multiQP = getMultiQP();
			multiQP.setCompanyID(referenceProduct.getCompany().getID());

			multiQP.setProductID(referenceProduct.getID());
			// multiQP.setReferenceProduct(product);
			String jointCalcMethod = referenceProduct.getJointCalcMethod();
			multiQP.setJointCalcMethod(jointCalcMethod);
			multiQP.setProductsAgeToUse(referenceProduct.getAgeToUse());
			multiQP.setOriginalFaceAmount(faceAmountInt);

			updateJointUWClassses(1);

			if (referenceProduct.isEnhanced()) {

				// calc and set enhanced faceAmoimt
				enhancedProductFacade.processEnhancedProduct(multiQP, referenceProduct);

				if (referenceProduct.getEnhancedFaceAmount() > 0) {

					// process enhanced face amount. in the case it requires a new amount, the
					// original multiQP.originalFaceAmt is set to prod.originalFaceAmt
					Integer enhancedFaceAmount = referenceProduct.getEnhancedFaceAmount();

					Band band = referenceProduct.getBandForDetails(tmpAge, genderUp1.charAt(0),
							multiQP.getClient().isSmoker(), multiQP.getFaceAmt());
					Integer minMaxFaceAmount = null;

					if (band != null) {
						minMaxFaceAmount = band.getMinMaxFaceAmount();
						System.out.println(band);
					}

					// set new face amount
					multiQP.setFaceAmt(enhancedFaceAmount); // EMA-Jan 29th 2017

					if (minMaxFaceAmount != null) {
						int tmp = Math.abs((enhancedFaceAmount) - minMaxFaceAmount);

						// upgrade original face amount
						if (tmp >= 0 && tmp <= 1000) {
							multiQP.setFaceAmt(minMaxFaceAmount); // EMA-Jan 29th 2017
						}
					}

					// set new face amount
					multiQP.setFaceAmt(enhancedFaceAmount);
					referenceProduct.setFaceAmount(enhancedFaceAmount);
//System.out.println("GenJoint 1184 enhancedFaceAmount="+enhancedFaceAmount);                      
				}

				product = quoteFacade.singleProductQuote(multiQP, referenceProduct);
			}

			updateJointUWClassses(1);
			updateJointUWClassses(2);

//System.out.println("GenJoint 1193 updateJointUWClassses");
			Product newProduct = quoteFacade.jointSingleQuote(multiQP);

//System.out.println("GenJoint 1197 newProduct="+newProduct.getDescription());                
			List<HealthClass> cliHealthClass1 = newProduct.getClientHealthClasses().get(0);
			List<HealthClass> cliHealthClass2 = newProduct.getClientHealthClasses().get(1);

			@SuppressWarnings("unused")
			String healthClass1 = "";
			@SuppressWarnings("unused")
			String healthClassFr1 = "";

			@SuppressWarnings("unused")
			String healthClass2 = "";
			@SuppressWarnings("unused")
			String healthClassFr2 = "";

			@SuppressWarnings("unused")
			boolean isFamilyHistory1 = false;
			@SuppressWarnings("unused")
			boolean isFamilyHistory2 = false;

			@SuppressWarnings("unused")
			boolean isCigarretSmoker1 = false;
			@SuppressWarnings("unused")
			boolean isCigarretSmoker2 = false;

			@SuppressWarnings("unused")
			String classFlag1 = "";
			@SuppressWarnings("unused")
			String lowMonths1 = "";

			@SuppressWarnings("unused")
			String classFlag2 = "";
			@SuppressWarnings("unused")
			String lowMonths2 = "";

			resetJointUWClasses(1);
			resetJointUWClasses(2);

			HealthClass client1HealthClass = null;
			HealthClass client2HealthClass = null;

			for (HealthClass cliHC : cliHealthClass1) {

				client1HealthClass = cliHC;

				healthClass1 = cliHC.getName();
				healthClassFr1 = cliHC.getNameFr();
				multiQP.getClient().setClassId(cliHC.getID());
				multiQP.getClient().setSmokeCode(cliHC.getSmokeCode());
				IMUWClass uwClass = productFacade.getIMUWClass(cliHC.getID());
				uwClass.setSmokeCode(cliHC.getSmokeCode());

				multiQP.getClient().setUwClass(uwClass);
				multiQP.getClient().getUwClass().setSmokeCode(cliHC.getSmokeCode());

				isCigarretSmoker1 = cliHC.isCigarretSmoker();
				isFamilyHistory1 = cliHC.isFamilyHistory();
				classFlag1 = cliHC.getDescription();
				lowMonths1 = cliHC.getNonSmokerMonths() + "";

			}

			updateJointUWClassses(1);

			/*
			 * Band bandA = referenceProduct.getBandForGlobalClass(tmpAge,
			 * multiQP.getClient().getGender(), multiQP.getFaceAmt(),
			 * multiQP.getClient().isSmoker(), "S");
			 * 
			 * if (bandA == null) {
			 * System.out.println("@@@@ unable to get Band for client1 for product :" +
			 * referenceProduct.getName()); // multiQP.setEnhancedFaceAmount(null);
			 * continue; }
			 * 
			 * // System.out.println("band1: "+band1);
			 * multiQP.getClient().setUwClass(multiQP.getClient().getUwClass(bandA.
			 * getClassId())); multiQP.getClient().setClassId(bandA.getClassId());
			 */
			for (HealthClass cliHC : cliHealthClass2) {

				client2HealthClass = cliHC;

				healthClass2 = cliHC.getName();
				healthClassFr2 = cliHC.getNameFr();
				multiQP.getClient2().setClassId(cliHC.getID());
				multiQP.getClient2().setSmokeCode(cliHC.getSmokeCode());
				IMUWClass uwClass = productFacade.getIMUWClass(cliHC.getID());
				uwClass.setSmokeCode(cliHC.getSmokeCode());

				multiQP.getClient2().setUwClass(uwClass);
				multiQP.getClient2().getUwClass().setSmokeCode(cliHC.getSmokeCode());

				isCigarretSmoker2 = cliHC.isCigarretSmoker();
				isFamilyHistory2 = cliHC.isFamilyHistory();
				classFlag2 = cliHC.getDescription();
				lowMonths2 = cliHC.getNonSmokerMonths() + "";

			}

			updateJointUWClassses(2);

			/*
			 * Band bandB = referenceProduct.getBandForGlobalClass(tmpAge2,
			 * multiQP.getClient2().getGender(), multiQP.getFaceAmt(),
			 * multiQP.getClient2().isSmoker(), "S");
			 * 
			 * if (bandB == null) {
			 * System.out.println("@@@@ unable to get Band for client1 for product :" +
			 * referenceProduct.getName()); // multiQP.setEnhancedFaceAmount(null);
			 * continue; }
			 * 
			 * // System.out.println("band1: "+band1);
			 * multiQP.getClient2().setUwClass(multiQP.getClient2().getUwClass(bandB.
			 * getClassId())); multiQP.getClient2().setClassId(bandB.getClassId());
			 * 
			 */
			if (jointCalcMethod.equals("A") || jointCalcMethod.equals("P")) {
				newProduct = quoteFacade.jointSingleQuote(multiQP);

				// get underwriting details based on prod specs and faceamt; one list for each
				// client;
				List<String> underwritingList = quoteFacade.getUnderwriting(newProduct.getCompany().getID(),
						newProduct.getClientUnderwritingGroupID(0), faceAmountInt, tmpAge,
						Character.toString(multiQP.getClient().getGender()));
				newProduct.setClientUnderwriting(0, underwritingList);

				underwritingList = quoteFacade.getUnderwritingFr(newProduct.getCompany().getID(),
						newProduct.getClientUnderwritingGroupID(0), faceAmountInt, tmpAge,
						Character.toString(multiQP.getClient().getGender()));
				newProduct.setClientUnderwritingFr(0, underwritingList);

				underwritingList = quoteFacade.getUnderwriting(newProduct.getCompany().getID(),
						newProduct.getClientUnderwritingGroupID(1), faceAmountInt, tmpAge2,
						Character.toString(multiQP.getClient2().getGender()));
				newProduct.setClientUnderwriting(1, underwritingList);

				underwritingList = quoteFacade.getUnderwritingFr(newProduct.getCompany().getID(),
						newProduct.getClientUnderwritingGroupID(1), faceAmountInt, tmpAge2,
						Character.toString(multiQP.getClient2().getGender()));
				newProduct.setClientUnderwritingFr(1, underwritingList);

			}

			clientESA = null;
			multiQP.setClientESA(null);

			if (jointCalcMethod.equals("E") || jointCalcMethod.equals("G")) {

				multiQP.setReferenceProduct(referenceProduct);
				clientESA = jointESAUtils.processJointESA(multiQP);

				multiQP.setClientESA(clientESA);

				QuoteParams tmpParams = new QuoteParams();
				tmpParams.initSingleMode();
				tmpParams.setReferenceProduct(referenceProduct);
				if (iq4Mode == IQ4Modes.JointMultiCompany) {
					tmpParams.setOnePeriodOnly(true);
				}

				tmpParams.setFaceAmt(multiQP.getFaceAmt());
				tmpParams.setOriginalFaceAmount(multiQP.getOriginalFaceAmount());
				tmpParams.setProductsAgeToUse(product.getAgeToUse());

				tmpParams.setClientESA(clientESA);
				tmpParams.setProductID(multiQP.getProductID());
				tmpParams.setCompanyID(multiQP.getCompanyID());
				tmpParams.setClient(multiQP.getClient());
				tmpParams.setClient2(multiQP.getClient2());
				tmpParams.setWebService(true);

// quote the Joint Product as Single Product
				newProduct = quoteFacade.singleProductQuote(tmpParams, null);

				Band band1 = referenceProduct.getBandForDetails(tmpAge, multiQP.getClient().getGender(),
						!multiQP.getClient().isNonSmoker(), faceAmountInt);
				Band band2 = referenceProduct.getBandForDetails(tmpAge2, multiQP.getClient2().getGender(),
						!multiQP.getClient2().isNonSmoker(), faceAmountInt);

				// get underwriting details based on prod specs and faceamt; one list for each
				// client;
				if (band1 != null) {

					List<String> underwritingList = quoteFacade.getUnderwriting(multiQP.getCompanyID(),
							band1.getUnderwritingGroup(), faceAmountInt, tmpAge,
							Character.toString(multiQP.getClient().getGender()));
					newProduct.setClientUnderwriting(0, underwritingList);
					underwritingList = quoteFacade.getUnderwritingFr(multiQP.getCompanyID(),
							band1.getUnderwritingGroup(), faceAmountInt, tmpAge,
							Character.toString(multiQP.getClient().getGender()));
					newProduct.setClientUnderwritingFr(0, underwritingList);
				} else {
					System.out.println("1226 GenericJoint band1 IS null");
				}

				if (band2 != null) {

					List<String> underwritingList = quoteFacade.getUnderwriting(multiQP.getCompanyID(),
							band2.getUnderwritingGroup(), faceAmountInt, tmpAge2,
							Character.toString(multiQP.getClient2().getGender()));
					newProduct.setClientUnderwriting(1, underwritingList);

					underwritingList = quoteFacade.getUnderwritingFr(multiQP.getCompanyID(),
							band2.getUnderwritingGroup(), faceAmountInt, tmpAge2,
							Character.toString(multiQP.getClient2().getGender()));
					newProduct.setClientUnderwritingFr(1, underwritingList);
				} else {
					System.out.println("1237 GenericJoint band2 IS null");
				}

			} // if (jointCalcMethod.equals("E") || jointCalcMethod.equals("G")) {

			quoteFacade.getProductShortDescriptions(newProduct);

			Company company = product.getCompany();
			@SuppressWarnings("unused")
			String companyNameEn = "";
			@SuppressWarnings("unused")
			String companyNameFr = "";
			if (company != null) {
				companyNameEn = product.getCompany().getName();
				companyNameFr = product.getCompany().getNameFr();
			}

			newProduct.setCompanyID(product.getCompanyID());
			newProduct.setProductType(product.getProductType());
			newProduct.setHealthClasses(product.getHealthClasses());

			newProduct.addClientHealthClass(0, client1HealthClass);
			newProduct.addClientHealthClass(1, client2HealthClass);

			findPromotion(newProduct);

			if (isHasPromotion() || product.hasPromoBuiltIn()) {

				newProduct.setPromotions(newProduct.getPromotions());
				if (getMultiQP().getClientESA() != null) {
					promotionsHelper.applyPromotion(newProduct, promo, getMultiQP().getClientESA().getActualAge(),
							multiQP.getFaceAmt(), multiQP);
				} else {
					promotionsHelper.applyPromotion(newProduct, promo, tmpAge, multiQP.getFaceAmt(), multiQP);
				}

			}

			promo = null;

			finalProductList.add(newProduct);
		} // forst for(

		int counter = 0;
		StringBuilder buf = new StringBuilder("[ ");

		try {
			Collections.sort(finalProductList, new TotalAnnComparator());

			for (Product productB : finalProductList) {

				int levDecr = 0;
				String coverageType = "A"; // default A for all

				if (productB.getInsuranceBen() != 0) {
					levDecr = productB.getInsuranceBen();
				}
				if (levDecr == 2) {
					coverageType = "L"; // L=level D=decreasing
				} else {
					coverageType = "D"; // L=level D=decreasing
				}

				int convertibleAge = productB.getLastConvertibleAge();
				int payableAge = productB.getAgePayable();

				boolean isFamilyHistory1 = false;
				boolean isFamilyHistory2 = false;

				boolean isCigarretSmoker1 = false;
				boolean isCigarretSmoker2 = false;

				String classFlag1 = "";
				String classFlag2 = "";

				String healthClass1 = "";
				String healthClassFr1 = "";

				String healthClass2 = "";
				String healthClassFr2 = "";

				String lowMonths1 = "";
				String lowMonths2 = "";

				List<HealthClass> cliHealthClass1 = productB.getClientHealthClasses().get(0);
				List<HealthClass> cliHealthClass2 = productB.getClientHealthClasses().get(1);
				for (HealthClass cliHC : cliHealthClass1) {
					healthClass1 = cliHC.getName();
					healthClassFr1 = cliHC.getNameFr();
					isCigarretSmoker1 = cliHC.isCigarretSmoker();
					isFamilyHistory1 = cliHC.isFamilyHistory();
					classFlag1 = cliHC.getDescription();
					lowMonths1 = cliHC.getNonSmokerMonths() + "";
				}

				updateJointUWClassses(1);

				for (HealthClass cliHC : cliHealthClass2) {
					healthClass2 = cliHC.getName();
					healthClassFr2 = cliHC.getNameFr();
					isCigarretSmoker2 = cliHC.isCigarretSmoker();
					isFamilyHistory2 = cliHC.isFamilyHistory();
					classFlag2 = cliHC.getDescription();
					lowMonths2 = cliHC.getNonSmokerMonths() + "";
				}
				updateJointUWClassses(2);

				List<String> cliUndw1 = productB.getClientUnderwriting().get(0);
				List<String> cliUndwFr1 = productB.getClientUnderwritingFr().get(0);
				@SuppressWarnings("unused")
				List<String> cliUndw2 = productB.getClientUnderwriting().get(1);
				List<String> cliUndwFr2 = productB.getClientUnderwritingFr().get(1);
				String clientUW1 = "";
				String clientUWFr1 = "";

				String clientUW2 = "";
				String clientUWFr2 = "";

				for (String cliUW : cliUndw1) {
					if (!clientUW1.equals("")) {
						clientUW1 += ", ";
					}
					clientUW1 += cliUW;
				}

				for (String cliUW : cliUndw1) {
					if (!clientUW2.equals("")) {
						clientUW2 += ", ";
					}
					clientUW2 += cliUW;
				}

				for (String cliUW : cliUndwFr1) {
					if (!clientUWFr1.equals("")) {
						clientUWFr1 += ", ";
					}
					clientUWFr1 += cliUW;
				}

				for (String cliUW : cliUndwFr2) {
					if (!clientUWFr2.equals("")) {
						clientUWFr2 += ", ";
					}
					clientUWFr2 += cliUW;
				}

				/*
				 * isCigarretSmoker1 = productB.getHealthClasses().get(0).isCigarretSmoker();
				 * isFamilyHistory1 = productB.getHealthClasses().get(0).isFamilyHistory();
				 * classFlag1 = productB.getHealthClasses().get(0).getDescription();
				 * 
				 * isCigarretSmoker2 = productB.getHealthClasses().get(0).isCigarretSmoker();
				 * isFamilyHistory2 = productB.getHealthClasses().get(0).isFamilyHistory();
				 * classFlag2 = productB.getHealthClasses().get(0).getDescription();
				 */
				++counter;
				if (counter > 1) {
					buf.append(", ");
				}

				// quoteWizard.jointQP.singleProduct.clientUnderwriting[0]
				DateFormat df = new SimpleDateFormat("dd-MMM-YYYY");
				// BigDecimal percentage = product.getPercentageGainWithAgent().setScale(2,
				// RoundingMode.HALF_UP);
				double percentage = 0;
				String res = JsonGenerator.getResponseValuesJoint(counter, provCodeUp,
						Util.getStringFromDouble(productB.getProductMonthlyPremiumTotal()),
						Util.getStringFromDouble(productB.getProductAnnualPremiumTotal()), "" + percentage,
						convertibleAge + "", payableAge + "", healthClass1, healthClassFr1, healthClass2,
						healthClassFr2, clientUW1, clientUWFr1, clientUW2, clientUWFr2,
						df.format(multiQP.getClient().getFullBirthDate()),
						df.format(multiQP.getClient2().getFullBirthDate()), multiQP.getClient().getNearestAge(),
						multiQP.getClient().getActualAge(), multiQP.getClient2().getNearestAge(),
						multiQP.getClient2().getActualAge(), smokingUp1, smokingUp2, faceAmount, productB,
						multiQP.getClient().getGender(), multiQP.getClient2().getGender(), classFlag1, lowMonths1,
						isCigarretSmoker1, isFamilyHistory1, classFlag2, lowMonths2, isCigarretSmoker2,
						isFamilyHistory2, coverageType);
				buf.append(res);

				// }// if(referenceProduct.getMinimumAnnualRequired() <=
				// referenceProduct.getProductAnnualPremiumTotal()){//mininmum premium added
				// april 14 2021 -aa
			} // for

			if (counter > 0) {
				buf.append(" ]");
				response = buf.toString();
			} else {
				response = "\"no matching records\"";
			}

		} catch (Exception ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: Reading the results raised: " + ex);
			}
			// response = "\"Reading the resultsSet raised:
			// "+cleanStringForJson(ex.getMessage())+"\"";
			throw new InsurfactPrivateException("Reading the results raised " + ex + " at counter " + counter
			// +" - "+ex.getMessage()
			// + "\n " + LogUtil.getStackTrace(ex, 10)
			// + "\n sql = " + sql
			);
		} finally {

		}

		try {
			if (con != null) {
				con.close();
			}
		} catch (SQLException e) {
			System.out.println("error closing connection con line 667 Generic e=" + e.getMessage());
		}

		return response;
	}

	protected final void prepareJointQuoteParamsProd(final int productTypeInt, final int faceAmountInt,
			final String provCodeUp, final String dob1, final String genderUp1, final String smokingUp1,
			final String dob2, final String genderUp2, final String smokingUp2, final String jointTypeUp,
			final String compID, final String prodID, final String newHealthClass1, final String newHealthClass2,
			final String levelOrDecr) throws InsurfactPrivateException {

		startJoint(dob1, genderUp1, dob2, genderUp2, compID); // sets the contact and client
																// com.insurfact.iq.domain.Client

		multiQP.setProductTypeID(productTypeInt);// 6); //TODO ????????????????????????????????

		multiQP.setJointType(jointTypeUp);

		List<Integer> filter = new ArrayList<>();
		filter.add(999);
		multiQP.setCompanyFilter(filter);

		if (compID != null) {
			if (!compID.equals("")) {
				multiQP.setCompanyID(Integer.parseInt(compID));
			}
		}

		if (prodID != null) {
			if (!prodID.equals("")) {
				multiQP.setProductID(Integer.parseInt(prodID));
			}
		}

		Object firstContactObject = multiQP.getFirstContact();

		// com.insurfact.skynet.entity.Contact
		Contact firstContact = null; // TODO confirm ########## needed???

		if (firstContactObject != null && firstContactObject instanceof Contact) {
			firstContact = (Contact) firstContactObject;
			if (DEBUG) {
				System.out.println(TAG + ".prepareQuoteParams: *multiQP.getFirstContact()* returned firstContact = {"
						+ firstContact + "}");
			}
		} else {
			if (DEBUG) {
				System.out.println(
						TAG + ".prepareQuoteParams: *multiQP.getFirstContact()* returned firstContactObject = {"
								+ firstContactObject + "}");
			}

			// TODO washere maybe create Contact in multiQP ???
			throw new InsurfactPrivateException("firstContactObject is null");
		}

		// here we have a Contact in multiQP
		if (DEBUG) {
			System.out.println(TAG + ".prepareQuoteParams: " + "getContactInfo(firstContact) #1 = "
					+ Util.getContactInfo(firstContact));
		}

		// smoking client.isNonSmoker()
		multiQP.getClient().setNonSmoker(!"Y".equals(smokingUp1));

		// com.insurfact.iq.domain.Client
		multiQP.getClient().setProvinceID(Util.getProvinceCodeInt(provCodeUp));

		multiQP.getClient().setClassId(0);// TODO washere ??????????????????

		if (!newHealthClass1.equals("")) {
			multiQP.getClient().setHealthClass(newHealthClass1);
		}

		// smoking client.isNonSmoker()
		multiQP.getClient2().setNonSmoker(!"Y".equals(smokingUp2));

		// com.insurfact.iq.domain.Client
		multiQP.getClient2().setProvinceID(Util.getProvinceCodeInt(provCodeUp));

		multiQP.getClient2().setClassId(0);// TODO washere ??????????????????

		if (!newHealthClass2.equals("")) {
			multiQP.getClient2().setHealthClass(newHealthClass2);
		}

		multiQP.setIssueProvinceID(Util.getProvinceCodeInt(provCodeUp));

		// if(DEBUG) System.out.println(TAG+".prepareQuoteParams:
		// *faceAmountBig.intValue()* returned {"+faceAmountBig.intValue()+"}");
		multiQP.setOriginalFaceAmount(faceAmountInt);

		multiQP.setFaceAmt(faceAmountInt);

		multiQP.setInsuranceTypeAbbr(PRODUCT_CLASS); // or CRIT for another web service

		// from QuoteWizard.goPage2():
		multiQP.setUsingMinReqPremiumFaceAmt(false);

		multiQP.setSelectedRider(null);

		multiQP.setWebService(true);
		multiQP.setNameWS("WIDGET");

		if (!levelOrDecr.equals("")) {
			if (!levelOrDecr.equalsIgnoreCase("A")) {
				if (levelOrDecr.equalsIgnoreCase("L")) {
					multiQP.setInsuranceBen(2);//// 2 or L=level 6 or D=decreasing
				} else {
					multiQP.setInsuranceBen(6);
				}
			}
		}

		multiQP.setDebug(DEBUG);

	}

	private String getJointResponseString(final String productTypeId, final String faceAmount, final String provCode,
			final String dateOfBirth1, final String gender1, final String smoking1, final String dateOfBirth2,
			final String gender2, final String smoking2, final String jointType, final String compID,
			final String newHealthClass1, final String newHealthClass2, final String levelOrDecr)
			throws InsurfactPrivateException {

		// step 1: validate the input values from the URL, important for security
		// ----------------------------------------------------------------------
		int productTypeInt = 0;
		try {
			productTypeInt = Integer.parseInt(productTypeId);
		} catch (NumberFormatException e) {
			throw new InsurfactPrivateException("invalid productTypeId input {" + productTypeId + "}");
		}

		int faceAmountInt = 0;
		try {
			faceAmountInt = Integer.parseInt(faceAmount);
		} catch (NumberFormatException e) {
			throw new InsurfactPrivateException("invalid faceAmount input {" + faceAmount + "}");
		}

		if (provCode == null || provCode.length() != 2) {
			throw new InsurfactPrivateException("invalid provCode input {" + provCode + "}");
		}
		String provCodeUp = provCode.toUpperCase();

		if (dateOfBirth1 == null || dateOfBirth1.isEmpty()) {
			throw new InsurfactPrivateException("invalid dateOfBirth input {" + dateOfBirth1 + "}");
		}
		String dob1 = dateOfBirth1;

		// gender M F
		if (gender1 == null || gender1.isEmpty()
				|| (!gender1.equalsIgnoreCase("M") && !gender1.equalsIgnoreCase("F"))) {
			throw new InsurfactPrivateException("invalid gender input {" + gender1 + "}");
		}
		String genderUp1 = gender1.toUpperCase();

		// smoking Y N
		if (smoking1 == null || smoking1.isEmpty()
				|| (!smoking1.equalsIgnoreCase("Y") && !smoking1.equalsIgnoreCase("N"))) {
			throw new InsurfactPrivateException("invalid smoking input {" + smoking1 + "}");
		}
		String smokingUp1 = smoking1.toUpperCase();

		if (dateOfBirth2 == null || dateOfBirth2.isEmpty()) {
			throw new InsurfactPrivateException("invalid dateOfBirth input {" + dateOfBirth2 + "}");
		}
		String dob2 = dateOfBirth2;

		// gender M F
		if (gender2 == null || gender2.isEmpty()
				|| (!gender2.equalsIgnoreCase("M") && !gender2.equalsIgnoreCase("F"))) {
			throw new InsurfactPrivateException("invalid gender input {" + gender2 + "}");
		}
		String genderUp2 = gender2.toUpperCase();

		// smoking Y N
		if (smoking2 == null || smoking2.isEmpty()
				|| (!smoking2.equalsIgnoreCase("Y") && !smoking2.equalsIgnoreCase("N"))) {
			throw new InsurfactPrivateException("invalid smoking input {" + smoking2 + "}");
		}
		String smokingUp2 = smoking2.toUpperCase();

		// jointType F or L
		if (jointType == null || jointType.isEmpty()
				|| (!jointType.equalsIgnoreCase("F") && !jointType.equalsIgnoreCase("L"))) {
			throw new InsurfactPrivateException("invalid smoking input {" + jointType + "}");
		}
		String jointTypeUp = jointType.toUpperCase();

		try {
			// ------------------------------------------------------------------------------------------

			prepareJointQuoteParams(productTypeInt, faceAmountInt, provCodeUp, dob1, genderUp1, smokingUp1, dob2,
					genderUp2, smokingUp2, jointTypeUp, compID, newHealthClass1, newHealthClass2, levelOrDecr);

			// ------------------------------------------------------------------------------------------
		} catch (InsurfactPrivateException e) {
			String s = "*prepareSingleQuoteParams(smokingUp, provCodeUp, faceAmountInt)* raised {" + e + "}";
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + s);
			}
			throw e;
		} catch (Throwable e) {
			String s = "*prepareSingleQuoteParams(smokingUp, provCodeUp, faceAmountInt)* raised {" + e + "}";
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + s);
			}
			throw new InsurfactPrivateException(s);
		}

		String response = "\"No matching records.\"";

		QuoteParams qp = getMultiQP();

		List<Product> finalProducts = new ArrayList<>();

		qp.setFirstFa(qp.getFaceAmt());

		Client c1 = qp.getClient();
		Client c2 = qp.getClient2();

		int initialFaceAmount = qp.getFaceAmt();

		@SuppressWarnings("unused")
		boolean status = false;

		// Clear lists
		qp.setProducts(new ArrayList<>());
		qp.setFilteredProducts(new ArrayList<>());
		qp.setSelectedProduct(null);

		if (levelOrDecr != null) {
			if (levelOrDecr.equals("L")) {
				qp.setInsuranceBen(2);
			} else {
				qp.setInsuranceBen(6);
			}
		} else {
			qp.setInsuranceBen(0);
		}

		/*
		 * if (c1.getFullBirthDate() == null) {
		 * 
		 * applicationBean.postErrorMessage("Invalid date of birth for Client #1",
		 * "Date d'anniversaire invalide pour le client #1", "", "");
		 * 
		 * return gotoFailPage1(); }
		 * 
		 * if (c2.getFullBirthDate() == null) {
		 * 
		 * applicationBean.postErrorMessage("Invalid date of birth for Client #2",
		 * "Date d'anniversaire invalide pour le client #2", "", "");
		 * 
		 * return gotoFailPage1(); }
		 */
		// set correct data based on Smoker status... not set in Multi Joint mode
		if (c1.getSmokerStatus().equalsIgnoreCase("N")) {
			c1.setNonSmoker(true);
			// c1.setNonSmokerMonths(12);
		} else {
			c1.setNonSmoker(false);
			c1.setNonSmokerMonths(0);
		}

		if (c2.getSmokerStatus().equalsIgnoreCase("N")) {
			c2.setNonSmoker(true);
			// c2.setNonSmokerMonths(12);
		} else {
			c2.setNonSmoker(false);
			c2.setNonSmokerMonths(0);
		}

		qp.setValuePage(false);
		qp.setIncludeCondensedDetail(false);

		List<Product> products = new ArrayList<>();

		try {
			products = getAvailableJointProducts(qp);

			if (products == null || products.isEmpty()) {

			}

			qp.setOnePeriodOnly(true);

			// iterate over the list of products returned earlier
			for (Product product : products) {
				qp.setFaceAmt(initialFaceAmount);
				qp.setGazooFaSuggestion(0);
				qp.setClientESA(null);
				System.out.println("checking product: " + product.getID());
				Product singleJointProduct = calcJointSingleFromMulti(qp, product);
				System.out.println("got the product: " + product.getID());
				if (singleJointProduct != null) {
					singleJointProduct.setCompanyID(product.getCompanyID());
					/*
					 * findPromotion(singleJointProduct);
					 * 
					 * if (singleJointProduct.hasPromoBuiltIn() &&
					 * (singleJointProduct.getBuiltInPromotion().getMinPremium() == null ||
					 * singleJointProduct.getBuiltInPromotion().getMinPremium() <=
					 * singleJointProduct.getPremiumRenewal().getAnnualBasePrem())) { if
					 * (qp.getClientESA() != null) {
					 * promotionsHelper.applyPromotion(singleJointProduct, promo,
					 * qp.getClientESA().getActualAge()); } else {
					 * promotionsHelper.applyPromotion(singleJointProduct, promo); } }
					 */
					finalProducts.add(singleJointProduct);
				}

				// reset Enhanced Face Amt
//                qp.setEnhancedFaceAmount(null);
			} // product loops

			////////////////////////
			//
			// Build the drop downs for page 2
			// clear cache of product and benefit types used by the filters
			// we clear it regardless of the Quote mode : single, multi or iq4Mode ==
			//////////////////////// IQ4Modes.Ranking
//            List<ProductType> productTypes = qp.getProductTypes();
			List<Integer> benefitTypes = qp.getBenefitTypes();
			List<Integer> uwTypes = qp.getUwTypes();
			List<String> healthClasses = qp.getHealthClasses();

//            productTypes.clear();
			benefitTypes.clear();
			uwTypes.clear();
			healthClasses.clear();

			// rebuild the Product and Benefit types lists and filter the products out
			for (Product tmpProduct : finalProducts) {
				// fetch all Type details
				com.insurfact.iq.domain.ProductType type = tmpProduct.getProductType();

				tmpProduct.setProductType(productFacade.populateGenericTypes(type));

				// Build and Update type caches
//                if(!productTypes.contains(tmpProduct.getProductType() )){
//                    productTypes.add(tmpProduct.getProductType());
//                }
				if (!benefitTypes.contains(tmpProduct.getInsuranceBen())) {
					benefitTypes.add(tmpProduct.getInsuranceBen());
				}

//                if(!healthClasses.contains(tmpProduct.getHealthClass().getGlobalClass())){
//                    healthClasses.add(tmpProduct.getHealthClass().getGlobalClass() );
//                }
				if (!uwTypes.contains(tmpProduct.getUwtype())) {
					uwTypes.add(tmpProduct.getUwtype());
				}

				// reset product eligibility
				tmpProduct.setEligible(false);

			} // product(s) loop

			// set the drop downs data
//            qp.setProductTypes(productTypes);
			qp.setBenefitTypes(benefitTypes);

			// reset to multiple periods
			qp.setQuiteMode(false);
			qp.setOnePeriodOnly(false);

//            List<com.insurfact.iq.domain.Product> toDelete = new ArrayList<>();
			// filter the products by profile
			// List<Integer> allProductsAvailable =
			// profileSelectionFacade.findAllProducts(applicationBean.getUsers().getProfile().getProfileIntId());
//            List<Integer> allProductsAvailable = qp.getProductFilter();
//            for (Product p : finalProducts) {
//                // System.out.println("1 " + p.getID());
//                if (!allProductsAvailable.contains(p.getID())) {
//                    //  System.out.println("2");
//                    toDelete.add(p);
//                }
//                /*else {
//                    findPromotion(p);
//                    if (p != null) {
//                        if (p.hasPromoBuiltIn() && (p.getBuiltInPromotion().getMinPremium() == null || p.getBuiltInPromotion().getMinPremium() <= p.getPremiumRenewal().getAnnualBasePrem())) {
//                            p.setPromotions(p.getPromotions());
//                            promo = null;
//                            promotionsHelper.applyPromotion(p, promo, qp.getClient().getActualAge());
//                        }
//                    }
//                }*/
//            }
//            finalProducts.removeAll(toDelete);
			// set Final products
			qp.setProducts(finalProducts);

			// reset
			qp.setReferenceProduct(null);
			qp.setCompanyID(0);
			qp.setProductID(0);

			////////////////////////
			//
			// sort final list
			qp.setProductSortBy(1); // set default to Annual Premium

			qp.setFilteredProducts(resort(finalProducts));

			// qp.setInsuranceBen(2);
			refreshFilteredProducts();

			// reset initial face amt
			qp.setFaceAmt(initialFaceAmount);
		} catch (Exception e) {
			e.printStackTrace();

		}

		if (qp.getProducts() == null || qp.getProducts().isEmpty()) {
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + "*productsWithPercentage = getProductsCombos()* "
						+ "returned null or empty list.");
			}

			return response;
		}

//        qp.setEnhancedFaceAmount(null);
		qp.setFaceAmt(initialFaceAmount);
		qp.setOnePeriodOnly(false);// forst for(

		int counter = 0;
		StringBuilder buf = new StringBuilder("[ ");

		try {
			Collections.sort(finalProducts, new TotalAnnComparator());

			for (Product productB : finalProducts) {

				int levDecr = 0;
				String coverageType = "A"; // default A for all

				if (productB.getInsuranceBen() != 0) {
					levDecr = productB.getInsuranceBen();
				}
				if (levDecr == 2) {
					coverageType = "L"; // L=level D=decreasing
				} else {
					coverageType = "D"; // L=level D=decreasing
				}

				int convertibleAge = productB.getLastConvertibleAge();
				int payableAge = productB.getAgePayable();

				boolean isFamilyHistory1 = false;
				boolean isFamilyHistory2 = false;

				boolean isCigarretSmoker1 = false;
				boolean isCigarretSmoker2 = false;

//                String classFlag1 = "";
//                String classFlag2 = "";
//
//                String healthClass1 = "";
//                String healthClassFr1 = "";
//
//                String healthClass2 = "";
//                String healthClassFr2 = "";
//
//                String lowMonths1 = "";
//                String lowMonths2 = "";
//
//                List<HealthClass> cliHealthClass1 = productB.getClientHealthClasses().get(0);
//                List<HealthClass> cliHealthClass2 = productB.getClientHealthClasses().get(1);
//                for (HealthClass cliHC : cliHealthClass1) {
//                    healthClass1 = cliHC.getName();
//                    healthClassFr1 = cliHC.getNameFr();
//                    isCigarretSmoker1 = cliHC.isCigarretSmoker();
//                    isFamilyHistory1 = cliHC.isFamilyHistory();
//                    classFlag1 = cliHC.getDescription();
//                    lowMonths1 = cliHC.getNonSmokerMonths() + "";
//                }
//
//                updateJointUWClassses(1);
//
//                for (HealthClass cliHC : cliHealthClass2) {
//                    healthClass2 = cliHC.getName();
//                    healthClassFr2 = cliHC.getNameFr();
//                    isCigarretSmoker2 = cliHC.isCigarretSmoker();
//                    isFamilyHistory2 = cliHC.isFamilyHistory();
//                    classFlag2 = cliHC.getDescription();
//                    lowMonths2 = cliHC.getNonSmokerMonths() + "";
//                }
//                updateJointUWClassses(2);
				List<String> cliUndw1 = productB.getClientUnderwriting().get(0);
				List<String> cliUndwFr1 = productB.getClientUnderwritingFr().get(0);
				@SuppressWarnings("unused")
				List<String> cliUndw2 = productB.getClientUnderwriting().get(1);
				List<String> cliUndwFr2 = productB.getClientUnderwritingFr().get(1);
				String clientUW1 = "";
				String clientUWFr1 = "";

				String clientUW2 = "";
				String clientUWFr2 = "";

				for (String cliUW : cliUndw1) {
					if (!clientUW1.equals("")) {
						clientUW1 += ", ";
					}
					clientUW1 += cliUW;
				}

				for (String cliUW : cliUndw1) {
					if (!clientUW2.equals("")) {
						clientUW2 += ", ";
					}
					clientUW2 += cliUW;
				}

				for (String cliUW : cliUndwFr1) {
					if (!clientUWFr1.equals("")) {
						clientUWFr1 += ", ";
					}
					clientUWFr1 += cliUW;
				}

				for (String cliUW : cliUndwFr2) {
					if (!clientUWFr2.equals("")) {
						clientUWFr2 += ", ";
					}
					clientUWFr2 += cliUW;
				}

				/*
				 * isCigarretSmoker1 = productB.getHealthClasses().get(0).isCigarretSmoker();
				 * isFamilyHistory1 = productB.getHealthClasses().get(0).isFamilyHistory();
				 * classFlag1 = productB.getHealthClasses().get(0).getDescription();
				 * 
				 * isCigarretSmoker2 = productB.getHealthClasses().get(0).isCigarretSmoker();
				 * isFamilyHistory2 = productB.getHealthClasses().get(0).isFamilyHistory();
				 * classFlag2 = productB.getHealthClasses().get(0).getDescription();
				 */
				++counter;
				if (counter > 1) {
					buf.append(", ");
				}

				// quoteWizard.jointQP.singleProduct.clientUnderwriting[0]
				DateFormat df = new SimpleDateFormat("dd-MMM-YYYY");
				// BigDecimal percentage = product.getPercentageGainWithAgent().setScale(2,
				// RoundingMode.HALF_UP);
				double percentage = 0;
				String res = JsonGenerator.getResponseValuesJoint(counter, provCodeUp,
						Util.getStringFromDouble(productB.getProductMonthlyPremiumTotal()),
						Util.getStringFromDouble(productB.getProductAnnualPremiumTotal()), "" + percentage,
						convertibleAge + "", payableAge + "", multiQP.getClient().getUwClass().getEngdesc(),
						multiQP.getClient().getUwClass().getFredesc(), multiQP.getClient2().getUwClass().getEngdesc(),
						multiQP.getClient2().getUwClass().getFredesc(), clientUW1, clientUWFr1, clientUW2, clientUWFr2,
						df.format(multiQP.getClient().getFullBirthDate()),
						df.format(multiQP.getClient2().getFullBirthDate()), multiQP.getClient().getNearestAge(),
						multiQP.getClient().getActualAge(), multiQP.getClient2().getNearestAge(),
						multiQP.getClient2().getActualAge(), smokingUp1, smokingUp2, faceAmount, productB,
						multiQP.getClient().getGender(), multiQP.getClient2().getGender(),
						multiQP.getClient().getHealthClass(),
						Integer.toString(multiQP.getClient().getUwClass().getMonthNonSmoker()), isCigarretSmoker1,
						isFamilyHistory1, multiQP.getClient2().getHealthClass(),
						Integer.toString(multiQP.getClient2().getUwClass().getMonthNonSmoker()), isCigarretSmoker2,
						isFamilyHistory2, coverageType);
				buf.append(res);

				// }// if(referenceProduct.getMinimumAnnualRequired() <=
				// referenceProduct.getProductAnnualPremiumTotal()){//mininmum premium added
				// april 14 2021 -aa
			} // for

			if (counter > 0) {
				buf.append(" ]");
				response = buf.toString();
			} else {
				response = "\"no matching records\"";
			}

		} catch (Exception ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: Reading the results raised: " + ex);
			}
			// response = "\"Reading the resultsSet raised:
			// "+cleanStringForJson(ex.getMessage())+"\"";
			throw new InsurfactPrivateException("Reading the results raised " + ex + " at counter " + counter
			// +" - "+ex.getMessage()
			// + "\n " + LogUtil.getStackTrace(ex, 10)
			// + "\n sql = " + sql
			);
		} finally {

		}

		try {
			if (con != null) {
				con.close();
			}
		} catch (SQLException e) {
			System.out.println("error closing connection con line 667 Generic e=" + e.getMessage());
		}

		return response;
	}

	private Product quoteJointSingleProduct(QuoteParams qp) {

//        System.out.println("smasse 2017-3-17 QuoteWizard.quoteJointSingleProduct");
		Client client1 = qp.getClient();
		Client client2 = qp.getClient2();
		Client clientESA = qp.getClientESA();

		// save it for later - enhanced or min-req.
//        int originalFaceAmount = qp.getFaceAmt();
		Product product = null;

		Product refProduct = qp.getReferenceProduct();

		if (iq4Mode == IQ4Modes.SingleJointCompanyTestPlatform) {
			findPromotionToTest(refProduct);
		} else {
			findPromotion(refProduct);
		}

		String ageToUse = refProduct.getAgeToUse();

		qp.setProductsAgeToUse(ageToUse);

//        System.out.println("** quickJointQuoteSingle  : "+refProduct.getCompany().getName()+ "- calcMethod=" + refProduct.getJointCalcMethod());
		int cie = qp.getCompanyID();
		int pid = qp.getProductID();

		String jointCalcMethod = qp.getJointCalcMethod();

		qp.setRiderSelected(false);
//        qp.setRiders(new ArrayList<>());

		int age1 = (ageToUse.equals("N") ? client1.getNearestAge() : client1.getActualAge());
		int age2 = (ageToUse.equals("N") ? client2.getNearestAge() : client2.getActualAge());
		@SuppressWarnings("unused")
		int oldestAge = 0;

		// save face amt
		qp.setOriginalFaceAmount(qp.getFaceAmt());
//        System.out.println("*** save orginal faceAmt : "+ qp.getOriginalFaceAmount() + " faceAmt="+qp.getFaceAmt());

		// Product enhanced Product first
		if (refProduct.isEnhanced()) {

			// calc and set enhanced faceAmoimt
			enhancedProductFacade.processEnhancedProduct(qp, refProduct);

			if (refProduct.getEnhancedFaceAmount() > 0) {
				Integer enhancedFaceAmount = refProduct.getEnhancedFaceAmount();

				Band band = refProduct.getBandForDetails(client1.getActualAge(), client1.getGender(),
						client1.isSmoker(), qp.getFaceAmt());
				Band band2 = refProduct.getBandForDetails(client1.getActualAge(), client1.getGender(),
						client1.isSmoker());
				Integer minMaxFaceAmount = null;

				if (band != null) {
					minMaxFaceAmount = band.getMinMaxFaceAmount();
					qp.setMinMax(band2.getMinMax());
				} else {
					qp.setMinMax(null);
				}

				// set new face amount
				qp.setFaceAmt(enhancedFaceAmount);// EMA-Jan 29th 2017

				if (minMaxFaceAmount != null) {
					int tmp = Math.abs((enhancedFaceAmount) - minMaxFaceAmount);

					if (tmp >= 0 && tmp <= 1000) {
//                                qp.setOriginalFaceAmount(minMaxFaceAmount /*+ 1000*/); // EMA-Jan 29th 2017
						qp.setFaceAmt(minMaxFaceAmount); // EMA-Jan 29th 2017
					}
				}
//                System.out.println("***  New enhanced face amount ="+ enhancedFaceAmount + " for " + refProduct ); 
			}
		}

		boolean enhancedWasLow = false;
		// Integer tmpFaceAmtEnhanced = 0;

		if (refProduct.isEnhanced()) {
			if (refProduct.getEnhancedFaceAmount() < qp.getMinMax().getMin()) {
				System.out.println("entering to min - enhanced");

				enhancedWasLow = true;
				int originalFa = qp.getOriginalFaceAmount();

				System.out.println("originalFa: " + originalFa);

				int newEnhanced = qp.getMinMax().getMin();

				System.out.println("newEnhanced: " + qp.getMinMax().getMin());

				if ((qp.getOriginalFaceAmount() - newEnhanced) < 1000) {
					int val = newEnhanced - qp.getOriginalFaceAmount();
					val = 1000 - val;
					qp.setFaceAmt(newEnhanced);
					qp.setOriginalFaceAmount(qp.getOriginalFaceAmount() + val);
				} else {
					qp.setFaceAmt(newEnhanced);
				}

				refProduct.setEnhancedFaceAmount(newEnhanced);

			}
		}

		// Calculate 'Age Diffence' Joint product without rider(s)
		if (jointCalcMethod.equals("A")) {

			// quote the Joint Product with normal Joint Code
			System.out.println("AAAAAAAA");
			System.out.println("qp.getMinMax().getMin(): " + qp.getMinMax());
			product = quoteFacade.jointSingleQuote(qp);

			System.out.println("A: " + product);

			if (product == null) {
				System.out.println("#### jointQuoteSingle (A) -- ERROR WHILE QUOTING...");

				// reset face amt
//                System.out.println("*** restore orginal faceAmt : "+ qp.getOriginalFaceAmount() + " faceAmt="+qp.getFaceAmt());
				qp.setFaceAmt(qp.getOriginalFaceAmount());

				return null;
			}

			if (product.getPremiumRenewal().isUsingMinimumAnnualPremium()) {
				System.out.println("#### jointQuoteSingle  -- PREMIUM BELOW MIN PREMIUM REQ...");
				product.setMinimumAnnualRequired(
						refProduct.getMinimumAnnualRequired(client1.getClassId(), age1, qp.getFirstFa()));

				// reset face amt
//                System.out.println("*** restore orginal faceAmt : "+ qp.getOriginalFaceAmount() + " faceAmt="+qp.getFaceAmt());
				qp.setFaceAmt(qp.getOriginalFaceAmount());

				return product;
			}

			// get underwriting details based on prod specs and faceamt; one list for each
			// client;
			List<String> underwritingList = quoteFacade.getUnderwriting(cie, product.getClientUnderwritingGroupID(0),
					qp.getOriginalFaceAmount(), age1, Character.toString(qp.getClient().getGender()));
			product.setClientUnderwriting(0, underwritingList);

			underwritingList = quoteFacade.getUnderwritingFr(cie, product.getClientUnderwritingGroupID(0),
					qp.getOriginalFaceAmount(), age1, Character.toString(qp.getClient().getGender()));
			product.setClientUnderwritingFr(0, underwritingList);

			underwritingList = quoteFacade.getUnderwriting(cie, product.getClientUnderwritingGroupID(1),
					qp.getOriginalFaceAmount(), age2, Character.toString(qp.getClient2().getGender()));
			product.setClientUnderwriting(1, underwritingList);

			underwritingList = quoteFacade.getUnderwritingFr(cie, product.getClientUnderwritingGroupID(1),
					qp.getOriginalFaceAmount(), age2, Character.toString(qp.getClient2().getGender()));
			product.setClientUnderwritingFr(1, underwritingList);

		}

		/// ****///
		// Calculate 'Equivalence' Joint product without rider(s)
		if (jointCalcMethod.equals("E") || jointCalcMethod.equals("G")) {

			if (clientESA == null) {
				System.out.println("@@@@ Convertion to  EAS Client FAILED!! ");

				// reset face amt
//                System.out.println("*** restore orginal faceAmt : "+ qp.getOriginalFaceAmount() + " faceAmt="+qp.getFaceAmt());
				qp.setFaceAmt(qp.getOriginalFaceAmount());

				return null;
			}

			QuoteParams tmpParams = new QuoteParams();

			tmpParams.initSingleMode();

			tmpParams.setReferenceProduct(refProduct);

			// La Capitale Logic for Joint Product calculated as Single Quote with ASE
			// calculated Client and using oldest age of the 2 clients
//            if(qp.getCompanyID() == 602 &&  refProduct.getJointType() == 'F'){
//                oldestAge = Math.max(age1,age2);
//                tmpParams.setOldestAge(oldestAge);
//            }
			if (iq4Mode == IQ4Modes.JointMultiCompany) {
				tmpParams.setOnePeriodOnly(true);
			}

			tmpParams.setFaceAmt(qp.getFaceAmt());
			tmpParams.setOriginalFaceAmount(qp.getOriginalFaceAmount());
			tmpParams.setProductsAgeToUse(refProduct.getAgeToUse());

//            client3.setSmokeCode(band.getSmokeCode());
			tmpParams.setClientESA(clientESA);

			tmpParams.setProductID(qp.getProductID());
			tmpParams.setCompanyID(qp.getCompanyID());

			product = null;

			// quote the Joint Product as Single Product
			product = quoteFacade.singleProductQuote(tmpParams, null);

			System.out.println("E or G: " + product);

			if (product == null) {
				System.out.println("####jointQuoteSingle (E-G)  -- ERROR WHILE QUOTING...");

				// reset face amt
//                System.out.println("*** restore orginal faceAmt : "+ qp.getOriginalFaceAmount() + " faceAmt="+qp.getFaceAmt());
				qp.setFaceAmt(qp.getOriginalFaceAmount());

				return null;
			}

			if (product.getPremiumRenewal().isUsingMinimumAnnualPremium()) {
				System.out.println("####jointQuoteSingle  -- PREMIUM BELOW MIN PREMIUM REQ...");
				product.setMinimumAnnualRequired(
						refProduct.getMinimumAnnualRequired(client1.getClassId(), age1, qp.getFirstFa()));
				// reset face amt
//                System.out.println("*** restore orginal faceAmt : "+ qp.getOriginalFaceAmount() + " faceAmt="+qp.getFaceAmt());
				qp.setFaceAmt(qp.getOriginalFaceAmount());

				return product;
			}

			// set iq4Mode == IQ4Modes.JointSingleCompany specific values back into the
			// SingleQuoted product
			product.setJointAgeToDisplay(refProduct.getJointAgeToDisplay());
			product.setJointCalcMethod(refProduct.getJointCalcMethod());
			product.setJointType(refProduct.getJointType());

			Band band1 = refProduct.getBandForDetails(age1, client1.getGender(), !client1.isNonSmoker(),
					qp.getFaceAmt(), client1.getClassId());
			Band band2 = refProduct.getBandForDetails(age2, client2.getGender(), !client2.isNonSmoker(),
					qp.getFaceAmt(), client2.getClassId());

			// get underwriting details based on prod specs and faceamt; one list for each
			// client;
			Iterator<String> iteratorL;
			if (band1 != null) {
				List<String> underwritingList = quoteFacade.getUnderwriting(cie, band1.getUnderwritingGroup(),
						qp.getOriginalFaceAmount(), age1, Character.toString(qp.getClient().getGender()));
				iteratorL = underwritingList.iterator();
				while (iteratorL.hasNext()) {
					String next = iteratorL.next();
					if (next.isEmpty()) {
						iteratorL.remove();
					}
				}
				product.setClientUnderwriting(0, underwritingList);

				underwritingList = quoteFacade.getUnderwritingFr(cie, band1.getUnderwritingGroup(),
						qp.getOriginalFaceAmount(), age1, Character.toString(qp.getClient().getGender()));
				iteratorL = underwritingList.iterator();
				while (iteratorL.hasNext()) {
					String next = iteratorL.next();
					if (next.isEmpty()) {
						iteratorL.remove();
					}
				}
				product.setClientUnderwritingFr(0, underwritingList);
			}

			if (band2 != null) {
				List<String> underwritingList = quoteFacade.getUnderwriting(cie, band2.getUnderwritingGroup(),
						qp.getOriginalFaceAmount(), age2, Character.toString(qp.getClient2().getGender()));
				iteratorL = underwritingList.iterator();
				while (iteratorL.hasNext()) {
					String next = iteratorL.next();
					if (next.isEmpty()) {
						iteratorL.remove();
					}
				}
				product.setClientUnderwriting(1, underwritingList);

				underwritingList = quoteFacade.getUnderwritingFr(cie, band2.getUnderwritingGroup(),
						qp.getOriginalFaceAmount(), age2, Character.toString(qp.getClient2().getGender()));
				iteratorL = underwritingList.iterator();
				while (iteratorL.hasNext()) {
					String next = iteratorL.next();
					if (next.isEmpty()) {
						iteratorL.remove();
					}
				}
				product.setClientUnderwritingFr(1, underwritingList);
			}
		}

		// Calculate 'Percentage' Joint product without rider(s)
		if (jointCalcMethod.equals("P")) {

			// quote the Joint Product with normal Joint Code
			product = quoteFacade.jointSingleQuote(qp);

			System.out.println("P: " + product);
			// System.out.println("PP: " + product.getClientHealthClasses().get(0));

			if (product == null) {
				System.out.println("#### jointQuoteSingle (P)  -- ERROR WHILE QUOTING...");

				// reset face amt
//                System.out.println("*** restore orginal faceAmt : "+ qp.getOriginalFaceAmount() + " faceAmt="+qp.getFaceAmt());
				qp.setFaceAmt(qp.getOriginalFaceAmount());

				return null;
			}

			if (product.getPremiumRenewal().isUsingMinimumAnnualPremium()) {
				System.out.println("####jointQuoteSingle  -- PREMIUM BELOW MIN PREMIUM REQ...");
				product.setMinimumAnnualRequired(
						refProduct.getMinimumAnnualRequired(client1.getClassId(), age1, qp.getFirstFa()));
				// reset face amt
//                System.out.println("*** restore orginal faceAmt : "+ qp.getOriginalFaceAmount() + " faceAmt="+qp.getFaceAmt());
				qp.setFaceAmt(qp.getOriginalFaceAmount());

				return product;
			}

			// get underwriting details based on prod specs and faceamt; one list for each
			// client;
			List<String> underwritingList = quoteFacade.getUnderwriting(cie, product.getClientUnderwritingGroupID(0),
					qp.getOriginalFaceAmount(), age1, Character.toString(qp.getClient().getGender()));
			product.setClientUnderwriting(0, underwritingList);

			underwritingList = quoteFacade.getUnderwritingFr(cie, product.getClientUnderwritingGroupID(0),
					qp.getOriginalFaceAmount(), age1, Character.toString(qp.getClient().getGender()));
			product.setClientUnderwritingFr(0, underwritingList);

			underwritingList = quoteFacade.getUnderwriting(cie, product.getClientUnderwritingGroupID(1),
					qp.getOriginalFaceAmount(), age2, Character.toString(qp.getClient2().getGender()));
			product.setClientUnderwriting(1, underwritingList);

			underwritingList = quoteFacade.getUnderwritingFr(cie, product.getClientUnderwritingGroupID(1),
					qp.getOriginalFaceAmount(), age2, Character.toString(qp.getClient2().getGender()));
			product.setClientUnderwritingFr(1, underwritingList);

		}

		if (iq4Mode == IQ4Modes.JointMultiCompany) {
			qp.setOnePeriodOnly(false);
		}

		qp.setCompanyID(cie);
		qp.setProductID(pid);

		if (product != null) {
			// set enhanced - original face amount
			// product.setOriginalFaceAmount(qp.getOriginalFaceAmount());
			if (!enhancedWasLow) {
				System.out.println(" !enhancedWasLow ");
				product.setOriginalFaceAmount(qp.getOriginalFaceAmount());
			} else {
				System.out.println(" enhancedWasLow :" + qp.getOriginalFaceAmount());
				product.setWasEnhanced(true);
				product.setOriginalFaceAmount(qp.getOriginalFaceAmount());
			}
			product.setEnhancedFaceAmount(refProduct.getEnhancedFaceAmount());
		}

		// set Single Joint Quoted Product
		qp.setSingleProduct(product);

//        System.out.println("#### Joint Product "+ product.getName() + " with values="+ product.isWithValues());
		// reset face amt
//        System.out.println("*** restore orginal faceAmt : "+ qp.getOriginalFaceAmount() + " faceAmt="+qp.getFaceAmt());
		qp.setFaceAmt(qp.getOriginalFaceAmount());
		if (product != null && product.getJointAnnualMethod() != null) {
			// System.out.println("product: "+product);
			// System.out.println("product.getJointAnnualMethod():
			// "+product.getJointAnnualMethod());
			if (product.getJointAnnualMethod().equalsIgnoreCase("aj_7oldagecheck")) {
				System.out.println("CREATE NEW QUOTE TO SAME PRODUCT WITH OLDER CLIENT");
				Client older = Client.cloneClient(qp.getClient());

				System.out.println("qp.getClient(): " + qp.getClient());

				if (qp.getClient2().getActualAge() > older.getActualAge()) {
					older = Client.cloneClient(qp.getClient2());
				}
				QuoteParams clonedQp = QuoteParams.clone(qp);
				clonedQp.setClient(older);
				clonedQp.setClientESA(null);

				HealthClass tempClass = new HealthClass();

				System.out.println("older.getUwClass().getClassid(): " + older.getUwClass().getClassid());
				System.out.println(
						"qp.getClient().getUwClass().getClassid(): " + qp.getClient().getUwClass().getClassid());
				System.out.println(
						"qp.getClient()2.getUwClass().getClassid(): " + qp.getClient2().getUwClass().getClassid());

				tempClass.setID(older.getUwClass().getClassid());
				tempClass.setName(older.getUwClass().getEngdesc());
				tempClass.setNameFr(older.getUwClass().getFredesc());
				tempClass.setNonSmokerMonths(older.getUwClass().getMonthNonSmoker());
				tempClass.setGlobalClass(older.getUwClass().getGlobalClass());
				tempClass.setSmokeCode(older.getUwClass().getSmokeCode());

				List<HealthClass> listFakeClass = new ArrayList<>();
				listFakeClass.add(tempClass);

				product.setHealthClasses(listFakeClass);

				Product olderP = quoteSingleFromMulti(clonedQp, product);
				System.out.println("olderP1: " + olderP);
				if (olderP != null) {
					int totalRenewals = product.getPremiumRenewals().size();
					if (olderP.getPremiumRenewals().size() < totalRenewals) {
						totalRenewals = olderP.getPremiumRenewals().size(); // has to be the min to not get a array out
																			// of index
					}
					for (int i = 0; i < totalRenewals; i++) {
						if (olderP.getPremiumRenewals().get(i).getAnnualBasePrem() > product.getPremiumRenewals().get(i)
								.getAnnualBasePrem()) {
							product.getPremiumRenewals().get(i)
									.setAnnualBasePrem(olderP.getPremiumRenewals().get(i).getAnnualBasePrem());
							product.getPremiumRenewals().get(i)
									.setMonthlyBasePrem(olderP.getPremiumRenewals().get(i).getMonthlyBasePrem());
						}
					}
				}
				System.out.println("qp.getClient(): " + qp.getClient());
				System.out.println("com.insurfact.avue.navigation.quote.QuoteWizard.quoteJointSingleProduct()");

			}
		}

		return product;
	}

	private void updateMinMaxFaceAmount() {

		QuoteParams qp = getMultiQP();

		Client client1 = qp.getClient();

		Product referenceProduct = qp.getReferenceProduct();
		int min = 0, max = 0;
		// System.out.println("QW 7100 referenceProduct.getAgeToUse= " +
		// referenceProduct.getAgeToUse());
		int age = client1.getNearestAge();
		if (referenceProduct.getAgeToUse() != null) {
			if (referenceProduct.getAgeToUse().equalsIgnoreCase("A")) {
				age = client1.getActualAge();
			}
		} else {
			System.out.println(" 7074 QW no age");
		}

		if (referenceProduct != null) {
			min = referenceProduct.getMinAmountForAgeGenderSmoker(age, client1.getGender(), client1.isSmoker());
			max = referenceProduct.getMaxAmountForAgeGenderSmoker(age, client1.getGender(), client1.isSmoker());
		}

//        System.out.println("**** updateMinMaxFaceAmount() min="+min +" max="+max );
		MinMax minMax = new MinMax(min, max);

		qp.setMinMax(minMax);
	}

	private Integer processMinimumPremiumRequirement(QuoteParams qp) {

		Client client = qp.getClient();

		Product refProduct = qp.getReferenceProduct();

		int age = 0;
		if (refProduct.getAgeToUse().equalsIgnoreCase("N")) {
			age = qp.getClient().getNearestAge();
		}

		if (refProduct.getAgeToUse().equalsIgnoreCase("A")) {
			age = qp.getClient().getActualAge();
		}

		double minPremiumReq = refProduct.getMinimumAnnualRequired(client.getClassId(), age, qp.getFaceAmt());
		double mminPremiumReq = refProduct.getMinimumMonthlyRequired(client.getClassId(), age, qp.getFaceAmt());

		System.out.println(
				"*** Single Product -  annual premium min req=" + minPremiumReq + " faceAmt=" + qp.getFaceAmt());
		System.out.println(
				"*** Single Product -  month  premium min req=" + mminPremiumReq + " faceAmt=" + qp.getFaceAmt());
		List<Band> bands = refProduct.getBands(client.getClassId());
		Collections.sort(bands, new MinimumBandComparator());

		Band activeBand = refProduct.getBandForDetails(age, client.getGender(), client.isSmoker(), qp.getFaceAmt(),
				client.getClassId());
		Band nextBand = null;

		// System.out.println("2784 QW - Bands for classId:" + client.getClassId() + "
		// rowkey=" + activeBand.getRowrey() + " activeBand:" +
		// activeBand.getSmokeCode() + activeBand.toString());

		@SuppressWarnings("unused")
		int activeBandId = 0;
		int nextBandId = 0;

		int i = 0;
		for (Band band : bands) {
			if (band.getGender() != client.getGender()) {
				continue;
			}

//            System.out.println("QW 2794 band: "+band.toString());
			if (band.getRowrey() == activeBand.getRowrey()) {
				activeBandId = i;
				nextBandId = i;
			}
			i++;
		}

		// System.out.println("QW 2803 Active Band (" + activeBandId + "): " +
		// activeBand.toString());
		// calculate the min face amount to match the min. premium req. using the same
		// Band
		BandRate bandRate = productFacade.getIQ4BandRate(activeBand.getRowrey(), age, client.getClassId());

		int tmpFaceAmt = quoteFacade.calcReverseFaceAmount(bandRate, minPremiumReq);

		System.out.println("QW 2811 Calculated face amount =" + tmpFaceAmt + " band_rate=" + bandRate);

		// inside the same band, but higher face amount
		if (activeBand.getMinMax().inside(tmpFaceAmt)) {

			return tmpFaceAmt;
		} // not inside the band.. let's upgrade to the next
		else {

			boolean tryUpgrade = true;

			while (tryUpgrade) {

				nextBandId++; // upgrading band
				nextBand = null;

				if ((nextBandId) <= (bands.size() - 1)) {
					nextBand = bands.get(nextBandId);

					if (nextBand.getGender() != client.getGender()) {
						continue;
					}
				}

				if (nextBand == null) {
					break;
					// bail out!!!
				} else {

					// System.out.println("QW 2841 Next Band (" +nextBandId+ "): " +
					// nextBand.toString());
					// fetch new rates for next band...
					bandRate = null;
					bandRate = productFacade.getIQ4BandRate(nextBand.getRowrey(), age, client.getClassId());

					if (bandRate != null) {
						// System.out.println(bandRate.toString() + "["+bandRate.getSex() +"]");

						tmpFaceAmt = quoteFacade.calcReverseFaceAmount(bandRate, minPremiumReq);
						// System.out.println("QW 2851 Calculated face amount ="+tmpFaceAmt + "
						// band_rate="+bandRate);

						/// Inside the new band!!
						if (nextBand.getMinMax().inside(tmpFaceAmt)) {
							// System.out.println("QW 2855 Final Face amount ="+tmpFaceAmt);
							return tmpFaceAmt;
						}
					} else {
						break;
						// bail out!!!
					}
				}

			} // end while loop over bands
		}

		return null;
	}

	private void findBuiltinsRiders(Product product, Date birthdate, boolean smoker) {

		QuoteParams qp = getMultiQP();

//        System.out.println("*** findBuiltinsRiders for : " + product);
		List<Rider> includedRiders = product.getIncludedRiders();

		if (includedRiders == null) {
			includedRiders = new ArrayList<>();
		} else {
			includedRiders.clear();
		}

//        System.out.println( "**** getAvailableRiders prodId="+product.getID()+" actual="+qp.getClient().getActualAge()+ " smoker="+smoker );
		includedRiders = quoteFacade.getBuiltinsRiders(qp, product);

		if (includedRiders != null) {
			Iterator<Rider> iterator = includedRiders.iterator();

			while (iterator.hasNext()) {

				Rider rider = iterator.next();

				// min fa max fa
				if ((qp.getFaceAmt() < rider.getMinFaceAmt())
						|| (rider.getMaxFaceAmt() != 0 && qp.getFaceAmt() > rider.getMaxFaceAmt())) {
					System.out.println("@@@@NEW id(" + rider.getID() + ") - " + rider.getName()
							+ " was excluded from the selection due to MIN  MAX face amount constraint : "
							+ rider.getMinFaceAmt() + " + " + +rider.getMaxFaceAmt());
					iterator.remove();
					continue;
				}

				if (qp.getFaceAmt() < rider.getMinBaseFaceAmt()) {
					System.out.println("@@@@ id(" + rider.getID() + ") - " + rider.getName()
							+ " was excluded from the selection due to MIN  Base constraint : "
							+ rider.getMinBaseFaceAmt());
					iterator.remove();
					continue;
				}

				if ((qp.getFaceAmt() > rider.getMaxBaseFaceAmt() && rider.getMaxBaseFaceAmt() != 0)) {
					System.out.println("@@@@ id(" + rider.getID() + ") - " + rider.getName()
							+ " was excluded from the selection due to MAX Base constraint : "
							+ rider.getMaxBaseFaceAmt());
					iterator.remove();
					continue;
				}

				if (rider.getAvailableFor() != null && !rider.getAvailableFor().equalsIgnoreCase("B")
						&& !rider.getAvailableFor().equalsIgnoreCase("O")
						&& !rider.getAvailableFor().equalsIgnoreCase("I")
						&& !rider.getAvailableFor().equalsIgnoreCase("S")) {
					iterator.remove();
					continue;
				}

				if (selectionRiderId > 0) {
					if (rider.getID() == 275 && selectionRiderId == 276) {
						iterator.remove();
						continue;
					}
					if (rider.getID() == 276 && selectionRiderId == 275) {
						iterator.remove();
						continue;
					}
				}
			}
		}

		// sorting
		if (includedRiders != null) {
			List<Rider> thoseB = new ArrayList<>();
			List<Rider> thoseNormal = new ArrayList<>();
			for (Rider r : includedRiders) {
				if (r.getAvailableFor() == null) {
					thoseNormal.add(r);
				} else {
					if (r.getAvailableFor().equalsIgnoreCase("B")) {
						thoseB.add(r);
					} else {
						thoseNormal.add(r);
					}
				}
			}

//            Collections.sort(thoseB, new IncludedRidersOrderComparator());
//            Collections.sort(thoseNormal, new IncludedRidersOrderComparator());
			includedRiders.clear();
			includedRiders.addAll(thoseNormal);
			includedRiders.addAll(thoseB);
		}

		product.setIncludedRiders(includedRiders);

	}

	private Integer roundNearest(Integer amount, Integer increment) {

		if (amount % increment == 0) {
			return amount;
		}

		Integer result = 0;

		int mod = (int) (amount / increment);

		int start = mod * increment;
		int half = start + (increment / 2) - 1;

//        System.out.println("amt="+amount+ " mod="+mod + " start="+start + " half="+ half);
		if (amount >= start && amount <= half) {
			result = start;
		} else {
			result = start + increment;
		}

		return result;
	}

	private Integer roundCeilling(Integer amount, Integer increment) {

		if (amount % increment == 0) {
			return amount;
		}

		Integer result = 0;

		int mod = (int) (amount / increment);

		int start = mod * increment;

		result = start + increment;

		return result;
	}

	public Product quoteSingleFromMulti(QuoteParams qp, Product prod) {

		Client client = qp.getClient();
		// save face amt
		qp.setOriginalFaceAmount(qp.getFaceAmt());
//        System.out.println("*** quoteSingleFromMulti() save orginal faceAmt : "+ qp.getOriginalFaceAmount() + " faceAmt="+qp.getFaceAmt());

		// get product details, rates and PremiumRenewals
		qp.setCompanyID(prod.getCompany().getID());
		qp.setProductID(prod.getID());
		qp.setProductsAgeToUse(prod.getAgeToUse());

		int classId = prod.getHealthClass().getID();
		// System.out.println("classId WHYYYYYY: " + classId);
		client.setClassId(classId);
		// System.out.println("classId WHYYYYYY2: " + client.getClassId());

		@SuppressWarnings("unused")
		boolean wasRoundedTemp = prod.isWasrounded();
		@SuppressWarnings("unused")
		boolean usingMinPremTemp = prod.getPremiumRenewal().isUsingMinimumAnnualPremium();

//        qp.setEnhancedFaceAmount(null);
		// reset flag for using min. req. premium new face amount
		qp.setUsingMinReqPremiumFaceAmt(false);

		// fetch product info with
		// System.out.println("prod.getID(): " + prod.getID());
		// System.out.println("client.getGender(): " + client.getGender());
		// System.out.println("client.getNearestAge(): " + client.getNearestAge());
		int ageToUse = client.getNearestAge();
		if (prod.getAgeToUse().equalsIgnoreCase("A")) {
			ageToUse = client.getActualAge();
		}
		Product refProduct = productFacade.getIQ4LifeProductDetail(prod.getID(), client.getGender(), ageToUse);
		// System.out.println("refProduct: " + refProduct);
		// refProduct.setFaceAmountRecheck(prod.getFaceAmountRecheck());

		// System.out.println("classId WHYYYYYY3: " + client.getClassId());
		// use the display product as template for the iq4Mode == IQ4Modes.Ranking
		// product selection on page #2
		qp.setReferenceProduct(refProduct);

		// Product enhanced Product first
		if (refProduct.getCompanyID() != 0) {
			System.out.println("line 9822 QuoteWizard -- " + refProduct.getCompanyID());
		}

		int age = client.getActualAge();
		if (refProduct.getAgeToUse().equalsIgnoreCase("N")) {
			age = client.getNearestAge();
		}

		if (refProduct.isEnhanced()) {

			enhancedProductFacade.processEnhancedProduct(qp, refProduct);

			if (refProduct.getEnhancedFaceAmount() > 0) {
				Integer enhancedFaceAmount = refProduct.getEnhancedFaceAmount();

				// System.out.println("qw 12959 enhancedFaceAmount: " + enhancedFaceAmount);
				Band band = refProduct.getBandForDetails(age, client.getGender(), client.isSmoker(), qp.getFaceAmt());
				Band band2 = refProduct.getBandForDetails(age, client.getGender(), client.isSmoker());
				Integer minMaxFaceAmount = null;

				// System.out.println("band: " + band);
				// set new face amount
				qp.setFaceAmt(enhancedFaceAmount); // EMA-Jan 29th 2017

				if (band != null) {
					qp.setMinMax(band.getMinMax());
					minMaxFaceAmount = band.getMinMaxFaceAmount();
					// System.out.println("band.getMinMax(): " + band.getMinMax());
				} else {
					if (band2 != null) {
						qp.setMinMax(band2.getMinMax());
						minMaxFaceAmount = band2.getMinMaxFaceAmount();
						// System.out.println("band2.getMinMax(): " + band2.getMinMax());
					} else {
						qp.setMinMax(null);
					}
				}

				if (minMaxFaceAmount != null) {
					int tmp = Math.abs((enhancedFaceAmount) - minMaxFaceAmount);

					if (tmp >= 0 && tmp <= 1000) {
//                                qp.setOriginalFaceAmount(minMaxFaceAmount /*+ 1000*/); // EMA-Jan 29th 2017
						qp.setFaceAmt(minMaxFaceAmount); // EMA-Jan 29th 2017
					}
				}

				// set new face amount
//                    qp.setFaceAmt(enhancedFaceAmount);
//                System.out.println("***  quoteSingleFromMulti() New enhanced face amount ="+ enhancedFaceAmount + " for : "+ refProduct);
			}
		}

		boolean enhancedWasLow = false;
		// Integer tmpFaceAmtEnhanced = 0;

		updateMinMaxFaceAmount();

		// System.out.println("classId WHYYYYYY4: " + client.getClassId());

		/*
		 * System.out.println("qp.getMinMax().getMin(): " + qp.getMinMax().getMin());
		 * System.out.println("refProduct.getEnhancedFaceAmount(): " +
		 * refProduct.getEnhancedFaceAmount());
		 * System.out.println("refProduct.isEnhanced(): " + refProduct.isEnhanced());
		 */
		if (refProduct.isEnhanced()) {
			// System.out.println("qw 13009 entered to enchanced");
			if (refProduct.getEnhancedFaceAmount() <= qp.getMinMax().getMin()) {
				// System.out.println("entering to min - enhanced");

				enhancedWasLow = true;
				@SuppressWarnings("unused")
				int originalFa = qp.getOriginalFaceAmount();

				// System.out.println("originalFa: " + originalFa);
				int newEnhanced = qp.getMinMax().getMin();

				// System.out.println("newEnhanced: " + qp.getMinMax().getMin());
				if ((qp.getOriginalFaceAmount() - newEnhanced) < 1000) {
					int val = newEnhanced - qp.getOriginalFaceAmount();
					val = 1000 - val;
					qp.setFaceAmt(newEnhanced);
					qp.setOriginalFaceAmount(qp.getOriginalFaceAmount() + val);
				} else {
					qp.setFaceAmt(newEnhanced);
				}

				refProduct.setEnhancedFaceAmount(newEnhanced);

			}
		}

		// do min premium req. if there
		double minPremiumReq = refProduct.getMinimumAnnualRequired(classId, age, qp.getFaceAmt());

//        System.out.println("*** Single Product -  min annual premium="+ minPremiumReq);
		Integer tmpFaceAmt = null;

		// System.out.println("classId WHYYYYYY5: " + client.getClassId());
		if (minPremiumReq > 0d) {

			Band activeBand = refProduct.getBandForDetails(age, client.getGender(), client.isSmoker(), qp.getFaceAmt(),
					classId);
			BandRate bandRate = productFacade.getIQ4BandRate(activeBand.getRowrey(), age, classId);

			double premium = quoteFacade.calcBasePremium(bandRate, qp.getFaceAmt());
			// System.out.println("line 9864 premium=" + premium);
//            System.out.println("*** Single Product -  calc premium="+ premium);

			if (qp.getCompanyID() != 74) {
				if (premium < minPremiumReq) {

					// try to get a resolution for the needed faceamout vs. required min. premium
					tmpFaceAmt = processMinimumPremiumRequirement(qp);

					// here gazoo
					// fixFaceAmount(qp, null, refProduct.getBands(), activeBand);
					// if (tmpFaceAmt < qp.getGazooFaSuggestion() && qp.getGazooFaSuggestion() != 0)
					// {
//                System.out.println("*** Single Product -  calc min. faceAmt="+ tmpFaceAmt);
					if (tmpFaceAmt != null) {

						qp.setFaceAmt(tmpFaceAmt);

						// comment for testing: 2016-10-31 3:27pm
//                    qp.setOriginalFaceAmount(tmpFaceAmt);
						// set flag for using min. req. premium new face amount
						qp.setUsingMinReqPremiumFaceAmt(true);

					}
					/*
					 * } else { qp.setFaceAmt(qp.getGazooFaSuggestion());
					 * prod.setUsingGazooSugenstion(true); }
					 */
				}

				// check if product has face amount increment...
				if (prod.getFaceAmountIncrement() > 0) {

					Integer inc = prod.getFaceAmountIncrement();
					Integer result = null;

					if (qp.isUsingMinReqPremiumFaceAmt()) {
						result = roundCeilling(qp.getFaceAmt(), inc);
					} else {
						result = roundNearest(qp.getFaceAmt(), inc);
					}

					if (result != null) {
						// flag that was rounded
						if (!qp.getFaceAmt().equals(result)) {
							prod.setWasrounded(true);
						}
						qp.setFaceAmt(result);
						prod.setFaceAmount(result);
					}

//                System.out.println("!!! product had min-premium-req faceAmt recalc... now verify faceAmt Increment  : faceAmt="+qp.getFaceAmt() +" after increment ajustment="+result);
				}
			}
		} else {
			// check if product has face amount increment...
			if (prod.getFaceAmountIncrement() > 0) {

				Integer inc = prod.getFaceAmountIncrement();
				Integer result = null;

				result = roundNearest(qp.getFaceAmt(), inc);

				if (result != null) {
					// flag that was rounded
					if (!qp.getFaceAmt().equals(result)) {
						prod.setWasrounded(true);
					}
					qp.setFaceAmt(result);
					prod.setFaceAmount(result);
				}

//                System.out.println("### product had min-premium-req faceAmt recalc... now verify faceAmt Increment  : faceAmt="+qp.getFaceAmt() +" after increment ajustment="+result);
			}
		}

//        qp.setDebug(true);
		// Quote the product as SingleQuote
		/*
		 * System.out.println("classId WHYYYYYY6: " + client.getClassId());
		 * System.out.println("classId WHYYYYYY7: " + qp.getClient().getClassId());
		 * System.out.println("before the quoteFacade.singleProductQuote(qp, prod): " +
		 * prod);
		 */
		if (iq4Mode == IQ4Modes.SingleCompanyTestPlatform || iq4Mode == IQ4Modes.SingleJointCompanyTestPlatform) {
			// System.out.println("is here");
			findPromotionToTest(qp.getReferenceProduct());
		} else {
			// System.out.println("not good");
			findPromotion(qp.getReferenceProduct());
		}
		Product product = quoteFacade.singleProductQuote(qp, prod);
		// System.out.println("after the quoteFacade.singleProductQuote(qp, prod): " +
		// product);

		if (product != null) {

			product.setUsingGazooSugenstion(prod.isUsingGazooSugenstion());
			if (minPremiumReq > 0d) {
				product.setMinimumAnnualRequired(minPremiumReq);
			}
			// getContact Builtins Riders only when displaying product details or generating
			// pdf
			// if (!qp.isMultiQuoteMode()) {

			// get builtins
			findBuiltinsRiders(product, client.getFullBirthDate(), client.isSmoker());
			// }

			// here the lists are builded
			if (qp.getInsuranceTypeAbbr().equals("CRIT")) {

				List<ProductIllness> adultList = quoteFacade.findAdultProductIllnesses(refProduct);
				List<ProductIllness> childList = new ArrayList<>();
				if (qp.getClient().getActualAge() < 16) {
					childList = quoteFacade.findChildProductIllnesses(refProduct);
				}
				List<ProductIllness> earlyList = quoteFacade.findEarlyBenProductIllnesses(refProduct);

				// sort
//                if (language.isEnglish()) {
				Collections.sort(adultList, new ProductIllnessComparator());
				Collections.sort(childList, new ProductIllnessComparator());
				Collections.sort(earlyList, new ProductIllnessComparator());
//                } else {
//                    Collections.sort(adultList, new ProductIllnessFrenchComparator());
//                    Collections.sort(childList, new ProductIllnessFrenchComparator());
//                    Collections.sort(earlyList, new ProductIllnessFrenchComparator());
//                }

				refProduct.setAdultIllnesses(adultList);
				refProduct.setChildIllnesses(childList);
				refProduct.setEarlyBenIllnesses(earlyList);

				qp.setReferenceProduct(refProduct);
			}

			// save enhanced and original amount to product
			product.setEnhancedFaceAmount(refProduct.getEnhancedFaceAmount());
			if (!enhancedWasLow) {
				System.out.println(" !enhancedWasLow ");
				product.setOriginalFaceAmount(qp.getOriginalFaceAmount());
			} else {
				System.out.println(" enhancedWasLow :" + qp.getOriginalFaceAmount());
				product.setWasEnhanced(true);
				product.setOriginalFaceAmount(qp.getOriginalFaceAmount());
			}

			// set Min Premium Req flag
			if (minPremiumReq == product.getPremiumRenewal().getAnnualBasePrem()) {
				product.getPremiumRenewal().setUsingMinimumAnnualPremium(true);
			}

			if (tmpFaceAmt != null) {
//                product.setOriginalFaceAmount(originalFaceAmount);
				product.setFaceAmount(tmpFaceAmt);
				product.setClassId(classId);
			}

			// re enter the increment face amount
			if (prod.getFaceAmountIncrement() > 0) {
				product.setFaceAmountIncrement(prod.getFaceAmountIncrement());
				product.setFaceAmount(qp.getFaceAmt());
				product.setWasrounded(prod.isWasrounded());
			}

		} else {
			qp.setFaceAmt(qp.getOriginalFaceAmount());
		}

		// product.setUsingMinReqPremiumFaceAmt(usingMinPremTemp);
		// reset params
		qp.setCompanyID(0);
		qp.setProductID(0);
		qp.setProductsAgeToUse("");
		qp.getClient().setClassId(0);

		qp.setDebug(false);

		// reset face amt
//        System.out.println("*** quoteSingleFromMulti() restore orginal faceAmt : "+ qp.getOriginalFaceAmount() + " faceAmt="+qp.getFaceAmt());
		qp.setFaceAmt(qp.getOriginalFaceAmount());

		return product;
	}

	public void fixFaceAmount(QuoteParams qp, Product p, List<Band> bands, Band active) {

		boolean checking = true;
		Band newactive = null;
		int newFa;

		while (checking) {
			System.out.println("com.insurfact.gazoo.GazooQuote.fixFaceAmount()");
			System.out.println("qp.getOriginalFaceAmount(): " + qp.getOriginalFaceAmount());
			System.out.println("qp.getFaceAmount(): " + qp.getFaceAmt());

			if (iq4Mode != IQ4Modes.MultiCompany && iq4Mode != IQ4Modes.JointMultiCompany) {
				if (newactive != null) {
					System.out.println("gets inside newactive");
					System.out.println("newactive: " + newactive);
					if (!newactive.getMinMax().inside(qp.getFaceAmt())) {
						newactive = bands.get(bands.indexOf(newactive) + 1);
						qp.setGazooFaSuggestion(newactive.getMinMax().getMin());
						newFa = processMinimumPremiumRequirementG(qp, newactive);
						if (newFa == 0) {
							checking = false;
						} else {
							qp.setGazooFaSuggestion(newFa);
						}
					} else {
						checking = false;
					}
				} else {
					System.out.println("gets inside else");
					System.out.println("active: " + active);
					if (!active.getMinMax().inside(qp.getFaceAmt())) {
						newactive = bands.get(bands.indexOf(active) + 1);
						System.out.println("newactive: " + newactive);
						qp.setGazooFaSuggestion(newactive.getMinMax().getMin());
						newFa = processMinimumPremiumRequirementG(qp, newactive);
						if (newFa == 0) {
							checking = false;
						} else {
							qp.setGazooFaSuggestion(newFa);
						}
					} else {
						checking = false;
					}
				}
			} else {
				if (newactive != null) {
					System.out.println("gets inside newactive");
					System.out.println("newactive: " + newactive);
					if (!newactive.getMinMax().inside(p.getFaceAmount())) {
						newactive = bands.get(bands.indexOf(newactive) + 1);
						qp.setGazooFaSuggestion(newactive.getMinMax().getMin());
						newFa = processMinimumPremiumRequirementG(qp, newactive);
						if (newFa == 0) {
							checking = false;
						} else {
							qp.setGazooFaSuggestion(newFa);
						}
					} else {
						checking = false;
					}
				} else {
					System.out.println("gets inside else");
					System.out.println("active: " + active);
					if (!active.getMinMax().inside(p.getFaceAmount())) {
						newactive = bands.get(bands.indexOf(active) + 1);
						System.out.println("newactive: " + newactive);
						qp.setGazooFaSuggestion(newactive.getMinMax().getMin());
						newFa = processMinimumPremiumRequirementG(qp, newactive);
						if (newFa == 0) {
							checking = false;
						} else {
							qp.setGazooFaSuggestion(newFa);
						}
					} else {
						checking = false;
					}
				}
			}
		}

		System.out.println("qp.getGazooFaSuggestion(): " + qp.getGazooFaSuggestion());

	}

	private Integer processMinimumPremiumRequirementG(QuoteParams qp, Band active) {

		Client client = qp.getClient();

		Product refProduct = qp.getReferenceProduct();

		int age = 0;
		if (refProduct.getAgeToUse().equalsIgnoreCase("N")) {
			age = qp.getClient().getNearestAge();
		}

		if (refProduct.getAgeToUse().equalsIgnoreCase("A")) {
			age = qp.getClient().getActualAge();
		}

		double minPremiumReq = refProduct.getMinimumAnnualRequired(client.getClassId(), age, qp.getGazooFaSuggestion());
		double mminPremiumReq = refProduct.getMinimumMonthlyRequired(client.getClassId(), age,
				qp.getGazooFaSuggestion());

		System.out.println(
				"*** Single Product -  annual premium min req=" + minPremiumReq + " faceAmt=" + qp.getFaceAmt());
		System.out.println(
				"*** Single Product -  month  premium min req=" + mminPremiumReq + " faceAmt=" + qp.getFaceAmt());

		Band activeBand = active;
		System.out.println("93 gazoo - Bands for classId:" + client.getClassId() + " rowkey=" + activeBand.getRowrey()
				+ " activeBand:" + activeBand.getSmokeCode() + activeBand.toString());
		System.out.println("93 gazoo - Bands for classId:" + client.getClassId() + " rowkey=" + activeBand.getRowrey()
				+ " activeBand:" + activeBand.getSmokeCode() + activeBand.toString());

		System.out.println("gazoo 96 Active Band (" + activeBand.getMinMax() + "): " + activeBand.toString());
		System.out.println("gazoo 97 Active Band (" + activeBand.getRowrey() + "): ");
		System.out.println("gazoo 97 age (" + age + "): ");
		System.out.println("gazoo 97 productFacade (" + productFacade + "): ");
		// calculate the min face amount to match the min. premium req. using the same
		// Band
		BandRate bandRate = productFacade.getIQ4BandRate(activeBand.getRowrey(), age, client.getClassId());

		int tmpFaceAmt;
		if (bandRate != null) {
			tmpFaceAmt = quoteFacade.calcReverseFaceAmount(bandRate, minPremiumReq);
		} else {
			tmpFaceAmt = 0;
		}

		System.out.println("gazoo 103 Calculated face amount =" + tmpFaceAmt + " band_rate=" + bandRate);

		// inside the same band, but higher face amount
		return tmpFaceAmt;

	}

	private List<Product> getAvailableJointProducts(QuoteParams qp) throws Exception {

//        System.out.println("smasse 2017-3-17 QuoteWizard.getAvailableJointProducts");
		// place in qp because we will need them again when we search for riders
		qp.setCompanyProducts(null);

		// disable Riders
		qp.setRiderSelected(false);

		// Fetch the product(s) without rider(s)
		// List<com.insurfact.iq.domain.Product> products =
		// quoteFacade.getAvailableJointProducts(qp, 18);
		// set default productId and Cie to 0
//        qp.setProductID(0);
//        qp.setCompanyID(0);
//        qp.setReferenceProduct(null);
//        System.out.println("**** Multi-Quote (get available products pass) completed, total candidate products = "+products.size());
		return quoteFacade.getAvailableJointProducts(qp, 18); // as in success
	}

	private Integer processMinimumPremiumRequirementJointPercent(QuoteParams qp) {

		Client client1 = qp.getClient();
		Client client2 = qp.getClient2();

		Product refProduct = qp.getReferenceProduct();

		int age1 = client1.getActualAge();
		int age2 = client2.getActualAge();

		if (refProduct.getAgeToUse().equalsIgnoreCase("N")) {
			age1 = client1.getNearestAge();
			age2 = client2.getNearestAge();
		}

		/*
		 * System.out.println("refProduct: " + refProduct);
		 * System.out.println("client1.getClassId(): " + client1.getClassId());
		 * System.out.println("age1: " + age1); System.out.println("qp.getFaceAmt(): " +
		 * qp.getFaceAmt());
		 */
		double minPremiumReq = refProduct.getJointMinimumAnnualRequired(client1.getClassId(), age1, qp.getFaceAmt());

		// System.out.println("minPremiumReq after: " + minPremiumReq);
//        System.out.println(">>> Joint Product (INIT)  premium min req="+ minPremiumReq +" faceAmt="+qp.getFaceAmt());
//        double premium = jointFacade.calcBasePremium_aj4(refProduct, bandRate1, bandRate2, qp.getFaceAmt());
		List<Band> bands1 = refProduct.getBands(client1.getClassId());
		Collections.sort(bands1, new MinimumBandComparator());

		List<Band> bands2 = refProduct.getBands(client2.getClassId());
		Collections.sort(bands2, new MinimumBandComparator());

		Band activeBand1 = refProduct.getBandForDetails(age1, client1.getGender(), client1.isSmoker(), qp.getFaceAmt(),
				client1.getClassId());
		Band activeBand2 = refProduct.getBandForDetails(age2, client2.getGender(), client2.isSmoker(), qp.getFaceAmt(),
				client2.getClassId());
		Band nextBand1 = null;
		Band nextBand2 = null;

//        System.out.println(">>> Bands #1 for classId:"+ client1.getClassId());
//        System.out.println(">>> Bands #2 for classId:"+ client2.getClassId());
		@SuppressWarnings("unused")
		int activeBandId = 0;
		int nextBandId = 0;

		int i = 0;
		for (Band band : bands1) {
//            System.out.println(band.toString());
			if (band.getRowrey() == activeBand1.getRowrey()) {
				activeBandId = i;
				nextBandId = i;
			}
			i++;
		}

//        System.out.println(">>> Active Band #1 (" + activeBandId + "): " + activeBand1.toString());
//        System.out.println(">>> Active Band #2 (" + activeBandId + "): " + activeBand2.toString());
		// calculate the min face amount to match the min. premium req. using the same
		// Band
		BandRate bandRate1 = productFacade.getIQ4BandRate(activeBand1.getRowrey(), age1, client1.getClassId());
		BandRate bandRate2 = productFacade.getIQ4BandRate(activeBand2.getRowrey(), age2, client2.getClassId());

		// System.out.println("bandRate1: " + bandRate1);
		// System.out.println("bandRate2: " + bandRate2);
		int tmpFaceAmt = 0;

		if (refProduct.getJointAnnualMethod().equalsIgnoreCase("aj_4")) {
			tmpFaceAmt = quoteFacade.jointCalcReverseFaceAmount_aj2(refProduct, bandRate1, bandRate2, minPremiumReq);
		}
		if (refProduct.getJointAnnualMethod().equalsIgnoreCase("aj_2")) {
			tmpFaceAmt = quoteFacade.jointCalcReverseFaceAmount_aj2(refProduct, bandRate1, bandRate2, minPremiumReq);
		}
		if (refProduct.getJointAnnualMethod().equalsIgnoreCase("aj_8")) {
			tmpFaceAmt = quoteFacade.jointCalcReverseFaceAmount_aj2(refProduct, bandRate1, bandRate2, minPremiumReq);
		}

		// System.out.println("tmpFaceAmt after the aj_X: " + tmpFaceAmt);
//        System.out.println(">>> Calculated face amount ="+tmpFaceAmt + " for premium="+minPremiumReq);
		// inside the same band, but higher face amount
		if (activeBand1.getMinMax().inside(tmpFaceAmt) && activeBand2.getMinMax().inside(tmpFaceAmt)) {

			return tmpFaceAmt;
		} // not inside the band.. let's upgrade to the next
		else {

			// System.out.println("this is the else trying to upgrade band");
			boolean tryUpgrade = true;

			while (tryUpgrade) {
				nextBandId++; // upgrading band
				nextBand1 = null;
				nextBand2 = null;

				if ((nextBandId) <= (bands1.size() - 1)) {
					nextBand1 = bands1.get(nextBandId);
				}

				if ((nextBandId) <= (bands2.size() - 1)) {
					nextBand2 = bands2.get(nextBandId);
				}
				// System.out.println("bands1 :" + bands1);
				// System.out.println("bands2 :" + bands2);

				if (nextBand1 == null || nextBand2 == null) {
					break;
					// bail out!!!
				} else {

//                    System.out.println(">>> Next Band #1 (" +nextBandId+ "): " + nextBand1.toString());
//                    System.out.println(">>> Next Band #2 (" +nextBandId+ "): " + nextBand2.toString());
					// fetch new rates for next band...
					bandRate1 = null;
					bandRate1 = productFacade.getIQ4BandRate(nextBand1.getRowrey(), age1, client1.getClassId());

					bandRate2 = null;
					bandRate2 = productFacade.getIQ4BandRate(nextBand2.getRowrey(), age2, client2.getClassId());

					// System.out.println("bandRate1 new :" + bandRate1);
					// System.out.println("bandRate2 new :" + bandRate2);
					if (bandRate1 != null && bandRate2 != null) {
//                        System.out.println(">>> Next rate #1 : " + bandRate1.toString());
//                        System.out.println(">>> Next rate #2 : " + bandRate2.toString());

						tmpFaceAmt = 0;
						if (refProduct.getJointAnnualMethod().equalsIgnoreCase("aj_4")) {
							// System.out.println("aj_4 reverse");
							tmpFaceAmt = quoteFacade.jointCalcReverseFaceAmount_aj2(refProduct, bandRate1, bandRate2,
									minPremiumReq);
						}
						if (refProduct.getJointAnnualMethod().equalsIgnoreCase("aj_2")) {
							// System.out.println("aj_2 reverse");
							tmpFaceAmt = quoteFacade.jointCalcReverseFaceAmount_aj2(refProduct, bandRate1, bandRate2,
									minPremiumReq);
						}
						if (refProduct.getJointAnnualMethod().equalsIgnoreCase("aj_8")) {
							// System.out.println("aj_2 reverse");
							tmpFaceAmt = quoteFacade.jointCalcReverseFaceAmount_aj2(refProduct, bandRate1, bandRate2,
									minPremiumReq);
						}

						// System.out.println(">>> Calculated face amount =" + tmpFaceAmt + " for
						// premium=" + minPremiumReq);
						/// Inside the new band!!
						if (activeBand1.getMinMax().inside(tmpFaceAmt) && activeBand2.getMinMax().inside(tmpFaceAmt)) {
							// System.out.println(">>> Final Face amount =" + tmpFaceAmt + " for premium=" +
							// minPremiumReq);
							return tmpFaceAmt;
						}
					} else {
						break;
						// bail out!!!
					}
				}

			} // end while loop over bands
		}

		return null;
	}

	private Integer processMinimumPremiumRequirementJointEquiv(QuoteParams qp) {

		Client client = qp.getClientESA();

		Product refProduct = qp.getReferenceProduct();

		int age = 0;
		if (refProduct.getAgeToUse().equalsIgnoreCase("N")) {
			age = client.getNearestAge();
		}

		if (refProduct.getAgeToUse().equalsIgnoreCase("A")) {
			age = client.getActualAge();
		}

		double minPremiumReq = refProduct.getJointMinimumAnnualRequired(client.getClassId(), age, qp.getFaceAmt());

//        System.out.println(">>> Joint Product (INIT)  premium min req="+ minPremiumReq +" faceAmt="+qp.getFaceAmt());
		List<Band> bands = refProduct.getBands(age, client.getGender(), client.isSmoker(), client.getClassId());
		Collections.sort(bands, new MinimumBandComparator());

		Band activeBand = refProduct.getBandForDetails(age, client.getGender(), client.isSmoker(), qp.getFaceAmt(),
				client.getClassId());
		Band nextBand = null;

//        System.out.println(">>> Bands for classId:"+ client.getClassId());
		@SuppressWarnings("unused")
		int activeBandId = 0;
		int nextBandId = 0;

		int i = 0;
		for (Band band : bands) {
//            System.out.println(band.toString());
			if (band.getRowrey() == activeBand.getRowrey()) {
				activeBandId = i;
				nextBandId = i;
			}
			i++;
		}

//        System.out.println(">>> Active Band (" + activeBandId + "): " + activeBand.toString());
		// calculate the min face amount to match the min. premium req. using the same
		// Band
		BandRate bandRate = productFacade.getIQ4BandRate(activeBand.getRowrey(), age, client.getClassId());
		int tmpFaceAmt = quoteFacade.calcReverseFaceAmount(bandRate, minPremiumReq);

//        System.out.println(">>> Calculated face amount ="+tmpFaceAmt + " for premium="+minPremiumReq);
		// inside the same band, but higher face amount
		if (activeBand.getMinMax().inside(tmpFaceAmt)) {

			return tmpFaceAmt;
		} // not inside the band.. let's upgrade to the next
		else {

			boolean tryUpgrade = true;

			while (tryUpgrade) {

				nextBandId++; // upgrading band
				nextBand = null;

				if ((nextBandId) <= (bands.size() - 1)) {
					nextBand = bands.get(nextBandId);
				}

				if (nextBand == null) {
					break;
					// bail out!!!
				} else {

//                    System.out.println(">>> Next Band (" +nextBandId+ "): " + nextBand.toString());
					// fetch new rates for next band...
					bandRate = null;
					bandRate = productFacade.getIQ4BandRate(nextBand.getRowrey(), age, client.getClassId());

					if (bandRate != null) {
//                        System.out.println(bandRate.toString());

						tmpFaceAmt = quoteFacade.calcReverseFaceAmount(bandRate, minPremiumReq);
//                        System.out.println(">>> Calculated face amount ="+tmpFaceAmt + " for premium="+minPremiumReq);

						/// Inside the new band!!
						if (nextBand.getMinMax().inside(tmpFaceAmt)) {
//                             System.out.println(">>> Final Face amount ="+tmpFaceAmt  + " for premium="+minPremiumReq);
							return tmpFaceAmt;
						}
					} else {
						break;
						// bail out!!!
					}
				}

			} // end while loop over bands
		}

		return null;
	}

	private Integer processMinimumPremiumRequirementJointEquivIvari(QuoteParams qp) {

		Client client = qp.getClientESA();

		Product refProduct = qp.getReferenceProduct();

		int age = 0;
		if (refProduct.getAgeToUse().equalsIgnoreCase("N")) {
			age = client.getNearestAge();
		}

		if (refProduct.getAgeToUse().equalsIgnoreCase("A")) {
			age = client.getActualAge();
		}

		double minPremiumReq = refProduct.getJointMinimumAnnualRequired(client.getClassId(), age, qp.getFaceAmt());

//        System.out.println(">>> Joint Product (INIT)  premium min req="+ minPremiumReq +" faceAmt="+qp.getFaceAmt());
		List<Band> bands = refProduct.getBands(age, client.getGender(), client.isSmoker(), client.getClassId());
		Collections.sort(bands, new MinimumBandComparator());

		Band activeBand = refProduct.getBandForDetails(age, client.getGender(), client.isSmoker(), qp.getFaceAmt(),
				client.getClassId());
		Band nextBand = null;

//        System.out.println(">>> Bands for classId:"+ client.getClassId());
		@SuppressWarnings("unused")
		int activeBandId = 0;
		int nextBandId = 0;

		int i = 0;
		for (Band band : bands) {
//            System.out.println(band.toString());
			if (band.getRowrey() == activeBand.getRowrey()) {
				activeBandId = i;
				nextBandId = i;
			}
			i++;
		}

//        System.out.println(">>> Active Band (" + activeBandId + "): " + activeBand.toString());
		// calculate the min face amount to match the min. premium req. using the same
		// Band
		BandRate bandRate = productFacade.getIQ4BandRate(activeBand.getRowrey(), age, client.getClassId());
		int tmpFaceAmt = quoteFacade.calcReverseFaceAmount(bandRate, minPremiumReq);

//        System.out.println(">>> Calculated face amount ="+tmpFaceAmt + " for premium="+minPremiumReq);
		// inside the same band, but higher face amount
		if (activeBand.getMinMax().inside(tmpFaceAmt)) {

			return tmpFaceAmt;
		} // not inside the band.. let's upgrade to the next
		else {

			boolean tryUpgrade = true;

			while (tryUpgrade) {

				nextBandId++; // upgrading band
				nextBand = null;

				if ((nextBandId) <= (bands.size() - 1)) {
					nextBand = bands.get(nextBandId);
				}

				if (nextBand == null) {
					break;
					// bail out!!!
				} else {

//                    System.out.println(">>> Next Band (" +nextBandId+ "): " + nextBand.toString());
					// fetch new rates for next band...
					bandRate = null;
					bandRate = productFacade.getIQ4BandRate(nextBand.getRowrey(), age, client.getClassId());

					if (bandRate != null) {
						return nextBand.getMinMax().getMin();
					} else {
						break;
						// bail out!!!
					}
				}

			} // end while loop over bands
		}

		return null;
	}

	private boolean proccessJointSingleMinimuns(QuoteParams qp) {

		Product refProduct = qp.getReferenceProduct();

		if (refProduct == null) {
			return false;
		}

		Client client1 = qp.getClient();
		Client client2 = qp.getClient2();
		Client clientESA = null;
		int ageESA = 0;
		if (qp.getClientESA() != null) {
			clientESA = qp.getClientESA();
			ageESA = clientESA.getActualAge();
		}

		int age1 = client1.getActualAge();
		int age2 = client2.getActualAge();

		if (refProduct.getAgeToUse().equalsIgnoreCase("N")) {
			age1 = client1.getNearestAge();
			age2 = client2.getNearestAge();
			if (qp.getClientESA() != null) {
				ageESA = clientESA.getNearestAge();
			}
		}

		@SuppressWarnings("unused")
		boolean quiet = qp.isQuiteMode();

		int minFaceAmount = (int) refProduct.getJointMinFaceAmount();
		String jointCalcMethod = qp.getJointCalcMethod();
		Double minPremiumReq = 0d;
		Double minPremiumReq2 = 0d;
		@SuppressWarnings("unused")
		double annualSubstract = refProduct.getJointAnnualRateSubstract();
		String jointAnnualMethod = refProduct.getJointAnnualMethod();

		// retrieve all available products from the IQ4 engine
//        boolean status = false;
//        try {
		/////
		/// 1. PREPARE THE QP, Clients and minimum requirements
		//
		// Calculate 'Matrix Equiv' ClientESA
		// Calculate 'Equivalence' ClientESA
		if (jointCalcMethod.equals("E") || jointCalcMethod.equals("G")) {
			minPremiumReq = refProduct.getJointMinimumAnnualRequired(clientESA.getClassId(), ageESA, qp.getFaceAmt());
		}

		// Calculate 'Percentage' Joint
		if (jointCalcMethod.equals("P")) {
			minPremiumReq = refProduct.getJointMinimumAnnualRequired(client1.getClassId(), age1, qp.getFaceAmt());
			minPremiumReq2 = refProduct.getJointMinimumAnnualRequired(client2.getClassId(), age2, qp.getFaceAmt());
		}

		if (qp.isDebug()) {
			System.out.println(" iq4Mode == IQ4Modes.JointSingleCompany Calc Method    = " + jointCalcMethod);
			System.out.println(" iq4Mode == IQ4Modes.JointSingleCompany Annual Method  = " + jointAnnualMethod);
			System.out.println(
					" iq4Mode == IQ4Modes.JointSingleCompany Percent Value  = " + refProduct.getJointPercentValue());
			System.out.println(" iq4Mode == IQ4Modes.JointSingleCompany Annual Subs.   = "
					+ refProduct.getJointAnnualRateSubstract());
			System.out.println(
					" iq4Mode == IQ4Modes.JointSingleCompany Rate From      = " + refProduct.getJointRatesFrom());
			System.out.println(" Joint Min. Premium 1 = " + minPremiumReq);
			System.out.println(" Joint Min. Premium 2 = " + minPremiumReq2);
			System.out.println(" Joint Min. FaceAmount= " + minFaceAmount);

			if (clientESA != null) {
				System.out.println(" Client ESA     = PRESENT");
				System.out.println(" Client ESA     = " + clientESA.toString());
			} else {
				System.out.println(" Client ESA     = NOT PRESENT!");
			}
		}

		if (minPremiumReq > 0) {

			// save initial faceAmt
//                qp.setInitialFaceAmt(qp.getFaceAmt());
//                if(qp.isDebug())
//                    System.out.println(" Initial Face Amt     = " + qp.getInitialFaceAmt());
			// set new face amount
			if (minFaceAmount > 0 && minFaceAmount > qp.getFaceAmt()) {
//                    qp.setInitialFaceAmt(qp.getFaceAmt());
				qp.setFaceAmt(minFaceAmount);
			}

			// Calculate 'Age Diffence' Joint product without rider(s)
			if (jointCalcMethod.equals("A")) {

			}

			// Calculate 'Equivalence' Joint product without rider(s)
			if (jointCalcMethod.equals("E") || jointCalcMethod.equals("G")) {

				Band activeBand = refProduct.getBandForDetails(ageESA, clientESA.getGender(), clientESA.isSmoker(),
						qp.getFaceAmt(), clientESA.getClassId());
				BandRate bandRate = productFacade.getIQ4BandRate(activeBand.getRowrey(), ageESA,
						clientESA.getClassId());

				double premium = quoteFacade.calcBasePremium(bandRate, qp.getFaceAmt());
				// System.out.println("line 5141 premium=" + premium);
				// System.out.println("line 5142 minPremiumReq=" + minPremiumReq);
//                    System.out.println("*** Joint Product - (PRE) calc premium="+ premium + " with faceAmt = "+qp.getFaceAmt());

				// BINGO we got it
				if (premium < minPremiumReq) {

					// try to get a resolution for the needed faceamout vs. required min. premium
					Integer tmpFaceAmt = 0;

					if (qp.getCompanyID() == 7) // Ivari
					{
						// System.out.println("qp.getCompanyID() == 7");
						tmpFaceAmt = processMinimumPremiumRequirementJointEquivIvari(qp);
					} else {
						// System.out.println("qp.getCompanyID() != 7");
						tmpFaceAmt = processMinimumPremiumRequirementJointEquiv(qp);
					}

					if (qp.isDebug()) {
						System.out.println("*** Joint Product (POST) calc min. faceAmt=" + tmpFaceAmt);
					}

					if (tmpFaceAmt != null) {
//                            qp.setInitialFaceAmt(qp.getFaceAmt() );
						qp.setFaceAmt(Math.abs(tmpFaceAmt));

						// set flag for using min. req. premium new face amount
						qp.setUsingMinReqPremiumFaceAmt(true);

					} // no resolution...
					else {

						// System.out.println("3616 @@@ NO BANDRATE AVAILABLE FROM WITHIN THIS CLASSID:
						// " + clientESA.getClassId());
//                        if (applicationBean.getLanguage().isEnglish()) {
//
//                            String tmp = "The minimum premium required is ";
//                            tmp += getStringAmount(minPremiumReq, true);
//                            tmp += ". No rates available for this Health class.  Please choose another Health class and face amount.";
//
//                            applicationBean.postMessage(FacesMessage.SEVERITY_WARN, tmp, "");
//                        } else {
//
//                            String tmp = "La prime minimum requis est ";
//                            tmp += getStringAmount(minPremiumReq, true);
//                            tmp += ". Aucun taux disponible selon la catégorie de tarification. Veuillez choisir une autre catégorie ainsi qu'un autre montant.";
//
//                            if (!quiet) {
//                                applicationBean.postMessage(FacesMessage.SEVERITY_WARN, tmp, "");
//                            }
//                        }

						return false;
					}
				}
			}

			// Calculate 'Percentage' Joint product without rider(s)
			if (jointCalcMethod.equals("P")) {

				Band activeBand1 = refProduct.getBandForDetails(client1.getActualAge(), client1.getGender(),
						client1.isSmoker(), qp.getFaceAmt(), client1.getClassId());
				BandRate bandRate1 = productFacade.getIQ4BandRate(activeBand1.getRowrey(), client1.getActualAge(),
						client1.getClassId());

				Band activeBand2 = refProduct.getBandForDetails(client2.getActualAge(), client2.getGender(),
						client2.isSmoker(), qp.getFaceAmt(), client2.getClassId());
				BandRate bandRate2 = productFacade.getIQ4BandRate(activeBand2.getRowrey(), client2.getActualAge(),
						client2.getClassId());
				if (qp.isDebug()) {
					System.out.println("*** Active Band #1 : " + activeBand1.toString());
					System.out.println("*** BandRate    #1 : " + bandRate1.toString());

					System.out.println("*** Active Band #2 : " + activeBand2.toString());
					System.out.println("*** BandRate    #2 : " + bandRate2.toString());
				}
				double premium = 0.0d;

				if (refProduct.getJointAnnualMethod().equalsIgnoreCase("aj_4")) {
					premium = quoteFacade.jointCalcBasePremium_aj4(refProduct, bandRate1, bandRate2, qp.getFaceAmt());
				}

				if (refProduct.getJointAnnualMethod().equalsIgnoreCase("aj_2")) {
					premium = quoteFacade.jointCalcBasePremium_aj2(refProduct, bandRate1, bandRate2, qp.getFaceAmt());
				}

				if (refProduct.getJointAnnualMethod().equalsIgnoreCase("aj_8")) {
					premium = quoteFacade.jointCalcBasePremium_aj8(refProduct, bandRate1, bandRate2, qp.getFaceAmt());
				}

				if (qp.isDebug()) {
					System.out.println(
							"*** Joint Product - (PRE) calc premium=" + premium + " with faceAmt = " + qp.getFaceAmt());
				}

				// BINGO we got it
				if (premium < minPremiumReq) {

					// try to get a resolution for the needed faceamout vs. required min. premium
					Integer tmpFaceAmt = processMinimumPremiumRequirementJointPercent(qp);

					if (qp.isDebug()) {
						System.out.println("*** Joint Product (POST) calc min. faceAmt=" + tmpFaceAmt);
					}

					if (tmpFaceAmt != null) {
//                            qp.setInitialFaceAmt(tmpFaceAmt);
						qp.setFaceAmt(Math.abs(tmpFaceAmt));

						// set flag for using min. req. premium new face amount
						qp.setUsingMinReqPremiumFaceAmt(true);

					} // no resolution...
					else {

						// System.out.println("3687 @@@ NO BANDRATE AVAILABLE FROM WITHIN THIS CLASSID:
						// " + clientESA.getClassId());
//                        if (applicationBean.getLanguage().isEnglish()) {
//
//                            //System.out.println("Here is where sends the message");
//                            String tmp = "The minimum premium required is ";
//                            tmp += getStringAmount(minPremiumReq, true);
//                            tmp += ". No rates available for this Health class.  Please choose another Health class and face amount.";
//
//                            if (!quiet) {
//                                applicationBean.postMessage(FacesMessage.SEVERITY_WARN, tmp, "");
//                            }
//
//                        } else {
//
//                            String tmp = "La prime minimum requis est ";
//                            tmp += getStringAmount(minPremiumReq, true);
//                            tmp += ". Aucun taux disponible selon la catégorie de tarification. Veuillez choisir une autre catégorie ainsi qu'un autre montant.";
//
//                            if (!quiet) {
//                                applicationBean.postMessage(FacesMessage.SEVERITY_WARN, tmp, "");
//                            }
//                        }

						return false;
					}
				}

			}
		}

//        }catch (Exception e){
//            
//        }
		return true;
	}

	private Product getJointReferenceProductDetail(Integer pid) {

		QuoteParams qp = getMultiQP();

		Product referenceProduct = productFacade.getIQ4LifeProductDetail(pid);

		// fetch iq4Mode == IQ4Modes.JointSingleCompany product info first
		referenceProduct = productFacade.getJointProductDetail(referenceProduct, qp.getJointType());

		// fetch Descriptions
		if (referenceProduct != null) {
			referenceProduct = quoteFacade.getProductShortDescriptions(referenceProduct);

			if (qp.getInsuranceTypeAbbr().equals("CRIT")) {
				referenceProduct.setAdultIllnesses(quoteFacade.findAdultProductIllnesses(referenceProduct));
				// sort
				if (qp.getClient().getActualAge() < 16) {
					referenceProduct.setChildIllnesses(quoteFacade.findChildProductIllnesses(referenceProduct));
				} else {
					referenceProduct.setChildIllnesses(new ArrayList<>());
				}
				// sort
				referenceProduct.setEarlyBenIllnesses(quoteFacade.findEarlyBenProductIllnesses(referenceProduct));
				// sort
			}
		} else {
			System.out.println("QW 7175 -  unable to locate reference product with id : " + pid);
		}

		return referenceProduct;
	}

	@SuppressWarnings("unused")
	private boolean firstime = true;

	private int savedClassId = 0;
	private int savedClassId2 = 0;

	private Product calcJointSingleFromMulti(QuoteParams qp, Product product) {

//        Product product = null;
		Client client1 = Client.cloneClient(qp.getClient());
		Client client2 = Client.cloneClient(qp.getClient2());

		Client clientESA = null;

		// getContact product
//        for(Product p : qp.getFilteredProducts()){
//            if(p.getID() == pid){
//                product = p;
//                break;
//            }
//        }
//        
		if (product == null) {
			return null;
		}

		firstime = true;

		qp.setUsingMinReqPremiumFaceAmt(false);
		qp.setUsingGazooSugenstion(false);
		int initialFa = qp.getFaceAmt();
		qp.setFirstFa(qp.getFaceAmt());

		qp.setDebug(false);

		Product referenceProduct = getJointReferenceProductDetail(product.getID());

		qp.setReferenceProduct(referenceProduct);

		if (qp.getReferenceProduct() == null) {
//            applicationBean.postErrorMessage("Select a Product",
//                    "Veuillez sélectionner un produit", "", "");
			qp.setFirstFa(initialFa);
			qp.setFaceAmt(initialFa);
			return null;
		}

		String jointCalcMethod = qp.getReferenceProduct().getJointCalcMethod();
		qp.setJointCalcMethod(jointCalcMethod);
		// System.out.println("jointCalcMethod: " + jointCalcMethod);

		if (jointCalcMethod == null || jointCalcMethod.isEmpty()) {
//            applicationBean.postErrorMessage("Something went wrong on our side, please advise us if the problem persist.",
//                    "Quelque chose a mal tourné de notre côté, s'il vous plaît nous aviser si le problème persiste.", "", "");
			qp.setFirstFa(initialFa);
			qp.setFaceAmt(initialFa);
			return null;
		}

		int age1 = 0;
		int age2 = 0;
		if (qp.getReferenceProduct().getAgeToUse().equalsIgnoreCase("N")) {
			age1 = client1.getNearestAge();
			age2 = client2.getNearestAge();
		}

		if (qp.getReferenceProduct().getAgeToUse().equalsIgnoreCase("A")) {
			age1 = client1.getActualAge();
			age2 = client2.getActualAge();
		}

		// System.out.println("min age=" + qp.getReferenceProduct().getMinAge());
		// System.out.println("max age=" + qp.getReferenceProduct().getMaxAge());
		if (Math.max(age1, age2) > qp.getReferenceProduct().getMaxAge()) {
			// System.out.println("null becosue maxage");
			qp.setFirstFa(initialFa);
			qp.setFaceAmt(initialFa);
			return null;
		}

		if (Math.min(age1, age2) < qp.getReferenceProduct().getMinAge()) {
			// System.out.println("null becosue minage");
			qp.setFirstFa(initialFa);
			qp.setFaceAmt(initialFa);
			return null;
		}

		if (qp.getFaceAmt() == null || qp.getFaceAmt() < 1000) {
			// System.out.println("null becosue faceamount");
			qp.setFirstFa(initialFa);
			qp.setFaceAmt(initialFa);
			return null;
		}

		// preparing the client, product and company
		qp.setCompanyID(product.getCompanyID());
		qp.setProductID(product.getID());

		// set classId for Client #1 - Standard
		updateJointUWClassses(1);

		client1 = Client.cloneClient(qp.getClient());

		Band band1 = referenceProduct.getBandForGlobalClass(age1, client1.getGender(), qp.getFaceAmt(),
				client1.isSmoker(), "S");

		if (band1 == null) {

			int age = 100;

			com.insurfact.im.IMProduct prodDet = productFacade.findMaxAgesProd(qp.getReferenceProduct().getID());
			if (client1.isSmoker()) {
				if (client1.getGender() == 'M') {
					age = prodDet.getIm_lifeProductDetail().getMHIGH_ISSUE_AGE_SMOKER();
				} else {
					age = prodDet.getIm_lifeProductDetail().getFHIGH_ISSUE_AGE_SMOKER();
				}
			} else {
				if (client1.getGender() == 'M') {
					age = prodDet.getIm_lifeProductDetail().getMHIGH_ISSUE_AGE();
				} else {
					age = prodDet.getIm_lifeProductDetail().getFHIGH_ISSUE_AGE();
				}
			}

			updateJointUWClasssesAgeChanger(1);

			band1 = referenceProduct.getBandForGlobalClass(age, client1.getGender(), qp.getFaceAmt(),
					client1.isSmoker(), "S");

			if (band1 == null) {
				System.out.println("@@@@ unable to get Band for client1 for product :" + referenceProduct.getName());
				qp.setFirstFa(initialFa);
				qp.setFaceAmt(initialFa);
				return null;
			}
		}

		// System.out.println("band1: "+band1);
		client1.setUwClass(client1.getUwClass(band1.getClassId()));
		client1.setClassId(band1.getClassId());

		System.out.println(">>>> Client1 : " + client1 + " band=" + band1);
		// set classId for Client #2
		updateJointUWClassses(2);

		client1 = Client.cloneClient(qp.getClient());

		Band band2 = referenceProduct.getBandForGlobalClass(age2, client2.getGender(), qp.getFaceAmt(),
				client2.isSmoker(), "S");

		if (band2 == null) {

			int age = 100;

			com.insurfact.im.IMProduct prodDet = productFacade.findMaxAgesProd(qp.getReferenceProduct().getID());
			if (client2.isSmoker()) {
				if (client2.getGender() == 'M') {
					age = prodDet.getIm_lifeProductDetail().getMHIGH_ISSUE_AGE_SMOKER();
				} else {
					age = prodDet.getIm_lifeProductDetail().getFHIGH_ISSUE_AGE_SMOKER();
				}
			} else {
				if (client2.getGender() == 'M') {
					age = prodDet.getIm_lifeProductDetail().getMHIGH_ISSUE_AGE();
				} else {
					age = prodDet.getIm_lifeProductDetail().getFHIGH_ISSUE_AGE();
				}
			}

			updateJointUWClasssesAgeChanger(2);

			band2 = referenceProduct.getBandForGlobalClass(age, client2.getGender(), qp.getFaceAmt(),
					client2.isSmoker(), "S");

			if (band2 == null) {
				System.out.println("@@@@ unable to get Band for client2 for product :" + referenceProduct.getName());
//                    qp.setEnhancedFaceAmount(null);
				qp.setFirstFa(initialFa);
				qp.setFaceAmt(initialFa);
				return null;
			}
		}

		client2.setUwClass(client2.getUwClass(band2.getClassId()));
		client2.setClassId(band2.getClassId());

		if (client1.getClassId() == 0) {
			System.out.println("null becosue classId");
			qp.setFirstFa(initialFa);
			qp.setFaceAmt(initialFa);
			return null;
		}

		if (client2.getClassId() == 0) {
			System.out.println("null becosue classId2");
			qp.setFirstFa(initialFa);
			qp.setFaceAmt(initialFa);
			return null;
		}

		savedClassId = client1.getClassId();
		savedClassId2 = client2.getClassId();
		qp.setClientESA(null);

		if (jointCalcMethod.equals("E") || jointCalcMethod.equals("G")) {

			clientESA = jointESAUtils.processJointESA(qp);

			qp.setClientESA(clientESA);

			if (clientESA == null) {
				System.out.println("@@@@ Convertion to  ESA Client FAILED!! ");

//                applicationBean.postMessage(FacesMessage.SEVERITY_WARN,
//                        "Unable to convert Client ESA Data.", "Unable to convert Client ESA Data.");
				qp.setFaceAmt(qp.getFirstFa());
				client1.setClassId(savedClassId);
				client2.setClassId(savedClassId2);
				qp.setFirstFa(initialFa);
				qp.setFaceAmt(initialFa);
				return null;
			}
		}
		// Process Min Premium Req. and/or Minimum Face Amount
		boolean status = proccessJointSingleMinimuns(qp);
		Product singleJointProduct = null;

		if (status) {

			// calc the Joint Single Product
			singleJointProduct = quoteJointSingleProduct(qp);

		}
		// success
		if (singleJointProduct != null) {

			// populate type
			singleJointProduct.setProductType(getReferenceProduct().getProductType());

			HealthClass tempClass = new HealthClass();

			Client sel = client1;

			if (clientESA != null) {
				sel = clientESA;
			}

			tempClass.setID(sel.getUwClass().getClassid());
			tempClass.setName(sel.getUwClass().getEngdesc());
			tempClass.setNameFr(sel.getUwClass().getFredesc());
			tempClass.setNonSmokerMonths(sel.getUwClass().getMonthNonSmoker());
			tempClass.setGlobalClass(sel.getUwClass().getGlobalClass());
			tempClass.setSmokeCode(sel.getUwClass().getSmokeCode());

			List<HealthClass> listFakeClass = new ArrayList<>();
			listFakeClass.add(tempClass);

			singleJointProduct.setHealthClasses(listFakeClass);

			System.out.println("qp.getSingleProduct: " + qp.getSingleProduct());

			// qp.setSingleProduct(singleJointProduct);
			// prepare riders for single quote
			Date date = client1.getFullBirthDate();
			boolean smoker = client1.isSmoker();

			if (clientESA != null) {
				date = clientESA.getFullBirthDate();
				smoker = clientESA.isSmoker();
			}

			// set riders for selected product - SINGLE MODE
			findAvailableRidersJoint(singleJointProduct, date, smoker);

			// set builtins
			findBuiltinsRidersJoint(singleJointProduct, date, smoker);

		} else {
			qp.setFirstFa(initialFa);
			qp.setFaceAmt(initialFa);
			return null;
		}

		singleJointProduct.setUsingGazooSugenstion(false);
		singleJointProduct.setUsingMinReqPremiumFaceAmt(qp.isUsingMinReqPremiumFaceAmt());

		if (qp.isUsingMinReqPremiumFaceAmt()) {
			System.out.println("isUsingMinimumAnnualPremium()");
			@SuppressWarnings("unused")
			int age = client1.getActualAge();
			if (referenceProduct.getAgeToUse().equalsIgnoreCase("N")) {
				age = client1.getNearestAge();
			}
			Band active = referenceProduct.getBandForGlobalClass(age1, client1.getGender(), initialFa,
					client1.isSmoker(), "S");

			double minPremiumReq = 0.0;

			@SuppressWarnings("unused")
			double minPremiumReq2 = 0.0;

			int ageESA = 0;
			if (qp.getClientESA() != null) {
				clientESA = qp.getClientESA();
				ageESA = clientESA.getActualAge();
			}

			if (qp.getJointCalcMethod().equals("E") || qp.getJointCalcMethod().equals("G")) {
				minPremiumReq = referenceProduct.getJointMinimumAnnualRequired(clientESA.getClassId(), ageESA,
						qp.getFirstFa());
			}

			// Calculate 'Percentage' Joint
			if (qp.getJointCalcMethod().equals("P")) {
				minPremiumReq = referenceProduct.getJointMinimumAnnualRequired(client1.getClassId(), age1,
						qp.getFaceAmt());
				minPremiumReq2 = referenceProduct.getJointMinimumAnnualRequired(client2.getClassId(), age2,
						qp.getFaceAmt());
			}
			/*
			 * System.out.println("qp.getFaceAmt(): " + qp.getFaceAmt());
			 * System.out.println("tmpProduct.getOriginalFaceAmount(): " +
			 * singleJointProduct.getOriginalFaceAmount());
			 * System.out.println("tmpProduct.getFaceAmount(): " +
			 * singleJointProduct.getFaceAmount());
			 */
			singleJointProduct.setFaceAmount(singleJointProduct.getOriginalFaceAmount());
			// System.out.println("active.getMinMax().getMax(): " +
			// active.getMinMax().getMax());

			if (singleJointProduct.getFaceAmount() > active.getMinMax().getMax()) {
				qp.setFaceAmt(initialFa);
				fixFaceAmount(qp, singleJointProduct,
						referenceProduct.getBands(age1, client1.getGender(), client1.isSmoker(), client1.getClassId()),
						active);
				// System.out.println("qp.getGazooFaSuggestion2(): " +
				// qp.getGazooFaSuggestion());
				// System.out.println("minPremiumReq: " + minPremiumReq);
				if (qp.getGazooFaSuggestion() > 0) {
					int tempOri = qp.getFaceAmt();
					double tempOriAp = minPremiumReq;
					qp.setFaceAmt(qp.getGazooFaSuggestion());
					singleJointProduct = quoteJointSingleProduct(qp);
					if (singleJointProduct != null) {
						singleJointProduct.setUsingGazooSugenstion(true);
						singleJointProduct.setGazooSavedAnnualP(tempOriAp);
						singleJointProduct.setUsingGazooSugenstion(true);
					}
					qp.setFaceAmt(tempOri);
				}
				singleJointProduct.setMinimumAnnualRequired(minPremiumReq);
			} else {
				if (singleJointProduct != null) {
					singleJointProduct.setUsingMinReqPremiumFaceAmt(true);
					singleJointProduct.setMinimumAnnualRequired(minPremiumReq);
				}
			}
		}

		// incase it was not hidden already
		/*
		 * if (qp.isUsingMinReqPremiumFaceAmt()) {
		 * 
		 * List<Band> bands = qp.getReferenceProduct().getBands();
		 * Collections.sort(bands, new MinimumBandComparator()); Band activeBand =
		 * qp.getReferenceProduct().getBandForDetails(age1, client1.getGender(),
		 * !client1.isNonSmoker(), qp.getFirstFa());
		 * System.out.println("qp.getFirstFa(): " + qp.getFirstFa());
		 * System.out.println("activeBand: " + activeBand); fixFaceAmount(qp,
		 * singleJointProduct, bands, activeBand);
		 * 
		 * Product singleProductG = new Product(); if (qp.getGazooFaSuggestion() != 0) {
		 * int tem = qp.getFaceAmt(); int tem2 = qp.getOriginalFaceAmount(); Product
		 * tem3 = qp.getSingleProduct(); qp.setFaceAmt(qp.getGazooFaSuggestion());
		 * singleProductG = quoteJointSingleProduct(qp); qp.setFaceAmt(tem);
		 * qp.setOriginalFaceAmount(tem2); qp.setSingleProduct(tem3); }
		 * 
		 * if (qp.getGazooFaSuggestion() != 0) { try { PremiumCalculator calc = new
		 * PremiumCalculator();
		 * qp.setGazooAnnualSuggestion(calc.calculateBaseGazoo(singleProductG, qp,
		 * singleProductG.getPremiumRenewal())); } catch (InstantiationException |
		 * NoSuchMethodException | IllegalAccessException | InvocationTargetException
		 * ex) { Logger.getLogger(QuoteWizard.class.getName()).log(Level.SEVERE, null,
		 * ex); } } }
		 */
		// added to fix teh recheck
		// updateJointMinMaxFaceAmount();
		promo = null;
		if (singleJointProduct != null) {
			findPromotion(singleJointProduct);
			if ((isHasPromotion() || singleJointProduct.hasPromoBuiltIn())) {// &&
																				// (singleJointProduct.getBuiltInPromotion().getMinPremium()
																				// == null ||
																				// singleJointProduct.getBuiltInPromotion().getMinPremium()
																				// <=
																				// singleJointProduct.getPremiumRenewal().getAnnualBasePrem()))
																				// {
				if (clientESA != null) {
					promotionsHelper.applyPromotion(singleJointProduct, promo, clientESA.getActualAge(),
							qp.getFaceAmt(), qp);
				} else {
					promotionsHelper.applyPromotion(singleJointProduct, promo, client1.getActualAge(), qp.getFaceAmt(),
							qp);
				}
			}
		}
		qp.setFaceAmt(initialFa);
		qp.setQuiteMode(false);
		qp.setCompanyID(0);
		qp.setProductID(0);
		qp.setFirstFa(initialFa);

		return singleJointProduct;
	}

	public Product getReferenceProduct() {
		QuoteParams qp = getMultiQP();

		return qp.getReferenceProduct();
	}

	private boolean updateJointUWClasssesAgeChanger(int c) {

//        System.out.println("**** updateJointUWClassses c="+c);
		QuoteParams qp = getMultiQP();

		Client client;

		if (c == 1) {
			client = qp.getClient();
		} else {
			client = qp.getClient2();
		}

		// reset values
		resetJointUWClasses(c);

//        qp.setInsuranceBen(0);
//        qp.setHealthClass("");
//        qp.setUwType(0);
		if (qp.getProductID() == 0) {
			return false;
		}

		if (qp.getFaceAmt() == null) {
			return false;
		}

		int age = 100;

		com.insurfact.im.IMProduct prodDet = productFacade.findMaxAgesProd(qp.getReferenceProduct().getID());
		if (client.isSmoker()) {
			if (client.getGender() == 'M') {
				age = prodDet.getIm_lifeProductDetail().getMHIGH_ISSUE_AGE_SMOKER();
			} else {
				age = prodDet.getIm_lifeProductDetail().getFHIGH_ISSUE_AGE_SMOKER();
			}
		} else {
			if (client.getGender() == 'M') {
				age = prodDet.getIm_lifeProductDetail().getMHIGH_ISSUE_AGE();
			} else {
				age = prodDet.getIm_lifeProductDetail().getFHIGH_ISSUE_AGE();
			}
		}

		List<Integer> ids = qp.getReferenceProduct().getClassIdsForBands(client.getGender(), client.isSmoker(), age,
				qp.getFaceAmt());

//        System.out.println(ids);
		List<IMUWClass> uwClasses = new ArrayList<>();

		if (ids == null || ids.isEmpty()) {
			return false;
		}

		for (Integer id : ids) {

			IMUWClass uwClass = productFacade.getIMUWClass(id);

			List<Band> bands = qp.getReferenceProduct().getBands(id);

			if (bands == null || bands.isEmpty()) {
				return false;
			}

			Band band = bands.get(0);

			if (uwClass != null) {
				uwClass.setGlobalClass(band.getGlobalHealthClass());
				uwClass.setInsuranceBen(qp.getReferenceProduct().getInsuranceBen());
				uwClass.setUwType(qp.getReferenceProduct().getUwtype());
				uwClass.setMonthNonSmoker(band.getMonths());
				uwClass.setSmokeCode(band.getSmokeCode());
				uwClass.setCigarette(band.isCigarettes());
				uwClass.setRowKey(band.getRowrey());

				System.out.println("adding HealthClass : " + uwClass.getEngdesc() + "  id=" + uwClass.getClassid()
						+ " gc=" + uwClass.getGlobalClass() + " smokeCode=" + uwClass.getSmokeCode() + " uw="
						+ uwClass.getUwType() + " ib=" + uwClass.getInsuranceBen());
				uwClasses.add(uwClass);
			}
		}

//        if (language.isEnglish()) {
//
//            if (qp.getClient().isNonSmoker()) {
//                Collections.sort(uwClasses, new HealthClassMonthComparator());
//            } else {
//                Collections.sort(uwClasses, new HealthClassByOrderComparator());
//            }
////            
//        } else {
//            if (qp.getClient().isNonSmoker()) {
//                Collections.sort(uwClasses, new HealthClassMonthComparator());
//            } else {
//                Collections.sort(uwClasses, new HealthClassFrenchComparator());
//            }
//        }

		client.setUwClasses(uwClasses);

		// set a default if not set
		if (!uwClasses.isEmpty()) {

			for (IMUWClass u : uwClasses) {
				if (u.getGlobalClass().equalsIgnoreCase("S")) {// why is doing this?????
					// if (u.getClassid() == client.getClassId()) {

					client.setUwClass(u);

					client.setClassId(u.getClassid());
//            qp.getReferenceProduct().setClassId(uwClass.getClassid());
					// System.out.println("CORRECT!! checking uwclass setting classid: " +
					// u.getClassid());
					client.setSmokeCode(u.getSmokeCode());
					break;
				}
			}
		} else {

//            applicationBean.postMessage(FacesMessage.SEVERITY_ERROR, 
//                    "Unable to qualify the requested product based on the provided information",
//                    "Impossible de qualifier le produit demandé sur la base des informations fournies", "","");
//  
			return false;
		}

		return true;
	}

	private void findAvailableRidersJoint(Product product, Date birthdate, boolean smoker) {

		QuoteParams qp = getMultiQP();

//        System.out.println( "**** getAvailableRidersJoint prodId="+prodId+" actual="+qp.getClient().getActualAge()+ " smoker="+smoker );
		List<Rider> availableRiders = product.getAvailableRiders();
		List<Rider> includedRiders = product.getIncludedRiders();

		if (availableRiders == null) {
			availableRiders = new ArrayList<>();
		} else {
			availableRiders.clear();
		}

		if (includedRiders == null) {
			includedRiders = new ArrayList<>();
		} else {
			includedRiders.clear();
		}

		List<Rider> avRiders = null;
		// lock the companyId
//        qp.setCompanyID(0);

		// lock the productId
//        qp.setProductID(product.getID());
//        System.out.println( "getAvailableRiders prodId="+prodId+" actual="+qp.getClient().getActualAge()+ " smoker="+smoker );
		avRiders = quoteFacade.getAvailableRiders(qp, product);

		for (Rider rider : avRiders) {

			if (!rider.isIncludedInBase()) {
				continue;
			}

			if (rider.getAvailableFor() != null) {

				if (rider.getAvailableFor().equalsIgnoreCase("B")) {
					rider = quoteFacade.getShortRiderDescriptions(qp.getSingleProduct(), rider);
					includedRiders.add(rider);
				}

				if (rider.getAvailableFor().equalsIgnoreCase("JL") && qp.getJointType().equalsIgnoreCase("L")) {
					rider = quoteFacade.getShortRiderDescriptions(qp.getSingleProduct(), rider);
					includedRiders.add(rider);
				}
				if (rider.getAvailableFor().equalsIgnoreCase("JF") && qp.getJointType().equalsIgnoreCase("F")) {
					rider = quoteFacade.getShortRiderDescriptions(qp.getSingleProduct(), rider);
					includedRiders.add(rider);
				}
				if (rider.getAvailableFor().equalsIgnoreCase("JFL")) {
					rider = quoteFacade.getShortRiderDescriptions(qp.getSingleProduct(), rider);
					includedRiders.add(rider);
				}
			}
		}

//        qp.setIncludedRiders(includedRiders);
//        product.setIncludedRiders(includedRiders);  // not supported
		product.setAvailableRiders(availableRiders);

	}

	private void findBuiltinsRidersJoint(Product product, Date birthdate, boolean smoker) {

		QuoteParams qp = getMultiQP();

//        System.out.println("*** findBuiltinsRidersJoint for : " + product);
		List<Rider> avRiders = product.getIncludedRiders();

		if (avRiders == null) {
			avRiders = new ArrayList<>();
		} else {
			avRiders.clear();
		}

		avRiders = quoteFacade.getBuiltinsRiders(qp, product);

		List<Rider> includedRiders = product.getIncludedRiders();

		if (avRiders != null) {

			Iterator<Rider> iterator = avRiders.iterator();

			while (iterator.hasNext()) {

				Rider rider = iterator.next();

				if ((qp.getFaceAmt() < rider.getMinFaceAmt())
						|| (rider.getMaxFaceAmt() != 0 && qp.getFaceAmt() > rider.getMaxFaceAmt())) {
					System.out.println("@@@@NEW id(" + rider.getID() + ") - " + rider.getName()
							+ " was excluded from the selection due to MIN  MAX face amount constraint : "
							+ rider.getMinFaceAmt() + " + " + +rider.getMaxFaceAmt());
					iterator.remove();
					continue;
				}

				if (qp.getFaceAmt() < rider.getMinBaseFaceAmt()) {
					iterator.remove();
					continue;
				}

				if ((qp.getFaceAmt() > rider.getMaxBaseFaceAmt() && rider.getMaxBaseFaceAmt() != 0)) {
					iterator.remove();
					continue;
				}

				if (selectionRiderId > 0) {
					if (rider.getID() == 275 && selectionRiderId == 276) {
						iterator.remove();
						continue;
					}
					if (rider.getID() == 276 && selectionRiderId == 275) {
						iterator.remove();
						continue;
					}
				}

				if (rider.getAvailableFor() != null) {

					if (rider.getAvailableFor().equalsIgnoreCase("B")) {
						rider = quoteFacade.getShortRiderDescriptions(product, rider);
						includedRiders.add(rider);
					}

					if (rider.getAvailableFor().equalsIgnoreCase("JL") && qp.getJointType().equalsIgnoreCase("L")) {
						rider = quoteFacade.getShortRiderDescriptions(product, rider);
						includedRiders.add(rider);
					}
					if (rider.getAvailableFor().equalsIgnoreCase("JF") && qp.getJointType().equalsIgnoreCase("F")) {
						rider = quoteFacade.getShortRiderDescriptions(product, rider);
						includedRiders.add(rider);
					}
					if (rider.getAvailableFor().equalsIgnoreCase("JFL")) {
						rider = quoteFacade.getShortRiderDescriptions(product, rider);
						includedRiders.add(rider);
					}
				}
			}
		}

		if (includedRiders != null) {
			// sorting
			List<Rider> thoseB = new ArrayList<>();
			List<Rider> thoseNormal = new ArrayList<>();
			for (Rider r : includedRiders) {
				if (r.getAvailableFor() == null) {
					thoseNormal.add(r);
				} else {
					if (r.getAvailableFor().equalsIgnoreCase("B")) {
						thoseB.add(r);
					} else {
						thoseNormal.add(r);
					}
				}
			}

			Collections.sort(thoseB, new IncludedRidersOrderComparator());
			Collections.sort(thoseNormal, new IncludedRidersOrderComparator());
			includedRiders.clear();
			includedRiders.addAll(thoseNormal);
			includedRiders.addAll(thoseB);
		}

		product.setIncludedRiders(includedRiders);

	}

	public void refreshFilteredProducts() {

//        RequestContext.getCurrentInstance().execute("PF('searchProgressDialog').show()");
		QuoteParams qp = getMultiQP();

		int total = 4;

//        System.out.println("** riderSelection = "+qp.getRiderSelection());
		/*
		 * if (qp.getRiderSelection() != 0) { //add it to refresh correct the view
		 * qp.setHealthClass(null); qp.setUwType(0); qp.setInsuranceBen(0);
		 * //qp.setProductTypeID(0);
		 * 
		 * recalculateMulti(false); System.out.println("** riderSelection = " +
		 * qp.getRiderSelection()); }
		 */
//        System.out.println("**** filters total : "+ total);
		List<Product> products = qp.getProducts();
//         System.out.println("** total Products : " + products.size());

		List<Product> filteredProducts = new ArrayList<>();

		for (Product product : products) {
			int score = 0;

//            System.out.println("*** Product : " + product.getName() + " " + product.getHealthClass().getGlobalClass() + " " + product.getUwtype() + " " + product.getInsuranceBen() + " " + product.getProductType().getMasterID());
			// Global Health Class Reqs.
			/*
			 * if (qp.getHealthClass() == null || qp.getHealthClass().isEmpty()) { score++;
			 * } else { if
			 * (product.getHealthClass().getGlobalClass().equalsIgnoreCase(qp.getHealthClass
			 * ())) { score++; } }
			 */
			// Global Health Class Reqs New.
			/*
			 * if (qp.getHealthClass() == null || qp.getHealthClass().isEmpty()) { score++;
			 * } else { if (language.isEnglish()) { if
			 * (product.getHealthClass().getName().equalsIgnoreCase(qp.getHealthClass())) {
			 * score++; } }
			 * 
			 * if (language.isFrench()) { if
			 * (product.getHealthClass().getNameFr().equalsIgnoreCase(qp.getHealthClass()))
			 * { score++; } }
			 * 
			 * }
			 */
			System.out.println("qp.getClient(): " + qp.getClient());
			System.out.println("qp.getClient().getSmokerStatus(): " + qp.getClient().getSmokerStatus());
			if (qp.getClient().getSmokerStatus().equals("N")) {
				if (qp.getHealthClass() == null || qp.getHealthClass().isEmpty()) {
					score++;
				} else {
					if (qp.getHealthClass().equals("S") && !product.getHealthClass().isFamilyHistory()) {
						score++;
					}
					if (qp.getHealthClass().equals("P") && product.getHealthClass().isFamilyHistory()) {
						score++;
					}
				}
			} else {
				if (qp.getHealthClass() == null || qp.getHealthClass().isEmpty()) {
					score++;
				} else {

					if (qp.getHealthClass().equals("S") && !product.getHealthClass().isCigarretSmoker()) {
						score++;
					}
					if (qp.getHealthClass().equals("P") && product.getHealthClass().isCigarretSmoker()) {
						score++;
					}

					// System.out.println("product.getBands(): "
					// +product.getBands().get(0).isSmokeAll());
					/*
					 * if (product.getBands().get(0).isSmokeAll() &&
					 * qp.getHealthClass().equalsIgnoreCase("Y")) { score++; } if
					 * (product.getHealthClass().isCigarretSmoker() &&
					 * qp.getHealthClass().equalsIgnoreCase("C")) { score++; } if
					 * ((!product.getHealthClass().isCigarretSmoker() &&
					 * qp.getHealthClass().equalsIgnoreCase("N")) ||
					 * !product.getBands().get(0).isSmokeAll()) { score++; }
					 */
				}
			}

			// Underwritting Reqs.
			if (qp.getUwType() == 0) {
				score++;
			} else {
				// System.out.println("product.getUwtype(): " + product.getUwtype());
				if (product.getUwtype() == qp.getUwType()) {
					score++;
				}
			}

			// Insurance Bene
			if (qp.getInsuranceBen() == 0) {
				score++;
			} else {
				// System.out.println("qp.getInsuranceBen(): " + qp.getInsuranceBen());
				if (product.getInsuranceBen() == qp.getInsuranceBen()) {
					score++;
				}
			}

			if (qp.getProductTypeID() == 0) {
				score++;
			} else {
				if (product.getProductType().getMasterID() == qp.getProductTypeID()) {
					score++;
				}
			}

			if (score == total) {
				filteredProducts.add(product);
			}
		}

//        System.out.println("** total filtered Products : " + filteredProducts.size());
		qp.setFilteredProducts(resort(filteredProducts));

//        applicationBean.hideProgressDialog();
	}

	private List<Product> resort(List<Product> prods) {
		// sort all available products

		QuoteParams qp = getMultiQP();

		int productSortBy = qp.getProductSortBy();

		switch (productSortBy) {
		case QuoteParams.SORT_BY_COMPANY: {
			Collections.sort(prods, new CompanyProductComparator());
			break;
		}
		case QuoteParams.SORT_BY_ANNUAL_PREMIUM: {
			Collections.sort(prods, new TotalAnnComparator());
			break;
		}
		case QuoteParams.SORT_BY_MONTHLY_PREMIUM: {
			Collections.sort(prods, new TotalMonComparator());
			break;
		}
		case QuoteParams.SORT_BY_HIGH_CASHVALUES_10TH_YEAR: {
			Collections.sort(prods, new HighestCashvalue10thYearComparator());
			break;
		}
		case QuoteParams.SORT_BY_HIGH_CASHVALUES_20TH_YEAR: {
			Collections.sort(prods, new HighestCashvalue20thYearComparator());
			break;
		}
		case QuoteParams.SORT_BY_HIGH_CASHVALUES_AGE65: {
			Collections.sort(prods, new HighestCashvalueAge65Comparator());
			break;
		}
		case QuoteParams.SORT_BY_HIGH_CASHVALUES_AGE75: {
			Collections.sort(prods, new HighestCashvalueAge75Comparator());
			break;
		}
		case QuoteParams.SORT_BY_COVERED_ILLNESSES: {
			Collections.sort(prods, new CoveredIllnessesComparator());
			break;
		}

		default: {
			Collections.sort(prods, new CompanyProductComparator());
		}
		}

		return prods;
	}

	private String getJointResponseStringGroup(final String faceAmount, final String provCode,
			final String dateOfBirth1, final String gender1, final String smoking1, final String dateOfBirth2,
			final String gender2, final String smoking2, final String jointType, final String levelOrDecr)
			throws InsurfactPrivateException {

		if (DEBUG) {
			System.out.println(TAG + ".getSingleResponseString: entering with "
			// + "productTypeId {" + productTypeId + "}"
					+ "faceAmount {" + faceAmount + "}" + "dateOfBirth {" + dateOfBirth1 + "}" + "gender {" + gender1
					+ "}" + "smoking {" + smoking1 + "}" + "provCode {" + provCode + "}");
		}

		// step 1: validate the input values from the URL, important for security
		// ----------------------------------------------------------------------
		String productTypeId = "9";
		int productTypeInt = 0;
		try {
			productTypeInt = Integer.parseInt(productTypeId);
		} catch (NumberFormatException e) {
			throw new InsurfactPrivateException("invalid productTypeId input {" + productTypeId + "}");
		}

		String compID = "";

		int faceAmountInt = 0;
		try {
			faceAmountInt = Integer.parseInt(faceAmount);
		} catch (NumberFormatException e) {
			throw new InsurfactPrivateException("invalid faceAmount input {" + faceAmount + "}");
		}

		if (provCode == null || provCode.length() != 2) {
			throw new InsurfactPrivateException("invalid provCode input {" + provCode + "}");
		}
		String provCodeUp = provCode.toUpperCase();

		if (dateOfBirth1 == null || dateOfBirth1.isEmpty()) {
			throw new InsurfactPrivateException("invalid dateOfBirth input {" + dateOfBirth1 + "}");
		}
		String dob1 = dateOfBirth1;

		// gender M F
		if (gender1 == null || gender1.isEmpty()
				|| (!gender1.equalsIgnoreCase("M") && !gender1.equalsIgnoreCase("F"))) {
			throw new InsurfactPrivateException("invalid gender input {" + gender1 + "}");
		}
		String genderUp1 = gender1.toUpperCase();

		// smoking Y N
		if (smoking1 == null || smoking1.isEmpty()
				|| (!smoking1.equalsIgnoreCase("Y") && !smoking1.equalsIgnoreCase("N"))) {
			throw new InsurfactPrivateException("invalid smoking input {" + smoking1 + "}");
		}
		String smokingUp1 = smoking1.toUpperCase();

		if (dateOfBirth2 == null || dateOfBirth2.isEmpty()) {
			throw new InsurfactPrivateException("invalid dateOfBirth input {" + dateOfBirth2 + "}");
		}
		String dob2 = dateOfBirth2;

		// gender M F
		if (gender2 == null || gender2.isEmpty()
				|| (!gender2.equalsIgnoreCase("M") && !gender2.equalsIgnoreCase("F"))) {
			throw new InsurfactPrivateException("invalid gender input {" + gender2 + "}");
		}
		String genderUp2 = gender2.toUpperCase();

		// smoking Y N
		if (smoking2 == null || smoking2.isEmpty()
				|| (!smoking2.equalsIgnoreCase("Y") && !smoking2.equalsIgnoreCase("N"))) {
			throw new InsurfactPrivateException("invalid smoking input {" + smoking2 + "}");
		}
		String smokingUp2 = smoking2.toUpperCase();

		// jointType F or L
		if (jointType == null || jointType.isEmpty()
				|| (!jointType.equalsIgnoreCase("F") && !jointType.equalsIgnoreCase("L"))) {
			throw new InsurfactPrivateException("invalid joinType input {" + jointType + "}");
		}
		String jointTypeUp = jointType.toUpperCase();
		System.out.println("1702 GenJoint jointTypeUp=" + jointTypeUp);

		try {
			// ------------------------------------------------------------------------------------------

			prepareJointQuoteParamsGroup(productTypeInt, faceAmountInt, provCodeUp, dob1, genderUp1, smokingUp1, dob2,
					genderUp2, smokingUp2, jointTypeUp, levelOrDecr, compID);

			// ------------------------------------------------------------------------------------------
		} catch (InsurfactPrivateException e) {
			String s = "*prepareSingleQuoteParams(smokingUp, provCodeUp, faceAmountInt)* raised {" + e + "}";
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + s);
			}
			throw e;
		} catch (Throwable e) {
			String s = "*prepareSingleQuoteParams(smokingUp, provCodeUp, faceAmountInt)* raised {" + e + "}";
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + s);
			}
			throw new InsurfactPrivateException(s);
		}

		// step 4: run the sql and quote engine logic
		// ------------------------------------------
		List<Product> productsWithPercentage = getProductsCombosNew();

		// step 5: prepare results to return to client requester
		// -----------------------------------------------------
		String response = "\"No matching records.\"";

		if (productsWithPercentage == null || productsWithPercentage.isEmpty()) {

			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + "*productsWithPercentage = getProductsCombos()* "
						+ "returned null or empty list.");
			}

			return response;
		}

		List<Product> finalList = new ArrayList<>();

		// 6, 9, 10, 15, 16, 22, 38
		Product p1 = new Product();
		p1.setID(0);
		Product p2 = new Product();
		p2.setID(0);
		Product p3 = new Product();
		p3.setID(0);
		Product p4 = new Product();
		p4.setID(0);
		Product p5 = new Product();
		p5.setID(0);
		Product p6 = new Product();
		p6.setID(0);
		Product p7 = new Product();
		p7.setID(0);

		Product p1a = new Product();
		p1a.setID(0);
		Product p2a = new Product();
		p2a.setID(0);
		Product p3a = new Product();
		p3a.setID(0);
		Product p4a = new Product();
		p4a.setID(0);
		Product p5a = new Product();
		p5a.setID(0);
		Product p6a = new Product();
		p6a.setID(0);
		Product p7a = new Product();
		p7a.setID(0);

		double min1 = 999999999;
		double min2 = 999999999;
		double min3 = 999999999;
		double min4 = 999999999;
		double min5 = 999999999;
		double min6 = 999999999;
		double min7 = 999999999;

		int id1 = 9;
		int id2 = 10;
		int id3 = 6;
		int id4 = 22;
		int id5 = 15;
		int id6 = 38;
		int id7 = 16;

		List<HealthClass> cliHealthClass1 = new ArrayList<>();
		List<HealthClass> cliHealthClass2 = new ArrayList<>();

		String healthClass1 = "";
		String healthClassFr1 = "";

		String healthClass2 = "";
		String healthClassFr2 = "";

		boolean isFamilyHistory1 = false;
		boolean isFamilyHistory2 = false;

		boolean isCigarretSmoker1 = false;
		boolean isCigarretSmoker2 = false;

		String classFlag1 = "";
		String lowMonths1 = "";

		String classFlag2 = "";
		String lowMonths2 = "";

		int convertibleAge = 0;
		int payableAge = 0;

		int counter = 0;
		StringBuilder buf = new StringBuilder("[ ");

		for (Product product : productsWithPercentage) {

			Product referenceProduct = productFacade.getIQ4LifeProductDetail(product.getID());
//System.out.println("995 GenericJoint getMinimumAnnualRequired "+referenceProduct.getMinimumAnnualRequired());
//System.out.println("996 GenericJoint getProductAnnualPremiumTotal"+referenceProduct.getProductAnnualPremiumTotal());

			// if(referenceProduct.getMinimumAnnualRequired() <=
			// referenceProduct.getProductAnnualPremiumTotal()){//mininmum premium added
			// april 14 2021 -aa
			referenceProduct = productFacade.getJointProductDetail(referenceProduct, multiQP.getJointType());
			multiQP.setReferenceProduct(referenceProduct);

			int tmpAge = 0;
			int tmpAge2 = 0;
			if (product.getAgeToUse().equalsIgnoreCase("N")) {
				tmpAge = multiQP.getClient().getNearestAge();
				tmpAge2 = multiQP.getClient2().getNearestAge();
			} else {
				tmpAge = multiQP.getClient().getActualAge();
				tmpAge2 = multiQP.getClient2().getActualAge();
			}

			multiQP = getMultiQP();
			multiQP.setCompanyID(product.getCompany().getID());

			multiQP.setProductID(product.getID());
			// multiQP.setReferenceProduct(product);
			String jointCalcMethod = product.getJointCalcMethod();
			multiQP.setJointCalcMethod(jointCalcMethod);
			multiQP.setProductsAgeToUse(product.getAgeToUse());

			multiQP.setProductTypeID(product.getProductType().getMasterID());

			convertibleAge = product.getLastConvertibleAge();
			payableAge = product.getAgePayable();

			if (referenceProduct.isEnhanced()) {

				// calc and set enhanced faceAmoimt
				enhancedProductFacade.processEnhancedProduct(multiQP, referenceProduct);

				if (referenceProduct.getEnhancedFaceAmount() > 0) {

					// process enhanced face amount. in the case it requires a new amount, the
					// original multiQP.originalFaceAmt is set to prod.originalFaceAmt
					Integer enhancedFaceAmount = referenceProduct.getEnhancedFaceAmount();

					Band band = referenceProduct.getBandForDetails(tmpAge, genderUp1.charAt(0),
							multiQP.getClient().isSmoker(), multiQP.getFaceAmt());
					Integer minMaxFaceAmount = null;

					if (band != null) {
						minMaxFaceAmount = band.getMinMaxFaceAmount();
						System.out.println(band);
					}

					// set new face amount
					multiQP.setFaceAmt(enhancedFaceAmount); // EMA-Jan 29th 2017

					if (minMaxFaceAmount != null) {
						int tmp = Math.abs((enhancedFaceAmount) - minMaxFaceAmount);

						// upgrade original face amount
						if (tmp >= 0 && tmp <= 1000) {
							multiQP.setFaceAmt(minMaxFaceAmount); // EMA-Jan 29th 2017
						}
					}

					// set new face amount
					multiQP.setFaceAmt(enhancedFaceAmount);
					referenceProduct.setFaceAmount(enhancedFaceAmount);

				}

				product = quoteFacade.singleProductQuote(multiQP, referenceProduct);

			}

			updateJointUWClassses(2);

			Product newProduct = quoteFacade.jointSingleQuote(multiQP);

			cliHealthClass1 = newProduct.getClientHealthClasses().get(0);
			cliHealthClass2 = newProduct.getClientHealthClasses().get(1);

			healthClass1 = "";
			healthClassFr1 = "";

			healthClass2 = "";
			healthClassFr2 = "";

			isFamilyHistory1 = false;
			isFamilyHistory2 = false;

			isCigarretSmoker1 = false;
			isCigarretSmoker2 = false;

			classFlag1 = "";
			lowMonths1 = "";

			classFlag2 = "";
			lowMonths2 = "";

			resetJointUWClasses(1);
			resetJointUWClasses(2);

			for (HealthClass cliHC : cliHealthClass1) {

				healthClass1 = cliHC.getName();
				healthClassFr1 = cliHC.getNameFr();
				multiQP.getClient().setClassId(cliHC.getID());
				multiQP.getClient().setSmokeCode(cliHC.getSmokeCode());
				IMUWClass uwClass = productFacade.getIMUWClass(cliHC.getID());
				uwClass.setSmokeCode(cliHC.getSmokeCode());

				multiQP.getClient().setUwClass(uwClass);
				multiQP.getClient().getUwClass().setSmokeCode(cliHC.getSmokeCode());

				isCigarretSmoker1 = cliHC.isCigarretSmoker();
				isFamilyHistory1 = cliHC.isFamilyHistory();
				classFlag1 = cliHC.getDescription();
				lowMonths1 = cliHC.getNonSmokerMonths() + "";

			}

			updateJointUWClassses(1);
			/*
			 * Band bandA = referenceProduct.getBandForGlobalClass(tmpAge,
			 * multiQP.getClient().getGender(), multiQP.getFaceAmt(),
			 * multiQP.getClient().isSmoker(), "S");
			 * 
			 * if (bandA == null) {
			 * System.out.println("@@@@ unable to get Band for client1 for product :" +
			 * referenceProduct.getName()); // multiQP.setEnhancedFaceAmount(null);
			 * continue; }
			 * 
			 * // System.out.println("band1: "+band1);
			 * multiQP.getClient().setUwClass(multiQP.getClient().getUwClass(bandA.
			 * getClassId())); multiQP.getClient().setClassId(bandA.getClassId());
			 */

			for (HealthClass cliHC : cliHealthClass2) {
				healthClass2 = cliHC.getName();
				healthClassFr2 = cliHC.getNameFr();
				multiQP.getClient2().setClassId(cliHC.getID());
				multiQP.getClient2().setSmokeCode(cliHC.getSmokeCode());
				IMUWClass uwClass = productFacade.getIMUWClass(cliHC.getID());
				uwClass.setSmokeCode(cliHC.getSmokeCode());

				multiQP.getClient2().setUwClass(uwClass);
				multiQP.getClient2().getUwClass().setSmokeCode(cliHC.getSmokeCode());

				isCigarretSmoker2 = cliHC.isCigarretSmoker();
				isFamilyHistory2 = cliHC.isFamilyHistory();
				classFlag2 = cliHC.getDescription();
				lowMonths2 = cliHC.getNonSmokerMonths() + "";

			}
			updateJointUWClassses(2);

			/*
			 * Band bandB = referenceProduct.getBandForGlobalClass(tmpAge2,
			 * multiQP.getClient2().getGender(), multiQP.getFaceAmt(),
			 * multiQP.getClient2().isSmoker(), "S");
			 * 
			 * if (bandB == null) {
			 * System.out.println("@@@@ unable to get Band for client1 for product :" +
			 * referenceProduct.getName()); // multiQP.setEnhancedFaceAmount(null);
			 * continue; }
			 * 
			 * // System.out.println("band1: "+band1);
			 * multiQP.getClient2().setUwClass(multiQP.getClient2().getUwClass(bandB.
			 * getClassId())); multiQP.getClient2().setClassId(bandB.getClassId());
			 * 
			 */
			if (jointCalcMethod.equals("A") || jointCalcMethod.equals("P")) {
				newProduct = quoteFacade.jointSingleQuote(multiQP);

				// get underwriting details based on prod specs and faceamt; one list for each
				// client;
				List<String> underwritingList = quoteFacade.getUnderwriting(newProduct.getCompany().getID(),
						newProduct.getClientUnderwritingGroupID(0), faceAmountInt, tmpAge,
						Character.toString(multiQP.getClient().getGender()));
				newProduct.setClientUnderwriting(0, underwritingList);

				underwritingList = quoteFacade.getUnderwritingFr(newProduct.getCompany().getID(),
						newProduct.getClientUnderwritingGroupID(0), faceAmountInt, tmpAge,
						Character.toString(multiQP.getClient().getGender()));
				newProduct.setClientUnderwritingFr(0, underwritingList);

				underwritingList = quoteFacade.getUnderwriting(newProduct.getCompany().getID(),
						newProduct.getClientUnderwritingGroupID(1), faceAmountInt, tmpAge2,
						Character.toString(multiQP.getClient2().getGender()));
				newProduct.setClientUnderwriting(1, underwritingList);

				underwritingList = quoteFacade.getUnderwritingFr(newProduct.getCompany().getID(),
						newProduct.getClientUnderwritingGroupID(1), faceAmountInt, tmpAge2,
						Character.toString(multiQP.getClient2().getGender()));
				newProduct.setClientUnderwritingFr(1, underwritingList);

			}

			Client clientESA = null;

			if (jointCalcMethod.equals("E") || jointCalcMethod.equals("G")) {

				multiQP.setReferenceProduct(referenceProduct);
				clientESA = jointESAUtils.processJointESA(multiQP);

				multiQP.setClientESA(clientESA);

				QuoteParams tmpParams = new QuoteParams();
				tmpParams.initSingleMode();
				tmpParams.setReferenceProduct(referenceProduct);
				if (iq4Mode == IQ4Modes.JointMultiCompany) {
					tmpParams.setOnePeriodOnly(true);
				}

				tmpParams.setFaceAmt(multiQP.getFaceAmt());
				tmpParams.setOriginalFaceAmount(multiQP.getOriginalFaceAmount());
				tmpParams.setProductsAgeToUse(product.getAgeToUse());

				tmpParams.setClientESA(clientESA);
				tmpParams.setProductID(multiQP.getProductID());
				tmpParams.setCompanyID(multiQP.getCompanyID());
				tmpParams.setClient(multiQP.getClient());
				tmpParams.setClient2(multiQP.getClient2());
				tmpParams.setWebService(true);

// quote the Joint Product as Single Product
				newProduct = quoteFacade.singleProductQuote(tmpParams, null);

				Band band1 = referenceProduct.getBandForDetails(tmpAge, multiQP.getClient().getGender(),
						!multiQP.getClient().isNonSmoker(), faceAmountInt);
				Band band2 = referenceProduct.getBandForDetails(tmpAge2, multiQP.getClient2().getGender(),
						!multiQP.getClient2().isNonSmoker(), faceAmountInt);

				// get underwriting details based on prod specs and faceamt; one list for each
				// client;
				if (band1 != null) {

					List<String> underwritingList = quoteFacade.getUnderwriting(multiQP.getCompanyID(),
							band1.getUnderwritingGroup(), faceAmountInt, tmpAge,
							Character.toString(multiQP.getClient().getGender()));
					newProduct.setClientUnderwriting(0, underwritingList);
					underwritingList = quoteFacade.getUnderwritingFr(multiQP.getCompanyID(),
							band1.getUnderwritingGroup(), faceAmountInt, tmpAge,
							Character.toString(multiQP.getClient().getGender()));
					newProduct.setClientUnderwritingFr(0, underwritingList);
				} else {
					System.out.println("1226 GenericJoint band1 IS null");
				}

				if (band2 != null) {

					List<String> underwritingList = quoteFacade.getUnderwriting(multiQP.getCompanyID(),
							band2.getUnderwritingGroup(), faceAmountInt, tmpAge2,
							Character.toString(multiQP.getClient2().getGender()));
					newProduct.setClientUnderwriting(1, underwritingList);

					underwritingList = quoteFacade.getUnderwritingFr(multiQP.getCompanyID(),
							band2.getUnderwritingGroup(), faceAmountInt, tmpAge2,
							Character.toString(multiQP.getClient2().getGender()));
					newProduct.setClientUnderwritingFr(1, underwritingList);
				} else {
					System.out.println("1237 GenericJoint band2 IS null");
				}

			} // if (jointCalcMethod.equals("E") || jointCalcMethod.equals("G")) {

			newProduct.setCompanyID(product.getCompanyID());
			newProduct.setProductType(product.getProductType());

			findPromotion(newProduct);

			if (isHasPromotion() || product.hasPromoBuiltIn()) {
				newProduct.setPromotions(newProduct.getPromotions());

				if (getMultiQP().getClientESA() != null) {
					promotionsHelper.applyPromotion(newProduct, promo, getMultiQP().getClientESA().getActualAge(),
							multiQP.getFaceAmt(), multiQP);
				} else {
					promotionsHelper.applyPromotion(newProduct, promo, tmpAge, multiQP.getFaceAmt(), multiQP);
				}

			}
			promo = null;

			if (product.getProductType().getMasterID() == id1) {
				if (min1 > newProduct.getProductAnnualPremiumTotal()) {
					min1 = newProduct.getProductAnnualPremiumTotal();
					p1 = newProduct;
					p1a = product;
				}
			}

			if (product.getProductType().getMasterID() == id2) {
				if (min2 > newProduct.getProductAnnualPremiumTotal()) {
					min2 = newProduct.getProductAnnualPremiumTotal();
					p2 = newProduct;
					p2a = product;
				}
			}

			if (product.getProductType().getMasterID() == id3) {
				if (min3 > newProduct.getProductAnnualPremiumTotal()) {
					min3 = newProduct.getProductAnnualPremiumTotal();
					p3 = newProduct;
					p3a = product;
				}
			}

			if (product.getProductType().getMasterID() == id4) {
				if (min4 > newProduct.getProductAnnualPremiumTotal()) {
					min4 = newProduct.getProductAnnualPremiumTotal();
					p4 = newProduct;
					p4a = product;
				}
			}

			if (product.getProductType().getMasterID() == id5) {
				if (min5 > newProduct.getProductAnnualPremiumTotal()) {
					min5 = newProduct.getProductAnnualPremiumTotal();
					p5 = newProduct;
					p5a = product;
				}
			}

			if (product.getProductType().getMasterID() == id6) {
				if (min6 > newProduct.getProductAnnualPremiumTotal()) {
					min6 = newProduct.getProductAnnualPremiumTotal();
					p6 = newProduct;
					p6a = product;
				}
			}

			if (product.getProductType().getMasterID() == id7) {
				if (min7 > newProduct.getProductAnnualPremiumTotal()) {
					min7 = newProduct.getProductAnnualPremiumTotal();
					p7 = newProduct;
					p7a = product;
				}
			}

		}

		if (p1.getID() != 0) {
			finalList.add(p1);
		}
		if (p2.getID() != 0) {
			finalList.add(p2);
		}
		if (p3.getID() != 0) {
			finalList.add(p3);
		}
		if (p4.getID() != 0) {
			finalList.add(p4);
		}
		if (p5.getID() != 0) {
			finalList.add(p5);
		}
		if (p6.getID() != 0) {
			finalList.add(p6);
		}
		if (p7.getID() != 0) {
			finalList.add(p7);
		}

		Collections.sort(finalList, new TotalAnnComparator());
		try {
			for (Product product : finalList) {

				++counter;
				if (counter > 1) {
					buf.append(", ");
				}

				quoteFacade.getProductShortDescriptions(product);

				Company company = product.getCompany();
				@SuppressWarnings("unused")
				String companyNameEn = "";
				@SuppressWarnings("unused")
				String companyNameFr = "";
				if (company != null) {
					companyNameEn = product.getCompany().getName();
					companyNameFr = product.getCompany().getNameFr();

				}

				List<String> cliUndw1 = product.getClientUnderwriting().get(0);

				List<String> cliUndwFr1 = product.getClientUnderwritingFr().get(0);

				List<String> cliUndw2 = product.getClientUnderwriting().get(1);

				List<String> cliUndwFr2 = product.getClientUnderwritingFr().get(1);

				String clientUW1 = "";
				String clientUWFr1 = "";

				String clientUW2 = "";
				String clientUWFr2 = "";

				for (String cliUW : cliUndw1) {
					if (!clientUW1.equals("")) {
						clientUW1 += ", ";
					}
					clientUW1 += cliUW;
				}

				for (String cliUW : cliUndw2) {
					if (!clientUW2.equals("")) {
						clientUW2 += ", ";
					}
					clientUW2 += cliUW;
				}

				for (String cliUW : cliUndwFr1) {
					if (!clientUWFr1.equals("")) {
						clientUWFr1 += ", ";
					}
					clientUWFr1 += cliUW;
				}
				for (String cliUW : cliUndwFr2) {
					if (!clientUWFr2.equals("")) {
						clientUWFr2 += ", ";
					}
					clientUWFr2 += cliUW;
				}

				int levDecr = 0;
				String coverageType = "L"; // default A for all
				if (product.getInsuranceBen() != 0) {
					levDecr = product.getInsuranceBen();
				}
				if (levDecr == 2) {
					coverageType = "L"; // L=level D=decreasing
				} else {
					coverageType = "D"; // L=level D=decreasing
				}

				// quoteWizard.jointQP.singleProduct.clientUnderwriting[0]
				DateFormat df = new SimpleDateFormat("dd-MMM-YYYY");
				// BigDecimal percentage = product.getPercentageGainWithAgent().setScale(2,
				// RoundingMode.HALF_UP);
				double percentage = 0;

				String res = JsonGenerator.getResponseValuesJoint(counter, provCodeUp,
						Util.getStringFromDouble(product.getProductMonthlyPremiumTotal()),
						Util.getStringFromDouble(product.getProductAnnualPremiumTotal()), "" + percentage,
						convertibleAge + "", payableAge + "", healthClass1, healthClassFr1, healthClass2,
						healthClassFr2, clientUW1, clientUWFr1, clientUW2, clientUWFr2,
						df.format(multiQP.getClient().getFullBirthDate()),
						df.format(multiQP.getClient2().getFullBirthDate()), multiQP.getClient().getNearestAge(),
						multiQP.getClient().getActualAge(), multiQP.getClient2().getNearestAge(),
						multiQP.getClient2().getActualAge(), smokingUp1, smokingUp2, faceAmount, product,
						multiQP.getClient().getGender(), multiQP.getClient2().getGender(), classFlag1, lowMonths1,
						isCigarretSmoker1, isFamilyHistory1, classFlag2, lowMonths2, isCigarretSmoker2,
						isFamilyHistory2, coverageType);
				buf.append(res);

				// }// if(referenceProduct.getMinimumAnnualRequired() <=
				// referenceProduct.getProductAnnualPremiumTotal()){//mininmum premium added
				// april 14 2021 -aa
			} // for

			if (counter > 0) {
				buf.append(" ]");
				response = buf.toString();
			} else {
				response = "\"no matching records\"";
			}

		} catch (Exception ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: Reading the results raised: " + ex);
			}
			// response = "\"Reading the resultsSet raised:
			// "+cleanStringForJson(ex.getMessage())+"\"";
			throw new InsurfactPrivateException("Reading the results raised " + ex + " at counter " + counter
			// +" - "+ex.getMessage()
			// + "\n " + LogUtil.getStackTrace(ex, 10)
			// + "\n sql = " + sql
			);
		} finally {

		}

		try {
			if (con != null) {
				con.close();
			}
		} catch (SQLException e) {
			System.out.println("error closing connection con line 667 Generic e=" + e.getMessage());
		}

		return response;
	}

	public void saveQuote(String ip, int ws_userID, String url, String face, String dob, String gender, String smoker,
			String prov, String compID, String prodID, String productType)
			throws IOException, InsurfactPrivateException {

		int faceAmountInt = 0;
		try {
			faceAmountInt = Integer.parseInt(face);
		} catch (NumberFormatException e) {
		}

		Statement insStmt = null;
		String sql = "";

		sql = " INSERT INTO WS_QUOTES  " + "   (WS_QUOTES_INT_ID,   " + "    QUOTED_DATE,        "
				+ "    IP_ADDRESS,         " + "    WEB_SERVICE_USERS,  " + "    LINK_NAME,          "
				+ "    FACE_AMOUNT,        " + "    DATE_OF_BIRTH,      " + "    GENDER,             "
				+ "    SMOKER,             " + "    PROVINCE_CODE,      " + "    COMPANY_ID,         "
				+ "    PRODUCT_ID,         " + "    PRODUCT_TYPE)       " + " VALUES (WS_QUOTES_SEQ.NEXTVAL, "
				+ "         SYSDATE, '" + ip + "',   " + "         " + ws_userID + ", '" + url + "'," + "         "
				+ faceAmountInt + ", TO_DATE('" + dob + "', 'YYYY-MM-DD')," + "         '" + gender + "', '" + smoker
				+ "'," + "         '" + prov + "','" + compID + "','" + prodID + "','" + productType + "')";

		try {
			Class.forName("oracle.jdbc.OracleDriver");
		} catch (ClassNotFoundException ex) {
			if (DEBUG) {
				System.out.println(
						TAG + ".getResponseString: *Class.forName(\"oracle.jdbc.OracleDriver\")* raised: " + ex);
			}
			throw new InsurfactPrivateException("*Class.forName(\"oracle.jdbc.OracleDriver\")* raised " + ex);
		}

		boolean forProdDb = false;
		try {
			forProdDb = InsurfactSdkProperties.isForProdDb();
		} catch (Throwable ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: *InsurfactSdkProperties.isForProdDb()* raised: " + ex);
			}
			throw new InsurfactPrivateException("*InsurfactSdkProperties.isForProdDb()* raised " + ex
			// +" - "+ex.getMessage()
					+ "\n" + LogUtil.getStackTrace(ex, 10));
		}

		if (DEBUG) {
			System.out.println(TAG + ".getResponseString: forProdDb = " + forProdDb);
		}

		try {

			con = InsurfactSdkProperties.getDb2Connection(forProdDb);// skynet -- getDb2Connection ins

		} catch (SQLException ex) {
			String connectionText = "";
			if (DEBUG) {
				connectionText = "\n" + Util.getTextOnDb2Connection(con);
				System.out.println(TAG + ".getResponseString: " + "*InsurfactSdkProperties.getDb2Connection(forProdDb {"
						+ forProdDb + "})* raised: " + ex + connectionText);
			}
			throw new InsurfactPrivateException(
					"*InsurfactSdkProperties.getDb2Connection(forProdDb {" + forProdDb + "})* raised " + ex
					// +" - "+ex.getMessage()
							+ connectionText + "\n" + LogUtil.getStackTrace(ex, 10));
		}

		if (DEBUG) {
			System.out.println(TAG + ".getResponseString: " + "*getTextOnDb2Connection()* returned "
					+ Util.getTextOnDb2Connection(con));
		}

		try {

			insStmt = con.createStatement();
//System.out.println("1472 GenericJoint before executeUpd");
			insStmt.executeUpdate(sql);
//System.out.println("1474 GenericJoint after executeUpd");          

		} catch (SQLException e) {
			System.out.println(" sql Exception message:" + e.getMessage());
		} finally {
			try {
				if (insStmt != null) {
					insStmt.close();
				}
				if (con != null) {
					con.close();
				}
			} catch (SQLException ignored) {
				System.out.println("SQLException ignored: " + ignored.getMessage() + " " + sql);
			}
		}
	}

	public QuoteParams getMultiQP() {
		return multiQP;
	}

	public void setMultiQP(QuoteParams multiQP) {
		this.multiQP = multiQP;
	}

	private boolean updateJointUWClassses(int c) {

//        System.out.println("**** updateJointUWClassses c="+c);
		QuoteParams qp = getMultiQP();

		Client client;

		if (c == 1) {
			client = qp.getClient();
		} else {
			client = qp.getClient2();
		}

		// reset values
		resetJointUWClasses(c);

//        qp.setInsuranceBen(0);
//        qp.setHealthClass("");
//        qp.setUwType(0);
		if (qp.getProductID() == 0) {
			return false;
		}

		if (qp.getFaceAmt() == null) {
			return false;
		}

		int age = client.getActualAge();

		if (age > qp.getReferenceProduct().getMaxAge()) {
			if (qp.getReferenceProduct().getAgeToUse().equalsIgnoreCase("N")) {
				age = client.getNearestAge();
			}
		}

		// System.out.println("qp.getReferenceProduct().getMaxAge():
		// "+qp.getReferenceProduct().getMaxAge());
		/*
		 * if(age > qp.getReferenceProduct().getMaxAge()){ age =
		 * qp.getReferenceProduct().getMaxAge(); }
		 */
		List<Integer> ids = qp.getReferenceProduct().getClassIdsForBands(client.getGender(), client.isSmoker(), age,
				qp.getFaceAmt());

//        System.out.println(ids);
		List<IMUWClass> uwClasses = new ArrayList<>();

		if (ids == null || ids.isEmpty()) {
			return false;
		}

		for (Integer id : ids) {

			IMUWClass uwClass = productFacade.getIMUWClass(id);

			List<Band> bands = qp.getReferenceProduct().getBands(id);

			if (bands == null || bands.isEmpty()) {
				return false;
			}

			Band band = bands.get(0);

			if (uwClass != null) {
				uwClass.setGlobalClass(band.getGlobalHealthClass());
				uwClass.setInsuranceBen(qp.getReferenceProduct().getInsuranceBen());
				uwClass.setUwType(qp.getReferenceProduct().getUwtype());
				uwClass.setMonthNonSmoker(band.getMonths());
				uwClass.setSmokeCode(band.getSmokeCode());
				uwClass.setCigarette(band.isCigarettes());
				uwClass.setRowKey(band.getRowrey());

				System.out.println("adding HealthClass : " + uwClass.getEngdesc() + "  id=" + uwClass.getClassid()
						+ " gc=" + uwClass.getGlobalClass() + " smokeCode=" + uwClass.getSmokeCode() + " uw="
						+ uwClass.getUwType() + " ib=" + uwClass.getInsuranceBen());
				uwClasses.add(uwClass);
			}
		}

//        if (language.isEnglish()) {
//
//            if (qp.getClient().isNonSmoker()) {
//                Collections.sort(uwClasses, new HealthClassMonthComparator());
//            } else {
//                Collections.sort(uwClasses, new HealthClassByOrderComparator());
//            }
////            
//        } else {
//            if (qp.getClient().isNonSmoker()) {
//                Collections.sort(uwClasses, new HealthClassMonthComparator());
//            } else {
//                Collections.sort(uwClasses, new HealthClassFrenchComparator());
//            }
//        }

		client.setUwClasses(uwClasses);

		// set a default if not set
		if (!uwClasses.isEmpty()) {

			for (IMUWClass u : uwClasses) {
				if (u.getGlobalClass().equalsIgnoreCase("S")) {// why is doing this?????
					// if (u.getClassid() == client.getClassId()) {

					client.setUwClass(u);

					client.setClassId(u.getClassid());
//            qp.getReferenceProduct().setClassId(uwClass.getClassid());
					// System.out.println("CORRECT!! checking uwclass setting classid: " +
					// u.getClassid());
					client.setSmokeCode(u.getSmokeCode());
					break;
				}
			}
		} else {

//            applicationBean.postMessage(FacesMessage.SEVERITY_ERROR, 
//                    "Unable to qualify the requested product based on the provided information",
//                    "Impossible de qualifier le produit demandé sur la base des informations fournies", "","");
//  
			return false;
		}

		return true;
	}

	private void resetJointUWClasses(int c) {

		QuoteParams multiQP = getMultiQP();

		Client client;

		if (c == 1) {
			client = multiQP.getClient();
		} else {
			client = multiQP.getClient2();
		}

		// reset values
		client.getUwClasses().clear();
		client.setUwClass(null);
		client.setClassId(0);
		client.setSmokeCode("");
	}

	public Promotions getPromo() {
		return promo;

	}

	private String promoCode;

	public String getPromoCode() {
		return promoCode;
	}

	public void setPromoCode(String promoCode) {
		this.promoCode = promoCode;
	}

	public void setPromo(Promotions promo) {
		System.out.println("promo: " + promo + "    " + this.promo);
		this.promo = promo;
	}

	public boolean isHasPromotion() {
		System.out.println("isHasPromotion: " + promo);
		if (promo != null) {
			return true;
		}
		return false;
	}

	public void findPromotion(Product product) {
		if (product != null) {
			int age = getMultiQP().getClient().getNearestAge();
			if ((iq4Mode == IQ4Modes.JointSingleCompany || iq4Mode == IQ4Modes.JointMultiCompany)
					&& getMultiQP().getClientESA() != null) {
				age = getMultiQP().getClientESA().getActualAge();
			}
			product.getPromotions().clear();
			for (Promotions p : promotionsHelper.findPromoByProductId(product.getID())) {
				if (p.hasPromotion(getMultiQP(), age)) {
					product.getPromotions().add(p);
					if (getMultiQP().getFaceAmt() != null) { // && (p.getPromoCode() == null ||
																// p.getPromoCode().isEmpty())
						promo = p;
					} else {// if (getQuoteParams().getFaceAmt() == null && (p.getPromoCode() == null ||
							// p.getPromoCode().isEmpty())) {
						promo = null;
					}
				}
			}

			// product.setPromotions(promotionsFacade.findByProductId(product.getID()));
		}
	}

	public void findPromotionToTest(Product product) {
		if (product != null) {
			int age = getMultiQP().getClient().getNearestAge();
			if ((iq4Mode == IQ4Modes.JointSingleCompany || iq4Mode == IQ4Modes.JointMultiCompany)
					&& getMultiQP().getClientESA() != null) {
				age = getMultiQP().getClientESA().getActualAge();
			}
			product.getPromotions().clear();
			for (Promotions p : promotionsHelper.findByProductIdToTest(product.getID())) {
				if (p.hasPromotion(getMultiQP(), age)) {
					product.getPromotions().add(p);
					if (getMultiQP().getFaceAmt() != null) { // && (p.getPromoCode() == null ||
																// p.getPromoCode().isEmpty())
						promo = p;
					} else {// if (getQuoteParams().getFaceAmt() == null && (p.getPromoCode() == null ||
							// p.getPromoCode().isEmpty())) {
						promo = null;
					}
				}
			}

			// product.setPromotions(promotionsFacade.findByProductId(product.getID()));
		}
	}

	public void saveQuote(String ip, int ws_userID, String url, String face, String dob, String gender, String smoker,
			String prov, String compID, String prodID, String productType, String urlOrigin)
			throws IOException, InsurfactPrivateException {

		int faceAmountInt = 0;
		try {
			faceAmountInt = Integer.parseInt(face);
		} catch (NumberFormatException e) {
		}

		Statement insStmt = null;
		String sql = "";

		sql = " INSERT INTO WS_QUOTES  " + "   (WS_QUOTES_INT_ID,   " + "    QUOTED_DATE,        "
				+ "    IP_ADDRESS,         " + "    WEB_SERVICE_USERS,  " + "    LINK_NAME,          "
				+ "    FACE_AMOUNT,        " + "    DATE_OF_BIRTH,      " + "    GENDER,             "
				+ "    SMOKER,             " + "    PROVINCE_CODE,      " + "    COMPANY_ID,         "
				+ "    PRODUCT_ID,         " + "    PRODUCT_TYPE, REQUEST_SITE)  " + " VALUES (WS_QUOTES_SEQ.NEXTVAL, "
				+ "         SYSDATE, '" + ip + "',   " + "         " + ws_userID + ", '" + url + "'," + "         "
				+ faceAmountInt + ", TO_DATE('" + dob + "', 'YYYY-MM-DD')," + "         '" + gender + "', '" + smoker
				+ "'," + "         '" + prov + "','" + compID + "','" + prodID + "','" + productType + "', '"
				+ urlOrigin + "')";

		try {
			Class.forName("oracle.jdbc.OracleDriver");
		} catch (ClassNotFoundException ex) {
			if (DEBUG) {
				System.out.println(
						TAG + ".getResponseString: *Class.forName(\"oracle.jdbc.OracleDriver\")* raised: " + ex);
			}
			throw new InsurfactPrivateException("*Class.forName(\"oracle.jdbc.OracleDriver\")* raised " + ex);
		}

		boolean forProdDb = false;
		try {
			forProdDb = InsurfactSdkProperties.isForProdDb();
		} catch (Throwable ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: *InsurfactSdkProperties.isForProdDb()* raised: " + ex);
			}
			throw new InsurfactPrivateException("*InsurfactSdkProperties.isForProdDb()* raised " + ex
			// +" - "+ex.getMessage()
					+ "\n" + LogUtil.getStackTrace(ex, 10));
		}

		if (DEBUG) {
			System.out.println(TAG + ".getResponseString: forProdDb = " + forProdDb);
		}

		try {

			con = InsurfactSdkProperties.getDb2Connection(forProdDb);// skynet -- getDb2Connection ins

		} catch (SQLException ex) {
			String connectionText = "";
			if (DEBUG) {
				connectionText = "\n" + Util.getTextOnDb2Connection(con);
				System.out.println(TAG + ".getResponseString: " + "*InsurfactSdkProperties.getDb2Connection(forProdDb {"
						+ forProdDb + "})* raised: " + ex + connectionText);
			}
			throw new InsurfactPrivateException(
					"*InsurfactSdkProperties.getDb2Connection(forProdDb {" + forProdDb + "})* raised " + ex
					// +" - "+ex.getMessage()
							+ connectionText + "\n" + LogUtil.getStackTrace(ex, 10));
		}

		if (DEBUG) {
			System.out.println(TAG + ".getResponseString: " + "*getTextOnDb2Connection()* returned "
					+ Util.getTextOnDb2Connection(con));
		}

		try {

			insStmt = con.createStatement();
//System.out.println("1472 GenericJoint before executeUpd");
			insStmt.executeUpdate(sql);
//System.out.println("1474 GenericJoint after executeUpd");          

		} catch (SQLException e) {
			System.out.println(" sql Exception message:" + e.getMessage());
		} finally {
			try {
				if (insStmt != null) {
					insStmt.close();
				}
				if (con != null) {
					con.close();
				}
			} catch (SQLException ignored) {
				System.out.println("SQLException ignored: " + ignored.getMessage() + " " + sql);
			}
		}
	}

}
