package com.insurfact.ins;
import com.insurfact.sdk.properties.InsurfactSdkProperties;
import com.insurfact.sdk.utils.InsurfactPrivateException;
import com.insurfact.websecuring.UserValidationDundas;
import com.insurfact.websecuring.UserValidationWidget;

import jakarta.ejb.EJB;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/webresources")
public class InsuranceLeadRestAPI {

    private static final Logger LOGGER = Logger.getLogger(InsuranceLeadRestAPI.class.getName());
    private static boolean DEBUG = false;

    @EJB
    private UserValidationWidget userValidationWidget;

    @Autowired
    private UserValidationDundas userValidationDundas;

    @Autowired
    private GenericLeads leadService;

    /**
     * Creates a new instance of InsuranceLeadController.
     */
    public InsuranceLeadRestAPI() {
    }

    /**
     * Endpoint for creating a single life insurance lead.
     *
     * @param compID Company ID
     * @param prodID Product ID
     * @param insAmount Insurance amount
     * @param annualPrem Annual premium
     * @param monthPrem Monthly premium
     * @param firstName First name
     * @param lastName Last name
     * @param gender Gender
     * @param dob Date of birth
     * @param smoker Smoker status
     * @param prov Province code
     * @param email Email address
     * @param phoneNumber Phone number
     * @param lang Language preference
     * @param message Additional message
     * @param request HTTP request
     * @param response HTTP response
     * @param tokenCli Optional token for authentication
     * @return Response entity with status and body
     */
    @ResponseBody
    @GetMapping(value = "/lifeLeads/{compID}/{prodID}/{insAmount}/{annualPrem}/{monthPrem}/{firstName}/{lastName}/{gender}/{dob}/{smoker}/{prov}/{email}/{phoneNumber}/{lang}/{message}",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> createSingleLifeLead(
            @PathVariable("compID") String compID,
            @PathVariable("prodID") String prodID,
            @PathVariable("insAmount") String insAmount,
            @PathVariable("annualPrem") String annualPrem,
            @PathVariable("monthPrem") String monthPrem,
            @PathVariable("firstName") String firstName,
            @PathVariable("lastName") String lastName,
            @PathVariable("gender") String gender,
            @PathVariable("dob") String dob,
            @PathVariable("smoker") String smoker,
            @PathVariable("prov") String prov,
            @PathVariable("email") String email,
            @PathVariable("phoneNumber") String phoneNumber,
            @PathVariable("lang") String lang,
            @PathVariable("message") String message,
            HttpServletRequest request,
            HttpServletResponse response,
            @RequestHeader(value = "token", required = false) String tokenCli) {

        DEBUG = InsurfactSdkProperties.getBoolean("debug.enabled", false);
        LOGGER.log(Level.INFO, "Processing single life lead request");

        // Process with empty values for second life
        return processLeadRequest(
                compID, prodID, insAmount, annualPrem, monthPrem,
                firstName, lastName, gender, dob, smoker,
                "", "", "", "", "",  // Empty values for second life
                prov, email, phoneNumber, lang, message,
                request, "SingleLifeLead", tokenCli);
    }

    /**
     * Endpoint for creating a joint life insurance lead.
     *
     * @param compID Company ID
     * @param prodID Product ID
     * @param insAmount Insurance amount
     * @param annualPrem Annual premium
     * @param monthPrem Monthly premium
     * @param firstName First name of primary insured
     * @param lastName Last name of primary insured
     * @param gender Gender of primary insured
     * @param dob Date of birth of primary insured
     * @param smoker Smoker status of primary insured
     * @param firstName2 First name of second insured
     * @param lastName2 Last name of second insured
     * @param gender2 Gender of second insured
     * @param dob2 Date of birth of second insured
     * @param smoker2 Smoker status of second insured
     * @param prov Province code
     * @param email Email address
     * @param phoneNumber Phone number
     * @param lang Language preference
     * @param message Additional message
     * @param request HTTP request
     * @param response HTTP response
     * @param tokenCli Optional token for authentication
     * @return Response entity with status and body
     */
    @ResponseBody
    @GetMapping(value = "/lifeLeadsJoint/{compID}/{prodID}/{insAmount}/{annualPrem}/{monthPrem}/{firstName}/{lastName}/{gender}/{dob}/{smoker}/{firstName2}/{lastName2}/{gender2}/{dob2}/{smoker2}/{prov}/{email}/{phoneNumber}/{lang}/{message}",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<?> createJointLifeLead(
            @PathVariable("compID") String compID,
            @PathVariable("prodID") String prodID,
            @PathVariable("insAmount") String insAmount,
            @PathVariable("annualPrem") String annualPrem,
            @PathVariable("monthPrem") String monthPrem,
            @PathVariable("firstName") String firstName,
            @PathVariable("lastName") String lastName,
            @PathVariable("gender") String gender,
            @PathVariable("dob") String dob,
            @PathVariable("smoker") String smoker,
            @PathVariable("firstName2") String firstName2,
            @PathVariable("lastName2") String lastName2,
            @PathVariable("gender2") String gender2,
            @PathVariable("dob2") String dob2,
            @PathVariable("smoker2") String smoker2,
            @PathVariable("prov") String prov,
            @PathVariable("email") String email,
            @PathVariable("phoneNumber") String phoneNumber,
            @PathVariable("lang") String lang,
            @PathVariable("message") String message,
            HttpServletRequest request,
            HttpServletResponse response,
            @RequestHeader(value = "token", required = false) String tokenCli) {

        DEBUG = InsurfactSdkProperties.getBoolean("debug.enabled", false);
        LOGGER.log(Level.INFO, "Processing joint life lead request");

        return processLeadRequest(
                compID, prodID, insAmount, annualPrem, monthPrem,
                firstName, lastName, gender, dob, smoker,
                firstName2, lastName2, gender2, dob2, smoker2,
                prov, email, phoneNumber, lang, message,
                request, "JointLifeLead", tokenCli);
    }

    /**
     * Common method to process both single and joint life lead requests.
     * Supports both origin-based and token-based authentication.
     *
     * @param compID Company ID
     * @param prodID Product ID
     * @param insAmount Insurance amount
     * @param annualPrem Annual premium
     * @param monthPrem Monthly premium
     * @param firstName First name
     * @param lastName Last name
     * @param gender Gender
     * @param dob Date of birth
     * @param smoker Smoker status
     * @param firstName2 First name of second life (optional)
     * @param lastName2 Last name of second life (optional)
     * @param gender2 Gender of second life (optional)
     * @param dob2 Date of birth of second life (optional)
     * @param smoker2 Smoker status of second life (optional)
     * @param prov Province code
     * @param email Email address
     * @param phoneNumber Phone number
     * @param lang Language preference
     * @param message Additional message
     * @param request HTTP request
     * @param requestType Type of request (for logging)
     * @param tokenCli Optional token for authentication
     * @return Response entity with status and body
     */
    private ResponseEntity<?> processLeadRequest(
            String compID, String prodID, String insAmount, String annualPrem, String monthPrem,
            String firstName, String lastName, String gender, String dob, String smoker,
            String firstName2, String lastName2, String gender2, String dob2, String smoker2,
            String prov, String email, String phoneNumber, String lang, String message,
            HttpServletRequest request, String requestType, String tokenCli) {

        String responseValue = "test";
        String ipAddress = extractIpAddress(request);
        String urlOrigin = extractUrlOrigin(request);

        int userId = 44444;// validateUser(urlOrigin, ipAddress, tokenCli, requestType);
        Logger.getLogger(InsuranceLeadRestAPI.class.getName()).log(Level.INFO, "User ID: {0}", userId);
        if (userId != 0) {
            LOGGER.log(Level.INFO, "User validated successfully: ID={0}, Origin={1}", new Object[]{userId, urlOrigin});

            try {
                responseValue = leadService.saveLeadLife(
                        urlOrigin, ipAddress, userId, compID, prodID, insAmount, annualPrem,
                        monthPrem, firstName, lastName, gender, dob, smoker, firstName2, lastName2,
                        gender2, dob2, smoker2, prov, email, phoneNumber, lang, message);

                LOGGER.log(Level.INFO, "Lead saved successfully for user ID: {0}", userId);

            } catch (IOException e) {
                LOGGER.log(Level.SEVERE, "IO Exception while saving lead: {0}", e.getMessage());
                return createErrorResponse(e);
            } catch (InsurfactPrivateException e) {
                LOGGER.log(Level.SEVERE, "Business Exception while saving lead: {0}", e.getMessage());
                return createErrorResponse(e);
            }
        } else {
            LOGGER.log(Level.WARNING, "User validation failed for IP: {0}, Origin: {1}, Token: {2}",
                    new Object[]{ipAddress, urlOrigin, tokenCli != null ? "provided" : "not provided"});

            // For token authentication, use specific message
            if (tokenCli != null && !tokenCli.isEmpty()) {
                return ResponseEntity
                        .status(HttpStatus.UNAUTHORIZED)
                        .contentType(MediaType.APPLICATION_JSON)
                        .body("{\"response\": \"token does not match\"}");
            }
        }

        if (DEBUG) {
            LOGGER.log(Level.INFO, "Response value: {0}", responseValue);
        }

        return createResponse(responseValue);
    }

    /**
     * Extracts the client IP address from the request.
     *
     * @param request HTTP request
     * @return IP address string
     */
    private String extractIpAddress(HttpServletRequest request) {
        return request.getRemoteAddr();
    }

    /**
     * Extracts the origin URL from the request headers.
     *
     * @param request HTTP request
     * @return Origin URL or "noOrigin" if not found
     */
    private String extractUrlOrigin(HttpServletRequest request) {
        String urlOrigin = request.getHeader("origin");

        if (urlOrigin == null || urlOrigin.isEmpty()) {
            urlOrigin = "noOrigin";
        }

        return urlOrigin;
    }

    /**
     * Validates the user based on origin URL, IP address, and token (if provided).
     * Supports both origin-based and token-based authentication.
     *
     * @param urlOrigin Origin URL
     * @param ipAddress IP address
     * @param tokenCli Authentication token (optional)
     * @param requestType Type of request (for logging)
     * @return User ID if validation successful, 0 otherwise
     */
    private int validateUser(String urlOrigin, String ipAddress, String tokenCli, String requestType) {
        int userId = 0;

        try {
            // Token-based authentication if token is provided
            if (tokenCli != null && !tokenCli.isEmpty()) {
                userId = userValidationDundas.getWSUserID(tokenCli, ipAddress);
                if (DEBUG) {
                    LOGGER.log(Level.INFO, "Token-based validation - User ID: {0}", userId);
                }
            }
            // Fall back to origin-based authentication if no token or token authentication failed
            else {
                userId = userValidationWidget.getUsersID(urlOrigin, ipAddress, true);
                if (DEBUG) {
                    LOGGER.log(Level.INFO, "Origin-based validation - User ID: {0}", userId);
                }
            }
        } catch (InsurfactPrivateException ex) {
            LOGGER.log(Level.SEVERE, "* {0} ERROR - IP: {1}, Origin: {2}, Token: {3}",
                    new Object[]{requestType, ipAddress, urlOrigin, tokenCli != null ? "provided" : "not provided"});
            LOGGER.log(Level.SEVERE, "User validation exception", ex);
        }

        return userId;
    }

    /**
     * Creates appropriate HTTP response based on the result value.
     *
     * @param responseValue Response value from service
     * @return ResponseEntity with appropriate status and body
     */
    private ResponseEntity<?> createResponse(String responseValue) {
        if ("test".equals(responseValue)) {
            return ResponseEntity
                    .status(HttpStatus.NOT_FOUND)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body("");
        } else {
            return ResponseEntity
                    .status(HttpStatus.OK)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body("{\"response\":\"OK\"}");
        }
    }

    /**
     * Creates error response for exceptions.
     *
     * @param e Exception that occurred
     * @return ResponseEntity with error status
     */
    private ResponseEntity<?> createErrorResponse(Exception e) {
        return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .contentType(MediaType.APPLICATION_JSON)
                .body("{\"error\":\"" + e.getMessage() + "\"}");
    }
}
