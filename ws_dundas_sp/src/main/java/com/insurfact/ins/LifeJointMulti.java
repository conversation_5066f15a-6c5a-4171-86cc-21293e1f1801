/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2019 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.ins;

import com.insurfact.iq4.ejb.EnhancedProductFacade;
import com.insurfact.iq4.ejb.IQ4EngineFacade;
import com.insurfact.iq4.ejb.IQ4JointSEAUtilsFacade;
import com.insurfact.iq4.ejb.IQ4ProductFacade;
import com.insurfact.iq4.ejb.PromotionsHelper;

import com.insurfact.sdk.properties.InsurfactSdkProperties;
import com.insurfact.sdk.utils.InsurfactPrivateException;
import java.io.IOException;

import java.util.Date;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.ejb.EJB;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.insurfact.websecuring.UserValidationDundas;
/*
 * <AUTHOR>
 */
//lifeJointMulti/{product_type_id}/{face_amount}/{province_code}{date_of_birth1}/{gender1}/{smoking1}/{date_of_birth2}/{gender2}/{smoking2}")
//http://10.10.88.22:28081/WS_INS_JNT/webresources/lifeJointMulti/6/150000/QC/1985-01-06/M/N/1985-01-06/F/N/F

//https://wsins.insurfact.com/WS_INS2_DUNDAS/webresources/lifeJointMulti/6/250000/QC/1985-01-06/M/N/P/1985-01-06/F/N/S/F
//http://10.10.88.22:28081/WS_INS2_DUNDAS/webresources/lifeJointMulti/6/250000/QC/1985-01-06/M/N/P/1985-01-06/F/N/S/F
//http://10.10.88.15:8080/WS_INS2_DUNDAS/webresources/lifeJointMulti/6/250000/QC/1985-01-06/M/N/S/1985-01-06/F/N/S/F
@RestController
@RequestMapping("/webresources/lifeJointMulti") // Base path for the controller
public class LifeJointMulti {

	private static final String TAG = LifeJointMulti.class.getName();
 

	private static boolean DEBUG = false;

	private static final String PRODUCT_CLASS = "LIFE";

	public static final String dobFormat = "yyyy-MM-dd";

	@EJB
	private IQ4EngineFacade quoteFacade;

	@EJB
	private IQ4JointSEAUtilsFacade jointESAUtils;

	@EJB
	private IQ4ProductFacade productFacade;

	@EJB
	private EnhancedProductFacade enhancedProductFacade;

	@EJB
	private PromotionsHelper promotionsHelper;

	@Autowired
	private final UserValidationDundas userValidation;

    public LifeJointMulti(UserValidationDundas userValidation) {
        this.userValidation = userValidation;
    }


    @GetMapping(value="/{product_type_id}/{face_amount}/{province_code}/{client1DOB}/{client1Gender}/{client1Smoking}/{healthClass1}/{client2DOB}/{client2Gender}/{client2Smoking}/{healthClass2}/{jointType}/{levelOrDecr}", produces = MediaType.APPLICATION_JSON_VALUE)
	@ResponseBody
	public String getJson(HttpServletRequest request, HttpServletResponse response,
			@PathVariable("product_type_id") String productTypeId, @PathVariable("face_amount") String faceAmount,
			@PathVariable("province_code") String provCode, @PathVariable("client1DOB") String dateOfBirth1,
			@PathVariable("client1Gender") String gender1, @PathVariable("client1Smoking") String smoking1,
			@PathVariable("healthClass1") String healthClass1, @PathVariable("client2DOB") String dateOfBirth2,
			@PathVariable("client2Gender") String gender2, @PathVariable("client2Smoking") String smoking2,
			@PathVariable("healthClass2") String healthClass2, @PathVariable("jointType") String jointType, // F=first to die L=last to die
			@PathVariable("levelOrDecr") String levelOrDecr, // F=first to die L=last to die
			@RequestHeader("token") String tokenCli) {

		DEBUG = InsurfactSdkProperties.getBoolean("debug.enabled", true);
		String responseAll = "{\"response\": token does not match }";
		String ipAddress = request.getRemoteAddr();

		String urlOrigin = request.getHeader("origin");// changed to origin from Referer Jan 17th, 2022 aa
		if (urlOrigin != null) {
			if (urlOrigin.equals(""))
				urlOrigin = "noOrigin";
		} else
			urlOrigin = "noOrigin";
 
		int wsUserID = 0;

		try {
			wsUserID = userValidation.getWSUserID(tokenCli, ipAddress);
		} catch (InsurfactPrivateException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		if (wsUserID != 0) {

			DateFormat df = new SimpleDateFormat(dobFormat);
			df.getCalendar().setLenient(false);
			Date date1 = Calendar.getInstance().getTime();
			Date date2 = Calendar.getInstance().getTime();
			try {
				date1 = df.parse(dateOfBirth1);
				date2 = df.parse(dateOfBirth2);
			} catch (Exception ex) {
				@SuppressWarnings("unused")
				final String s = TAG + ".startMulti: *df.parse(dob)* with dobFormat {" + dobFormat + "} raised: " + ex;
			}

			String newDOB = dateOfBirth2;
			String newGend = gender2;
			String newSmok = smoking2;
			String newHealth = healthClass2;

			if (date1.after(date2)) {
				dateOfBirth2 = dateOfBirth1;
				gender2 = gender1;
				smoking2 = smoking1;
				healthClass2 = healthClass1;

				dateOfBirth1 = newDOB;
				gender1 = newGend;
				smoking1 = newSmok;
				healthClass1 = newHealth;

			}

			GenericJoint generic = new GenericJoint(PRODUCT_CLASS, "ALL", quoteFacade, jointESAUtils, productFacade,
					enhancedProductFacade, promotionsHelper);
			String responseValue = generic.getNoExceptionJoint(productTypeId, faceAmount, provCode, dateOfBirth1,
					gender1, smoking1, dateOfBirth2, gender2, smoking2, jointType, "", healthClass1, healthClass2,
					levelOrDecr);

			try {
				generic.saveQuote(ipAddress, wsUserID, "lifeJointMulti", faceAmount, dateOfBirth1, gender1, smoking1,
						provCode, "", "", "", urlOrigin);
			} catch (IOException e) {
				System.out.println("119 lifeJointMulti IOException e=" + e.getMessage());
			} catch (InsurfactPrivateException e) {
				System.out.println("122 lifeJointMulti InsurfactPrivateException e=" + e.getMessage());
			}

			responseAll = "{\"response\": " + responseValue + " }";

			if (DEBUG) {
				System.out.println(TAG + ".getJson: returning $$" + responseAll + "$$");
			}
		}

		return responseAll;
	}

}
