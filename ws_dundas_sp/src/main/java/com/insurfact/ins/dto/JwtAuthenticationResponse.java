package com.insurfact.ins.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class JwtAuthenticationResponse {
    private boolean authenticated;
    private String token;
    private String message;
    private Long userId;

    public JwtAuthenticationResponse(boolean authenticated, String message) {
        this.authenticated = authenticated;
        this.message = message;
    }

    public JwtAuthenticationResponse(boolean authenticated, String token, Long userId, String message) {
        this.authenticated = authenticated;
        this.token = token;
        this.userId = userId;
        this.message = message;
    }

    // Manual getter/setter in case Lombok doesn't work
    public boolean isAuthenticated() {
        return authenticated;
    }

    public void setAuthenticated(boolean authenticated) {
        this.authenticated = authenticated;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
}