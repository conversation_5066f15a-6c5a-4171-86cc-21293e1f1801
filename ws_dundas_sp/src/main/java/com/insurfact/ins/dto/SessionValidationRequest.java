package com.insurfact.ins.dto; // Adjust package as needed

// Using Lombok for boilerplate, or generate getters/setters manually
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SessionValidationRequest {
    private String sessionId;

    // Manual getter/setter in case Lombok doesn't work
    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }
}