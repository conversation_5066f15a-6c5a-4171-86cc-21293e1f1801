package com.insurfact.ins.dto;

import com.insurfact.ins.enums.Language;
import com.insurfact.ins.enums.ProvinceCode;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;

public class LeadCreationRequest {
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @NotNull(message = "Company ID cannot be null")
    private Integer companyId;

    @NotNull(message = "Product ID cannot be null")
    private Integer productId;

    @NotNull(message = "Insurance amount cannot be null")
    @Positive(message = "Insurance amount must be positive")
    private BigDecimal insuranceAmount;

    @PositiveOrZero(message = "Annual premium must be positive or zero")
    private BigDecimal annualPremium;

    @PositiveOrZero(message = "Monthly premium must be positive or zero")
    private BigDecimal monthlyPremium;

    @Valid
    @NotNull(message = "Primary insured information cannot be null")
    private PersonalInfo primaryInsured;

    @Valid
    private PersonalInfo secondaryInsured;

    @NotNull(message = "Province code cannot be null")
    private ProvinceCode provinceCode;

    @NotBlank(message = "Email cannot be blank")
    @Email(message = "Invalid email format")
    @Size(max = 255)
    private String email;

    @NotBlank(message = "Phone number cannot be blank")
    @Size(max = 20)
    @Pattern(regexp = "^(\\d{3}-\\d{3}-\\d{4}|\\d{10}|\\d{3}-\\d{7})$", message = "Phone number must be in format XXX-XXX-XXXX")
    private String phoneNumber;

    @NotNull(message = "Language preference cannot be null")
    private Language languagePreference;

    @Size(max = 1000)
    private String message;


    public boolean checkIfJointApplication() {
        return secondaryInsured != null && secondaryInsured.hasValidName();
    }
    // Getters and setters
    public PersonalInfo getPrimaryInsured() {
        return primaryInsured;
    }

    public void setPrimaryInsured(PersonalInfo primaryInsured) {
        this.primaryInsured = primaryInsured;
    }

    public PersonalInfo getSecondaryInsured() {
        return secondaryInsured;
    }

    public void setSecondaryInsured(PersonalInfo secondaryInsured) {
        this.secondaryInsured = secondaryInsured;
    }

    // Other getters and setters...


    public @NotNull(message = "Company ID cannot be null") Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(@NotNull(message = "Company ID cannot be null") Integer companyId) {
        this.companyId = companyId;
    }

    public @NotNull(message = "Product ID cannot be null") Integer getProductId() {
        return productId;
    }

    public void setProductId(@NotNull(message = "Product ID cannot be null") Integer productId) {
        this.productId = productId;
    }

    public @NotNull(message = "Insurance amount cannot be null") @Positive(message = "Insurance amount must be positive") BigDecimal getInsuranceAmount() {
        return insuranceAmount;
    }

    public void setInsuranceAmount(@NotNull(message = "Insurance amount cannot be null") @Positive(message = "Insurance amount must be positive") BigDecimal insuranceAmount) {
        this.insuranceAmount = insuranceAmount;
    }

    public @PositiveOrZero(message = "Annual premium must be positive or zero") BigDecimal getAnnualPremium() {
        return annualPremium;
    }

    public void setAnnualPremium(@PositiveOrZero(message = "Annual premium must be positive or zero") BigDecimal annualPremium) {
        this.annualPremium = annualPremium;
    }

    public @PositiveOrZero(message = "Monthly premium must be positive or zero") BigDecimal getMonthlyPremium() {
        return monthlyPremium;
    }

    public void setMonthlyPremium(@PositiveOrZero(message = "Monthly premium must be positive or zero") BigDecimal monthlyPremium) {
        this.monthlyPremium = monthlyPremium;
    }

    public @NotNull(message = "Province code cannot be null") ProvinceCode getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(@NotNull(message = "Province code cannot be null") ProvinceCode provinceCode) {
        this.provinceCode = provinceCode;
    }

    public @NotBlank(message = "Email cannot be blank") @Email(message = "Invalid email format") @Size(max = 255) String getEmail() {
        return email;
    }

    public void setEmail(@NotBlank(message = "Email cannot be blank") @Email(message = "Invalid email format") @Size(max = 255) String email) {
        this.email = email;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(@NotBlank(message = "Phone number cannot be blank") @Size(max = 20) @Pattern(regexp = "^(\\d{3}-\\d{3}-\\d{4}|\\d{10}|\\d{3}-\\d{7})$", message = "Phone number must be in format XXX-XXX-XXXX") String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public @NotNull(message = "Language preference cannot be null") Language getLanguagePreference() {
        return languagePreference;
    }

    public void setLanguagePreference(@NotNull(message = "Language preference cannot be null") Language languagePreference) {
        this.languagePreference = languagePreference;
    }

    public @Size(max = 1000) String getMessage() {
        return message;
    }

    public void setMessage(@Size(max = 1000) String message) {
        this.message = message;
    }
}