package com.insurfact.ins.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Data Transfer Object for Phone information.
 * Represents a phone number associated with a contact.
 * 
 * <AUTHOR> zhu
 * @version 1.0
 * @since 2025-01-01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "Phone number information")
public class PhoneDTO {

    @Schema(description = "Unique phone identifier", example = "22222")
    private Long phoneId;

    @Schema(description = "Type of phone", example = "MOBILE", allowableValues = {"MOBILE", "HOME", "WORK", "FAX", "OTHER"})
    private String phoneType;

    @Schema(description = "Area code", example = "416")
    private String areaCode;

    @Schema(description = "Phone number", example = "555-1234")
    private String phoneNumber;

    @Schema(description = "Extension number", example = "123")
    private String extension;

    @Schema(description = "Complete formatted phone number", example = "(************* ext. 123")
    private String formattedNumber;

    @Schema(description = "Whether this is the primary phone number", example = "true")
    private Boolean isPrimary;

    @Schema(description = "Do not call flag", example = "false")
    private Boolean doNotCall;

    @Schema(description = "Additional phone notes")
    private String notes;
}
