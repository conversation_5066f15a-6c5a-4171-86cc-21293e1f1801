package com.insurfact.ins.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Data Transfer Object for Address information.
 * Represents a physical address associated with a contact.
 * 
 * <AUTHOR> zhu
 * @version 1.0
 * @since 2025-01-01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "Physical address information")
public class AddressDTO {

    @Schema(description = "Unique address identifier", example = "11111")
    private Long addressId;

    @Schema(description = "Type of address", example = "HOME", allowableValues = {"HOME", "BUSINESS", "MAILING", "OTHER"})
    private String addressType;

    @Schema(description = "Primary street address line 1", example = "123 Main Street")
    private String streetAddress1;

    @Schema(description = "Secondary street address line 2", example = "Apt 4B")
    private String streetAddress2;

    @Schema(description = "Additional street address line 3", example = "Building C")
    private String streetAddress3;

    @Schema(description = "City name", example = "Toronto")
    private String city;

    @Schema(description = "Province or state code", example = "ON")
    private String provinceCode;

    @Schema(description = "Province or state name", example = "Ontario")
    private String provinceName;

    @Schema(description = "Postal or ZIP code", example = "M5V 3A8")
    private String postalCode;

    @Schema(description = "Country code", example = "CA")
    private String countryCode;

    @Schema(description = "Country name", example = "Canada")
    private String countryName;

    @Schema(description = "Whether this is the primary address", example = "true")
    private Boolean isPrimary;

    @Schema(description = "Care of information", example = "c/o John Smith")
    private String careOf;

    @Schema(description = "Additional address notes or instructions")
    private String notes;
}
