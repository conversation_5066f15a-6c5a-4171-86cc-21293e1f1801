package com.insurfact.ins.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Data Transfer Object for Email information.
 * Represents an email address associated with a contact.
 * 
 * <AUTHOR> zhu
 * @version 1.0
 * @since 2025-01-01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Schema(description = "Email address information")
public class EmailDTO {

    @Schema(description = "Unique email identifier", example = "33333")
    private Long emailId;

    @Schema(description = "Type of email", example = "PERSONAL", allowableValues = {"PERSONAL", "WORK", "OTHER"})
    private String emailType;

    @Schema(description = "Email address", example = "<EMAIL>")
    private String emailAddress;

    @Schema(description = "Whether this is the primary email address", example = "true")
    private Boolean isPrimary;

    @Schema(description = "Send solicitation flag", example = "true")
    private Boolean sendSolicitation;

    @Schema(description = "Additional email notes")
    private String notes;
}
