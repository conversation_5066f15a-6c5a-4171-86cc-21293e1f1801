package com.insurfact.ins.dto;


/*
  Data class to hold second life-insured information
 */
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import jakarta.validation.constraints.*;
import java.time.LocalDate;

public class PersonalInfo {
    @NotBlank(message = "First name cannot be blank")
    @Size(max = 100)
    private String firstName;

    @NotBlank(message = "Last name cannot be blank")
    @Size(max = 100)
    private String lastName;

    @NotNull(message = "Gender cannot be null")
    private Gender gender;

    @NotNull(message = "Date of birth cannot be null")
    @Past(message = "Date of birth must be in the past")
    private LocalDate dateOfBirth;

    @NotNull(message = "Smoker status cannot be null")
    private SmokerStatus smokerStatus;
    @NotNull(message = "Gender cannot be null")
    public enum Gender {
        MALE('M', 1),
        FEMALE('F', 2);

        private final char code;
        private final int dbValue;

        Gender(char code, int dbValue) {
            this.code = code;
            this.dbValue = dbValue;
        }

        // No changes needed for getCode()
        public char getCode() {
            return code;
        }

        // No changes needed for getDbValue()
        public int getDbValue() {
            return dbValue;
        }

        // Add @JsonCreator annotation to tell Jackson to use this method for deserialization from a String
        @JsonCreator
        public static Gender fromCode(String code) {
            if (code == null || code.isEmpty()) {
                throw new IllegalArgumentException("Gender code cannot be empty");
            }
            return switch (Character.toUpperCase(code.charAt(0))) {
                case 'M' -> MALE;
                case 'F' -> FEMALE;
                default -> throw new IllegalArgumentException("Invalid gender code: " + code);
            };
        }

        @JsonValue
        public String toJsonValue() {
            // Convert the char code to a String for JSON representation
            return String.valueOf(this.code);
        }
    }

    public enum SmokerStatus {
        SMOKER('Y', "Smoker"),
        NON_SMOKER('N', "Non-smoker");

        private final char code;
        private final String dbValue;

        SmokerStatus(char code, String dbValue) {
            this.code = code;
            this.dbValue = dbValue;
        }

        public char getCode() {
            return code;
        }

        public String getDbValue() {
            return dbValue;
        }

        public static SmokerStatus fromCode(String code) {
            if (code == null || code.isEmpty()) {
                throw new IllegalArgumentException("Smoker status code cannot be empty");
            }
            return switch (code.toLowerCase()) {
                case "y", "smoker" -> SMOKER;
                default -> NON_SMOKER;
            };
        }
        @JsonValue
        public String toJsonValue() {
            return String.valueOf(this.code);
        }
    }

    // --- Getters and Setters remain the same ---

    public boolean hasValidName() {
        return firstName != null && !firstName.isEmpty() &&
               lastName != null && !lastName.isEmpty();
    }

    public @NotBlank(message = "First name cannot be blank") @Size(max = 100) String getFirstName() {
        return firstName;
    }

    public void setFirstName(@NotBlank(message = "First name cannot be blank") @Size(max = 100) String firstName) {
        this.firstName = firstName;
    }

    public @NotBlank(message = "Last name cannot be blank") @Size(max = 100) String getLastName() {
        return lastName;
    }

    public void setLastName(@NotBlank(message = "Last name cannot be blank") @Size(max = 100) String lastName) {
        this.lastName = lastName;
    }

    public @NotNull(message = "Gender cannot be null") Gender getGender() {
        return gender;
    }

    public void setGender(@NotNull(message = "Gender cannot be null") Gender gender) {
        this.gender = gender;
    }

    public @NotNull(message = "Date of birth cannot be null") @Past(message = "Date of birth must be in the past") LocalDate getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(@NotNull(message = "Date of birth cannot be null") @Past(message = "Date of birth must be in the past") LocalDate dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public @NotNull(message = "Smoker status cannot be null") SmokerStatus getSmokerStatus() {
        return smokerStatus;
    }

    public void setSmokerStatus(@NotNull(message = "Smoker status cannot be null") SmokerStatus smokerStatus) {
        this.smokerStatus = smokerStatus;
    }
}