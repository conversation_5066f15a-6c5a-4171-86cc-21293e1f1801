package com.insurfact.ins.mapper;

import com.insurfact.ins.dto.*;
import com.insurfact.skynet.entity.*;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper class for converting between skynet entity objects and API DTOs.
 * 
 * This mapper handles the conversion from com.insurfact.skynet.entity objects
 * (returned by the repository layer) to com.insurfact.ins.dto objects
 * (used by the API layer).
 * 
 * The mapper also handles data formatting, masking, and business logic
 * such as SIN masking and phone number formatting.
 * 
 * <AUTHOR> zhu
 * @version 1.0
 * @since 2025-01-01
 */
@Component
public class AdvisorMapper {

    /**
     * Converts an Advisor entity to AdvisorProfileDTO.
     * Updated to use correct field names from actual SKYTEST schema.
     *
     * @param advisor the advisor entity from skynet-ejb
     * @return AdvisorProfileDTO for API response
     */
    public AdvisorProfileDTO toAdvisorProfileDTO(Advisor advisor) {
        if (advisor == null) {
            return null;
        }

        AdvisorProfileDTO.AdvisorProfileDTOBuilder builder = AdvisorProfileDTO.builder()
            .advisorId(convertIntegerToLong(advisor.getAdvisorIntId())) // Convert Integer to Long
            .advisorCode(advisor.getAdvisorNumber()) // Using ADVISOR_NUMBER from schema
            .status(convertStatusToString(advisor.getStatus())) // STATUS is NUMBER(4) in schema
            .advisorType(convertAdvisorTypeToString(advisor.getAdvisorType())) // ADVISOR_TYPE is NUMBER(4)
            .notes(advisor.getNote()); // Field name is NOTE in schema

        // Handle SIN field - check if method exists
        try {
            if (advisor.getSin() != null) {
                builder.sin(maskSin(advisor.getSin()));
            }
        } catch (Exception e) {
            // getSin() method may not exist in entity, skip for now
            builder.sin(null);
        }

        // Handle Corporate Name field - check if method exists
        try {
            if (advisor.getCorporateName() != null) {
                builder.corporateName(advisor.getCorporateName());
            }
        } catch (Exception e) {
            // getCorporateName() method may not exist in entity, skip for now
            builder.corporateName(null);
        }

        // Convert Date to LocalDateTime (Date doesn't have toLocalDateTime method)
        if (advisor.getCreationDate() != null) {
            builder.createdDate(convertDateToLocalDateTime(advisor.getCreationDate()));
        }
        if (advisor.getLastModificationDate() != null) {
            builder.lastModifiedDate(convertDateToLocalDateTime(advisor.getLastModificationDate()));
        }

        // Handle user information - check if getUsers() method exists
        try {
            if (advisor.getUsers() != null) {
                builder.username(advisor.getUsers().getUsername())
                       .hasLoginAccount(true);
            } else {
                builder.hasLoginAccount(false);
            }
        } catch (Exception e) {
            // getUsers() method may not exist in entity, skip for now
            builder.hasLoginAccount(false);
        }

        // Convert contact information
        if (advisor.getContact() != null) {
            builder.contact(toContactDTO(advisor.getContact()));
        }

        return builder.build();
    }

    /**
     * Converts a Contact entity to ContactDTO.
     * Updated to use correct field names from actual SKYTEST schema.
     */
    public ContactDTO toContactDTO(Contact contact) {
        if (contact == null) {
            return null;
        }

        ContactDTO.ContactDTOBuilder builder = ContactDTO.builder()
            .contactId(convertIntegerToLong(contact.getContactIntId())) // Convert Integer to Long
            .firstName(contact.getFirstname()) // Field name is FIRSTNAME in schema
            .lastName(contact.getLastname()) // Field name is LASTNAME in schema
            .middleName(contact.getMiddlename()) // Field name is MIDDLENAME in schema
            .preferredLanguage(convertLanguageCodeToString(contact.getPreferredLanguage())) // NUMBER(4) in schema
            .gender(convertGenderCodeToString(contact.getGender())) // NUMBER(4) in schema
            .salutation(convertSalutationCodeToString(contact.getSalutation())) // NUMBER in schema
            .contactType(contact.getContactType()); // NUMBER(4) in schema

        // Handle new fields - check if methods exist
        try {
            if (contact.getJobTitle() != null) {
                builder.jobTitle(contact.getJobTitle());
            }
        } catch (Exception e) {
            // getJobTitle() method may not exist, skip
        }

        try {
            if (contact.getJobRole() != null) {
                builder.jobRole(contact.getJobRole());
            }
        } catch (Exception e) {
            // getJobRole() method may not exist, skip
        }

        try {
            if (contact.getMaritalStatus() != null) {
                builder.maritalStatus(convertMaritalStatusToString(contact.getMaritalStatus()));
            }
        } catch (Exception e) {
            // getMaritalStatus() method may not exist, skip
        }

        try {
            if (contact.getIncome() != null) {
                builder.income(convertDoubleToBigDecimal(contact.getIncome())); // Convert Double to BigDecimal
            }
        } catch (Exception e) {
            // getIncome() method may not exist, skip
        }

        // Convert birth date (Date doesn't have toLocalDate method)
        if (contact.getBirthDate() != null) {
            builder.dateOfBirth(convertDateToLocalDate(contact.getBirthDate()));
        }

        // Convert addresses - check if getAddressList() method exists
        try {
            if (contact.getAddressList() != null) {
                List<AddressDTO> addresses = contact.getAddressList().stream()
                    .map(this::toAddressDTO)
                    .collect(Collectors.toList());
                builder.addresses(addresses);
            } else {
                builder.addresses(new ArrayList<>());
            }
        } catch (Exception e) {
            // getAddressList() method may not exist, use empty list
            builder.addresses(new ArrayList<>());
        }

        // Convert phones - check if getPhoneList() method exists
        try {
            if (contact.getPhoneList() != null) {
                List<PhoneDTO> phones = contact.getPhoneList().stream()
                    .map(this::toPhoneDTO)
                    .collect(Collectors.toList());
                builder.phones(phones);
            } else {
                builder.phones(new ArrayList<>());
            }
        } catch (Exception e) {
            // getPhoneList() method may not exist, use empty list
            builder.phones(new ArrayList<>());
        }

        // Convert emails - check if getEmailList() method exists
        try {
            if (contact.getEmailList() != null) {
                List<EmailDTO> emails = contact.getEmailList().stream()
                    .map(this::toEmailDTO)
                    .collect(Collectors.toList());
                builder.emails(emails);
            } else {
                builder.emails(new ArrayList<>());
            }
        } catch (Exception e) {
            // getEmailList() method may not exist, use empty list
            builder.emails(new ArrayList<>());
        }

        return builder.build();
    }

    /**
     * Converts an Address entity to AddressDTO.
     * Updated to use correct field names from actual SKYTEST schema.
     */
    public AddressDTO toAddressDTO(Address address) {
        if (address == null) {
            return null;
        }

        AddressDTO.AddressDTOBuilder builder = AddressDTO.builder()
            .addressId(convertIntegerToLong(address.getAddressIntId())) // Convert Integer to Long
            .addressType(convertAddressTypeToString(address.getType())) // TYPE is NUMBER(8) in schema (CORRECTED)
            .streetAddress1(address.getAddressLine1()) // Field name is ADDRESS_LINE1 in schema
            .streetAddress2(address.getAddressLine2()) // Field name is ADDRESS_LINE2 in schema
            .city(address.getCity())
            .postalCode(address.getPostalCode())
            .isPrimary(convertStringToBoolean(address.getIsPrimary())); // IS_PRIMARY is VARCHAR2(1) in schema

        // Handle new fields - check if methods exist
        try {
            if (address.getAddressLine3() != null) {
                builder.streetAddress3(address.getAddressLine3());
            }
        } catch (Exception e) {
            // getAddressLine3() method may not exist, skip
        }

        try {
            if (address.getCareOf() != null) {
                builder.careOf(address.getCareOf());
            }
        } catch (Exception e) {
            // getCareOf() method may not exist, skip
        }

        // Handle province information
        if (address.getProvince() != null) {
            builder.provinceCode(address.getProvince().getProvinceCode())
                   .provinceName(address.getProvince().getNameEn()); // Using NAME_EN from schema
        }

        // Handle country information
        if (address.getCountry() != null) {
            builder.countryCode(address.getCountry().getCountryCode())
                   .countryName(address.getCountry().getCountryNameEn()); // Using COUNTRY_NAME_EN from schema
        }

        return builder.build();
    }

    /**
     * Converts a Phone entity to PhoneDTO.
     * Updated to use correct field names from actual SKYTEST schema.
     */
    public PhoneDTO toPhoneDTO(Phone phone) {
        if (phone == null) {
            return null;
        }

        PhoneDTO.PhoneDTOBuilder builder = PhoneDTO.builder()
            .phoneId(convertIntegerToLong(phone.getPhoneIntId())) // Convert Integer to Long
            .phoneType(convertPhoneTypeToString(phone.getType())) // TYPE is NUMBER(8) in schema (CORRECTED)
            .areaCode(phone.getAreaCode()) // AREA_CODE is VARCHAR2(4) in schema
            .phoneNumber(phone.getPhoneNumber()) // PHONE_NUMBER is VARCHAR2(20) in schema
            .extension(phone.getExtension())
            .formattedNumber(formatPhoneNumber(phone.getAreaCode(), phone.getPhoneNumber(), phone.getExtension()))
            .isPrimary(convertStringToBoolean(phone.getIsPrimary())); // IS_PRIMARY is VARCHAR2(1) in schema

        // Handle new fields - check if methods exist
        try {
            if (phone.getDoNotCall() != null) {
                builder.doNotCall(convertStringToBoolean(phone.getDoNotCall()));
            }
        } catch (Exception e) {
            // getDoNotCall() method may not exist, skip
        }

        return builder.build();
    }

    /**
     * Converts an Email entity to EmailDTO.
     * Updated to use correct field names from actual SKYTEST schema.
     */
    public EmailDTO toEmailDTO(Email email) {
        if (email == null) {
            return null;
        }

        EmailDTO.EmailDTOBuilder builder = EmailDTO.builder()
            .emailId(convertIntegerToLong(email.getEmailIntId())) // Convert Integer to Long
            .emailType(convertEmailTypeToString(email.getType())) // TYPE is NUMBER(8) in schema (CORRECTED)
            .emailAddress(email.getEmailAddress()) // EMAIL_ADDRESS is VARCHAR2(100) in schema
            .isPrimary(convertStringToBoolean(email.getIsPrimary())); // IS_PRIMARY is VARCHAR2(1) in schema

        // Handle new fields - check if methods exist
        try {
            if (email.getSendSolicitation() != null) {
                builder.sendSolicitation(convertStringToBoolean(email.getSendSolicitation()));
            }
        } catch (Exception e) {
            // getSendSolicitation() method may not exist, skip
        }

        return builder.build();
    }

    /**
     * Masks SIN for security purposes.
     * Shows only the last 3 digits.
     */
    private String maskSin(String sin) {
        if (sin == null || sin.length() < 4) {
            return sin;
        }
        return "***-***-" + sin.substring(sin.length() - 3);
    }

    /**
     * Formats phone number for display.
     */
    private String formatPhoneNumber(String areaCode, String phoneNumber, String extension) {
        if (phoneNumber == null) {
            return null;
        }

        StringBuilder formatted = new StringBuilder();
        if (areaCode != null && !areaCode.trim().isEmpty()) {
            formatted.append("(").append(areaCode).append(") ");
        }
        formatted.append(phoneNumber);
        if (extension != null && !extension.trim().isEmpty()) {
            formatted.append(" ext. ").append(extension);
        }
        return formatted.toString();
    }

    /**
     * Converts Date to LocalDateTime.
     * Handles the fact that Date doesn't have toLocalDateTime() method.
     */
    private LocalDateTime convertDateToLocalDateTime(java.util.Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant()
            .atZone(java.time.ZoneId.systemDefault())
            .toLocalDateTime();
    }

    /**
     * Converts Date to LocalDate.
     * Handles the fact that Date doesn't have toLocalDate() method.
     */
    private java.time.LocalDate convertDateToLocalDate(java.util.Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant()
            .atZone(java.time.ZoneId.systemDefault())
            .toLocalDate();
    }

    /**
     * Converts VARCHAR2(1) database field to Boolean.
     * Database stores 'Y'/'N' or '1'/'0' as strings.
     */
    private Boolean convertStringToBoolean(String value) {
        if (value == null) {
            return false;
        }
        return "Y".equalsIgnoreCase(value.trim()) || "1".equals(value.trim());
    }

    /**
     * Converts Integer to Long for ID fields.
     * Entity classes use Integer for ID fields, but DTOs expect Long.
     */
    private Long convertIntegerToLong(Integer value) {
        return value != null ? value.longValue() : null;
    }

    /**
     * Converts Double to BigDecimal for income fields.
     * Entity classes use Double for income, but DTOs expect BigDecimal.
     */
    private java.math.BigDecimal convertDoubleToBigDecimal(Double value) {
        return value != null ? java.math.BigDecimal.valueOf(value) : null;
    }

    // TODO: Add missing conversion methods for new fields
    // These methods need to be implemented based on business requirements

    private String convertMaritalStatusToString(Integer maritalStatus) {
        // TODO: Implement marital status conversion based on TYPE_CLASS lookup
        return maritalStatus != null ? maritalStatus.toString() : null;
    }

    private String convertStatusToString(Integer status) {
        // TODO: Implement status conversion based on business rules
        return status != null ? status.toString() : null;
    }

    private String convertAdvisorTypeToString(Integer advisorType) {
        // TODO: Implement advisor type conversion based on business rules
        return advisorType != null ? advisorType.toString() : null;
    }

    private String convertLanguageCodeToString(Integer languageCode) {
        // TODO: Implement language code conversion
        return languageCode != null ? languageCode.toString() : null;
    }

    private String convertGenderCodeToString(Integer genderCode) {
        // TODO: Implement gender code conversion
        return genderCode != null ? genderCode.toString() : null;
    }

    private String convertSalutationCodeToString(Integer salutationCode) {
        // TODO: Implement salutation code conversion
        return salutationCode != null ? salutationCode.toString() : null;
    }

    private String convertAddressTypeToString(Integer addressType) {
        // TODO: Implement address type conversion based on TYPE_CLASS lookup
        return addressType != null ? addressType.toString() : null;
    }

    private String convertPhoneTypeToString(Integer phoneType) {
        // TODO: Implement phone type conversion based on TYPE_CLASS lookup
        return phoneType != null ? phoneType.toString() : null;
    }

    private String convertEmailTypeToString(Integer emailType) {
        // TODO: Implement email type conversion based on TYPE_CLASS lookup
        return emailType != null ? emailType.toString() : null;
    }
}
