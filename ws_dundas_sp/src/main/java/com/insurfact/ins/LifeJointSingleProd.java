/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.insurfact.ins;

import com.insurfact.iq4.ejb.EnhancedProductFacade;
import com.insurfact.iq4.ejb.IQ4EngineFacade;
import com.insurfact.iq4.ejb.IQ4JointSEAUtilsFacade;
import com.insurfact.iq4.ejb.IQ4ProductFacade;
import com.insurfact.iq4.ejb.PromotionsHelper;
import com.insurfact.sdk.utils.InsurfactPrivateException;
import com.insurfact.websecuring.UserValidationDundas;
import jakarta.ejb.EJB;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */

//https://wsinsurance.insurfact.com/WS_INS_WIDGET/webresources/lifeJointSingleProd/250000/QC/1985-01-06/M/N/1985-01-06/F/N/F/54/8329
//          http://10.10.88.15:8080/WS_INS_WIDGET/webresources/lifeJointSingleProd/250000/QC/1985-01-06/M/N/1985-01-06/F/N/F/6/7953
//http://10.10.88.15:8080/WS_INS_WIDGET/webresources/lifeJointSingleProd/250000/QC/1985-01-06/M/N/1985-01-06/F/N/F/54/8329
@RestController
@RequestMapping("/webresources/lifeJointSingleProd")
public class LifeJointSingleProd {
	private static final String TAG = LifeSingleComp.class.getName();

	@SuppressWarnings("unused")
	private static final Logger LOG = Logger.getLogger(TAG);

	@SuppressWarnings("unused")
	private static boolean DEBUG = false;

	private static final String PRODUCT_CLASS = "LIFE";

	public static final String dobFormat = "yyyy-MM-dd";

	@EJB
	private IQ4EngineFacade quoteFacade;

	@EJB
	private IQ4JointSEAUtilsFacade jointESAUtils;

	@EJB
	private IQ4ProductFacade productFacade;

	@EJB
	private EnhancedProductFacade enhancedProductFacade;

	@EJB
	private PromotionsHelper promotionsHelper;

	@EJB
	private UserValidationDundas userValidation;

	public LifeJointSingleProd() {
	}

	@ResponseBody
	@GetMapping(value = "/{face_amount}/{province_code}/{client1DOB}/{client1Gender}/{client1Smoking}/{client2DOB}/{client2Gender}/{client2Smoking}/{jointType}/{compID}/{prodID}", produces = MediaType.APPLICATION_JSON_VALUE)
	public String getJson(@PathVariable("face_amount") String faceAmount,
			@PathVariable("province_code") String provCode, @PathVariable("client1DOB") String dateOfBirth1,
			@PathVariable("client1Gender") String gender1, @PathVariable("client1Smoking") String smoking1,
			@PathVariable("client2DOB") String dateOfBirth2, @PathVariable("client2Gender") String gender2,
			@PathVariable("client2Smoking") String smoking2, @PathVariable("jointType") String jointType, // F=first to
																											// die
																											// L=last to
																											// die
			@PathVariable("compID") String compID, @PathVariable("prodID") String prodID,
			@RequestHeader("token") String tokenCli
			, HttpServletRequest request, HttpServletResponse response) {

		// String productTypeSel = "S"; // S= single product type

		DEBUG = false;

		String ipAddress = request.getRemoteAddr();

		String responseAll = "{\"response\": token does not match }";

		DateFormat df = new SimpleDateFormat(dobFormat);
		df.getCalendar().setLenient(false);
		Date date1 = Calendar.getInstance().getTime();
		Date date2 = Calendar.getInstance().getTime();
		try {
			date1 = df.parse(dateOfBirth1);
			date2 = df.parse(dateOfBirth2);
		} catch (Exception ex) {
			// final String s = TAG + ".startMulti: *df.parse(dob)* with dobFormat {" +
			// dobFormat + "} raised: " + ex;
		}

		String newDOB = dateOfBirth2;
		String newGend = gender2;
		String newSmok = smoking2;

		if (date1.after(date2)) {
			dateOfBirth2 = dateOfBirth1;
			gender2 = gender1;
			smoking2 = smoking1;

			dateOfBirth1 = newDOB;
			gender1 = newGend;
			smoking1 = newSmok;

		}
		String urlOrigin = request.getHeader("origin");// changed to origin from Referer Jan 17th, 2022 aa
		if (urlOrigin != null) {
			if (urlOrigin.equals(""))
				urlOrigin = "noOrigin";
		}
		int wsUserID = 0;// 15201 for test

		try {
			wsUserID = userValidation.getWSUserID(tokenCli, ipAddress);
		} catch (InsurfactPrivateException ex) {
			System.out.println("* LifeJointSingleComp ERROR originIP=" + ipAddress + " token=" + urlOrigin);
			Logger.getLogger(LifeJointSingleProd.class.getName()).log(Level.SEVERE, null, ex);
		}
		@SuppressWarnings("unused")
		String productTypeId = "6";
		if (wsUserID != 0) {

			GenericJoint generic = new GenericJoint(PRODUCT_CLASS, "ALL", quoteFacade, jointESAUtils, productFacade,
					enhancedProductFacade, promotionsHelper);
			String responseValue = generic.getNoExceptionJointSingleProd("", faceAmount, provCode, dateOfBirth1,
					gender1, smoking1, dateOfBirth2, gender2, smoking2, jointType, compID, prodID, "A", "A", "");
			// String responseValue = generic.getNoExceptionJoint(productTypeId, faceAmount,
			// provCode, dateOfBirth1, gender1,
			// smoking1, dateOfBirth2, gender2, smoking2, jointType,"", "", "", "");

			responseAll = "{\"response\": " + responseValue + " }";
		}

		return responseAll;
	}

}
