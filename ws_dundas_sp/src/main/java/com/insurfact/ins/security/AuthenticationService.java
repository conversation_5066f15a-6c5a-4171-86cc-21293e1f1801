package com.insurfact.ins.security;

import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.insurfact.sdk.utils.InsurfactPrivateException;
import com.insurfact.websecuring.UserValidationDundas;
import com.insurfact.websecuring.UserValidationWidget;

@Service
public class AuthenticationService {

    private static final Logger logger = LoggerFactory.getLogger(AuthenticationService.class);

    private final UserValidationDundas tokenValidator;
    private final UserValidationWidget originValidator;

    public AuthenticationService(UserValidationDundas tokenValidator, UserValidationWidget originValidator) {
        this.tokenValidator = tokenValidator;
        this.originValidator = originValidator;
    }

    /**
     * Authenticate a user with either token or origin-based methods.
     *
     * @param token Optional authentication token
     * @param ipAddress Client IP address
     * @param origin Request origin
     * @return User ID if authenticated, 0 otherwise
     */
    public int authenticateUser(String token, String ipAddress, String origin) {
        try {
            // Try token authentication first if provided
            if (token != null && !token.isEmpty()) {
                int userId = tokenValidator.getWSUserID(token, ipAddress);
                if (userId != 0) {
                    logger.debug("User authenticated via token: {}", userId);
                    return userId;
                }
            }

            // Fall back to origin-based authentication
            if (origin != null && !origin.isEmpty()) {
                int userId = originValidator.getUsersID(origin, ipAddress, true);
                if (userId != 0) {
                    logger.debug("User authenticated via origin: {}", userId);
                    return userId;
                }
            }

            logger.warn("Authentication failed for IP: {}, Origin: {}", ipAddress, origin);
            return 0;

        } catch (InsurfactPrivateException ex) {
            logger.error("Authentication error: {}", ex.getMessage(), ex);
            return 0;
        }
    }
}