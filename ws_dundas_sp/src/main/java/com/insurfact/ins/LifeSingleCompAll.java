/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2019 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.ins;
 
import com.insurfact.iq4.ejb.EnhancedProductFacade;
import com.insurfact.iq4.ejb.IQ4EngineFacade;
import com.insurfact.iq4.ejb.IQ4ProductFacade;
import com.insurfact.iq4.ejb.PromotionsHelper;

import com.insurfact.sdk.properties.InsurfactSdkProperties;
import com.insurfact.sdk.utils.InsurfactPrivateException;
import java.io.IOException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.ejb.EJB;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.insurfact.websecuring.UserValidationDundas;


//http://10.10.88.22:28081/WS_INS_TEST/webresources/lifeSingleCompAll/350000/1985-06-01/M/N/QC/71/S
// new parameter healthClass = S for standard - P for preferred  or -  A for all
//http://10.10.88.22:28081/WS_INS2_DUNDAS/webresources/lifeSingleCompAll/500000/1979-01-01/M/N/ON/29/S
//https://wsins.insurfact.com/WS_INSF_T/webresources/lifeSingleCompAll/500000/1979-01-01/M/N/ON/29/P24

///carriers/{carrierId}/products/{productId}/quote?language=en&includeDetails=true
@RestController
@RequestMapping("/webresources/lifeSingleCompAll") // Base path for the controller
public class LifeSingleCompAll {

	private static final String TAG = LifeSingleComp.class.getName();
 
	private static boolean DEBUG = false;

	private static final String PRODUCT_CLASS = "LIFE";

	@EJB
	private IQ4ProductFacade productFacade;

	@EJB
	private EnhancedProductFacade enhancedProductFacade;

	@EJB
	private IQ4EngineFacade quoteFacade;

	@EJB
	private PromotionsHelper promotionsHelper;

	@Autowired
	private final UserValidationDundas userValidation;

    public LifeSingleCompAll(UserValidationDundas userValidation) {
        this.userValidation = userValidation;
    }


    @GetMapping(value="/{face_amount}/{date_of_birth}/{gender}/{smoking}/{province_code}/{carrier_id}", produces = MediaType.APPLICATION_JSON_VALUE)
	@ResponseBody
	public String getJson(HttpServletRequest request, HttpServletResponse response,
			@PathVariable("face_amount") String faceAmount, @PathVariable("date_of_birth") String dateOfBirth,
			@PathVariable("gender") String gender, @PathVariable("smoking") String smoking,
			@PathVariable("province_code") String provCode, @PathVariable("carrier_id") String carrierID,
			@RequestHeader("token") String tokenCli

	) {

		DEBUG = InsurfactSdkProperties.getBoolean("debug.enabled", false);

		String responseAll = "{\"response\": token does not match }";
		String ipAddress = request.getRemoteAddr();

		String urlOrigin = request.getHeader("origin");// changed to origin from Referer Jan 17th, 2022 aa
		if (urlOrigin != null) {
			if (urlOrigin.equals(""))
				urlOrigin = "noOrigin";
		} else
			urlOrigin = "noOrigin";
 
		int wsUserID = 0;
		try {
			wsUserID = userValidation.getWSUserID(tokenCli, ipAddress);
		} catch (InsurfactPrivateException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		if (wsUserID != 0) {

			String productTypeSel = "A"; // A= all product types
			String productTypeId = "0";

			Generic generic = new Generic(PRODUCT_CLASS, "ALL", quoteFacade, enhancedProductFacade, productFacade,
					promotionsHelper);
			String responseValue = generic.getNoExceptionSingle(productTypeId, faceAmount, dateOfBirth, gender, smoking,
					provCode, carrierID, productTypeSel);

			try {
				generic.saveQuote(ipAddress, wsUserID, "lifeSingleCompAll", faceAmount, dateOfBirth, gender, smoking,
						provCode, carrierID, "", productTypeSel, urlOrigin);
			} catch (IOException e) {
				System.out.println("112 lifeSingleCompAll IOException e=" + e.getMessage());
			} catch (InsurfactPrivateException e) {
				System.out.println("115 lifeSingleCompAll InsurfactPrivateException e=" + e.getMessage());
			}

			responseAll = "{\"response\": " + responseValue + " }";

			if (DEBUG) {
				System.out.println(TAG + ".getJson: returning $$" + responseAll + "$$");
			}
		}

		return responseAll;
	}

}
