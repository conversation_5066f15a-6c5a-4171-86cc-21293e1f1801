/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2019 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THES
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.ins;

import com.insurfact.im.IMProduct;
import com.insurfact.iq.domain.Band;
import com.insurfact.iq.domain.Client;
import com.insurfact.iq.domain.Company;
import com.insurfact.iq.domain.HealthClass;
import com.insurfact.iq.domain.Product;
import com.insurfact.iq.domain.ProductType;
import com.insurfact.iq.domain.QuoteParams;
import com.insurfact.iq.domain.util.DateFunctions;
import com.insurfact.iq.domain.util.TotalAnnComparator;
import com.insurfact.iq.domain.util.TotalMonComparator;

import com.insurfact.iq4.ejb.EnhancedProductFacade;
import com.insurfact.iq4.ejb.IQ4EngineFacade;
import com.insurfact.iq4.ejb.IQ4ProductFacade;
import com.insurfact.iq4.ejb.PromotionsHelper;

import com.insurfact.sdk.mail.MailMngr;
import com.insurfact.sdk.properties.InsurfactSdkProperties;
import com.insurfact.sdk.utils.InsurfactPrivateException;
import com.insurfact.sdk.utils.LogUtil;
import com.insurfact.skynet.entity.AboutMe;
import com.insurfact.skynet.entity.Contact;
import com.insurfact.skynet.entity.Promotions;
import com.insurfact.skynet.entity.Province;
import com.insurfact.skynet.entity.Types;
import com.insurfact.wsutil.IQ4Modes;
import com.insurfact.wsutil.Combos;
import com.insurfact.wsutil.CombosCritNew;
import com.insurfact.wsutil.CombosLifeNew;
import com.insurfact.wsutil.JsonUtil;
import com.insurfact.wsutil.Util;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

import jakarta.ejb.EJB;

/**
 *
 * <AUTHOR>
 */
public class Generic {

	private static final String TAG = Generic.class.getName();

	private static boolean DEBUG = false;

	private static final boolean SEND_EMAIL_FOR_ANOMALIES = true;

	private Promotions promo;

	private QuoteParams multiQP;

	private IQ4Modes iq4Mode;

	boolean queried = false;
	// @ EJB
	// private IQ4ProductFacade productFacade;

	@EJB
	final private IQ4EngineFacade quoteFacade;

	@EJB
	final private IQ4ProductFacade productFacade;

	@EJB
	final private EnhancedProductFacade enhancedProductFacade;

	@EJB
	final private PromotionsHelper promotionsHelper;

	public Connection con = null;

	public final String PRODUCT_CLASS;

	public final String OUTPUT_TYPE;

	public Generic(final String quoteType, final String outputType, final IQ4EngineFacade quoteFacadeGiven,
			final EnhancedProductFacade enhancedProductFacadeGiven, final IQ4ProductFacade productFacadeGiven,
			final PromotionsHelper promotionsHelperGiven) {

		PRODUCT_CLASS = quoteType;

		OUTPUT_TYPE = outputType;

		quoteFacade = quoteFacadeGiven;
		enhancedProductFacade = enhancedProductFacadeGiven;
		productFacade = productFacadeGiven;
		promotionsHelper = promotionsHelperGiven;

		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.YEAR, 1); // Where n is int

		// createJWT(String id, String issuer, String subject, Date expireDate)
		// String key = Securing.createJWT("138259", "InsurFactConnectInc",
		// "WebServiceQuoting", cal.getTime());
		// System.out.println("line 102 Generic security key for PolicyAdvisor= "+key);
	}

	String getNoExceptionLocale(String urlOrigin, int usersID, int serviceID) throws InsurfactPrivateException {

		String responseValue = "";

		try {
			responseValue = getResponseStringLocale(urlOrigin, usersID, serviceID);
		} catch (IllegalArgumentException ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex);
			}

		} catch (InsurfactPrivateException ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex + "\n" + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10));
			}

		} catch (Throwable ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex + "\n" + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10));
			}
		}

		return responseValue;
	}

	private String getResponseStringLocale(String urlOrigin, int usersID, int serviceID)
			throws InsurfactPrivateException {

		String response = "\"No matching records.\"";

		// step 3: prepare the input data to the sql
		// -----------------------------------------
		try {

			/*
			 * 3 Widget Basic Widget Basic Widget includes Multi-Company. Single life,
			 * Shopit Widget includes Multi-Company. Single life, Shopit 4 Widget Basic+
			 * Widget basic+ Widget includes Multi & Single-Company. Single life, Shopit &
			 * All products Widget includes Multi & Single-Company. Single life, Shopit &
			 * All products 5 Widget Premium Widget Premium Widget includes Multi &
			 * Single-Company. Single & Joint life, Shopit & All products Widget includes
			 * Multi & Single-Company. Single & Joint life, Shopit & All products
			 * 
			 */
			List<ProductType> productTypes = getProductTypes(serviceID);

			List<IMProduct> products = getWSProducts();
			List<Province> provs = getProvinces();
			List<Types> types = getTypes(serviceID);
			List<Types> titleTypes = getTitleTypes();

			Province defaultProv = getDefaultProvince(usersID);
			AboutMe aboutMe = getAboutMe(usersID);

			// int counter = 0;
			StringBuilder buf = new StringBuilder("[ ");

			String res = JsonGenerator.getLocalesValues(productTypes, productTypes, provs, types, products, titleTypes,
					defaultProv, aboutMe);

			buf.append(res);
			buf.append(" ]");

			response = buf.toString();

		} catch (InsurfactPrivateException ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex + "\n" + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10));
			}

		} catch (Throwable ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex + "\n" + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10));
			}
		}

		return response;
	}

	/**
	 * Call example: Generic generic = new Generic(PRODUCT_CLASS,"ALL");
	 *
	 * @param productTypeId
	 * @param faceAmount
	 * @param dateOfBirth
	 * @param gender
	 * @param smoking
	 * @param provCode
	 * @return
	 * @throws InsurfactPrivateException
	 */
	String getNoException(final String productTypeId, final String faceAmount, final String dateOfBirth,
			final String gender, final String smoking, final String provCode, final String healthClass) {

		// get debug flag from properties
		DEBUG = InsurfactSdkProperties.getBoolean("debug.enabled", false);

		String responseValue = "";

		try {
			responseValue = getResponseString(productTypeId, faceAmount, dateOfBirth, gender, smoking, provCode,
					healthClass, "A", "A");
		} catch (IllegalArgumentException ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex);
			}
			if (SEND_EMAIL_FOR_ANOMALIES) {
				MailMngr.sendAlert(TAG + " Alert - Probable invalid parameter(s) from client",
						"getResponseString raised " + ex.getMessage() + LogUtil.getStackTrace(ex, 10)
								+ "\n\nHttp request parameters:" + " product_type_id {" + productTypeId + "}"
								+ " face_amount {" + faceAmount + "}" + " date_of_birth {" + dateOfBirth + "}"
								+ " gender {" + gender + "}" + " smoking {" + smoking + "}" + " province_code {"
								+ provCode + "}");
			}
			String s = "An anomaly was detected; please review the parameters in your http request"
					+ "; the values that we received:" + " product_type_id {" + productTypeId + "}" + " face_amount {"
					+ faceAmount + "}" + " date_of_birth {" + dateOfBirth + "}" + " gender {" + gender + "}"
					+ " smoking {" + smoking + "}" + " province_code {" + provCode + "}";

			responseValue = "\"" + JsonUtil.cleanStringForJson(s) + "\"";

		} catch (InsurfactPrivateException ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex + "\n" + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10));
			}
			String body = "";
			if (SEND_EMAIL_FOR_ANOMALIES) {
				String subject = TAG + " Alert - Probable Bug";
				body = "\ngetResponseString raised InsurfactPrivateException: " + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10) + "\n\nHttp request parameters:" + " product_type_id {"
						+ productTypeId + "}" + " face_amount {" + faceAmount + "}" + " date_of_birth {" + dateOfBirth
						+ "}" + " gender {" + gender + "}" + " smoking {" + smoking + "}" + " province_code {"
						+ provCode + "}";
				if (DEBUG) {
					System.out.println(TAG + ".getJson: alert email; Subject: " + subject + "; email body: " + body);
				}
				MailMngr.sendAlert(subject, body);
			}
			String s = "An anomaly was detected; please contact support and please review the parameters in your http request"
					+ "; the values that we received are:" + " product_type_id {" + productTypeId + "}"
					+ " face_amount {" + faceAmount + "}" + " date_of_birth {" + dateOfBirth + "}" + " gender {"
					+ gender + "}" + " smoking {" + smoking + "}" + " province_code {" + provCode + "}" + "\n" // ==========
			// + body // remove in prod ##################################
			// ==========
			;

			responseValue = "\"" + JsonUtil.cleanStringForJson(s) + "\"";
		} catch (Throwable ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex + "\n" + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10));
			}
			String body = "";
			if (SEND_EMAIL_FOR_ANOMALIES) {
				String subject = TAG + " Alert - Probable Bug";
				body = "\ngetResponseString raised: " + ex.getMessage() + LogUtil.getStackTrace(ex, 10)
						+ "\n\nHttp request parameters:" + " product_type_id {" + productTypeId + "}" + " face_amount {"
						+ faceAmount + "}" + " date_of_birth {" + dateOfBirth + "}" + " gender {" + gender + "}"
						+ " smoking {" + smoking + "}" + " province_code {" + provCode + "}";
				if (DEBUG) {
					System.out.println(TAG + ".getJson: alert email; Subject: " + subject + "; email body: " + body);
				}
				MailMngr.sendAlert(subject, body);
			}
			String s = "An anomaly was detected; "
					+ "please contact support and please review the parameters in your http request"
					+ "; the values that we received are:" + " product_type_id {" + productTypeId + "}"
					+ " face_amount {" + faceAmount + "}" + " date_of_birth {" + dateOfBirth + "}" + " gender {"
					+ gender + "}" + " smoking {" + smoking + "}" + " province_code {" + provCode + "}" + "\n" // ========
			// + body // remove in prod ##################################
			// ========
			;

			responseValue = "\"" + JsonUtil.cleanStringForJson(s) + "\"";
		}

//        String responseAll = "{\"response\": " + responseValue + " }";
//
//        if (DEBUG) {
//            System.out.println(TAG + ".getJson: returning $$" + responseAll + "$$");
//        }
		return responseValue;
	}

	String getNoExceptionWidget(final String productTypeId, final String faceAmount, final String dateOfBirth,
			final String gender, final String smoking, final String provCode, boolean isGroup, final String healthClass,
			final String permOrTerm, final String levelOrDecr, final String limitedFull) {

		// get debug flag from properties
		DEBUG = false;

		String responseValue = "";

		try {

			responseValue = getResponseStringWidgetSpec(productTypeId, faceAmount, dateOfBirth, gender, smoking,
					provCode, isGroup, healthClass, permOrTerm, levelOrDecr, limitedFull);

		} catch (IllegalArgumentException ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex);
			}
			if (SEND_EMAIL_FOR_ANOMALIES) {
				MailMngr.sendAlert(TAG + " Alert - Probable invalid parameter(s) from client",
						"getResponseString raised " + ex.getMessage() + LogUtil.getStackTrace(ex, 10)
								+ "\n\nHttp request parameters:" + " product_type_id {" + productTypeId + "}"
								+ " face_amount {" + faceAmount + "}" + " date_of_birth {" + dateOfBirth + "}"
								+ " gender {" + gender + "}" + " smoking {" + smoking + "}" + " province_code {"
								+ provCode + "}");
			}
			@SuppressWarnings("unused")
			String s = "An anomaly was detected; please review the parameters in your http request"
					+ "; the values that we received:" + " product_type_id {" + productTypeId + "}" + " face_amount {"
					+ faceAmount + "}" + " date_of_birth {" + dateOfBirth + "}" + " gender {" + gender + "}"
					+ " smoking {" + smoking + "}" + " province_code {" + provCode + "}";

			responseValue = "";

		} catch (InsurfactPrivateException ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex + "\n" + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10));
			}
			String body = "";
			if (SEND_EMAIL_FOR_ANOMALIES) {
				String subject = TAG + " Alert - Probable Bug";
				body = "\ngetResponseString raised InsurfactPrivateException: " + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10) + "\n\nHttp request parameters:" + " product_type_id {"
						+ productTypeId + "}" + " face_amount {" + faceAmount + "}" + " date_of_birth {" + dateOfBirth
						+ "}" + " gender {" + gender + "}" + " smoking {" + smoking + "}" + " province_code {"
						+ provCode + "}";
				if (DEBUG) {
					System.out.println(TAG + ".getJson: alert email; Subject: " + subject + "; email body: " + body);
				}
				// MailMngr.sendAlert(subject, body);
			}
			@SuppressWarnings("unused")
			String s = "An anomaly was detected; please contact support and please review the parameters in your http request"
					+ "; the values that we received are:" + " product_type_id {" + productTypeId + "}"
					+ " face_amount {" + faceAmount + "}" + " date_of_birth {" + dateOfBirth + "}" + " gender {"
					+ gender + "}" + " smoking {" + smoking + "}" + " province_code {" + provCode + "}" + "\n" // ==========
			// + body // remove in prod ##################################
			// ==========
			;

			responseValue = "";
		} catch (Throwable ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex + "\n" + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10));
			}
			String body = "";
			if (SEND_EMAIL_FOR_ANOMALIES) {
				String subject = TAG + " Alert - Probable Bug";
				body = "\ngetResponseString raised: " + ex.getMessage() + LogUtil.getStackTrace(ex, 10)
						+ "\n\nHttp request parameters:" + " product_type_id {" + productTypeId + "}" + " face_amount {"
						+ faceAmount + "}" + " date_of_birth {" + dateOfBirth + "}" + " gender {" + gender + "}"
						+ " smoking {" + smoking + "}" + " province_code {" + provCode + "}";
				if (DEBUG) {
					System.out.println(TAG + ".getJson: alert email; Subject: " + subject + "; email body: " + body);
				}
				// MailMngr.sendAlert(subject, body);
			}
			String s = "An anomaly was detected; "
					+ "please contact support and please review the parameters in your http request"
					+ "; the values that we received are:" + " product_type_id {" + productTypeId + "}"
					+ " face_amount {" + faceAmount + "}" + " date_of_birth {" + dateOfBirth + "}" + " gender {"
					+ gender + "}" + " smoking {" + smoking + "}" + " province_code {" + provCode + "}" + "\n" // ========
			// + body // remove in prod ##################################
			// ========
			;
			System.out.println("500 Generic in widget ws error=" + s + body);
			responseValue = "";
		}

		return responseValue;
	}

	public static final String dobFormat = "yyyy-MM-dd";

	/**
	 * bootstrap for Multi Quote with no Contact
	 */
	void startMulti(final String dob, final String genderUp) throws InsurfactPrivateException {

		iq4Mode = IQ4Modes.MultiCompany;

		if (multiQP == null) {

			multiQP = new QuoteParams();

			multiQP.initMultiCompanyMode();

			multiQP.setTermOrPerm(null);

			// com.insurfact.skynet.entity.Contact
			Contact firstContact = Util.create30YearsOldMaleContact();
			multiQP.setFirstContact(firstContact);

			DateFormat df = new SimpleDateFormat(dobFormat);
			df.getCalendar().setLenient(false);
			Date date;
			try {
				date = df.parse(dob);
			} catch (ParseException ex) {
				final String s = TAG + ".startMulti: *df.parse(dob)* with dobFormat {" + dobFormat + "} raised: " + ex;
				if (DEBUG) {
					System.out.println(s); // Logger.getLogger(Life.class.getName()).log(Level.SEVERE, s, ex);
				}
				throw new InsurfactPrivateException(s, ex);
			}

			if (DEBUG) {
				final String s = TAG + ".startMulti: *df.parse(dob {" + dob + "})* with dobFormat {" + dobFormat + "}: "
						+ date;
				System.out.println(s);
			}

			firstContact.setBirthDate(date);

			// com.insurfact.iq.domain.Client client = multiQP.getClient();
			Client client = multiQP.getClient();

			client.setFullBirthDate(date); // TODO washere use the dob from http url

			SimpleDateFormat formatDD = new SimpleDateFormat("dd");
			SimpleDateFormat formatMM = new SimpleDateFormat("MM");
			SimpleDateFormat formatYYYY = new SimpleDateFormat("yyyy");
			int bd_day = Integer.parseInt(formatDD.format(client.getFullBirthDate()));
			int bd_month = Integer.parseInt(formatMM.format(client.getFullBirthDate()));
			int bd_year = Integer.parseInt(formatYYYY.format(client.getFullBirthDate()));

			client.setActualAge(DateFunctions.calcActualAge(bd_month - 1, bd_day, bd_year));
			client.setNearestAge(DateFunctions.calcNearestAge(bd_month - 1, bd_day, bd_year));

			client.setGender(genderUp.charAt(0)); // firstContact.getGender() == 1 ? 'M' : 'F');

			multiQP.setClient(client);

			setMultiQP(multiQP);

//            initializeIQ4();
//            updateAllProductTypes();
		}
	}

	void startMultiSingle(final String dob, final String genderUp, final String carrierID)
			throws InsurfactPrivateException {

		iq4Mode = IQ4Modes.MultiCompany;

		if (multiQP == null) {

			multiQP = new QuoteParams();

			multiQP.initMultiCompanyMode();

			multiQP.setTermOrPerm(null);

			// com.insurfact.skynet.entity.Contact
			Contact firstContact = Util.create30YearsOldMaleContact();
			multiQP.setFirstContact(firstContact);

			multiQP.setCompanyID(Integer.parseInt(carrierID));

			System.out.println("313 Generic.startMultiSingle multiQP.getCompanyID=" + multiQP.getCompanyID());

			DateFormat df = new SimpleDateFormat(dobFormat);
			df.getCalendar().setLenient(false);
			Date date;
			try {
				date = df.parse(dob);
			} catch (ParseException ex) {
				final String s = TAG + ".startMulti: *df.parse(dob)* with dobFormat {" + dobFormat + "} raised: " + ex;
				if (DEBUG) {
					System.out.println(s); // Logger.getLogger(Life.class.getName()).log(Level.SEVERE, s, ex);
				}
				throw new InsurfactPrivateException(s, ex);
			}

			if (DEBUG) {
				final String s = TAG + ".startMulti: *df.parse(dob {" + dob + "})* with dobFormat {" + dobFormat + "}: "
						+ date;
				System.out.println(s);
			}

			firstContact.setBirthDate(date);

			// com.insurfact.iq.domain.Client client = multiQP.getClient();
			Client client = multiQP.getClient();

			client.setFullBirthDate(date); // TODO washere use the dob from http url

			SimpleDateFormat formatDD = new SimpleDateFormat("dd");
			SimpleDateFormat formatMM = new SimpleDateFormat("MM");
			SimpleDateFormat formatYYYY = new SimpleDateFormat("yyyy");
			int bd_day = Integer.parseInt(formatDD.format(client.getFullBirthDate()));
			int bd_month = Integer.parseInt(formatMM.format(client.getFullBirthDate()));
			int bd_year = Integer.parseInt(formatYYYY.format(client.getFullBirthDate()));

			client.setActualAge(DateFunctions.calcActualAge(bd_month - 1, bd_day, bd_year));
			client.setNearestAge(DateFunctions.calcNearestAge(bd_month - 1, bd_day, bd_year));

			client.setGender(genderUp.charAt(0)); // firstContact.getGender() == 1 ? 'M' : 'F');

			multiQP.setClient(client);

			setMultiQP(multiQP);

//            initializeIQ4();
//            updateAllProductTypes();
		}
	}

	/**
	 * @param productTypeId int in string = Master product type
	 * @param faceAmount    $$$
	 * @param dateOfBirth   yyyymmdd
	 * @param gender        M, F
	 * @param smoking       Y, N
	 * @param provCode      QC
	 * @return example of content: [ { "f1": "v1", "f2": "v2", "f3": "v3" ... }, {
	 *         ... }, { ... } ]
	 */
	private String getResponseString(final String productTypeId, final String faceAmount, final String dateOfBirth,
			final String gender, final String smoking, final String provCode, final String healthClass,
			final String permOrTerm, final String levelOrDecr) throws InsurfactPrivateException {

		// http://10.10.88.22:28081/WS_INSF/webresources/life/6/500000/1979-01-01/M/N/QC
		if (DEBUG) {
			System.out.println(TAG + ".getResponseString: entering with " + "productTypeId {" + productTypeId + "}"
					+ "faceAmount {" + faceAmount + "}" + "dateOfBirth {" + dateOfBirth + "}" + "gender {" + gender
					+ "}" + "smoking {" + smoking + "}" + "provCode {" + provCode + "}");
		}

		// step 1: validate the input values from the URL, important for security
		// ----------------------------------------------------------------------
		int productTypeInt = 0;
		try {
			productTypeInt = Integer.parseInt(productTypeId);
		} catch (NumberFormatException e) {
			throw new InsurfactPrivateException("invalid productTypeId input {" + productTypeId + "}");
		}

		int faceAmountInt = 0;
		try {
			faceAmountInt = Integer.parseInt(faceAmount);
		} catch (NumberFormatException e) {
			throw new InsurfactPrivateException("invalid faceAmount input {" + faceAmount + "}");
		}

		if (dateOfBirth == null || dateOfBirth.isEmpty()) {
			throw new InsurfactPrivateException("invalid dateOfBirth input {" + dateOfBirth + "}");
		}
		String dob = dateOfBirth;

		// gender M F
		if (gender == null || gender.isEmpty() || (!gender.equalsIgnoreCase("M") && !gender.equalsIgnoreCase("F"))) {
			throw new InsurfactPrivateException("invalid gender input {" + gender + "}");
		}
		String genderUp = gender.toUpperCase();

		// healthClass E P F
		if (healthClass == null || healthClass.isEmpty() || (!healthClass.equalsIgnoreCase("E")
				&& !healthClass.equalsIgnoreCase("P") && !healthClass.equalsIgnoreCase("S") && !healthClass.equalsIgnoreCase("A"))) {
			throw new InsurfactPrivateException("invalid healthClass input {" + healthClass + "}");
		}

		// smoking Y N
		if (smoking == null || smoking.isEmpty()
				|| (!smoking.equalsIgnoreCase("Y") && !smoking.equalsIgnoreCase("N"))) {
			throw new InsurfactPrivateException("invalid smoking input {" + smoking + "}");
		}
		String smokingUp = smoking.toUpperCase();

		if (provCode == null || provCode.length() != 2) {
			throw new InsurfactPrivateException("invalid provCode input {" + provCode + "}");
		}
		String provCodeUp = provCode.toUpperCase();

		// step 2: setup database acccess
		// ------------------------------
		try {
			Class.forName("oracle.jdbc.OracleDriver");
		} catch (ClassNotFoundException ex) {
			if (DEBUG) {
				System.out.println(
						TAG + ".getResponseString: *Class.forName(\"oracle.jdbc.OracleDriver\")* raised: " + ex);
			}
			throw new InsurfactPrivateException("*Class.forName(\"oracle.jdbc.OracleDriver\")* raised " + ex);
		}

		boolean forProdDb = false;
		try {
			forProdDb = InsurfactSdkProperties.isForProdDb();
		} catch (Throwable ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: *InsurfactSdkProperties.isForProdDb()* raised: " + ex);
			}
			throw new InsurfactPrivateException("*InsurfactSdkProperties.isForProdDb()* raised " + ex
			// +" - "+ex.getMessage()
					+ "\n" + LogUtil.getStackTrace(ex, 10));
		}

		if (DEBUG) {
			System.out.println(TAG + ".getResponseString: forProdDb = " + forProdDb);
		}

		try {

			con = InsurfactSdkProperties.getDb1Connection(forProdDb); // NOTE \\ For what is this connection?

		} catch (SQLException ex) {
			String connectionText = "";
			if (DEBUG) {
				connectionText = "\n" + Util.getTextOnDb1Connection(con);
				System.out.println(TAG + ".getResponseString: " + "*InsurfactSdkProperties.getDb1Connection(forProdDb {"
						+ forProdDb + "})* raised: " + ex + connectionText);
			}
			throw new InsurfactPrivateException(
					"*InsurfactSdkProperties.getDb1Connection(forProdDb {" + forProdDb + "})* raised " + ex
					// +" - "+ex.getMessage()
							+ connectionText + "\n" + LogUtil.getStackTrace(ex, 10));
		}

		if (DEBUG) {
			System.out.println(TAG + ".getResponseString: " + "*getTextOnDb1Connection()* returned "
					+ Util.getTextOnDb1Connection(con));
		}

		// step 3: prepare the input data to the sql
		// -----------------------------------------
		try {
			// ------------------------------------------------------------------------------------------
			prepareQuoteParams(smokingUp, provCodeUp, faceAmountInt, productTypeInt, dob, genderUp, healthClass,
					permOrTerm, levelOrDecr);
			// ------------------------------------------------------------------------------------------
		} catch (InsurfactPrivateException e) {
			String s = "*prepareQuoteParam(smokingUp, provCodeUp, faceAmountInt)* raised {" + e + "}";
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + s);
			}
			throw e;
		} catch (Throwable e) {
			String s = "*prepareQuoteParam(smokingUp, provCodeUp, faceAmountInt)* raised {" + e + "}";
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + s);
			}
			throw new InsurfactPrivateException(s);
		}

		// step 4: run the sql and quote engine logic
		// ------------------------------------------
		List<Product> productsWithPercentage = getProductsCombos();

		// step 5: prepare results to return to client requester
		// -----------------------------------------------------
		String response = "\"No matching records.\"";

		if (productsWithPercentage == null || productsWithPercentage.isEmpty()) {

			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + "*productsWithPercentage = getProductsCombos()* "
						+ "returned null or empty list.");
			}

			return response;
		}

		int counter = 0;
		StringBuilder buf = new StringBuilder("[ ");

		try {
			for (Product product : productsWithPercentage) {

				int tmpAge = 0;

				multiQP = getMultiQP();

				if (product.getAgeToUse().equalsIgnoreCase("N")) {
					tmpAge = multiQP.getClient().getNearestAge();
				} else {
					tmpAge = multiQP.getClient().getActualAge();
				}

				promo = null;
				findPromotion(product);
				if (isHasPromotion() || product.hasPromoBuiltIn()) {
					product.setPromotions(product.getPromotions());
					promotionsHelper.applyPromotion(product, promo, tmpAge, multiQP.getFaceAmt(), multiQP);
				}

			}

			Collections.sort(productsWithPercentage, new TotalAnnComparator());

			for (Product product : productsWithPercentage) {

				if ("ALL".equals(OUTPUT_TYPE)) {
					List<String> underwritingList = quoteFacade.getUnderwriting(product.getCompany().getID(),
							product.getUnderwritingGroupID(), faceAmountInt, product.getAge(),
							Character.toString(multiQP.getClient().getGender()));
					product.setUnderwriting(underwritingList);

					List<String> underwritingListFr = quoteFacade.getUnderwritingFr(product.getCompany().getID(),
							product.getUnderwritingGroupID(), faceAmountInt, product.getAge(),
							Character.toString(multiQP.getClient().getGender()));

					product.setUnderwritingFr(underwritingListFr);
				}

				++counter;
				if (counter > 1) {
					buf.append(", ");
				}

				quoteFacade.getProductShortDescriptions(product);

				Company company = product.getCompany();
				String companyNameEn = "";
				String companyNameFr = "";
				if (company != null) {
					companyNameEn = product.getCompany().getName();
					companyNameFr = product.getCompany().getNameFr();
				}

				if (DEBUG) {
					System.out.println(TAG + ".getResponseString: calling getResponseValuesInJsonForOneResult with"
							+ "\n counter {" + counter + "}" + "\n companyNameEn {" + companyNameEn + "}"
							+ "\n companyNameFr {" + companyNameFr + "}" + "\n product.getName() {" + product.getName()
							+ "}" + "\n product.getNameFr() {" + product.getNameFr() + "}" + "\n provCodeUp {"
							+ provCodeUp + "}" + "\n getStringFromDouble(product.getProductMonthlyPremiumTotal()) {"
							+ Util.getStringFromDouble(product.getProductMonthlyPremiumTotal()) + "}"
							+ "\n getStringFromDouble(product.getProductAnnualPremiumTotal()) {"
							+ Util.getStringFromDouble(product.getProductAnnualPremiumTotal()) + "}"
							+ "\n product.getDescription() {" + product.getDescription() + "}"
							+ "\n product.getDescriptionFr() {" + product.getDescription() + "}"
							+ "\n product.getUnderwriting() {" + product.getUnderwriting() + "}");
				}

				// product data: company, product name,
				// yearly premium, monthly premium, short desc en, short desc fr
				String uw = "";
				String uwF = ""; // french underwriting
				/*
				 * if(product.getUnderwriting() != null){ Iterator<String> iterator =
				 * product.getUnderwriting().iterator(); while (iterator.hasNext()) { uw +=
				 * iterator.next(); if (iterator.hasNext()) { uw += ", "; } } } else
				 * System.out.println(" product.getUnderwriting() IS NULL ~ ~ ~ ");
				 * 
				 * 
				 * 
				 * if(product.getUnderwritingFr() != null){ Iterator<String> iterator =
				 * product.getUnderwritingFr().iterator(); while (iterator.hasNext()) { uwF +=
				 * iterator.next(); if (iterator.hasNext()) { uwF += ", "; } } }
				 */
				String premRenew = "";

				int tmpAge = 0;

				multiQP = getMultiQP();

				if (product.getAgeToUse().equalsIgnoreCase("N")) {
					tmpAge = multiQP.getClient().getNearestAge();
				} else {
					tmpAge = multiQP.getClient().getActualAge();
				}

				multiQP.setProductID(product.getID());
				multiQP.setCompanyID(product.getCompany().getID());
				multiQP.setReferenceProduct(product);
				// multiQP.setHealthClass(product.getHealthClass().getGlobalClass());

				/*
				 * Product newProduct = new Product();
				 * 
				 * if("ALL".equals(OUTPUT_TYPE)){ newProduct =
				 * quoteFacade.singleProductQuote(multiQP, product); }
				 */
				/*
				 * int lowAge = tmpAge; int highAge = 0;
				 */
				/*
				 * for (PremiumRenewal pr : newProduct.getPremiumRenewals()) { lowAge =
				 * tmpAge+pr.getLowBound(); highAge = tmpAge+pr.getHighBound();
				 * 
				 * premRenew+=lowAge+"-"+highAge+" m="+
				 * Util.getStringFromDouble(pr.getTotalMonthly()) + " a="+
				 * Util.getStringFromDouble(pr.getTotalAnnual())+"  ";
				 * 
				 * }
				 */
				int convertibleAge = product.getLastConvertibleAge();
				int payableAge = product.getAgePayable();
				String healthClassEn = product.getHealthClass().getName();
				String healthClassFr = product.getHealthClass().getNameFr();

				if ("ALL".equals(OUTPUT_TYPE)) {
					BigDecimal percentage = product.getPercentageGainWithAgent().setScale(2, RoundingMode.HALF_UP);
					if ("LIFE".equals(PRODUCT_CLASS)) {
						String res = JsonGenerator.getResponseValuesLIFEFineo(counter, companyNameEn, companyNameFr,
								product.getName(), product.getNameFr(), "" + product.getProductType().getID(),
								product.getProductTypeLabel(), provCodeUp,
								Util.getStringFromDouble(product.getProductMonthlyPremiumTotal()),
								Util.getStringFromDouble(product.getProductAnnualPremiumTotal()), "" + percentage,
								product.getDescription(), product.getDescriptionFr(), premRenew, uw, uwF,
								convertibleAge + "", payableAge + "", healthClassEn, healthClassFr, smokingUp,
								faceAmount, tmpAge, product);
						buf.append(res);
					} else {
						// crit
						String res = JsonGenerator.getResponseValuesCRITFineo(counter, companyNameEn, companyNameFr,
								product.getName(), product.getNameFr(), "" + product.getProductType().getID(),
								product.getProductTypeLabel(), provCodeUp,
								Util.getStringFromDouble(product.getProductMonthlyPremiumTotal()),
								Util.getStringFromDouble(product.getProductAnnualPremiumTotal()), "" + percentage,
								"" + product.getNumIllnesses(), product.getDescription(), product.getDescriptionFr(),
								premRenew, uw, uwF, convertibleAge + "", payableAge + "", healthClassEn, healthClassFr,
								smokingUp, faceAmount, tmpAge, product);
						buf.append(res);
					}
				} else {
					// percentage

					BigDecimal percentage = product.getPercentageGainWithAgent().setScale(2, RoundingMode.HALF_UP);

					if ("LIFE".equals(PRODUCT_CLASS)) {
						String res = JsonGenerator.getResponseValuesInJsonForOneResultForPercentage(counter,
								Util.getStringFromDouble(product.getProductMonthlyPremiumTotal()),
								Util.getStringFromDouble(product.getProductAnnualPremiumTotal()), "" + percentage,
								"" + product.getProductType().getID(), product.getProductTypeLabel());
						buf.append(res);
					} else {
						// crit
						String res = JsonGenerator.getResponseValuesInJsonForOneResultForPercentage(counter,
								Util.getStringFromDouble(product.getProductMonthlyPremiumTotal()),
								Util.getStringFromDouble(product.getProductAnnualPremiumTotal()),
								// number of illnesses
								"" + product.getNumIllnesses(), "" + percentage, "" + product.getProductType().getID(),
								product.getProductTypeLabel());
						buf.append(res);
					}
				}
			} // for

			if (counter > 0) {
				buf.append(" ]");
				response = buf.toString();
			} else {
				response = "\"no matching records\"";
			}
		} catch (Exception ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: Reading the results raised: " + ex);
			}
			// response = "\"Reading the resultsSet raised:
			// "+cleanStringForJson(ex.getMessage())+"\"";
			throw new InsurfactPrivateException("Reading the results raised " + ex + " at counter " + counter
			// +" - "+ex.getMessage()
					+ "\n " + LogUtil.getStackTrace(ex, 10)
			// + "\n sql = " + sql
			);
		} finally {
		}

		try {
			if (con != null) {
				con.close();
			}
		} catch (SQLException e) {
			System.out.println("error closing connection con line 690 Generic e=" + e.getMessage());
		}

		return response;
	}

	@SuppressWarnings("unused")
	private String getResponseStringWidget(final String productTypeId, final String faceAmount,
			final String dateOfBirth, final String gender, final String smoking, final String provCode, boolean isGroup,
			final String healthClass, final String permOrTerm) throws InsurfactPrivateException {

		// http://10.10.88.22:28081/WS_INSF/webresources/lifeSingle/6/500000/1979-01-01/M/N/QC
		if (DEBUG) {
			System.out.println(TAG + ".getSingleResponseString: entering with " + "productTypeId {" + productTypeId
					+ "}" + "faceAmount {" + faceAmount + "}" + "dateOfBirth {" + dateOfBirth + "}" + "gender {"
					+ gender + "}" + "smoking {" + smoking + "}" + "provCode {" + provCode + "}");
		}

		// step 1: validate the input values from the URL, important for security
		// ----------------------------------------------------------------------
		int productTypeInt = 0;
		/*
		 * try { productTypeInt = Integer.parseInt(productTypeId); } catch
		 * (NumberFormatException e) { throw new
		 * InsurfactPrivateException("invalid productTypeId input {" + productTypeId +
		 * "}"); }
		 */
		int faceAmountInt = 0;
		try {
			faceAmountInt = Integer.parseInt(faceAmount);
		} catch (NumberFormatException e) {
			throw new InsurfactPrivateException("invalid faceAmount input {" + faceAmount + "}");
		}

		if (dateOfBirth == null || dateOfBirth.isEmpty()) {
			throw new InsurfactPrivateException("invalid dateOfBirth input {" + dateOfBirth + "}");
		}
		String dob = dateOfBirth;

		// gender M F
		if (gender == null || gender.isEmpty() || (!gender.equalsIgnoreCase("M") && !gender.equalsIgnoreCase("F"))) {
			throw new InsurfactPrivateException("invalid gender input {" + gender + "}");
		}
		String genderUp = gender.toUpperCase();

		// smoking Y N
		if (smoking == null || smoking.isEmpty()
				|| (!smoking.equalsIgnoreCase("Y") && !smoking.equalsIgnoreCase("N"))) {
			throw new InsurfactPrivateException("invalid smoking input {" + smoking + "}");
		}
		String smokingUp = smoking.toUpperCase();

		if (provCode == null || provCode.length() != 2) {
			throw new InsurfactPrivateException("invalid provCode input {" + provCode + "}");
		}
		String provCodeUp = provCode.toUpperCase();

		// step 2: setup database acccess ?????????????
		// ------------------------------
		// step 3: prepare the input data to the sql
		// -----------------------------------------
		try {
			// ------------------------------------------------------------------------------------------
			prepareQuoteParams(smokingUp, provCodeUp, faceAmountInt, productTypeInt, dob, genderUp, healthClass,
					permOrTerm, "A");
			// ------------------------------------------------------------------------------------------
		} catch (InsurfactPrivateException e) {
			String s = "*prepareSingleQuoteParams(smokingUp, provCodeUp, faceAmountInt)* raised {" + e + "}";
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + s);
			}
			throw e;
		} catch (Throwable e) {
			String s = "*prepareSingleQuoteParams(smokingUp, provCodeUp, faceAmountInt)* raised {" + e + "}";
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + s);
			}
			throw new InsurfactPrivateException(s);
		}

		// step 4: run the sql and quote engine logic
		// ------------------------------------------
		List<Product> productsWithPercentage = new ArrayList<>();

		List<Product> finalList = new ArrayList<>();

		if (isGroup) {
			productsWithPercentage = getProductsCombos();
//System.out.println("1142 isGroup"+isGroup);

		} else {
			productsWithPercentage = getProductsCombosSingle("S");
//System.out.println("1147 isGroup"+isGroup);
		}

		// step 5: prepare results to return to client requester
		// -----------------------------------------------------
		String response = "";

		if (productsWithPercentage == null || productsWithPercentage.isEmpty()) {

			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + "*productsWithPercentage = getProductsCombos()* "
						+ "returned null or empty list.");
			}

			return response;
		}

		Product p1 = new Product();
		Product p2 = new Product();
		Product p3 = new Product();
		Product p4 = new Product();

		double min1 = 999999999;
		double min2 = 999999999;
		double min3 = 999999999;
		double min4 = 999999999;

		int id1 = 0;
		int id2 = 0;
		int id3 = 0;
		int id4 = 0;

		if (isGroup) {// group of product type IDs
			for (Product product : productsWithPercentage) {
				Product referenceProduct = productFacade.getIQ4LifeProductDetail(product.getID());

				multiQP.setReferenceProduct(referenceProduct);

				String premRenew = "";
				int tmpAge = 0;

				multiQP = getMultiQP();

				if (product.getAgeToUse().equalsIgnoreCase("N")) {
					tmpAge = multiQP.getClient().getNearestAge();
				} else {
					tmpAge = multiQP.getClient().getActualAge();
				}
				/*
				 * product.getHealthClass().getID() Band activeBand =
				 * referenceProduct.getBandForDetails(age, client.getGender(),
				 * client.isSmoker(), qp.getOriginalFaceAmount(), client.getClassId());
				 */
				multiQP.setCompanyID(product.getCompany().getID());

				multiQP.setProductID(product.getID());

				multiQP.setFaceAmt(Integer.parseInt(faceAmount));
				multiQP.setOriginalFaceAmount(Integer.parseInt(faceAmount));

				boolean isNonSmoker = true;

				if (smokingUp.equals("Y")) {
					isNonSmoker = false;
				}

				multiQP.getClient().setNonSmoker(isNonSmoker);
				multiQP.getClient().setClassId(product.getHealthClass().getID());

				if (referenceProduct.isEnhanced()) {

					// calc and set enhanced faceAmoimt
					enhancedProductFacade.processEnhancedProduct(multiQP, referenceProduct);

					if (referenceProduct.getEnhancedFaceAmount() > 0) {

						// process enhanced face amount. in the case it requires a new amount, the
						// original qp.originalFaceAmt is set to prod.originalFaceAmt
						Integer enhancedFaceAmount = referenceProduct.getEnhancedFaceAmount();

						Band band = referenceProduct.getBandForDetails(tmpAge, genderUp.charAt(0),
								multiQP.getClient().isSmoker(), multiQP.getFaceAmt());
						Integer minMaxFaceAmount = null;

						if (band != null) {
							minMaxFaceAmount = band.getMinMaxFaceAmount();
						}

						// set new face amount
						multiQP.setFaceAmt(enhancedFaceAmount); // EMA-Jan 29th 2017

						if (minMaxFaceAmount != null) {
							int tmp = Math.abs((enhancedFaceAmount) - minMaxFaceAmount);

							// upgrade original face amount
							if (tmp >= 0 && tmp <= 1000) {
								multiQP.setFaceAmt(minMaxFaceAmount); // EMA-Jan 29th 2017
							}
						}

						// set new face amount
						multiQP.setFaceAmt(enhancedFaceAmount);
						referenceProduct.setFaceAmount(enhancedFaceAmount);
					}

					product = quoteFacade.singleProductQuote(multiQP, referenceProduct);

				}

				findPromotion(product);
				if (isHasPromotion() || product.hasPromoBuiltIn()) {
					product.setPromotions(product.getPromotions());
					promotionsHelper.applyPromotion(product, promo, tmpAge, multiQP.getFaceAmt(), multiQP);
				}
				promo = null;

				if (id1 == 0) {
					id1 = product.getProductType().getMasterID();
				} else {
					if (product.getProductType().getMasterID() != id1 && id2 == 0) {
						id2 = product.getProductType().getMasterID();
					} else {
						if (product.getProductType().getMasterID() != id2
								&& product.getProductType().getMasterID() != id1 && id3 == 0) {
							id3 = product.getProductType().getMasterID();
						}
					}
				}

				if (product.getProductType().getMasterID() == id1) {
					if (min1 > product.getProductAnnualPremiumTotal()) {
						min1 = product.getProductAnnualPremiumTotal();
						p1 = product;
					}
				}

				if (product.getProductType().getMasterID() == id2) {
					if (min2 > product.getProductAnnualPremiumTotal()) {
						min2 = product.getProductAnnualPremiumTotal();
						p2 = product;
					}
				}

				if (product.getProductType().getMasterID() == id3) {
					if (min3 > product.getProductAnnualPremiumTotal()) {
						min3 = product.getProductAnnualPremiumTotal();
						p3 = product;
					}
				}
			}
			// order by 9, 10, 6

			Product firstInList = p1;
			Product secInList = p2;
			Product thirdInList = p3;

			if (p1.getProductType().getMasterID() == 9) {
				firstInList = p1;
			}

			if (p2.getProductType().getMasterID() == 9) {
				firstInList = p2;
			}

			if (p3.getProductType().getMasterID() == 9) {
				firstInList = p3;
			}

			if (p1.getProductType().getMasterID() == 10) {
				secInList = p1;
			}

			if (p2.getProductType().getMasterID() == 10) {
				secInList = p2;
			}

			if (p3.getProductType().getMasterID() == 10) {
				secInList = p3;
			}

			if (p1.getProductType().getMasterID() == 6) {
				thirdInList = p1;
			}

			if (p2.getProductType().getMasterID() == 6) {
				thirdInList = p2;
			}

			if (p3.getProductType().getMasterID() == 6) {
				thirdInList = p3;
			}

			/*
			 * 12 = Pay to 65 16 = T100 17 = T100 w/csv
			 */
			if (p1.getProductType().getMasterID() == 12) {
				firstInList = p1;
			}

			if (p2.getProductType().getMasterID() == 12) {
				firstInList = p2;
			}

			if (p3.getProductType().getMasterID() == 12) {
				firstInList = p3;
			}

			if (p1.getProductType().getMasterID() == 16) {
				secInList = p1;
			}

			if (p2.getProductType().getMasterID() == 16) {
				secInList = p2;
			}

			if (p3.getProductType().getMasterID() == 16) {
				secInList = p3;
			}

			if (p1.getProductType().getMasterID() == 17) {
				thirdInList = p1;
			}

			if (p2.getProductType().getMasterID() == 17) {
				thirdInList = p2;
			}

			if (p3.getProductType().getMasterID() == 17) {
				thirdInList = p3;
			}

			/*
			 * 22 = T25 15 = T30 38 = T35
			 */
			if (p1.getProductType().getMasterID() == 22) {
				firstInList = p1;
			}

			if (p2.getProductType().getMasterID() == 22) {
				firstInList = p2;
			}

			if (p3.getProductType().getMasterID() == 22) {
				firstInList = p3;
			}

			if (p1.getProductType().getMasterID() == 15) {
				secInList = p1;
			}

			if (p2.getProductType().getMasterID() == 15) {
				secInList = p2;
			}

			if (p3.getProductType().getMasterID() == 15) {
				secInList = p3;
			}

			if (p1.getProductType().getMasterID() == 38) {
				thirdInList = p1;
			}

			if (p2.getProductType().getMasterID() == 38) {
				thirdInList = p2;
			}

			if (p3.getProductType().getMasterID() == 38) {
				thirdInList = p3;
			}

			/*
			 * 82 = T40 1 = T65 73 = T80
			 */
			if (p1.getProductType().getMasterID() == 82) {
				firstInList = p1;
			}

			if (p2.getProductType().getMasterID() == 82) {
				firstInList = p2;
			}

			if (p3.getProductType().getMasterID() == 82) {
				firstInList = p3;
			}

			if (p1.getProductType().getMasterID() == 1) {
				secInList = p1;
			}

			if (p2.getProductType().getMasterID() == 1) {
				secInList = p2;
			}

			if (p3.getProductType().getMasterID() == 1) {
				secInList = p3;
			}

			if (p1.getProductType().getMasterID() == 73) {
				thirdInList = p1;
			}

			if (p2.getProductType().getMasterID() == 73) {
				thirdInList = p2;
			}

			if (p3.getProductType().getMasterID() == 73) {
				thirdInList = p3;
			}

			/*
			 * 11 = 10 pay 2 = 15 pay 3 = 20 pay
			 */
			if (p1.getProductType().getMasterID() == 11) {
				firstInList = p1;
			}

			if (p2.getProductType().getMasterID() == 11) {
				firstInList = p2;
			}

			if (p3.getProductType().getMasterID() == 11) {
				firstInList = p3;
			}

			if (p1.getProductType().getMasterID() == 2) {
				secInList = p1;
			}

			if (p2.getProductType().getMasterID() == 2) {
				secInList = p2;
			}

			if (p3.getProductType().getMasterID() == 2) {
				secInList = p3;
			}

			if (p1.getProductType().getMasterID() == 3) {
				thirdInList = p1;
			}

			if (p2.getProductType().getMasterID() == 3) {
				thirdInList = p2;
			}

			if (p3.getProductType().getMasterID() == 3) {
				thirdInList = p3;
			}

			finalList.add(firstInList);
			finalList.add(secInList);
			finalList.add(thirdInList);

			// newProduct.getProductType().getMasterID()
		} else {// sigle product type
			for (Product product : productsWithPercentage) {
//System.out.println("1479 Generic product.getHealthClass().getName()"+product.getHealthClass().getName()+" product.getHealthClass().getID()"+product.getHealthClass().getID());
				Product referenceProduct = productFacade.getIQ4LifeProductDetail(product.getID());

				multiQP.setReferenceProduct(referenceProduct);

				String premRenew = "";
				int tmpAge = 0;

				multiQP = getMultiQP();

				if (product.getAgeToUse().equalsIgnoreCase("N")) {
					tmpAge = multiQP.getClient().getNearestAge();
				} else {
					tmpAge = multiQP.getClient().getActualAge();
				}
				/*
				 * product.getHealthClass().getID() Band activeBand =
				 * referenceProduct.getBandForDetails(age, client.getGender(),
				 * client.isSmoker(), qp.getOriginalFaceAmount(), client.getClassId());
				 */
				multiQP.setCompanyID(product.getCompany().getID());

				multiQP.setProductID(product.getID());

				multiQP.setFaceAmt(Integer.parseInt(faceAmount));
				multiQP.setOriginalFaceAmount(Integer.parseInt(faceAmount));

				boolean isNonSmoker = true;

				if (smokingUp.equals("Y")) {
					isNonSmoker = false;
				}

				multiQP.getClient().setNonSmoker(isNonSmoker);
				multiQP.getClient().setClassId(product.getHealthClass().getID());

				if (referenceProduct.isEnhanced()) {

					// calc and set enhanced faceAmoimt
					enhancedProductFacade.processEnhancedProduct(multiQP, referenceProduct);

					if (referenceProduct.getEnhancedFaceAmount() > 0) {

						// process enhanced face amount. in the case it requires a new amount, the
						// original qp.originalFaceAmt is set to prod.originalFaceAmt
						Integer enhancedFaceAmount = referenceProduct.getEnhancedFaceAmount();

						Band band = referenceProduct.getBandForDetails(tmpAge, genderUp.charAt(0),
								multiQP.getClient().isSmoker(), multiQP.getFaceAmt());
						Integer minMaxFaceAmount = null;

//System.out.println("1527 Generic enhanced face amount=" + enhancedFaceAmount);
						if (band != null) {
							minMaxFaceAmount = band.getMinMaxFaceAmount();
							// System.out.println(band);
						}

						// set new face amount
						multiQP.setFaceAmt(enhancedFaceAmount); // EMA-Jan 29th 2017

						if (minMaxFaceAmount != null) {
							int tmp = Math.abs((enhancedFaceAmount) - minMaxFaceAmount);

							// upgrade original face amount
							if (tmp >= 0 && tmp <= 1000) {
								multiQP.setFaceAmt(minMaxFaceAmount); // EMA-Jan 29th 2017
							}
						}

						// set new face amount
						multiQP.setFaceAmt(enhancedFaceAmount);
						referenceProduct.setFaceAmount(enhancedFaceAmount);
						// System.out.println("1549 Generic -- New enhanced face amount =" +
						// enhancedFaceAmount);
					}

					product = quoteFacade.singleProductQuote(multiQP, referenceProduct);

				}
				findPromotion(product);
				if (isHasPromotion() || product.hasPromoBuiltIn()) {
					product.setPromotions(product.getPromotions());
					promotionsHelper.applyPromotion(product, promo, tmpAge, multiQP.getFaceAmt(), multiQP);
				}
				promo = null;

				if (min1 > product.getProductAnnualPremiumTotal()) {
					min1 = product.getProductAnnualPremiumTotal();
					p1 = product;
				}
			}
			finalList.add(p1);
		}

		int counter = 0;
		StringBuilder buf = new StringBuilder("[ ");
		try {
			for (Product product : finalList) {
				if ("ALL".equals(OUTPUT_TYPE)) {
					List<String> underwritingList = quoteFacade.getUnderwriting(product.getCompany().getID(),
							product.getUnderwritingGroupID(), faceAmountInt, product.getAge(),
							Character.toString(multiQP.getClient().getGender()));
					product.setUnderwriting(underwritingList);

					List<String> underwritingListFr = quoteFacade.getUnderwritingFr(product.getCompany().getID(),
							product.getUnderwritingGroupID(), faceAmountInt, product.getAge(),
							Character.toString(multiQP.getClient().getGender()));

					product.setUnderwritingFr(underwritingListFr);
				}

				++counter;
				if (counter > 1) {
					buf.append(", ");
				}

				quoteFacade.getProductShortDescriptions(product);

				Company company = product.getCompany();
				String companyNameEn = "";
				String companyNameFr = "";
				if (company != null) {
					companyNameEn = product.getCompany().getName();
					companyNameFr = product.getCompany().getNameFr();
				}
				if (DEBUG) {
					System.out.println(TAG + ".getResponseString: calling getResponseValuesInJsonForOneResult with"
							+ "\n counter {" + counter + "}" + "\n companyNameEn {" + companyNameEn + "}"
							+ "\n companyNameFr {" + companyNameFr + "}" + "\n product.getName() {" + product.getName()
							+ "}" + "\n product.getNameFr() {" + product.getNameFr() + "}" + "\n provCodeUp {"
							+ provCodeUp + "}" + "\n getStringFromDouble(product.getProductMonthlyPremiumTotal()) {"
							+ Util.getStringFromDouble(product.getProductMonthlyPremiumTotal()) + "}"
							+ "\n getStringFromDouble(product.getProductAnnualPremiumTotal()) {"
							+ Util.getStringFromDouble(product.getProductAnnualPremiumTotal()) + "}"
							+ "\n product.getDescription() {" + product.getDescription() + "}"
							+ "\n product.getDescriptionFr() {" + product.getDescription() + "}"
							+ "\n product.getUnderwriting() {" + product.getUnderwriting() + "}");
				}

				// product data: company, product name,
				// yearly premium, monthly premium, short desc en, short desc fr
				String uw = "";
				String uwF = ""; // french underwriting

				String premRenew = "";
				int tmpAge = 0;

				multiQP = getMultiQP();

				if (product.getAgeToUse().equalsIgnoreCase("N")) {
					tmpAge = multiQP.getClient().getNearestAge();
				} else {
					tmpAge = multiQP.getClient().getActualAge();
				}

				multiQP.setCompanyID(product.getCompany().getID());

				multiQP.setProductID(product.getID());
				multiQP.setReferenceProduct(product);
				int lowAge = tmpAge;
				int highAge = 0;

				int convertibleAge = product.getLastConvertibleAge();
				int payableAge = product.getAgePayable();
				String healthClassEn = product.getHealthClass().getName();
				String healthClassFr = product.getHealthClass().getNameFr();

				BigDecimal percentage = product.getPercentageGainWithAgent().setScale(2, RoundingMode.HALF_UP);
				if ("LIFE".equals(PRODUCT_CLASS)) {
					String res = JsonGenerator.getResponseValuesLIFEWidget(counter, companyNameEn, companyNameFr,
							product.getName(), product.getNameFr(), "" + product.getProductType().getID(),
							product.getProductTypeLabel(), provCodeUp,
							Util.getStringFromDouble(product.getProductMonthlyPremiumTotal()),
							Util.getStringFromDouble(product.getProductAnnualPremiumTotal()), "" + percentage,
							product.getDescription(), product.getDescriptionFr(), premRenew, uw, uwF,
							convertibleAge + "", payableAge + "", healthClassEn, healthClassFr, smokingUp, faceAmount,
							tmpAge, product);
					buf.append(res);
				} else {
					// crit
					String res = JsonGenerator.getResponseValuesCRITWidget(counter, companyNameEn, companyNameFr,
							product.getName(), product.getNameFr(), "" + product.getProductType().getID(),
							product.getProductTypeLabel(), provCodeUp,
							Util.getStringFromDouble(product.getProductMonthlyPremiumTotal()),
							Util.getStringFromDouble(product.getProductAnnualPremiumTotal()), "" + percentage,
							"" + product.getNumIllnesses(), product.getDescription(), product.getDescriptionFr(),
							premRenew, uw, uwF, convertibleAge + "", payableAge + "", healthClassEn, healthClassFr,
							smokingUp, faceAmount, tmpAge, product);
					buf.append(res);
				}
			} // for{

			if (counter > 0) {
				buf.append(" ]");
				response = buf.toString();
			} else {
				response = "\"no matching records\"";
			}

		} catch (Exception ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: Reading the results raised: " + ex);
			}
			// response = "\"Reading the resultsSet raised:
			// "+cleanStringForJson(ex.getMessage())+"\"";
			throw new InsurfactPrivateException("Reading the results raised " + ex + " at counter " + counter
			// +" - "+ex.getMessage()
					+ "\n " + LogUtil.getStackTrace(ex, 10)
			// + "\n sql = " + sql
			);
		}

		try {
			if (con != null) { // NOTE \\ what is this connection closed here?
				con.close();
			}
		} catch (SQLException e) {
			System.out.println("error closing connection con line 667 Generic e=" + e.getMessage());
		}

		return response;
	}

	private String getResponseStringWidgetSpec(final String productTypeId, final String faceAmount,
			final String dateOfBirth, final String gender, final String smoking, final String provCode, boolean isGroup,
			final String healthClass, final String permOrTerm, final String levelOrDecr, final String limitedFull)
			throws InsurfactPrivateException {

		// http://10.10.88.22:28081/WS_INSF/webresources/lifeSingle/6/500000/1979-01-01/M/N/QC
		if (DEBUG) {
			System.out.println(TAG + ".getSingleResponseString: entering with " + "productTypeId {" + productTypeId
					+ "}" + "faceAmount {" + faceAmount + "}" + "dateOfBirth {" + dateOfBirth + "}" + "gender {"
					+ gender + "}" + "smoking {" + smoking + "}" + "provCode {" + provCode + "}");
		}

		// step 1: validate the input values from the URL, important for security
		// ----------------------------------------------------------------------
		int productTypeInt = 0;

		int faceAmountInt = 0;
		try {
			faceAmountInt = Integer.parseInt(faceAmount);
		} catch (NumberFormatException e) {
			throw new InsurfactPrivateException("invalid faceAmount input {" + faceAmount + "}");
		}

		if (dateOfBirth == null || dateOfBirth.isEmpty()) {
			throw new InsurfactPrivateException("invalid dateOfBirth input {" + dateOfBirth + "}");
		}
		String dob = dateOfBirth;

		// gender M F
		if (gender == null || gender.isEmpty() || (!gender.equalsIgnoreCase("M") && !gender.equalsIgnoreCase("F"))) {
			throw new InsurfactPrivateException("invalid gender input {" + gender + "}");
		}
		String genderUp = gender.toUpperCase();

		// smoking Y N
		if (smoking == null || smoking.isEmpty()
				|| (!smoking.equalsIgnoreCase("Y") && !smoking.equalsIgnoreCase("N"))) {
			throw new InsurfactPrivateException("invalid smoking input {" + smoking + "}");
		}
		String smokingUp = smoking.toUpperCase();

		if (provCode == null || provCode.length() != 2) {
			throw new InsurfactPrivateException("invalid provCode input {" + provCode + "}");
		}
		String provCodeUp = provCode.toUpperCase();

		// step 2: prepare the input data to the sql
		// -----------------------------------------
		try {
			// ------------------------------------------------------------------------------------------
			prepareQuoteParams(smokingUp, provCodeUp, faceAmountInt, productTypeInt, dob, genderUp, healthClass,
					permOrTerm, levelOrDecr);
			// ------------------------------------------------------------------------------------------
		} catch (InsurfactPrivateException e) {
			String s = "*prepareSingleQuoteParams(smokingUp, provCodeUp, faceAmountInt)* raised {" + e + "}";
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + s);
			}
			throw e;
		} catch (Throwable e) {
			String s = "*prepareSingleQuoteParams(smokingUp, provCodeUp, faceAmountInt)* raised {" + e + "}";
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + s);
			}
			throw new InsurfactPrivateException(s);
		}

		// step 3: run the sql and quote engine logic
		// ------------------------------------------
		List<Product> productsWithPercentage = new ArrayList<>();

		List<Product> finalList = new ArrayList<>();

		if (limitedFull != null) {
			if (limitedFull.equals("F")) {// limited >= 10 illnesses
				multiQP.setNumIllness("F");
			}
			if (limitedFull.equals("L")) {// limited < 10 illnesses
				multiQP.setNumIllness("L");

			}
			if (limitedFull.equals("")) {// all
				multiQP.setNumIllness("A");

			}
		}

		if (isGroup) {
			productsWithPercentage = getProductsCombosNew();
		} else {
			productsWithPercentage = getProductsCombosSingle("S");
//System.out.println("1147 isGroup"+isGroup);
		}

		// step 4: prepare results to return to client requester
		// -----------------------------------------------------
		String response = "";

		if (productsWithPercentage == null || productsWithPercentage.isEmpty()) {

			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + "*productsWithPercentage = getProductsCombos()* "
						+ "returned null or empty list.");
			}

			return response;
		}
		// 6, 9, 10, 15, 16, 22, 38
		Product p1 = new Product();
		p1.setID(0);
		Product p2 = new Product();
		p2.setID(0);
		Product p3 = new Product();
		p3.setID(0);
		Product p4 = new Product();
		p4.setID(0);
		Product p5 = new Product();
		p5.setID(0);
		Product p6 = new Product();
		p6.setID(0);
		Product p7 = new Product();
		p7.setID(0);

		double min1 = 999999999;
		double min2 = 999999999;
		double min3 = 999999999;
		double min4 = 999999999;
		double min5 = 999999999;
		double min6 = 999999999;
		double min7 = 999999999;

		int id1 = 9;
		int id2 = 10;
		int id3 = 6;
		int id4 = 22;
		int id5 = 15;
		int id6 = 38;
		int id7 = 16;

		if (PRODUCT_CLASS.equalsIgnoreCase("CRIT")) { // critical illness 8, 21, 69, 70, 4, 31, 5
			id1 = 8;
			id2 = 21;
			id3 = 69;
			id4 = 70;
			id5 = 4;
			id6 = 31;
			id7 = 5;
		}

		if (isGroup) {// group of product type IDs
			for (Product product : productsWithPercentage) {
				Product referenceProduct = productFacade.getIQ4LifeProductDetail(product.getID());

				multiQP.setReferenceProduct(referenceProduct);

				@SuppressWarnings("unused")
				String premRenew = "";
				int tmpAge = 0;

				multiQP = getMultiQP();

				if (product.getAgeToUse().equalsIgnoreCase("N")) {
					tmpAge = multiQP.getClient().getNearestAge();
				} else {
					tmpAge = multiQP.getClient().getActualAge();
				}
				/*
				 * product.getHealthClass().getID() Band activeBand =
				 * referenceProduct.getBandForDetails(age, client.getGender(),
				 * client.isSmoker(), qp.getOriginalFaceAmount(), client.getClassId());
				 */
				multiQP.setCompanyID(product.getCompany().getID());

				multiQP.setProductID(product.getID());

				multiQP.setFaceAmt(Integer.parseInt(faceAmount));
				multiQP.setOriginalFaceAmount(Integer.parseInt(faceAmount));

				boolean isNonSmoker = true;

				if (smokingUp.equals("Y")) {
					isNonSmoker = false;
				}

				multiQP.getClient().setNonSmoker(isNonSmoker);
				multiQP.getClient().setClassId(product.getHealthClass().getID());

				if (referenceProduct.isEnhanced()) {

					// calc and set enhanced faceAmoimt
					enhancedProductFacade.processEnhancedProduct(multiQP, referenceProduct);

					if (referenceProduct.getEnhancedFaceAmount() > 0) {

						// process enhanced face amount. in the case it requires a new amount, the
						// original qp.originalFaceAmt is set to prod.originalFaceAmt
						Integer enhancedFaceAmount = referenceProduct.getEnhancedFaceAmount();

						Band band = referenceProduct.getBandForDetails(tmpAge, genderUp.charAt(0),
								multiQP.getClient().isSmoker(), multiQP.getFaceAmt());
						Integer minMaxFaceAmount = null;

						if (band != null) {
							minMaxFaceAmount = band.getMinMaxFaceAmount();
						}

						// set new face amount
						multiQP.setFaceAmt(enhancedFaceAmount); // EMA-Jan 29th 2017

						if (minMaxFaceAmount != null) {
							int tmp = Math.abs((enhancedFaceAmount) - minMaxFaceAmount);

							// upgrade original face amount
							if (tmp >= 0 && tmp <= 1000) {
								multiQP.setFaceAmt(minMaxFaceAmount); // EMA-Jan 29th 2017
							}
						}

						// set new face amount
						multiQP.setFaceAmt(enhancedFaceAmount);
						referenceProduct.setFaceAmount(enhancedFaceAmount);
					}

					product = quoteFacade.singleProductQuote(multiQP, referenceProduct);

				}

				findPromotion(product);

				if (isHasPromotion() || product.hasPromoBuiltIn()) {
					product.setPromotions(product.getPromotions());

					promotionsHelper.applyPromotion(product, promo, tmpAge, multiQP.getFaceAmt(), multiQP);

				}
				promo = null;

				if (product.getProductType().getMasterID() == id1) {
					if (min1 > product.getProductAnnualPremiumTotal()) {
						min1 = product.getProductAnnualPremiumTotal();
						p1 = product;
					}
				}

				if (product.getProductType().getMasterID() == id2) {
					if (min2 > product.getProductAnnualPremiumTotal()) {
						min2 = product.getProductAnnualPremiumTotal();
						p2 = product;
					}
				}

				if (product.getProductType().getMasterID() == id3) {
					if (min3 > product.getProductAnnualPremiumTotal()) {
						min3 = product.getProductAnnualPremiumTotal();
						p3 = product;
					}
				}

				if (product.getProductType().getMasterID() == id4) {
					if (min4 > product.getProductAnnualPremiumTotal()) {
						min4 = product.getProductAnnualPremiumTotal();
						p4 = product;
					}
				}

				if (product.getProductType().getMasterID() == id5) {
					if (min5 > product.getProductAnnualPremiumTotal()) {
						min5 = product.getProductAnnualPremiumTotal();
						p5 = product;
					}
				}

				if (product.getProductType().getMasterID() == id6) {
					if (min6 > product.getProductAnnualPremiumTotal()) {
						min6 = product.getProductAnnualPremiumTotal();
						p6 = product;
					}
				}

				if (product.getProductType().getMasterID() == id7) {
					if (min7 > product.getProductAnnualPremiumTotal()) {
						min7 = product.getProductAnnualPremiumTotal();
						p7 = product;
					}
				}
			}

			if (p1.getID() != 0) {
				finalList.add(p1);
			}
			if (p2.getID() != 0) {
				finalList.add(p2);
			}
			if (p3.getID() != 0) {
				finalList.add(p3);
			}
			if (p4.getID() != 0) {
				finalList.add(p4);
			}
			if (p5.getID() != 0) {
				finalList.add(p5);
			}
			if (p6.getID() != 0) {
				finalList.add(p6);
			}
			if (p7.getID() != 0) {
				finalList.add(p7);
			}

			// newProduct.getProductType().getMasterID()
		} else {// sigle product type
			for (Product product : productsWithPercentage) {
//System.out.println("1479 Generic product.getHealthClass().getName()"+product.getHealthClass().getName()+" product.getHealthClass().getID()"+product.getHealthClass().getID());
				Product referenceProduct = productFacade.getIQ4LifeProductDetail(product.getID());

				multiQP.setReferenceProduct(referenceProduct);

				@SuppressWarnings("unused")
				String premRenew = "";
				int tmpAge = 0;

				multiQP = getMultiQP();

				if (product.getAgeToUse().equalsIgnoreCase("N")) {
					tmpAge = multiQP.getClient().getNearestAge();
				} else {
					tmpAge = multiQP.getClient().getActualAge();
				}
				/*
				 * product.getHealthClass().getID() Band activeBand =
				 * referenceProduct.getBandForDetails(age, client.getGender(),
				 * client.isSmoker(), qp.getOriginalFaceAmount(), client.getClassId());
				 */
				multiQP.setCompanyID(product.getCompany().getID());

				multiQP.setProductID(product.getID());

				multiQP.setFaceAmt(Integer.parseInt(faceAmount));
				multiQP.setOriginalFaceAmount(Integer.parseInt(faceAmount));

				boolean isNonSmoker = true;

				if (smokingUp.equals("Y")) {
					isNonSmoker = false;
				}

				multiQP.getClient().setNonSmoker(isNonSmoker);
				multiQP.getClient().setClassId(product.getHealthClass().getID());

				if (referenceProduct.isEnhanced()) {

					// calc and set enhanced faceAmoimt
					enhancedProductFacade.processEnhancedProduct(multiQP, referenceProduct);

					if (referenceProduct.getEnhancedFaceAmount() > 0) {

						// process enhanced face amount. in the case it requires a new amount, the
						// original qp.originalFaceAmt is set to prod.originalFaceAmt
						Integer enhancedFaceAmount = referenceProduct.getEnhancedFaceAmount();

						Band band = referenceProduct.getBandForDetails(tmpAge, genderUp.charAt(0),
								multiQP.getClient().isSmoker(), multiQP.getFaceAmt());
						Integer minMaxFaceAmount = null;

//System.out.println("1527 Generic enhanced face amount=" + enhancedFaceAmount);
						if (band != null) {
							minMaxFaceAmount = band.getMinMaxFaceAmount();
							// System.out.println(band);
						}

						// set new face amount
						multiQP.setFaceAmt(enhancedFaceAmount); // EMA-Jan 29th 2017

						if (minMaxFaceAmount != null) {
							int tmp = Math.abs((enhancedFaceAmount) - minMaxFaceAmount);

							// upgrade original face amount
							if (tmp >= 0 && tmp <= 1000) {
								multiQP.setFaceAmt(minMaxFaceAmount); // EMA-Jan 29th 2017
							}
						}

						// set new face amount
						multiQP.setFaceAmt(enhancedFaceAmount);
						referenceProduct.setFaceAmount(enhancedFaceAmount);
						// System.out.println("1549 Generic -- New enhanced face amount =" +
						// enhancedFaceAmount);
					}

					product = quoteFacade.singleProductQuote(multiQP, referenceProduct);

				}
				findPromotion(product);
				if (isHasPromotion() || product.hasPromoBuiltIn()) {
					product.setPromotions(product.getPromotions());
					promotionsHelper.applyPromotion(product, promo, tmpAge, multiQP.getFaceAmt(), multiQP);
				}
				promo = null;

				if (min1 > product.getProductAnnualPremiumTotal()) {
					min1 = product.getProductAnnualPremiumTotal();
					p1 = product;
				}
			}

			finalList.add(p1);
		}

		int counter = 0;
		StringBuilder buf = new StringBuilder("[ ");
		try {
			for (Product product : finalList) {
				if ("ALL".equals(OUTPUT_TYPE)) {
					List<String> underwritingList = quoteFacade.getUnderwriting(product.getCompany().getID(),
							product.getUnderwritingGroupID(), faceAmountInt, product.getAge(),
							Character.toString(multiQP.getClient().getGender()));
					product.setUnderwriting(underwritingList);

					List<String> underwritingListFr = quoteFacade.getUnderwritingFr(product.getCompany().getID(),
							product.getUnderwritingGroupID(), faceAmountInt, product.getAge(),
							Character.toString(multiQP.getClient().getGender()));

					product.setUnderwritingFr(underwritingListFr);
				}

				++counter;
				if (counter > 1) {
					buf.append(", ");
				}

				quoteFacade.getProductShortDescriptions(product);

				Company company = product.getCompany();
				String companyNameEn = "";
				String companyNameFr = "";
				if (company != null) {
					companyNameEn = product.getCompany().getName();
					companyNameFr = product.getCompany().getNameFr();
				}
				if (DEBUG) {
					System.out.println(TAG + ".getResponseString: calling getResponseValuesInJsonForOneResult with"
							+ "\n counter {" + counter + "}" + "\n companyNameEn {" + companyNameEn + "}"
							+ "\n companyNameFr {" + companyNameFr + "}" + "\n product.getName() {" + product.getName()
							+ "}" + "\n product.getNameFr() {" + product.getNameFr() + "}" + "\n provCodeUp {"
							+ provCodeUp + "}" + "\n getStringFromDouble(product.getProductMonthlyPremiumTotal()) {"
							+ Util.getStringFromDouble(product.getProductMonthlyPremiumTotal()) + "}"
							+ "\n getStringFromDouble(product.getProductAnnualPremiumTotal()) {"
							+ Util.getStringFromDouble(product.getProductAnnualPremiumTotal()) + "}"
							+ "\n product.getDescription() {" + product.getDescription() + "}"
							+ "\n product.getDescriptionFr() {" + product.getDescription() + "}"
							+ "\n product.getUnderwriting() {" + product.getUnderwriting() + "}");
				}

				// product data: company, product name,
				// yearly premium, monthly premium, short desc en, short desc fr
				// String uw = "";
				// String uwF = ""; //french underwriting

				// String premRenew = "";
				int tmpAge = 0;

				multiQP = getMultiQP();

				if (product.getAgeToUse().equalsIgnoreCase("N")) {
					tmpAge = multiQP.getClient().getNearestAge();
				} else {
					tmpAge = multiQP.getClient().getActualAge();
				}

				multiQP.setCompanyID(product.getCompany().getID());

				multiQP.setProductID(product.getID());
				multiQP.setReferenceProduct(product);
				// int lowAge = tmpAge;
				// int highAge = 0;

				int convertibleAge = product.getLastConvertibleAge();
				int payableAge = product.getAgePayable();
				String healthClassEn = product.getHealthClass().getName();
				String healthClassFr = product.getHealthClass().getNameFr();

				String classFlag = product.getHealthClass().getDescription();

				int levDecr = 0;
				String coverageType = "A"; // default A for all

				if (product.getInsuranceBen() != 0) {
					levDecr = product.getInsuranceBen();
				}
				if (levDecr == 2) {
					coverageType = "L"; // L=level D=decreasing
				} else {
					coverageType = "D"; // L=level D=decreasing
				}

//                BigDecimal percentage = product.getPercentageGainWithAgent().setScale(2, RoundingMode.HALF_UP);
				String res = JsonGenerator.getResponseValues(counter, companyNameEn, companyNameFr, product.getName(),
						product.getNameFr(), "" + product.getProductType().getID(), product.getProductTypeLabel(),
						provCodeUp, Util.getStringFromDouble(product.getProductMonthlyPremiumTotal()),
						Util.getStringFromDouble(product.getProductAnnualPremiumTotal()),
						// ""+percentage,
						product.getDescription(), product.getDescriptionFr(), convertibleAge + "", payableAge + "",
						healthClassEn, healthClassFr, classFlag, smokingUp, faceAmount, tmpAge, product, coverageType, // Level
																														// or
																														// Decreasing
						PRODUCT_CLASS);
				buf.append(res);
			} // for{

			if (counter > 0) {
				buf.append(" ]");
				response = buf.toString();
			} else {
				response = "\"no matching records\"";
			}

		} catch (Exception ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: Reading the results raised: " + ex);
			}
			// response = "\"Reading the resultsSet raised:
			// "+cleanStringForJson(ex.getMessage())+"\"";
			throw new InsurfactPrivateException("Reading the results raised " + ex + " at counter " + counter
			// +" - "+ex.getMessage()
					+ "\n " + LogUtil.getStackTrace(ex, 10)
			// + "\n sql = " + sql
			);
		}

		try {
			if (con != null) { // same here, i think all those connections can be removed
				con.close();
			}
		} catch (SQLException e) {
			System.out.println("error closing connection con line 667 Generic e=" + e.getMessage());
		}

		return response;
	}

	private List<Product> getProductsCombos() throws InsurfactPrivateException {
		List<Product> products = new LinkedList<>();

		multiQP = getMultiQP();

		// int type1 = getProductType(multiQP);
		int type1 = multiQP.getProductTypeID();

		try {
			Combos combosit = PRODUCT_CLASS.equals("LIFE") ? CombosLifeNew.IT : CombosCritNew.IT;
			List<Integer> combos;
			try {
				combos = combosit.getCombos(type1);
			} catch (Exception e) {
				if (DEBUG) {
					System.out.println(TAG + ".getProductsCombos: getCombos(type1) failed - " + e.getMessage());
				}
				return products;
			}
			for (Integer p : combos) {
				multiQP.setProductTypeID(p);
				// setProductType(multiQP, p);

				// =====================================================
				List<Product> products2 = getProductsOneType();
				// =====================================================

				// -----------------------------------------------------------------------------------
				List<Product> productsWithPercentage = setPercentageGainsWithAgent(products2, multiQP);
				// -----------------------------------------------------------------------------------

				if (DEBUG) {
					System.out.println(TAG + ".getProductsCombos: " + "*setPercentageGainsWithAgent(products,multiQP)* "
							+ "returned productsWithPercentage.size {" + productsWithPercentage.size() + "}");
				}

				products.addAll(productsWithPercentage);
			}
		} catch (InsurfactPrivateException e) {
			throw e;
		} catch (Throwable e) {
			StackTraceElement[] stack = e.getStackTrace();
			StringBuilder buf = new StringBuilder();
			for (StackTraceElement st : stack) {
				buf.append("\n").append(st);
			}
			String s = "*product = quoteFacade.multiProductQuote(multiQP, null)* raised {" + e + "} cause: "
					+ e.getCause() + buf;
			if (DEBUG) {
				System.out.println(TAG + ".getProductsCombos: " + s);
			}
			throw new InsurfactPrivateException(s, e);
		}
		// reset the type to the original one:
		// setProductType(multiQP, type1);
		multiQP.setProductTypeID(type1);

		return products;
	}

	private List<Product> getProductsCombosNew() throws InsurfactPrivateException {
		List<Product> products = new LinkedList<>();

		multiQP = getMultiQP();

		// int type1 = getProductType(multiQP);
		int[] types = { 9, 10, 6, 22, 15, 38, 16 }; // LIFE
		int[] typesCI = { 8, 21, 69, 70, 4, 31, 5 }; // CRIT

		try {
			int i = 0;
			while (i < 7) {
				if (PRODUCT_CLASS.equalsIgnoreCase("CRIT")) {
					multiQP.setProductTypeID(typesCI[i]);
				} else {
					multiQP.setProductTypeID(types[i]);
				}
				// =====================================================
				List<Product> products2 = getProductsOneType();
				// List<Product> productsWithPercentage =
				// setPercentageGainsWithAgent(products2,multiQP);
				products.addAll(products2);

				i++;
			}
		} catch (InsurfactPrivateException e) {
			throw e;
		} catch (Throwable e) {
			StackTraceElement[] stack = e.getStackTrace();
			StringBuilder buf = new StringBuilder();
			for (StackTraceElement st : stack) {
				buf.append("\n").append(st);
			}
			String s = "*product = quoteFacade.multiProductQuote(multiQP, null)* raised {" + e + "} cause: "
					+ e.getCause() + buf;
			if (DEBUG) {
				System.out.println(TAG + ".getProductsCombos: " + s);
			}
			throw new InsurfactPrivateException(s, e);
		}

		multiQP.setProductTypeID(0);

		return products;
	}

	private List<Product> getProductsCombosSingle(String productTypeSel) throws InsurfactPrivateException {

		List<Product> products = new LinkedList<>();

		multiQP = getMultiQP();

		// int type1 = 0;

		if (productTypeSel.equalsIgnoreCase("S")) { // single product type
			// type1 = multiQP.getProductTypeID();

			List<Product> products2 = getProductsOneType();
			List<Product> productsWithPercentage = setPercentageGainsWithAgent(products2, multiQP);
			products.addAll(productsWithPercentage);

		}

		if (productTypeSel.equalsIgnoreCase("A")) { // all product type
			multiQP.setProductTypeID(0);
			List<Product> products2 = getProductsOneType();
			@SuppressWarnings("unused")
			List<Product> productsWithPercentage = setPercentageGainsWithAgent(products2, multiQP); // esclude the
																									// percentage
			products.addAll(products2);

		}
		/*
		 * if(productTypeSel.equalsIgnoreCase("C")){ // reset the type to the original
		 * one: -> only if COMBO types multiQP.setProductTypeID(type1);
		 * setProductType(multiQP, type1); }
		 */
		return products;
	}

	private List<Product> getProductsOneType() throws InsurfactPrivateException {
		List<Product> products = null;

		try {
			// ===============================================================

			products = quoteFacade.multiProductQuote(getMultiQP(), null);
			// ===============================================================
		} catch (Throwable e) {
			StackTraceElement[] stack = e.getStackTrace();
			StringBuilder buf = new StringBuilder();
			for (StackTraceElement st : stack) {
				buf.append("\n").append(st);
			}
			String s = "*product = quoteFacade.multiProductQuote(multiQP, null)* raised {" + e + "} cause: "
					+ e.getCause() + buf;
			if (DEBUG) {
				System.out.println(TAG + ".getProductsOneType: " + s);
			}
			throw new InsurfactPrivateException(s, e);
		}

		return products;
	}

	private List<ProductType> getProductTypes(int serviceID) throws InsurfactPrivateException {
		List<ProductType> productTypes = null;

		try {
			// ===============================================================

			productTypes = productFacade.findIQ4ProductTypes(serviceID);
			// ===============================================================
		} catch (Throwable e) {
			StackTraceElement[] stack = e.getStackTrace();
			StringBuilder buf = new StringBuilder();
			for (StackTraceElement st : stack) {
				buf.append("\n").append(st);
			}
			String s = "*product = quoteFacade.multiProductQuote(multiQP, null)* raised {" + e + "} cause: "
					+ e.getCause() + buf;
			if (DEBUG) {
				System.out.println(TAG + ".getProductsOneType: " + s);
			}
			throw new InsurfactPrivateException(s, e);
		}

		return productTypes;
	}

	private List<IMProduct> getWSProducts() throws InsurfactPrivateException {
		List<IMProduct> products = null;

		try {
			// ===============================================================

			products = productFacade.findWSProducts();
			// ===============================================================
		} catch (Throwable e) {
			StackTraceElement[] stack = e.getStackTrace();
			StringBuilder buf = new StringBuilder();
			for (StackTraceElement st : stack) {
				buf.append("\n").append(st);
			}
			String s = "*product = quoteFacade.multiProductQuote(multiQP, null)* raised {" + e + "} cause: "
					+ e.getCause() + buf;
			if (DEBUG) {
				System.out.println(TAG + ".getProductsOneType: " + s);
			}
			throw new InsurfactPrivateException(s, e);
		}

		return products;
	}

	private List<Province> getProvinces() throws InsurfactPrivateException {
		List<Province> provs = null;

		try {
			// ===============================================================

			provs = productFacade.findProvinces();
			// ===============================================================
		} catch (Throwable e) {
			StackTraceElement[] stack = e.getStackTrace();
			StringBuilder buf = new StringBuilder();
			for (StackTraceElement st : stack) {
				buf.append("\n").append(st);
			}
			String s = "*product = quoteFacade.multiProductQuote(multiQP, null)* raised {" + e + "} cause: "
					+ e.getCause() + buf;
			if (DEBUG) {
				System.out.println(TAG + ".getProductsOneType: " + s);
			}
			throw new InsurfactPrivateException(s, e);
		}
		return provs;
	}

	private Province getDefaultProvince(int usersID) throws InsurfactPrivateException {

		Province prov = null;

		try {
			// ===============================================================

			prov = productFacade.getDefaultProvince(usersID);

			// ===============================================================
		} catch (Throwable e) {
			StackTraceElement[] stack = e.getStackTrace();
			StringBuilder buf = new StringBuilder();
			for (StackTraceElement st : stack) {
				buf.append("\n").append(st);
			}
			String s = "2567 Generic Widget getLocales raised {" + e + "} cause: " + e.getCause() + buf;
			if (DEBUG) {
				System.out.println(TAG + ".getProductsOneType: " + s);
			}
			throw new InsurfactPrivateException(s, e);
		}

		return prov;
	}

	private AboutMe getAboutMe(int usersID) throws InsurfactPrivateException {

		AboutMe aboutMe = null;

		try {
			// ===============================================================

			aboutMe = productFacade.getAboutMe(usersID);

			// ===============================================================
		} catch (Throwable e) {
			StackTraceElement[] stack = e.getStackTrace();
			StringBuilder buf = new StringBuilder();
			for (StackTraceElement st : stack) {
				buf.append("\n").append(st);
			}
			String s = "2567 Generic Widget getLocales raised {" + e + "} cause: " + e.getCause() + buf;
			if (DEBUG) {
				System.out.println(TAG + ".getProductsOneType: " + s);
			}
			throw new InsurfactPrivateException(s, e);
		}

		return aboutMe;
	}

	private List<Types> getTypes(int serviceID) throws InsurfactPrivateException {
		List<Types> types = null;

		try {
			// ===============================================================

			types = productFacade.findTypes(serviceID);
			// ===============================================================
		} catch (Throwable e) {
			StackTraceElement[] stack = e.getStackTrace();
			StringBuilder buf = new StringBuilder();
			for (StackTraceElement st : stack) {
				buf.append("\n").append(st);
			}
			String s = "*product = quoteFacade.multiProductQuote(multiQP, null)* raised {" + e + "} cause: "
					+ e.getCause() + buf;
			if (DEBUG) {
				System.out.println(TAG + ".getProductsOneType: " + s);
			}
			throw new InsurfactPrivateException(s, e);
		}

		return types;
	}

	private List<Types> getTitleTypes() throws InsurfactPrivateException {
		List<Types> types = null;

		try {
			// ===============================================================

			types = productFacade.findTitleTypes();
			// ===============================================================
		} catch (Throwable e) {
			StackTraceElement[] stack = e.getStackTrace();
			StringBuilder buf = new StringBuilder();
			for (StackTraceElement st : stack) {
				buf.append("\n").append(st);
			}
			String s = "*product = quoteFacade.multiProductQuote(multiQP, null)* raised {" + e + "} cause: "
					+ e.getCause() + buf;
			if (DEBUG) {
				System.out.println(TAG + ".getProductsOneType: " + s);
			}
			throw new InsurfactPrivateException(s, e);
		}

		return types;
	}

	@SuppressWarnings("unused")
	private List<Product> getProductsJoint() throws InsurfactPrivateException {

		List<Product> products = null;

		try {
			// ===============================================================

			products = quoteFacade.getAvailableJointProducts(getMultiQP(), 0);

			// ===============================================================
		} catch (Throwable e) {
			StackTraceElement[] stack = e.getStackTrace();
			StringBuilder buf = new StringBuilder();
			for (StackTraceElement st : stack) {
				buf.append("\n").append(st);
			}
			String s = "*product = quoteFacade.multiProductQuote(multiQP, null)* raised {" + e + "} cause: "
					+ e.getCause() + buf;
			if (DEBUG) {
				System.out.println(TAG + ".getProductsOneType: " + s);
			}
			throw new InsurfactPrivateException(s, e);
		}

		return products;
	}

	/**
	 * <p>
	 * step 1: get largest set of applicable products from IQ4Engine; this is the
	 * given list from the caller.
	 *
	 * <p>
	 * step 2: get the most expensive set of products: with smokerCode N for
	 * non-smoker, or with smokerCode S for smoker; this is the list returned by
	 * this method.
	 *
	 * <p>
	 * step 3: get the lowest annualPremium (for E & P for non-smoker, and for 1 and
	 * P for smoker) for the entire list given by the caller.
	 *
	 * <p>
	 * step 4: go over the list from step 2 and calculate the gain:
	 * <p>
	 * ((annualPremium - lowestPremium) / annualPremium) * 100
	 *
	 *
	 * @param productsGiven
	 * @param qp
	 * @return List{Product}
	 */
	protected final List<Product> setPercentageGainsWithAgent(final List<Product> productsGiven, final QuoteParams qp) {

		List<Product> expensive = getExpensiveProducts(productsGiven, qp);

		if (DEBUG) {
			System.out.println(TAG + ".setPercentageGainsWithAgent: " + "*getExpensiveProducts(productsGiven,qp)* "
					+ "returned expensive.size {" + expensive.size() + "}");
		}

		BigDecimal lowestAnnualPremium = getLowestPremium(productsGiven, qp);

		if (DEBUG) {
			System.out.println(TAG + ".setPercentageGainsWithAgent: " + "*getLowestPremium(productsGiven,qp)* "
					+ "returned lowestAnnualPremium {" + lowestAnnualPremium + "}");
		}

		for (Product product : expensive) {

			// ((annualPremium - lowestPremium) / annualPremium) * 100
			product.setPercentageGainWithAgent(
					getPercentageGain(product.getAnnualBaseSumPremium(), lowestAnnualPremium));
		}

		return expensive;
	}

	/*
	 * protected final List<Product> getExpensiveProducts(final List<Product>
	 * productsGiven, final QuoteParams qp){
	 * 
	 * List<Product> expensiveProducts = new ArrayList<>();
	 * 
	 * boolean smoker = qp.getClient().isSmoker();
	 * 
	 * for(Product product: productsGiven){
	 * 
	 * List<HealthClass> hcs = product.getHealthClasses();
	 * 
	 * if(DEBUG) System.out.println(TAG + ".getExpensiveProducts: " +
	 * "*product.getHealthClasses()* size = "+(hcs!=null?hcs.size():"(hcs is null)")
	 * );
	 * 
	 * if(hcs!=null && hcs.size()>0){
	 * 
	 * for(HealthClass hc: hcs){ if(DEBUG) System.out.println(TAG +
	 * ".getExpensiveProducts: " + "*hc.getSmokeCode()* {"+hc.getSmokeCode() +
	 * "} *hc.getName()* {"+hc.getName()+"}"); if( smoker ){
	 * 
	 * if("S".equals(hc.getSmokeCode())){
	 * 
	 * expensiveProducts.add(product);
	 * 
	 * }else{ if("S".equals(hc.getName())){
	 * 
	 * expensiveProducts.add(product); } }
	 * 
	 * }else{
	 * 
	 * if("N".equals(hc.getSmokeCode())){
	 * 
	 * expensiveProducts.add(product);
	 * 
	 * }else{ if("N".equals(hc.getName())){
	 * 
	 * expensiveProducts.add(product); } } } } }else{ if(DEBUG)
	 * System.out.println(TAG + ".getExpensiveProducts: " +
	 * "*product.getHealthClasses()* is empty");
	 * 
	 * HealthClass hc = product.getHealthClass();
	 * 
	 * if(DEBUG) System.out.println(TAG + ".getExpensiveProducts: " +
	 * "*product.getHealthClass()*: " + "*hc.getSmokeCode()* {"+hc.getSmokeCode() +
	 * "} *hc.getName()* {"+hc.getName()+"}"); if( smoker ){
	 * 
	 * if("S".equals(hc.getSmokeCode())){
	 * 
	 * expensiveProducts.add(product);
	 * 
	 * }else{ if("S".equals(hc.getName())){
	 * 
	 * expensiveProducts.add(product); } }
	 * 
	 * }else{
	 * 
	 * if("N".equals(hc.getSmokeCode())){
	 * 
	 * expensiveProducts.add(product);
	 * 
	 * }else{ if("N".equals(hc.getName())){
	 * 
	 * expensiveProducts.add(product); } } } } }
	 * 
	 * if(expensiveProducts.isEmpty()){ if(DEBUG) System.out.println(TAG +
	 * ".getExpensiveProducts: " +
	 * "returning productsGiven because expensiveProducts is empty"); return
	 * productsGiven; }
	 * 
	 * return expensiveProducts; }
	 */
	protected final List<Product> getExpensiveProducts(final List<Product> productsGiven, final QuoteParams qp) {

		List<Product> expensiveProducts = new ArrayList<>();

		boolean smoker = qp.getClient().isSmoker();

		for (Product product : productsGiven) {

			List<HealthClass> hcs = product.getHealthClasses();

			if (DEBUG) {
				System.out.println(TAG + ".getExpensiveProducts: " + "*product.getHealthClasses()* size = "
						+ (hcs != null ? hcs.size() : "(hcs is null)"));
			}

			if (hcs != null && hcs.size() > 0) {

				for (HealthClass hc : hcs) {
					if (DEBUG) {
						System.out.println(TAG + ".getExpensiveProducts: " + "*hc.getSmokeCode()* {" + hc.getSmokeCode()
								+ "} *hc.getName()* {" + hc.getName() + "}");
					}
					if (smoker) {
						expensiveProducts.add(product);

					} else {

						expensiveProducts.add(product);

					}
				}
			} else {
				if (DEBUG) {
					System.out.println(TAG + ".getExpensiveProducts: " + "*product.getHealthClasses()* is empty");
				}

				HealthClass hc = product.getHealthClass();

				if (DEBUG) {
					System.out.println(TAG + ".getExpensiveProducts: " + "*product.getHealthClass()*: "
							+ "*hc.getSmokeCode()* {" + hc.getSmokeCode() + "} *hc.getName()* {" + hc.getName() + "}");
				}
				if (smoker) {

					expensiveProducts.add(product);

				} else {
					expensiveProducts.add(product);
				}
			}
		}

		if (expensiveProducts.isEmpty()) {
			if (DEBUG) {
				System.out.println(
						TAG + ".getExpensiveProducts: " + "returning productsGiven because expensiveProducts is empty");
			}
			return productsGiven;
		}

		return expensiveProducts;
	}

	/**
	 *
	 * @param productsGiven
	 * @param qp
	 * @return BigDecimal lowest annual premium from the given list of products
	 */
	protected final BigDecimal getLowestPremium(final List<Product> productsGiven, final QuoteParams qp) {

		double lowest = Double.MAX_VALUE;

		for (Product product : productsGiven) {

			double candidate = product.getAnnualBaseSumPremium();

			if (candidate < lowest) {
				lowest = candidate;
			}
		}

		return BigDecimal.valueOf(lowest);
	}

	/**
	 * ((annualPremium - lowestPremium) / annualPremium) * 100
	 *
	 * @param productPremium
	 * @param lowest
	 * @return ((annualPremium - lowestPremium) / annualPremium) * 100
	 */
	protected BigDecimal getPercentageGain(double productPremium, BigDecimal lowest) {

		BigDecimal premium = BigDecimal.valueOf(productPremium).setScale(5, RoundingMode.HALF_UP);

		BigDecimal diff = premium.subtract(lowest).setScale(5, RoundingMode.HALF_UP);

		return diff.divide(premium, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
	}

	/*
	 * private int getProductType(QuoteParams qp){ List<ProductType> types =
	 * qp.getProductTypes(); ProductType pt = types.get(0); return pt.getID(); }
	 * 
	 * private void setProductType(QuoteParams qp, int typeInt){ List<ProductType>
	 * types = new LinkedList<>(); ProductType type = new ProductType();
	 * type.setID(typeInt); types.add(type); qp.setProductTypes(types); }
	 */
	protected final void prepareQuoteParams(final String smokingUp, final String provCodeUp, final int faceAmountInt,
			final int productTypeInt, final String dob, final String genderUp, final String healthClass,
			final String permOrTerm, final String levelOrDecr) throws InsurfactPrivateException {

		startMulti(dob, genderUp); // sets the contact and client com.insurfact.iq.domain.Client

//        List<ProductType> types = new LinkedList<>();
//        ProductType type = new ProductType();
//        type.setID(productTypeInt);
//        types.add(type);
//        multiQP.setProductTypes(types);
		// setProductType(multiQP,productTypeInt);
		multiQP.setProductTypeID(productTypeInt);// 6); //TODO ????????????????????????????????

		if (!healthClass.equals("")) {
			multiQP.setHealthClass(healthClass);
		} else {
			multiQP.setHealthClass("S");
		}

		if (!permOrTerm.equals("")) {
			if (!permOrTerm.equalsIgnoreCase("A")) {
				multiQP.setTermOrPerm(permOrTerm.toUpperCase());
			}
		}

		if (!levelOrDecr.equals("")) {
			if (!levelOrDecr.equalsIgnoreCase("A")) {
				if (levelOrDecr.equalsIgnoreCase("L")) {
					multiQP.setInsuranceBen(2);//// 2 or L=level 6 or D=decreasing
				} else {
					multiQP.setInsuranceBen(6);
				}
			}
		}

		Object firstContactObject = multiQP.getFirstContact();

		// com.insurfact.skynet.entity.Contact
		Contact firstContact = null; // TODO confirm ########## needed???

		if (firstContactObject != null && firstContactObject instanceof Contact) {
			firstContact = (Contact) firstContactObject;
			if (DEBUG) {
				System.out.println(TAG + ".prepareQuoteParams: *multiQP.getFirstContact()* returned firstContact = {"
						+ firstContact + "}");
			}
		} else {
			if (DEBUG) {
				System.out.println(
						TAG + ".prepareQuoteParams: *multiQP.getFirstContact()* returned firstContactObject = {"
								+ firstContactObject + "}");
			}

			// TODO washere maybe create Contact in multiQP ???
			throw new InsurfactPrivateException("firstContactObject is null");
		}

		// here we have a Contact in multiQP
		if (DEBUG) {
			System.out.println(TAG + ".prepareQuoteParams: " + "getContactInfo(firstContact) #1 = "
					+ Util.getContactInfo(firstContact));
		}

		// dob set in startMulti()
		// gender set in startMulti()
		// smoking client.isNonSmoker()
		multiQP.getClient().setNonSmoker(!"Y".equals(smokingUp));

		// com.insurfact.iq.domain.Client
		multiQP.getClient().setProvinceID(Util.getProvinceCodeInt(provCodeUp));

		multiQP.getClient().setClassId(0);// TODO washere ??????????????????

		multiQP.setIssueProvinceID(Util.getProvinceCodeInt(provCodeUp));

		// if(DEBUG) System.out.println(TAG+".prepareQuoteParams:
		// *faceAmountBig.intValue()* returned {"+faceAmountBig.intValue()+"}");
		multiQP.setOriginalFaceAmount(faceAmountInt);

		multiQP.setFaceAmt(faceAmountInt);

		multiQP.setInsuranceTypeAbbr(PRODUCT_CLASS); // or CRIT for another web service

		// from QuoteWizard.goPage2():
		multiQP.setUsingMinReqPremiumFaceAmt(false);

		multiQP.setSelectedRider(null);

		if (DEBUG) {
			System.out.println(TAG + ".prepareQuoteParams: " + "getContactInfo(firstContact) #2 = "
					+ Util.getContactInfo(firstContact));
		}

		multiQP.setNameWS("DUNDAS");
		multiQP.setWebService(true);

		multiQP.setDebug(DEBUG);

		if (DEBUG) {
			// url test = /10/500000/19890101/M/N/QC
			// {product_type_id}/{face_amount}/{date_of_birth}/{gender}/{smoking}/{province_code}
			// get relevant QueryParams data
			System.out.println(TAG + ".prepareQuoteParams: exiting with " + "\n multiQP.isWebService() {"
					+ multiQP.isWebService() + "}" + "\n multiQP.isDebug() {" + multiQP.isDebug() + "}"
					+ "\n multiQP.getProductID() {" + multiQP.getProductID() + "}" + "\n multiQP.getProductTypes() {"
					+ (multiQP.getProductTypes() == null ? "null" : "get(0)=" + multiQP.getProductTypes().get(0))
					+ "} <== from URL; test#1, 2, 3: 10, 6, 8" + "\n multiQP.getProductTypeID() {"
					+ multiQP.getProductTypeID() + "}" + "\n multiQP.getFaceAmt() {" + multiQP.getFaceAmt()
					+ "} <== from URL; test#1: 500000" + "\n multiQP.getStartAge() {" + multiQP.getStartAge() + "}" + // "\n
																														// multiQP.getAgeMode()
																														// {"+multiQP.getAgeMode()+"}"+
																														// "\n
																														// multiQP.getAgeModeS()
																														// {"+multiQP.getAgeModeS()+"}"+
																														// "\n
																														// multiQP.getCompanyType()
																														// {"+multiQP.getCompanyType()+"}"+
					"\n multiQP.getCompanyID() {" + multiQP.getCompanyID() + "}" + // "\n multiQP.getFaceAmountMode()
																					// {"+multiQP.getFaceAmountMode()+"}"+
																					// "\n multiQP.getFaceAmountModeS()
																					// {"+multiQP.getFaceAmountModeS()+"}"+
																					// "\n multiQP.getFeature()
																					// {"+multiQP.getFeature()+"}"+
																					// "\n multiQP.getHealthClass()
																					// {"+multiQP.getHealthClass()+"}"+
					"\n multiQP.getInsuranceTypeAbbr() {" + multiQP.getInsuranceTypeAbbr() + "}" + // "\n
																									// multiQP.getInsuranceTypeFull()
																									// {"+multiQP.getInsuranceTypeFull()+"}"+
																									// "\n
																									// multiQP.getPlanTypeName()
																									// {"+multiQP.getPlanTypeName()+"}"+
					"\n multiQP.isBusinessQuote() {" + multiQP.isBusinessQuote() + "}" + // "\n multiQP.getProdType()
																							// {"+multiQP.getProdType()+"}"+
																							// "\n
																							// multiQP.getProductsAgeToUse()
																							// {"+multiQP.getProductsAgeToUse()+"}"+
					"\n multiQP.getUwType() {" + multiQP.getUwType() + "}" + "\n multiQP.getTermOrPerm() {"
					+ multiQP.getTermOrPerm() + "}" + "\n multiQP.getFeature() {" + multiQP.getFeature() + "}"
					+ "\n multiQP.isGuaranteedPremium() {" + multiQP.isGuaranteedPremium() + "}"
					+ "\n multiQP.getRenewFrequency() {" + multiQP.getRenewFrequency() + "}"
					// "\n multiQP.getAllProductTypes().size()
					// {"+(multiQP.getAllProductTypes()==null?"(null)":""+multiQP.getAllProductTypes().size())+"}"
					// +"\n multiQP.getAvailableProduct().size()
					// {"+(multiQP.getAvailableProduct()==null?"(null)":""+multiQP.getAvailableProduct().size())+"}"
					+ "\n multiQP.getClient() {" + Util.getClientData(multiQP.getClient()) + "}"
			// +"\n multiQP.getCompanies().size()
			// {"+(multiQP.getCompanies()==null?"(null)":""+multiQP.getCompanies().size())+"}"
			);
		}

	}

	protected final void prepareSingleQuoteParams(final String smokingUp, final String provCodeUp,
			final int faceAmountInt, final int productTypeInt, final String dob, final String genderUp,
			final int carrierID, final String productTypeSel) throws InsurfactPrivateException {

		startMulti(dob, genderUp); // sets the contact and client com.insurfact.iq.domain.Client

//        List<ProductType> types = new LinkedList<>();
//        ProductType type = new ProductType();
//        type.setID(productTypeInt);
//        types.add(type);
//        multiQP.setProductTypes(types);
		// setProductType(multiQP,productTypeInt);
		if (!productTypeSel.equalsIgnoreCase("A")) {
			multiQP.setProductTypeID(productTypeInt);
		}

		if (productTypeSel.equalsIgnoreCase("A")) {
			multiQP.setProductTypeID(0);
		}

		// multiQP.setProductID(productTypeInt);
		Object firstContactObject = multiQP.getFirstContact();

		// com.insurfact.skynet.entity.Contact
		Contact firstContact = null; // TODO confirm ########## needed???

		if (firstContactObject != null && firstContactObject instanceof Contact) {
			firstContact = (Contact) firstContactObject;
			if (DEBUG) {
				System.out.println(TAG + ".prepareQuoteParams: *multiQP.getFirstContact()* returned firstContact = {"
						+ firstContact + "}");
			}
		} else {
			if (DEBUG) {
				System.out.println(
						TAG + ".prepareQuoteParams: *multiQP.getFirstContact()* returned firstContactObject = {"
								+ firstContactObject + "}");
			}

			// TODO washere maybe create Contact in multiQP ???
			throw new InsurfactPrivateException("firstContactObject is null");
		}

		// here we have a Contact in multiQP
		if (DEBUG) {
			System.out.println(TAG + ".prepareQuoteParams: " + "getContactInfo(firstContact) #1 = "
					+ Util.getContactInfo(firstContact));
		}

		// dob set in startMulti()
		// gender set in startMulti()
		// smoking client.isNonSmoker()
		multiQP.getClient().setNonSmoker(!"Y".equals(smokingUp));

		// com.insurfact.iq.domain.Client
		multiQP.getClient().setProvinceID(Util.getProvinceCodeInt(provCodeUp));

		multiQP.getClient().setClassId(0);

		multiQP.setIssueProvinceID(Util.getProvinceCodeInt(provCodeUp));

		// if(DEBUG) System.out.println(TAG+".prepareQuoteParams:
		// *faceAmountBig.intValue()* returned {"+faceAmountBig.intValue()+"}");
		multiQP.setOriginalFaceAmount(faceAmountInt);

		multiQP.setFaceAmt(faceAmountInt);

		multiQP.setCompanyID(carrierID); ///// NEW!!!!! setting one company march 27, 2020 aa

		multiQP.setInsuranceTypeAbbr(PRODUCT_CLASS); // or CRIT for another web service

		// from QuoteWizard.goPage2():
		multiQP.setUsingMinReqPremiumFaceAmt(false);

		multiQP.setSelectedRider(null);

		if (DEBUG) {
			System.out.println(TAG + ".prepareQuoteParams: " + "getContactInfo(firstContact) #2 = "
					+ Util.getContactInfo(firstContact));
		}

		multiQP.setWebService(true);

		multiQP.setDebug(DEBUG);

		setMultiQP(multiQP);

		if (DEBUG) {
			// url test = /10/500000/19890101/M/N/QC
			// {product_type_id}/{face_amount}/{date_of_birth}/{gender}/{smoking}/{province_code}
			// get relevant QueryParams data
			System.out.println(TAG + ".prepareQuoteParams: exiting with " + "\n multiQP.isWebService() {"
					+ multiQP.isWebService() + "}" + "\n multiQP.isDebug() {" + multiQP.isDebug() + "}"
					+ "\n multiQP.getProductID() {" + multiQP.getProductID() + "}" + "\n multiQP.getProductTypes() {"
					+ (multiQP.getProductTypes() == null || multiQP.getProductTypes().isEmpty() ? "null/empty" : "get(0)=" + multiQP.getProductTypes().getFirst())
					+ "} <== from URL; test#1, 2, 3: 10, 6, 8" + "\n multiQP.getProductTypeID() {"
					+ multiQP.getProductTypeID() + "}" + "\n multiQP.getFaceAmt() {" + multiQP.getFaceAmt()
					+ "} <== from URL; test#1: 500000" + "\n multiQP.setCompanyID() {" + multiQP.getCompanyID()
					+ "} <== from URL; test#1: 485" + "\n multiQP.getStartAge() {" + multiQP.getStartAge() + "}" + // "\n
																													// multiQP.getAgeMode()
																													// {"+multiQP.getAgeMode()+"}"+
																													// "\n
																													// multiQP.getAgeModeS()
																													// {"+multiQP.getAgeModeS()+"}"+
																													// "\n
																													// multiQP.getCompanyType()
																													// {"+multiQP.getCompanyType()+"}"+
					"\n multiQP.getCompanyID() {" + multiQP.getCompanyID() + "}" + // "\n multiQP.getFaceAmountMode()
																					// {"+multiQP.getFaceAmountMode()+"}"+
																					// "\n multiQP.getFaceAmountModeS()
																					// {"+multiQP.getFaceAmountModeS()+"}"+
																					// "\n multiQP.getFeature()
																					// {"+multiQP.getFeature()+"}"+
																					// "\n multiQP.getHealthClass()
																					// {"+multiQP.getHealthClass()+"}"+
					"\n multiQP.getInsuranceTypeAbbr() {" + multiQP.getInsuranceTypeAbbr() + "}" + // "\n
																									// multiQP.getInsuranceTypeFull()
																									// {"+multiQP.getInsuranceTypeFull()+"}"+
																									// "\n
																									// multiQP.getPlanTypeName()
																									// {"+multiQP.getPlanTypeName()+"}"+
					"\n multiQP.isBusinessQuote() {" + multiQP.isBusinessQuote() + "}" + // "\n multiQP.getProdType()
																							// {"+multiQP.getProdType()+"}"+
																							// "\n
																							// multiQP.getProductsAgeToUse()
																							// {"+multiQP.getProductsAgeToUse()+"}"+
					"\n multiQP.getUwType() {" + multiQP.getUwType() + "}" + "\n multiQP.getTermOrPerm() {"
					+ multiQP.getTermOrPerm() + "}" + "\n multiQP.getFeature() {" + multiQP.getFeature() + "}"
					+ "\n multiQP.isGuaranteedPremium() {" + multiQP.isGuaranteedPremium() + "}"
					+ "\n multiQP.getRenewFrequency() {" + multiQP.getRenewFrequency() + "}"
					// "\n multiQP.getAllProductTypes().size()
					// {"+(multiQP.getAllProductTypes()==null?"(null)":""+multiQP.getAllProductTypes().size())+"}"
					// +"\n multiQP.getAvailableProduct().size()
					// {"+(multiQP.getAvailableProduct()==null?"(null)":""+multiQP.getAvailableProduct().size())+"}"
					+ "\n multiQP.getClient() {" + Util.getClientData(multiQP.getClient()) + "}"
			// +"\n multiQP.getCompanies().size()
			// {"+(multiQP.getCompanies()==null?"(null)":""+multiQP.getCompanies().size())+"}"
			);
		}

	}

	protected final void prepareSingleProdQuoteParams(final String smokingUp, final String provCodeUp,
			final int faceAmountInt, final String dob, final String genderUp, final int carrierID, final int prodID)
			throws InsurfactPrivateException {

		startMulti(dob, genderUp); // sets the contact and client com.insurfact.iq.domain.Client

		// setProductType(multiQP,productTypeInt);
		multiQP.setProductTypeID(0);

		Object firstContactObject = multiQP.getFirstContact();

		// com.insurfact.skynet.entity.Contact
		Contact firstContact = null; // TODO confirm ########## needed???

		if (firstContactObject != null && firstContactObject instanceof Contact) {
			firstContact = (Contact) firstContactObject;
			if (DEBUG) {
				System.out.println(TAG + ".prepareQuoteParams: *multiQP.getFirstContact()* returned firstContact = {"
						+ firstContact + "}");
			}
		} else {
			if (DEBUG) {
				System.out.println(
						TAG + ".prepareQuoteParams: *multiQP.getFirstContact()* returned firstContactObject = {"
								+ firstContactObject + "}");
			}

			// TODO washere maybe create Contact in multiQP ???
			throw new InsurfactPrivateException("firstContactObject is null");
		}

		// here we have a Contact in multiQP
		if (DEBUG) {
			System.out.println(TAG + ".prepareQuoteParams: " + "getContactInfo(firstContact) #1 = "
					+ Util.getContactInfo(firstContact));
		}

		// dob set in startMulti()
		// gender set in startMulti()
		// smoking client.isNonSmoker()
		multiQP.getClient().setNonSmoker(!"Y".equals(smokingUp));

		// com.insurfact.iq.domain.Client
		multiQP.getClient().setProvinceID(Util.getProvinceCodeInt(provCodeUp));

		multiQP.getClient().setClassId(0);

		multiQP.setIssueProvinceID(Util.getProvinceCodeInt(provCodeUp));

		// if(DEBUG) System.out.println(TAG+".prepareQuoteParams:
		// *faceAmountBig.intValue()* returned {"+faceAmountBig.intValue()+"}");
		multiQP.setOriginalFaceAmount(faceAmountInt);

		multiQP.setFaceAmt(faceAmountInt);

		multiQP.setCompanyID(carrierID); ///// NEW!!!!! setting one company march 27, 2020 aa

		multiQP.setProductID(prodID);

		if (prodID == 0) {
			multiQP.setInsuranceTypeAbbr(PRODUCT_CLASS); // or CRIT for another web service
		}
		// from QuoteWizard.goPage2():

		multiQP.setUsingMinReqPremiumFaceAmt(false);

		multiQP.setSelectedRider(null);

		if (DEBUG) {
			System.out.println(TAG + ".prepareQuoteParams: " + "getContactInfo(firstContact) #2 = "
					+ Util.getContactInfo(firstContact));
		}

		multiQP.setWebService(true);

		multiQP.setDebug(DEBUG);

		setMultiQP(multiQP);

		if (DEBUG) {
			// url test = /10/500000/19890101/M/N/QC
			// {product_type_id}/{face_amount}/{date_of_birth}/{gender}/{smoking}/{province_code}
			// get relevant QueryParams data
			System.out.println(TAG + ".prepareQuoteParams: exiting with " + "\n multiQP.isWebService() {"
					+ multiQP.isWebService() + "}" + "\n multiQP.isDebug() {" + multiQP.isDebug() + "}"
					+ "\n multiQP.getProductID() {" + multiQP.getProductID() + "}" + "\n multiQP.getProductTypes() {"
					+ (multiQP.getProductTypes() == null || multiQP.getProductTypes().isEmpty() ? "null/empty" : "get(0)=" + multiQP.getProductTypes().getFirst())
					+ "} <== from URL; test#1, 2, 3: 10, 6, 8" + "\n multiQP.getProductTypeID() {"
					+ multiQP.getProductTypeID() + "}" + "\n multiQP.getFaceAmt() {" + multiQP.getFaceAmt()
					+ "} <== from URL; test#1: 500000" + "\n multiQP.setCompanyID() {" + multiQP.getCompanyID()
					+ "} <== from URL; test#1: 485" + "\n multiQP.getStartAge() {" + multiQP.getStartAge() + "}" + // "\n
																													// multiQP.getAgeMode()
																													// {"+multiQP.getAgeMode()+"}"+
																													// "\n
																													// multiQP.getAgeModeS()
																													// {"+multiQP.getAgeModeS()+"}"+
																													// "\n
																													// multiQP.getCompanyType()
																													// {"+multiQP.getCompanyType()+"}"+
					"\n multiQP.getCompanyID() {" + multiQP.getCompanyID() + "}" + // "\n multiQP.getFaceAmountMode()
																					// {"+multiQP.getFaceAmountMode()+"}"+
																					// "\n multiQP.getFaceAmountModeS()
																					// {"+multiQP.getFaceAmountModeS()+"}"+
																					// "\n multiQP.getFeature()
																					// {"+multiQP.getFeature()+"}"+
																					// "\n multiQP.getHealthClass()
																					// {"+multiQP.getHealthClass()+"}"+
					"\n multiQP.getInsuranceTypeAbbr() {" + multiQP.getInsuranceTypeAbbr() + "}" + // "\n
																									// multiQP.getInsuranceTypeFull()
																									// {"+multiQP.getInsuranceTypeFull()+"}"+
																									// "\n
																									// multiQP.getPlanTypeName()
																									// {"+multiQP.getPlanTypeName()+"}"+
					"\n multiQP.isBusinessQuote() {" + multiQP.isBusinessQuote() + "}" + // "\n multiQP.getProdType()
																							// {"+multiQP.getProdType()+"}"+
																							// "\n
																							// multiQP.getProductsAgeToUse()
																							// {"+multiQP.getProductsAgeToUse()+"}"+
					"\n multiQP.getUwType() {" + multiQP.getUwType() + "}" + "\n multiQP.getTermOrPerm() {"
					+ multiQP.getTermOrPerm() + "}" + "\n multiQP.getFeature() {" + multiQP.getFeature() + "}"
					+ "\n multiQP.isGuaranteedPremium() {" + multiQP.isGuaranteedPremium() + "}"
					+ "\n multiQP.getRenewFrequency() {" + multiQP.getRenewFrequency() + "}"
					// "\n multiQP.getAllProductTypes().size()
					// {"+(multiQP.getAllProductTypes()==null?"(null)":""+multiQP.getAllProductTypes().size())+"}"
					// +"\n multiQP.getAvailableProduct().size()
					// {"+(multiQP.getAvailableProduct()==null?"(null)":""+multiQP.getAvailableProduct().size())+"}"
					+ "\n multiQP.getClient() {" + Util.getClientData(multiQP.getClient()) + "}"
			// +"\n multiQP.getCompanies().size()
			// {"+(multiQP.getCompanies()==null?"(null)":""+multiQP.getCompanies().size())+"}"
			);
		}

	}

	/**
	 * Call example: Generic generic = new Generic(PRODUCT_CLASS,"ALL");
	 *
	 * @param productTypeId
	 * @param faceAmount
	 * @param dateOfBirth
	 * @param gender
	 * @param smoking
	 * @param provCode
	 * @return
	 * @throws InsurfactPrivateException
	 */
	String getNoExceptionSingle(final String productTypeId, final String faceAmount, final String dateOfBirth,
			final String gender, final String smoking, final String provCode, final String carrierID,
			final String productTypeSel) {

		// get debug flag from properties
		DEBUG = true;

		String responseValue = "";

		try {

			responseValue = getSingleResponseString(productTypeId, faceAmount, dateOfBirth, gender, smoking, provCode,
					carrierID, productTypeSel);

		} catch (IllegalArgumentException ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex);
			}
			String s = "An anomaly was detected; please review the parameters in your http request"
					+ "; the values that we received:" + " product_type_id {" + productTypeId + "}" + " face_amount {"
					+ faceAmount + "}" + " date_of_birth {" + dateOfBirth + "}" + " gender {" + gender + "}"
					+ " smoking {" + smoking + "}" + " province_code {" + provCode + "}";

			responseValue = "\"" + JsonUtil.cleanStringForJson(s) + "\"";

		} catch (InsurfactPrivateException ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex + "\n" + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10));
			}
			String body = "";
			if (DEBUG) {
				System.out.println(TAG + ".getJson: alert email; Subject: " + "; email body: " + body);
			}
			String s = "An anomaly was detected; please contact support and please review the parameters in your http request"
					+ "; the values that we received are:" + " product_type_id {" + productTypeId + "}"
					+ " face_amount {" + faceAmount + "}" + " date_of_birth {" + dateOfBirth + "}" + " gender {"
					+ gender + "}" + " smoking {" + smoking + "}" + " province_code {" + provCode + "}" + "\n" // ==========
			// + body // remove in prod ##################################
			// ==========
			;

			responseValue = "\"" + JsonUtil.cleanStringForJson(s) + "\"";
		} catch (Throwable ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex + "\n" + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10));
			}
			String body = "";
			if (SEND_EMAIL_FOR_ANOMALIES) {
				String subject = TAG + " Alert - Probable Bug";
				body = "\ngetResponseString raised: " + ex.getMessage() + LogUtil.getStackTrace(ex, 10)
						+ "\n\nHttp request parameters:" + " product_type_id {" + productTypeId + "}" + " face_amount {"
						+ faceAmount + "}" + " date_of_birth {" + dateOfBirth + "}" + " gender {" + gender + "}"
						+ " smoking {" + smoking + "}" + " province_code {" + provCode + "}";
				if (DEBUG) {
					System.out.println(TAG + ".getJson: alert email; Subject: " + subject + "; email body: " + body);
				}

				MailMngr.sendAlert(subject, body);
			}
			String s = "An anomaly was detected; "
					+ "please contact support and please review the parameters in your http request"
					+ "; the values that we received are:" + " product_type_id {" + productTypeId + "}"
					+ " face_amount {" + faceAmount + "}" + " date_of_birth {" + dateOfBirth + "}" + " gender {"
					+ gender + "}" + " smoking {" + smoking + "}" + " province_code {" + provCode + "}" + "\n" // ========
			// + body // remove in prod ##################################
			// ========
			;

			responseValue = "\"" + JsonUtil.cleanStringForJson(s) + "\"";
		}

//        String responseAll = "{\"response\": " + responseValue + " }";
//
//        if (DEBUG) {
//            System.out.println(TAG + ".getJson: returning $$" + responseAll + "$$");
//        }
		return responseValue;
	}

	String getNoExceptionMulti(final String productTypeId, final String faceAmount, final String dateOfBirth,
			final String gender, final String smoking, final String provCode, final String healthClass,
			final String permOrTerm, final String levelOrDecr, final String uw, final String limitedFull) { // new jan
																											// 2022, aa
																											// - uwtype
																											// in the
																											// table
																											// im_lifeproductdetail
																											// 1= Full
																											// underwriting,
																											// 2 =
																											// Guaranteed
																											// issue , 3
																											// =
																											// Simplified
																											// underwriting
																											// -

		// get debug flag from properties
		DEBUG = false;

		String responseValue = "";

		try {

			responseValue = getMultiResponseString(productTypeId, faceAmount, dateOfBirth, gender, smoking, provCode,
					healthClass, permOrTerm, levelOrDecr, uw, limitedFull);

		} catch (IllegalArgumentException ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex);
			}
			if (SEND_EMAIL_FOR_ANOMALIES) {
				MailMngr.sendAlert(TAG + " Alert - Probable invalid parameter(s) from client",
						"getResponseString raised " + ex.getMessage() + LogUtil.getStackTrace(ex, 10)
								+ "\n\nHttp request parameters:" + " product_type_id {" + productTypeId + "}"
								+ " face_amount {" + faceAmount + "}" + " date_of_birth {" + dateOfBirth + "}"
								+ " gender {" + gender + "}" + " smoking {" + smoking + "}" + " province_code {"
								+ provCode + "}");
			}
			String s = "An anomaly was detected; please review the parameters in your http request"
					+ "; the values that we received:" + " product_type_id {" + productTypeId + "}" + " face_amount {"
					+ faceAmount + "}" + " date_of_birth {" + dateOfBirth + "}" + " gender {" + gender + "}"
					+ " smoking {" + smoking + "}" + " province_code {" + provCode + "}";

			responseValue = "\"" + JsonUtil.cleanStringForJson(s) + "\"";

		} catch (InsurfactPrivateException ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex + "\n" + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10));
			}
			String body = "";
			if (SEND_EMAIL_FOR_ANOMALIES) {
				String subject = TAG + " Alert - Probable Bug";
				body = "\ngetResponseString raised InsurfactPrivateException: " + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10) + "\n\nHttp request parameters:" + " product_type_id {"
						+ productTypeId + "}" + " face_amount {" + faceAmount + "}" + " date_of_birth {" + dateOfBirth
						+ "}" + " gender {" + gender + "}" + " smoking {" + smoking + "}" + " province_code {"
						+ provCode + "}";
				if (DEBUG) {
					System.out.println(TAG + ".getJson: alert email; Subject: " + subject + "; email body: " + body);
				}
				MailMngr.sendAlert(subject, body);
			}
			String s = "An anomaly was detected; please contact support and please review the parameters in your http request"
					+ "; the values that we received are:" + " product_type_id {" + productTypeId + "}"
					+ " face_amount {" + faceAmount + "}" + " date_of_birth {" + dateOfBirth + "}" + " gender {"
					+ gender + "}" + " smoking {" + smoking + "}" + " province_code {" + provCode + "}" + "\n" // ==========
			// + body // remove in prod ##################################
			// ==========
			;

			responseValue = "\"" + JsonUtil.cleanStringForJson(s) + "\"";
		} catch (Throwable ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex + "\n" + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10));
			}
			String body = "";
			if (SEND_EMAIL_FOR_ANOMALIES) {
				String subject = TAG + " Alert - Probable Bug";
				body = "\ngetResponseString raised: " + ex.getMessage() + LogUtil.getStackTrace(ex, 10)
						+ "\n\nHttp request parameters:" + " product_type_id {" + productTypeId + "}" + " face_amount {"
						+ faceAmount + "}" + " date_of_birth {" + dateOfBirth + "}" + " gender {" + gender + "}"
						+ " smoking {" + smoking + "}" + " province_code {" + provCode + "}";
				if (DEBUG) {
					System.out.println(TAG + ".getJson: alert email; Subject: " + subject + "; email body: " + body);
				}
				MailMngr.sendAlert(subject, body);
			}
			String s = "An anomaly was detected; "
					+ "please contact support and please review the parameters in your http request"
					+ "; the values that we received are:" + " product_type_id {" + productTypeId + "}"
					+ " face_amount {" + faceAmount + "}" + " date_of_birth {" + dateOfBirth + "}" + " gender {"
					+ gender + "}" + " smoking {" + smoking + "}" + " province_code {" + provCode + "}" + "\n" // ========
			// + body // remove in prod ##################################
			// ========
			;

			responseValue = "\"" + JsonUtil.cleanStringForJson(s) + "\"";
		}

//        String responseAll = "{\"response\": " + responseValue + " }";
//
//        if (DEBUG) {
//            System.out.println(TAG + ".getJson: returning $$" + responseAll + "$$");
//        }
		return responseValue;
	}

	String getNoExceptionMultiGroup(final String productTypeId, final String faceAmount, final String dateOfBirth,
			final String gender, final String smoking, final String provCode, final String healthClass,
			final String permOrTerm, final String levelOrDecr, final String uw, final String limitedFull) { // new jan
																											// 2022, aa
																											// - uwtype
																											// in the
																											// table
																											// im_lifeproductdetail
																											// 1= Full
																											// underwriting,
																											// 2 =
																											// Guaranteed
																											// issue , 3
																											// =
																											// Simplified
																											// underwriting
																											// -

		// get debug flag from properties
		DEBUG = false;

		String responseValue = "";

		try {

			responseValue = getMultiGroupResponseString(productTypeId, faceAmount, dateOfBirth, gender, smoking,
					provCode, healthClass, permOrTerm, levelOrDecr, uw, limitedFull);

		} catch (IllegalArgumentException ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex);
			}
			if (SEND_EMAIL_FOR_ANOMALIES) {
				MailMngr.sendAlert(TAG + " Alert - Probable invalid parameter(s) from client",
						"getResponseString raised " + ex.getMessage() + LogUtil.getStackTrace(ex, 10)
								+ "\n\nHttp request parameters:" + " product_type_id {" + productTypeId + "}"
								+ " face_amount {" + faceAmount + "}" + " date_of_birth {" + dateOfBirth + "}"
								+ " gender {" + gender + "}" + " smoking {" + smoking + "}" + " province_code {"
								+ provCode + "}");
			}
			String s = "An anomaly was detected; please review the parameters in your http request"
					+ "; the values that we received:" + " product_type_id {" + productTypeId + "}" + " face_amount {"
					+ faceAmount + "}" + " date_of_birth {" + dateOfBirth + "}" + " gender {" + gender + "}"
					+ " smoking {" + smoking + "}" + " province_code {" + provCode + "}";

			responseValue = "\"" + JsonUtil.cleanStringForJson(s) + "\"";

		} catch (InsurfactPrivateException ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex + "\n" + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10));
			}
			String body = "";
			if (SEND_EMAIL_FOR_ANOMALIES) {
				String subject = TAG + " Alert - Probable Bug";
				body = "\ngetResponseString raised InsurfactPrivateException: " + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10) + "\n\nHttp request parameters:" + " product_type_id {"
						+ productTypeId + "}" + " face_amount {" + faceAmount + "}" + " date_of_birth {" + dateOfBirth
						+ "}" + " gender {" + gender + "}" + " smoking {" + smoking + "}" + " province_code {"
						+ provCode + "}";
				if (DEBUG) {
					System.out.println(TAG + ".getJson: alert email; Subject: " + subject + "; email body: " + body);
				}
				MailMngr.sendAlert(subject, body);
			}
			String s = "An anomaly was detected; please contact support and please review the parameters in your http request"
					+ "; the values that we received are:" + " product_type_id {" + productTypeId + "}"
					+ " face_amount {" + faceAmount + "}" + " date_of_birth {" + dateOfBirth + "}" + " gender {"
					+ gender + "}" + " smoking {" + smoking + "}" + " province_code {" + provCode + "}" + "\n" // ==========
			// + body // remove in prod ##################################
			// ==========
			;

			responseValue = "\"" + JsonUtil.cleanStringForJson(s) + "\"";
		} catch (Throwable ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex + "\n" + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10));
			}
			String body = "";
			if (SEND_EMAIL_FOR_ANOMALIES) {
				String subject = TAG + " Alert - Probable Bug";
				body = "\ngetResponseString raised: " + ex.getMessage() + LogUtil.getStackTrace(ex, 10)
						+ "\n\nHttp request parameters:" + " product_type_id {" + productTypeId + "}" + " face_amount {"
						+ faceAmount + "}" + " date_of_birth {" + dateOfBirth + "}" + " gender {" + gender + "}"
						+ " smoking {" + smoking + "}" + " province_code {" + provCode + "}";
				if (DEBUG) {
					System.out.println(TAG + ".getJson: alert email; Subject: " + subject + "; email body: " + body);
				}
				MailMngr.sendAlert(subject, body);
			}
			String s = "An anomaly was detected; "
					+ "please contact support and please review the parameters in your http request"
					+ "; the values that we received are:" + " product_type_id {" + productTypeId + "}"
					+ " face_amount {" + faceAmount + "}" + " date_of_birth {" + dateOfBirth + "}" + " gender {"
					+ gender + "}" + " smoking {" + smoking + "}" + " province_code {" + provCode + "}" + "\n" // ========
			// + body // remove in prod ##################################
			// ========
			;

			responseValue = "\"" + JsonUtil.cleanStringForJson(s) + "\"";
		}

//        String responseAll = "{\"response\": " + responseValue + " }";
//
//        if (DEBUG) {
//            System.out.println(TAG + ".getJson: returning $$" + responseAll + "$$");
//        }
		return responseValue;
	}

	String getNoExceptionSingleProd(final String faceAmount, final String dateOfBirth, final String gender,
			final String smoking, final String provCode, final String carrierID, final String prodID) {

		// get debug flag from properties
		DEBUG = InsurfactSdkProperties.getBoolean("debug.enabled", false);

		String responseValue = "";
		try {
			responseValue = getSingleProdResponseString(faceAmount, dateOfBirth, gender, smoking, provCode, carrierID,
					prodID);
		} catch (IllegalArgumentException ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex);
			}
			if (SEND_EMAIL_FOR_ANOMALIES) {
				MailMngr.sendAlert(TAG + " Alert - Probable invalid parameter(s) from client",
						"getResponseString raised " + ex.getMessage() + LogUtil.getStackTrace(ex, 10)
								+ "\n\nHttp request parameters:" + " face_amount {" + faceAmount + "}"
								+ " date_of_birth {" + dateOfBirth + "}" + " gender {" + gender + "}" + " smoking {"
								+ smoking + "}" + " province_code {" + provCode + "}");
			}
			String s = "An anomaly was detected; please review the parameters in your http request"
					+ "; the values that we received:" + " face_amount {" + faceAmount + "}" + " date_of_birth {"
					+ dateOfBirth + "}" + " gender {" + gender + "}" + " smoking {" + smoking + "}" + " province_code {"
					+ provCode + "}";

			responseValue = "\"" + JsonUtil.cleanStringForJson(s) + "\"";

		} catch (InsurfactPrivateException ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex + "\n" + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10));
			}
			String body = "";
			if (SEND_EMAIL_FOR_ANOMALIES) {
				String subject = TAG + " Alert - Probable Bug";
				body = "\ngetResponseString raised InsurfactPrivateException: " + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10) + "\n\nHttp request parameters:" + " face_amount {" + faceAmount
						+ "}" + " date_of_birth {" + dateOfBirth + "}" + " gender {" + gender + "}" + " smoking {"
						+ smoking + "}" + " province_code {" + provCode + "}";
				if (DEBUG) {
					System.out.println(TAG + ".getJson: alert email; Subject: " + subject + "; email body: " + body);
				}
				MailMngr.sendAlert(subject, body);
			}
			String s = "An anomaly was detected; please contact support and please review the parameters in your http request"
					+ "; the values that we received are:" + " face_amount {" + faceAmount + "}" + " date_of_birth {"
					+ dateOfBirth + "}" + " gender {" + gender + "}" + " smoking {" + smoking + "}" + " province_code {"
					+ provCode + "}" + "\n" // ==========
			// + body // remove in prod ##################################
			// ==========
			;

			responseValue = "\"" + JsonUtil.cleanStringForJson(s) + "\"";
		} catch (Throwable ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex + "\n" + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10));
			}
			String body = "";
			if (SEND_EMAIL_FOR_ANOMALIES) {
				String subject = TAG + " Alert - Probable Bug";
				body = "\ngetResponseString raised: " + ex.getMessage() + LogUtil.getStackTrace(ex, 10)
						+ "\n\nHttp request parameters:" + " face_amount {" + faceAmount + "}" + " date_of_birth {"
						+ dateOfBirth + "}" + " gender {" + gender + "}" + " smoking {" + smoking + "}"
						+ " province_code {" + provCode + "}";
				if (DEBUG) {
					System.out.println(TAG + ".getJson: alert email; Subject: " + subject + "; email body: " + body);
				}
				MailMngr.sendAlert(subject, body);
			}
			String s = "An anomaly was detected; "
					+ "please contact support and please review the parameters in your http request"
					+ "; the values that we received are:" + " face_amount {" + faceAmount + "}" + " date_of_birth {"
					+ dateOfBirth + "}" + " gender {" + gender + "}" + " smoking {" + smoking + "}" + " province_code {"
					+ provCode + "}" + "\n" // ========
			// + body // remove in prod ##################################
			// ========
			;

			responseValue = "\"" + JsonUtil.cleanStringForJson(s) + "\"";
		}

//        String responseAll = "{\"response\": " + responseValue + " }";
//
//        if (DEBUG) {
//            System.out.println(TAG + ".getJson: returning $$" + responseAll + "$$");
//        }
		return responseValue;
	}

	String getNoExceptionSingleProdCreditor(final String faceAmount, final String dateOfBirth, final String gender,
			final String smoking, final String provCode, final String carrierID, final String prodID) {

		// get debug flag from properties
		DEBUG = InsurfactSdkProperties.getBoolean("debug.enabled", false);

		String responseValue = "";
		try {
			responseValue = getSingleProdCreditorResponse(faceAmount, dateOfBirth, gender, smoking, provCode, carrierID,
					prodID);
		} catch (IllegalArgumentException ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex);
			}
			if (SEND_EMAIL_FOR_ANOMALIES) {
				MailMngr.sendAlert(TAG + " Alert - Probable invalid parameter(s) from client",
						"getResponseString raised " + ex.getMessage() + LogUtil.getStackTrace(ex, 10)
								+ "\n\nHttp request parameters:" + " face_amount {" + faceAmount + "}"
								+ " date_of_birth {" + dateOfBirth + "}" + " gender {" + gender + "}" + " smoking {"
								+ smoking + "}" + " province_code {" + provCode + "}");
			}
			String s = "An anomaly was detected; please review the parameters in your http request"
					+ "; the values that we received:" + " face_amount {" + faceAmount + "}" + " date_of_birth {"
					+ dateOfBirth + "}" + " gender {" + gender + "}" + " smoking {" + smoking + "}" + " province_code {"
					+ provCode + "}";

			responseValue = "\"" + JsonUtil.cleanStringForJson(s) + "\"";

		} catch (InsurfactPrivateException ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex + "\n" + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10));
			}
			String body = "";
			if (SEND_EMAIL_FOR_ANOMALIES) {
				String subject = TAG + " Alert - Probable Bug";
				body = "\ngetResponseString raised InsurfactPrivateException: " + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10) + "\n\nHttp request parameters:" + " face_amount {" + faceAmount
						+ "}" + " date_of_birth {" + dateOfBirth + "}" + " gender {" + gender + "}" + " smoking {"
						+ smoking + "}" + " province_code {" + provCode + "}";
				if (DEBUG) {
					System.out.println(TAG + ".getJson: alert email; Subject: " + subject + "; email body: " + body);
				}
				// MailMngr.sendAlert(subject, body);
			}
			String s = "An anomaly was detected; please contact support and please review the parameters in your http request"
					+ "; the values that we received are:" + " face_amount {" + faceAmount + "}" + " date_of_birth {"
					+ dateOfBirth + "}" + " gender {" + gender + "}" + " smoking {" + smoking + "}" + " province_code {"
					+ provCode + "}" + "\n" // ==========
			// + body // remove in prod ##################################
			// ==========
			;

			responseValue = "\"" + JsonUtil.cleanStringForJson(s) + "\"";
		} catch (Throwable ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex + "\n" + ex.getMessage()
						+ LogUtil.getStackTrace(ex, 10));
			}
			String body = "";
			if (SEND_EMAIL_FOR_ANOMALIES) {
				String subject = TAG + " Alert - Probable Bug";
				body = "\ngetResponseString raised: " + ex.getMessage() + LogUtil.getStackTrace(ex, 10)
						+ "\n\nHttp request parameters:" + " face_amount {" + faceAmount + "}" + " date_of_birth {"
						+ dateOfBirth + "}" + " gender {" + gender + "}" + " smoking {" + smoking + "}"
						+ " province_code {" + provCode + "}";
				if (DEBUG) {
					System.out.println(TAG + ".getJson: alert email; Subject: " + subject + "; email body: " + body);
				}
				// MailMngr.sendAlert(subject, body);
			}
			String s = "An anomaly was detected; "
					+ "please contact support and please review the parameters in your http request"
					+ "; the values that we received are:" + " face_amount {" + faceAmount + "}" + " date_of_birth {"
					+ dateOfBirth + "}" + " gender {" + gender + "}" + " smoking {" + smoking + "}" + " province_code {"
					+ provCode + "}" + "\n" // ========
			// + body // remove in prod ##################################
			// ========
			;
			System.out.println("3880 Generic error=" + body);

			responseValue = "\"" + JsonUtil.cleanStringForJson(s) + "\"";
		}

//        String responseAll = "{\"response\": " + responseValue + " }";
//
//        if (DEBUG) {
//            System.out.println(TAG + ".getJson: returning $$" + responseAll + "$$");
//        }
		return responseValue;
	}

	/**
	 * @param productTypeId int in string = Master product type
	 * @param faceAmount    $$$
	 * @param dateOfBirth   yyyymmdd
	 * @param gender        M, F
	 * @param smoking       Y, N
	 * @param provCode      QC
	 * @return example of content: [ { "f1": "v1", "f2": "v2", "f3": "v3" ... }, {
	 *         ... }, { ... } ]
	 */
	private String getSingleResponseString(final String productTypeId, final String faceAmount,
			final String dateOfBirth, final String gender, final String smoking, final String provCode,
			final String carrierID, final String productTypeSel) throws InsurfactPrivateException {

		// http://10.10.88.22:28081/WS_INSF/webresources/lifeSingle/6/500000/1979-01-01/M/N/QC
		if (DEBUG) {
			System.out.println(TAG + ".getSingleResponseString: entering with " + "productTypeId {" + productTypeId
					+ "}" + "faceAmount {" + faceAmount + "}" + "dateOfBirth {" + dateOfBirth + "}" + "gender {"
					+ gender + "}" + "smoking {" + smoking + "}" + "provCode {" + provCode + "}" + "carrierID {"
					+ carrierID + "}" + "productTypeSel {" + productTypeSel + "}");
		}

		// step 1: validate the input values from the URL, important for security
		// ----------------------------------------------------------------------
		int productTypeInt = 0;
		try {
			productTypeInt = Integer.parseInt(productTypeId);
		} catch (NumberFormatException e) {
			throw new InsurfactPrivateException("invalid productTypeId input {" + productTypeId + "}");
		}

		int carrierIDInt = 0; // new! added march 27, 2020 aa
		try {
			carrierIDInt = Integer.parseInt(carrierID);
		} catch (NumberFormatException e) {
			throw new InsurfactPrivateException("invalid carrierID input {" + carrierID + "}");
		}

		int faceAmountInt = 0;
		try {
			faceAmountInt = Integer.parseInt(faceAmount);
		} catch (NumberFormatException e) {
			throw new InsurfactPrivateException("invalid faceAmount input {" + faceAmount + "}");
		}

		if (dateOfBirth == null || dateOfBirth.isEmpty()) {
			throw new InsurfactPrivateException("invalid dateOfBirth input {" + dateOfBirth + "}");
		}
		String dob = dateOfBirth;

		// gender M F
		if (gender == null || gender.isEmpty() || (!gender.equalsIgnoreCase("M") && !gender.equalsIgnoreCase("F"))) {
			throw new InsurfactPrivateException("invalid gender input {" + gender + "}");
		}
		String genderUp = gender.toUpperCase();

		// smoking Y N
		if (smoking == null || smoking.isEmpty()
				|| (!smoking.equalsIgnoreCase("Y") && !smoking.equalsIgnoreCase("N"))) {
			throw new InsurfactPrivateException("invalid smoking input {" + smoking + "}");
		}
		String smokingUp = smoking.toUpperCase();

		if (provCode == null || provCode.length() != 2) {
			throw new InsurfactPrivateException("invalid provCode input {" + provCode + "}");
		}
		String provCodeUp = provCode.toUpperCase();

		try {

			// ------------------------------------------------------------------------------------------
			prepareSingleQuoteParams(smokingUp, provCodeUp, faceAmountInt, productTypeInt, dob, genderUp, carrierIDInt,
					productTypeSel);
			// ------------------------------------------------------------------------------------------

		} catch (InsurfactPrivateException e) {
			String s = "*prepareSingleQuoteParams(smokingUp, provCodeUp, faceAmountInt)* raised {" + e + "}";
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + s);
			}
			throw e;
		} catch (Throwable e) {
			String s = "*prepareSingleQuoteParams(smokingUp, provCodeUp, faceAmountInt)* raised {" + e + "}";
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + s);
			}
			throw new InsurfactPrivateException(s);
		}

		// step 4: run the sql and quote engine logic
		// ------------------------------------------
		List<Product> productsWithPercentage = null;
		if (productTypeSel.equalsIgnoreCase("C")) {
			productsWithPercentage = getProductsCombos();
			// System.out.println("1939 C
			// productsWithPercentage.size="+productsWithPercentage.size());
		} else {
			productsWithPercentage = getProductsCombosSingle(productTypeSel);
			// System.out.println("1943 not C
			// productsWithPercentage.size="+productsWithPercentage.size());
		}

		// step 5: prepare results to return to client requester
		// -----------------------------------------------------
		String response = "\"No matching records.\"";

		if (productsWithPercentage == null || productsWithPercentage.isEmpty()) {

			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + "*productsWithPercentage = getProductsCombos()* "
						+ "returned null or empty list.");
			}

			return response;
		}

		int counter = 0;
		StringBuilder buf = new StringBuilder("[ ");
		try {
			for (Product product : productsWithPercentage) {

				if ("ALL".equals(OUTPUT_TYPE)) {
					List<String> underwritingList = quoteFacade.getUnderwriting(product.getCompany().getID(),
							product.getUnderwritingGroupID(), faceAmountInt, product.getAge(),
							Character.toString(multiQP.getClient().getGender()));
					product.setUnderwriting(underwritingList);

					List<String> underwritingListFr = quoteFacade.getUnderwritingFr(product.getCompany().getID(),
							product.getUnderwritingGroupID(), faceAmountInt, product.getAge(),
							Character.toString(multiQP.getClient().getGender()));

					product.setUnderwritingFr(underwritingListFr);
				}

				++counter;
				if (counter > 1) {
					buf.append(", ");
				}

				quoteFacade.getProductShortDescriptions(product);

				Company company = product.getCompany();
				String companyNameEn = "";
				String companyNameFr = "";
				if (company != null) {
					companyNameEn = product.getCompany().getName();
					companyNameFr = product.getCompany().getNameFr();
				}
				if (DEBUG) {
					System.out.println(TAG + ".getResponseString: calling getResponseValuesInJsonForOneResult with"
							+ "\n counter {" + counter + "}" + "\n companyNameEn {" + companyNameEn + "}"
							+ "\n companyNameFr {" + companyNameFr + "}" + "\n product.getName() {" + product.getName()
							+ "}" + "\n product.getNameFr() {" + product.getNameFr() + "}" + "\n provCodeUp {"
							+ provCodeUp + "}" + "\n getStringFromDouble(product.getProductMonthlyPremiumTotal()) {"
							+ Util.getStringFromDouble(product.getProductMonthlyPremiumTotal()) + "}"
							+ "\n getStringFromDouble(product.getProductAnnualPremiumTotal()) {"
							+ Util.getStringFromDouble(product.getProductAnnualPremiumTotal()) + "}"
							+ "\n product.getDescription() {" + product.getDescription() + "}"
							+ "\n product.getDescriptionFr() {" + product.getDescription() + "}"
							+ "\n product.getUnderwriting() {" + product.getUnderwriting() + "}");
				}

				// product data: company, product name,
				// yearly premium, monthly premium, short desc en, short desc fr
				// String uw = "";
				// String uwF = ""; //french underwriting
				/*
				 * if(product.getUnderwriting() != null){ Iterator<String> iterator =
				 * product.getUnderwriting().iterator(); while (iterator.hasNext()) { uw +=
				 * iterator.next(); if (iterator.hasNext()) { uw += ", "; } } } else
				 * System.out.println(" product.getUnderwriting() IS NULL ~ ~ ~ ");
				 * 
				 * 
				 * 
				 * if(product.getUnderwritingFr() != null){ Iterator<String> iterator =
				 * product.getUnderwritingFr().iterator(); while (iterator.hasNext()) { uwF +=
				 * iterator.next(); if (iterator.hasNext()) { uwF += ", "; } } }
				 * 
				 */
				// String premRenew = "";
				int tmpAge = 0;

				multiQP = getMultiQP();

				if (product.getAgeToUse().equalsIgnoreCase("N")) {
					tmpAge = multiQP.getClient().getNearestAge();
				} else {
					tmpAge = multiQP.getClient().getActualAge();
				}

				multiQP.setCompanyID(product.getCompany().getID());

				multiQP.setProductID(product.getID());
				multiQP.setReferenceProduct(product);
				// multiQP.setHealthClass(product.getHealthClass().getGlobalClass());

				/*
				 * Product newProduct = new Product(); if("ALL".equals(OUTPUT_TYPE)){ newProduct
				 * = quoteFacade.singleProductQuote(multiQP, product); }
				 */
				// Product newProduct = quoteFacade.singleProductQueryProdID(product.getID(),
				// multiQP);
				// int lowAge = tmpAge;
				// int highAge = 0;
				/*
				 * for (PremiumRenewal pr : newProduct.getPremiumRenewals()) { lowAge =
				 * tmpAge+pr.getLowBound(); highAge = tmpAge+pr.getHighBound();
				 * 
				 * premRenew+=lowAge+"-"+highAge+" m="+
				 * Util.getStringFromDouble(pr.getTotalMonthly()) + " a="+
				 * Util.getStringFromDouble(pr.getTotalAnnual())+"  ";
				 * 
				 * }
				 */
				int convertibleAge = product.getLastConvertibleAge();
				int payableAge = product.getAgePayable();
				String healthClassEn = product.getHealthClass().getName();
				String healthClassFr = product.getHealthClass().getNameFr();

				String classFlag = product.getHealthClass().getDescription();

				int levDecr = 0;
				String coverageType = "A"; // default A for all

				if (product.getInsuranceBen() != 0) {
					levDecr = product.getInsuranceBen();
				}
				if (levDecr == 2) {
					coverageType = "L"; // L=level D=decreasing
				} else {
					coverageType = "D"; // L=level D=decreasing
				}

//                BigDecimal percentage = product.getPercentageGainWithAgent().setScale(2, RoundingMode.HALF_UP);
				String res = JsonGenerator.getResponseValues(counter, companyNameEn, companyNameFr, product.getName(),
						product.getNameFr(), "" + product.getProductType().getID(), product.getProductTypeLabel(),
						provCodeUp, Util.getStringFromDouble(product.getProductMonthlyPremiumTotal()),
						Util.getStringFromDouble(product.getProductAnnualPremiumTotal()),
						// ""+percentage,
						product.getDescription(), product.getDescriptionFr(), convertibleAge + "", payableAge + "",
						healthClassEn, healthClassFr, classFlag, smokingUp, faceAmount, tmpAge, product, coverageType, // Level
																														// or
																														// Decreasing
						PRODUCT_CLASS);
				buf.append(res);
			} // for

			if (counter > 0) {
				buf.append(" ]");
				response = buf.toString();
			} else {
				response = "\"no matching records\"";
			}
		} catch (Exception ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: Reading the results raised: " + ex);
			}
			// response = "\"Reading the resultsSet raised:
			// "+cleanStringForJson(ex.getMessage())+"\"";
			throw new InsurfactPrivateException("Reading the results raised " + ex + " at counter " + counter
			// +" - "+ex.getMessage()
					+ "\n " + LogUtil.getStackTrace(ex, 10)
			// + "\n sql = " + sql
			);
		}

		try {
			if (con != null) {
				con.close(); // again
			}
		} catch (SQLException e) {
			System.out.println("error closing connection con line 667 Generic e=" + e.getMessage());
		}

		return response;
	}

	private String getSingleProdResponseString(final String faceAmount, final String dateOfBirth, final String gender,
			final String smoking, final String provCode, final String carrierID, final String prodID)
			throws InsurfactPrivateException {

		if (DEBUG) {
			System.out.println(TAG + ".getSingleResponseString: entering with " + "faceAmount {" + faceAmount + "}"
					+ "dateOfBirth {" + dateOfBirth + "}" + "gender {" + gender + "}" + "smoking {" + smoking + "}"
					+ "provCode {" + provCode + "}" + "carrierID {" + carrierID + "}" + "prodID {" + prodID + "}");
		}

		// step 1: validate the input values from the URL, important for security
		// ----------------------------------------------------------------------
		int carrierIDInt = 0; // new! added march 27, 2020 aa

		try {
			carrierIDInt = Integer.parseInt(carrierID);
		} catch (NumberFormatException e) {
			throw new InsurfactPrivateException("invalid carrierID input {" + carrierID + "}");
		}

		int prodIDInt = 0; // new! added april 2, 2020 aa
		try {
			prodIDInt = Integer.parseInt(prodID);
		} catch (NumberFormatException e) {
			throw new InsurfactPrivateException("invalid prodID input {" + prodID + "}");
		}

		int faceAmountInt = 0;
		try {
			faceAmountInt = Integer.parseInt(faceAmount);
		} catch (NumberFormatException e) {
			throw new InsurfactPrivateException("invalid faceAmount input {" + faceAmount + "}");
		}

		if (dateOfBirth == null || dateOfBirth.isEmpty()) {
			throw new InsurfactPrivateException("invalid dateOfBirth input {" + dateOfBirth + "}");
		}
		String dob = dateOfBirth;

		// gender M F
		if (gender == null || gender.isEmpty() || (!gender.equalsIgnoreCase("M") && !gender.equalsIgnoreCase("F"))) {
			throw new InsurfactPrivateException("invalid gender input {" + gender + "}");
		}
		String genderUp = gender.toUpperCase();

		// smoking Y N
		if (smoking == null || smoking.isEmpty()
				|| (!smoking.equalsIgnoreCase("Y") && !smoking.equalsIgnoreCase("N"))) {
			throw new InsurfactPrivateException("invalid smoking input {" + smoking + "}");
		}
		String smokingUp = smoking.toUpperCase();

		if (provCode == null || provCode.length() != 2) {
			throw new InsurfactPrivateException("invalid provCode input {" + provCode + "}");
		}
		String provCodeUp = provCode.toUpperCase();

		try {
			// ------------------------------------------------------------------------------------------
			prepareSingleProdQuoteParams(smokingUp, provCodeUp, faceAmountInt, dob, genderUp, carrierIDInt, prodIDInt);
			// ------------------------------------------------------------------------------------------
		} catch (InsurfactPrivateException e) {
			String s = "*prepareSingleQuoteParams(smokingUp, provCodeUp, faceAmountInt)* raised {" + e + "}";
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + s);
			}
			throw e;
		} catch (Throwable e) {
			String s = "*prepareSingleQuoteParams(smokingUp, provCodeUp, faceAmountInt)* raised {" + e + "}";
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + s);
			}
			throw new InsurfactPrivateException(s);
		}

		// step 4: run the sql and quote engine logic
		// ------------------------------------------
		List<Product> productsWithPercentage = getProductsCombosSingle("S");

		// step 5: prepare results to return to client requester
		// -----------------------------------------------------
		String response = "\"No matching records.\"";

		if (productsWithPercentage == null || productsWithPercentage.isEmpty()) {

			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + "*productsWithPercentage = getProductsCombos()* "
						+ "returned null or empty list.");
			}

			return response;
		}

		// ------------------------------------------------------------------
//        if(DEBUG) System.out.println(TAG + ".getResponseString: "
//            + "*quoteFacade.singleProductQuote(multiQP, null)* "
//            + "returned products.size {" + products.size() + "}");
//        //-----------------------------------------------------------------------------------
//        List<Product> productsWithPercentage = setPercentageGainsWithAgent(products,multiQP);
//        //-----------------------------------------------------------------------------------
//        
//        if(DEBUG) System.out.println(TAG + ".getResponseString: "
//            + "*setPercentageGainsWithAgent(products,multiQP)* "
//            + "returned productsWithPercentage.size {" + productsWithPercentage.size() + "}");
		// responseString = [ {"f1": v1, "f2": v2, "f3": v3 ...}, { }, { } ]
		int counter = 0;
		StringBuilder buf = new StringBuilder("[ ");
		try {
			for (Product product : productsWithPercentage) {

				findPromotion(product);
				int tmpAge = 0;

				multiQP = getMultiQP();

				if (product.getAgeToUse().equalsIgnoreCase("N")) {
					tmpAge = multiQP.getClient().getNearestAge();
				} else {
					tmpAge = multiQP.getClient().getActualAge();
				}
				if (isHasPromotion() || product.hasPromoBuiltIn()) {
					product.setPromotions(product.getPromotions());
					promotionsHelper.applyPromotion(product, promo, tmpAge, multiQP.getFaceAmt(), multiQP);
				}
				promo = null;

				multiQP.setInsuranceTypeAbbr(product.getProductType().getProductClass());

				if ("ALL".equals(OUTPUT_TYPE)) {
					List<String> underwritingList = quoteFacade.getUnderwriting(product.getCompany().getID(),
							product.getUnderwritingGroupID(), faceAmountInt, product.getAge(),
							Character.toString(multiQP.getClient().getGender()));
					product.setUnderwriting(underwritingList);

					List<String> underwritingListFr = quoteFacade.getUnderwritingFr(product.getCompany().getID(),
							product.getUnderwritingGroupID(), faceAmountInt, product.getAge(),
							Character.toString(multiQP.getClient().getGender()));

					product.setUnderwritingFr(underwritingListFr);
				}

				++counter;
				if (counter > 1) {
					buf.append(", ");
				}

				quoteFacade.getProductShortDescriptions(product);

				Company company = product.getCompany();
				String companyNameEn = "";
				String companyNameFr = "";
				if (company != null) {
					companyNameEn = product.getCompany().getName();
					companyNameFr = product.getCompany().getNameFr();
				}
				if (DEBUG) {
					System.out.println(TAG + ".getResponseString: calling getResponseValuesInJsonForOneResult with"
							+ "\n counter {" + counter + "}" + "\n companyNameEn {" + companyNameEn + "}"
							+ "\n companyNameFr {" + companyNameFr + "}" + "\n product.getName() {" + product.getName()
							+ "}" + "\n product.getNameFr() {" + product.getNameFr() + "}" + "\n provCodeUp {"
							+ provCodeUp + "}" + "\n getStringFromDouble(product.getProductMonthlyPremiumTotal()) {"
							+ Util.getStringFromDouble(product.getProductMonthlyPremiumTotal()) + "}"
							+ "\n getStringFromDouble(product.getProductAnnualPremiumTotal()) {"
							+ Util.getStringFromDouble(product.getProductAnnualPremiumTotal()) + "}"
							+ "\n product.getDescription() {" + product.getDescription() + "}"
							+ "\n product.getDescriptionFr() {" + product.getDescription() + "}"
							+ "\n product.getUnderwriting() {" + product.getUnderwriting() + "}");
				}

				// product data: company, product name,
				// yearly premium, monthly premium, short desc en, short desc fr
				// String uw = "";
				// String uwF = ""; //french underwriting
				/*
				 * if(product.getUnderwriting() != null){ Iterator<String> iterator =
				 * product.getUnderwriting().iterator(); while (iterator.hasNext()) { uw +=
				 * iterator.next(); if (iterator.hasNext()) { uw += ", "; } } } else
				 * System.out.println(" product.getUnderwriting() IS NULL ~ ~ ~ ");
				 * 
				 * 
				 * 
				 * if(product.getUnderwritingFr() != null){ Iterator<String> iterator =
				 * product.getUnderwritingFr().iterator(); while (iterator.hasNext()) { uwF +=
				 * iterator.next(); if (iterator.hasNext()) { uwF += ", "; } } }
				 * 
				 */
				// String premRenew = "";

				multiQP.setCompanyID(product.getCompany().getID());

				multiQP.setProductID(product.getID());
				multiQP.setReferenceProduct(product);
				// multiQP.setHealthClass(product.getHealthClass().getGlobalClass());

				/*
				 * Product newProduct = new Product(); if("ALL".equals(OUTPUT_TYPE)){ newProduct
				 * = quoteFacade.singleProductQuote(multiQP, product); }
				 */
				// Product newProduct = quoteFacade.singleProductQueryProdID(product.getID(),
				// multiQP);
				// int lowAge = tmpAge;
				// int highAge = 0;
				/*
				 * for (PremiumRenewal pr : newProduct.getPremiumRenewals()) { lowAge =
				 * tmpAge+pr.getLowBound(); highAge = tmpAge+pr.getHighBound();
				 * 
				 * premRenew+=lowAge+"-"+highAge+" m="+
				 * Util.getStringFromDouble(pr.getTotalMonthly()) + " a="+
				 * Util.getStringFromDouble(pr.getTotalAnnual())+"  ";
				 * 
				 * }
				 */
				int convertibleAge = product.getLastConvertibleAge();
				int payableAge = product.getAgePayable();
				String healthClassEn = product.getHealthClass().getName();
				String healthClassFr = product.getHealthClass().getNameFr();

				String classFlag = product.getHealthClass().getDescription();

				int levDecr = 0;
				String coverageType = "A"; // default A for all

				if (product.getInsuranceBen() != 0) {
					levDecr = product.getInsuranceBen();
				}
				if (levDecr == 2) {
					coverageType = "L"; // L=level D=decreasing
				} else {
					coverageType = "D"; // L=level D=decreasing
				}

//              BigDecimal percentage = product.getPercentageGainWithAgent().setScale(2, RoundingMode.HALF_UP);
				String res = JsonGenerator.getResponseValues(counter, companyNameEn, companyNameFr, product.getName(),
						product.getNameFr(), "" + product.getProductType().getID(), product.getProductTypeLabel(),
						provCodeUp, Util.getStringFromDouble(product.getProductMonthlyPremiumTotal()),
						Util.getStringFromDouble(product.getProductAnnualPremiumTotal()),
						// ""+percentage,
						product.getDescription(), product.getDescriptionFr(), convertibleAge + "", payableAge + "",
						healthClassEn, healthClassFr, classFlag, smokingUp, faceAmount, tmpAge, product, coverageType, // Level
																														// or
																														// Decreasing
						product.getProductType().getProductClass());
				buf.append(res);
			} // for

			if (counter > 0) {
				buf.append(" ]");
				response = buf.toString();
			} else {
				response = "\"no matching records\"";
			}
		} catch (Exception ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: Reading the results raised: " + ex);
			}
			// response = "\"Reading the resultsSet raised:
			// "+cleanStringForJson(ex.getMessage())+"\"";
			throw new InsurfactPrivateException("Reading the results raised " + ex + " at counter " + counter
			// +" - "+ex.getMessage()
					+ "\n " + LogUtil.getStackTrace(ex, 10)
			// + "\n sql = " + sql
			);
		}

		try {
			if (con != null) { // here also
				con.close();
			}
		} catch (SQLException e) {
			System.out.println("error closing connection con line 667 Generic e=" + e.getMessage());
		}

		return response;
	}

	private String getSingleProdCreditorResponse(final String faceAmount, final String dateOfBirth, final String gender,
			final String smoking, final String provCode, final String carrierID, final String prodID)
			throws InsurfactPrivateException {

		if (DEBUG) {
			System.out.println(TAG + ".getSingleResponseString: entering with " + "faceAmount {" + faceAmount + "}"
					+ "dateOfBirth {" + dateOfBirth + "}" + "gender {" + gender + "}" + "smoking {" + smoking + "}"
					+ "provCode {" + provCode + "}" + "carrierID {" + carrierID + "}" + "prodID {" + prodID + "}");
		}

		// step 1: validate the input values from the URL, important for security
		// ----------------------------------------------------------------------
		int carrierIDInt = 0; // new! added march 27, 2020 aa

		try {
			carrierIDInt = Integer.parseInt(carrierID);
		} catch (NumberFormatException e) {
			throw new InsurfactPrivateException("invalid carrierID input {" + carrierID + "}");
		}

		int prodIDInt = 0; // new! added april 2, 2020 aa
		try {
			prodIDInt = Integer.parseInt(prodID);
		} catch (NumberFormatException e) {
			throw new InsurfactPrivateException("invalid prodID input {" + prodID + "}");
		}

		int faceAmountInt = 0;
		try {
			faceAmountInt = Integer.parseInt(faceAmount);
		} catch (NumberFormatException e) {
			throw new InsurfactPrivateException("invalid faceAmount input {" + faceAmount + "}");
		}

		if (dateOfBirth == null || dateOfBirth.isEmpty()) {
			throw new InsurfactPrivateException("invalid dateOfBirth input {" + dateOfBirth + "}");
		}
		String dob = dateOfBirth;

		// gender M F
		if (gender == null || gender.isEmpty() || (!gender.equalsIgnoreCase("M") && !gender.equalsIgnoreCase("F"))) {
			throw new InsurfactPrivateException("invalid gender input {" + gender + "}");
		}
		String genderUp = gender.toUpperCase();

		// smoking Y N
		if (smoking == null || smoking.isEmpty()
				|| (!smoking.equalsIgnoreCase("Y") && !smoking.equalsIgnoreCase("N"))) {
			throw new InsurfactPrivateException("invalid smoking input {" + smoking + "}");
		}
		String smokingUp = smoking.toUpperCase();

		if (provCode == null || provCode.length() != 2) {
			throw new InsurfactPrivateException("invalid provCode input {" + provCode + "}");
		}
		String provCodeUp = provCode.toUpperCase();

		try {
			// ------------------------------------------------------------------------------------------
			prepareSingleProdQuoteParams(smokingUp, provCodeUp, faceAmountInt, dob, genderUp, carrierIDInt, prodIDInt);
			// ------------------------------------------------------------------------------------------
		} catch (InsurfactPrivateException e) {
			String s = "*prepareSingleQuoteParams(smokingUp, provCodeUp, faceAmountInt)* raised {" + e + "}";
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + s);
			}
			throw e;
		} catch (Throwable e) {
			String s = "*prepareSingleQuoteParams(smokingUp, provCodeUp, faceAmountInt)* raised {" + e + "}";
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + s);
			}
			throw new InsurfactPrivateException(s);
		}

		multiQP.setInsuranceTypeAbbr("DI");

		// step 4: run the sql and quote engine logic
		// ------------------------------------------
		Product product = quoteFacade.singleProductQuote(multiQP, null);

		// step 5: prepare results to return to client requestera
		// -----------------------------------------------------
		multiQP.setReferenceProduct(product);

		String response = "\"No matching records.\"";

		int counter = 0;
		StringBuilder buf = new StringBuilder("[ ");
		try {
			findPromotion(product);
			int tmpAge = 0;

			multiQP = getMultiQP();

			if (product.getAgeToUse().equalsIgnoreCase("N")) {
				tmpAge = multiQP.getClient().getNearestAge();
			} else {
				tmpAge = multiQP.getClient().getActualAge();
			}
			if (isHasPromotion() || product.hasPromoBuiltIn()) {
				product.setPromotions(product.getPromotions());
				promotionsHelper.applyPromotion(product, promo, tmpAge, multiQP.getFaceAmt(), multiQP);
			}
			promo = null;

			multiQP.setInsuranceTypeAbbr(product.getProductType().getProductClass());

			if ("ALL".equals(OUTPUT_TYPE)) {
				List<String> underwritingList = quoteFacade.getUnderwriting(product.getCompany().getID(),
						product.getUnderwritingGroupID(), faceAmountInt, product.getAge(),
						Character.toString(multiQP.getClient().getGender()));
				product.setUnderwriting(underwritingList);

				List<String> underwritingListFr = quoteFacade.getUnderwritingFr(product.getCompany().getID(),
						product.getUnderwritingGroupID(), faceAmountInt, product.getAge(),
						Character.toString(multiQP.getClient().getGender()));

				product.setUnderwritingFr(underwritingListFr);
			}

			++counter;
			if (counter > 1) {
				buf.append(", ");
			}

			quoteFacade.getProductShortDescriptions(product);

			Company company = product.getCompany();
			String companyNameEn = "";
			String companyNameFr = "";
			if (company != null) {
				companyNameEn = product.getCompany().getName();
				companyNameFr = product.getCompany().getNameFr();
			}
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: calling getResponseValuesInJsonForOneResult with"
						+ "\n counter {" + counter + "}" + "\n companyNameEn {" + companyNameEn + "}"
						+ "\n companyNameFr {" + companyNameFr + "}" + "\n product.getName() {" + product.getName()
						+ "}" + "\n product.getNameFr() {" + product.getNameFr() + "}" + "\n provCodeUp {" + provCodeUp
						+ "}" + "\n getStringFromDouble(product.getProductMonthlyPremiumTotal()) {"
						+ Util.getStringFromDouble(product.getProductMonthlyPremiumTotal()) + "}"
						+ "\n getStringFromDouble(product.getProductAnnualPremiumTotal()) {"
						+ Util.getStringFromDouble(product.getProductAnnualPremiumTotal()) + "}"
						+ "\n product.getDescription() {" + product.getDescription() + "}"
						+ "\n product.getDescriptionFr() {" + product.getDescription() + "}"
						+ "\n product.getUnderwriting() {" + product.getUnderwriting() + "}");
			}

			// product data: company, product name,
			// yearly premium, monthly premium, short desc en, short desc fr
			// String uw = "";
			// String uwF = ""; //french underwriting
			// String premRenew = "";

			multiQP.setCompanyID(product.getCompany().getID());

			multiQP.setProductID(product.getID());
			multiQP.setReferenceProduct(product);
			// multiQP.setHealthClass(product.getHealthClass().getGlobalClass());

			// Product newProduct = quoteFacade.singleProductQueryProdID(product.getID(),
			// multiQP);
			updateProductWBCombos(multiQP);

			// int lowAge = tmpAge;
			// int highAge = 0;
			int convertibleAge = product.getLastConvertibleAge();
			int payableAge = product.getAgePayable();
			String healthClassEn = product.getHealthClass().getName();
			String healthClassFr = product.getHealthClass().getNameFr();

			String classFlag = product.getHealthClass().getDescription();

			int levDecr = 0;
			String coverageType = "A"; // default A for all

			if (product.getInsuranceBen() != 0) {
				levDecr = product.getInsuranceBen();
			}
			if (levDecr == 2) {
				coverageType = "L"; // L=level D=decreasing
			} else {
				coverageType = "D"; // L=level D=decreasing
			}

			product = multiQP.getReferenceProduct();

//              BigDecimal percentage = product.getPercentageGainWithAgent().setScale(2, RoundingMode.HALF_UP);
			String res = JsonGenerator.getResponseValues(counter, companyNameEn, companyNameFr, product.getName(),
					product.getNameFr(), "" + product.getProductType().getID(), product.getProductTypeLabel(),
					provCodeUp, Util.getStringFromDouble(product.getProductMonthlyPremiumTotal()),
					Util.getStringFromDouble(product.getProductAnnualPremiumTotal()),
					// ""+percentage,
					product.getDescription(), product.getDescriptionFr(), convertibleAge + "", payableAge + "",
					healthClassEn, healthClassFr, classFlag, smokingUp, faceAmount, tmpAge, product, coverageType, // Level
																													// or
																													// Decreasing
					product.getProductType().getProductClass());
			buf.append(res);

			if (counter > 0) {
				buf.append(" ]");
				response = buf.toString();
			} else {
				response = "\"no matching records\"";
			}
		} catch (Exception ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: Reading the results raised: " + ex);
			}
			// response = "\"Reading the resultsSet raised:
			// "+cleanStringForJson(ex.getMessage())+"\"";
			throw new InsurfactPrivateException("Reading the results raised " + ex + " at counter " + counter
			// +" - "+ex.getMessage()
					+ "\n " + LogUtil.getStackTrace(ex, 10)
			// + "\n sql = " + sql
			);
		}

		try {
			if (con != null) {
				con.close();// here
			}
		} catch (SQLException e) {
			System.out.println("error closing connection con line 667 Generic e=" + e.getMessage());
		}

		return response;
	}

	public void updateProductWBCombos(QuoteParams qp) {

		if (!qp.isDI()) {
			return;
		}
		Client client = qp.getClient();

		Product referenceProduct = qp.getReferenceProduct();

		if (client == null || referenceProduct == null) {
			return;
		}

		client.setSelectedWBComboRowKey(null);
		referenceProduct.getWbcombos().clear();

		int age = client.getActualAge();

		if (referenceProduct.getAgeToUse().equalsIgnoreCase("N")) {
			age = client.getNearestAge();
		}

		referenceProduct = productFacade.findWBCombosForDIProduct(referenceProduct, client.isSmoker(),
				client.getGender(), age, qp.getFaceAmt(), client.getClassId());

		multiQP.setReferenceProduct(referenceProduct);

	}

	private String getMultiResponseString(final String productTypeId, final String faceAmount, final String dateOfBirth,
			final String gender, final String smoking, final String provCode, final String healthClass,
			final String permOrTerm, final String levelOrDecr, final String uwtype, final String limitedFull // uwtype
																												// in
																												// the
																												// table
																												// im_lifeproductdetail
																												// 1=
																												// Full
																												// underwriting,
																												// 2 =
																												// Simplified
																												// underwriting,
																												// 3 =
																												// Guaranteed
																												// issue
	) throws InsurfactPrivateException {

		// http://10.10.88.22:28081/WS_INSF/webresources/lifeSingle/6/500000/1979-01-01/M/N/QC
		if (DEBUG) {
			System.out.println(TAG + ".getSingleResponseString: entering with " + "productTypeId {" + productTypeId
					+ "}" + "faceAmount {" + faceAmount + "}" + "dateOfBirth {" + dateOfBirth + "}" + "gender {"
					+ gender + "}" + "smoking {" + smoking + "}" + "provCode {" + provCode + "}");
		}

		// step 1: validate the input values from the URL, important for security
		// ----------------------------------------------------------------------
		int productTypeInt = 0;
		try {
			productTypeInt = Integer.parseInt(productTypeId);
		} catch (NumberFormatException e) {
			throw new InsurfactPrivateException("invalid productTypeId input {" + productTypeId + "}");
		}

		int faceAmountInt = 0;
		try {
			faceAmountInt = Integer.parseInt(faceAmount);
		} catch (NumberFormatException e) {
			throw new InsurfactPrivateException("invalid faceAmount input {" + faceAmount + "}");
		}

		if (dateOfBirth == null || dateOfBirth.isEmpty()) {
			throw new InsurfactPrivateException("invalid dateOfBirth input {" + dateOfBirth + "}");
		}
		String dob = dateOfBirth;

		// gender M F
		if (gender == null || gender.isEmpty() || (!gender.equalsIgnoreCase("M") && !gender.equalsIgnoreCase("F"))) {
			throw new InsurfactPrivateException("invalid gender input {" + gender + "}");
		}

		String genderUp = gender.toUpperCase();

		// uwtype A F S G only when life //limitedFull F A L when is crit
		if (PRODUCT_CLASS.equalsIgnoreCase("LIFE")) {
			if (uwtype == null || (!uwtype.equalsIgnoreCase("A") && !uwtype.equalsIgnoreCase("F")
					&& !uwtype.equalsIgnoreCase("S") && !uwtype.equalsIgnoreCase("G"))) {
				throw new InsurfactPrivateException("invalid uwtype/limitedFull input {" + uwtype + "}");
			}
		} else {
			if (uwtype == null || (!uwtype.equalsIgnoreCase("A") && !uwtype.equalsIgnoreCase("F")
					&& !uwtype.equalsIgnoreCase("L"))) {
				throw new InsurfactPrivateException("invalid uwtype/limitedFull input {" + uwtype + "}");
			}
		}

		// healthClass E P S
		if (healthClass == null || (!healthClass.equalsIgnoreCase("E") && !healthClass.equalsIgnoreCase("P")
				&& !healthClass.equalsIgnoreCase("S"))) {
			throw new InsurfactPrivateException("invalid healthClass input {" + healthClass + "}");
		}

		// smoking Y N
		if (smoking == null || smoking.isEmpty()
				|| (!smoking.equalsIgnoreCase("Y") && !smoking.equalsIgnoreCase("N"))) {
			throw new InsurfactPrivateException("invalid smoking input {" + smoking + "}");
		}
		String smokingUp = smoking.toUpperCase();

		if (provCode == null || provCode.length() != 2) {
			throw new InsurfactPrivateException("invalid provCode input {" + provCode + "}");
		}
		String provCodeUp = provCode.toUpperCase();

		try {
			// ------------------------------------------------------------------------------------------
			prepareQuoteParams(smokingUp, provCodeUp, faceAmountInt, productTypeInt, dob, genderUp, healthClass,
					permOrTerm, levelOrDecr);
			// ------------------------------------------------------------------------------------------
		} catch (InsurfactPrivateException e) {
			String s = "*prepareSingleQuoteParams(smokingUp, provCodeUp, faceAmountInt)* raised {" + e + "}";
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + s);
			}
			throw e;
		} catch (Throwable e) {
			String s = "*prepareSingleQuoteParams(smokingUp, provCodeUp, faceAmountInt)* raised {" + e + "}";
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + s);
			}
			throw new InsurfactPrivateException(s);
		}
		int uwInt = 0;

		if (limitedFull != null) {
			if (limitedFull.equals("F")) {// limited >= 10 illnesses
				multiQP.setNumIllness("F");
			}
			if (limitedFull.equals("L")) {// limited < 10 illnesses
				multiQP.setNumIllness("L");

			}
			if (limitedFull.equals("") || limitedFull.equals("A")) {// all
				multiQP.setNumIllness("A");

			}
		}

		if (uwtype != null) {
			if (PRODUCT_CLASS.equalsIgnoreCase("LIFE")) {
				if (!uwtype.equals("") && !uwtype.equalsIgnoreCase("A")) {
					// 1= Full underwriting, 2 = Simplified underwriting, 3 = Guaranteed issue
					if (uwtype.equalsIgnoreCase("F")) {
						uwInt = 1;
					}
					if (uwtype.equalsIgnoreCase("S")) {
						uwInt = 2;
					}
					if (uwtype.equalsIgnoreCase("G")) {
						uwInt = 3;
					}

				}
			} else {
				if (uwtype.equals("F")) {// limited >= 10 illnesses
					multiQP.setNumIllness("F");
				}
				if (uwtype.equals("L")) {// limited < 10 illnesses
					multiQP.setNumIllness("L");

				}
				if (uwtype.equals("") || limitedFull.equals("A")) {// all
					multiQP.setNumIllness("A");

				}
			}

		}

		multiQP.setUwType(uwInt);

		// step 3: run the sql and quote engine logic

		System.out.println("before gettingproductCombosSingle");
		// ------------------------------------------
		List<Product> productsWithPercentage = getProductsCombosSingle("S");
		// List<Product> productsWithPercentage = getProductsCombos();

		// step 5: prepare results to return to client requester
		// -----------------------------------------------------
		String response = "\"No matching records.\"";

		if (productsWithPercentage == null || productsWithPercentage.isEmpty()) {

			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + "*productsWithPercentage = getProductsCombos()* "
						+ "returned null or empty list.");
			}

			return response;
		}

		List<Product> prodList = new ArrayList<>();

		int counter = 0;
		StringBuilder buf = new StringBuilder("[ ");
		try {
			Collections.sort(productsWithPercentage, new TotalAnnComparator());

			for (Product product : productsWithPercentage) {

				System.out.println("product: " + product);

				Product referenceProduct = productFacade.getIQ4LifeProductDetail(product.getID());
				multiQP.setReferenceProduct(referenceProduct);

				int tmpAge = 0;

				multiQP = getMultiQP();

				if (product.getAgeToUse().equalsIgnoreCase("N")) {
					tmpAge = multiQP.getClient().getNearestAge();
				} else {
					tmpAge = multiQP.getClient().getActualAge();
				}
				/*
				 * product.getHealthClass().getID() Band activeBand =
				 * referenceProduct.getBandForDetails(age, client.getGender(),
				 * client.isSmoker(), qp.getOriginalFaceAmount(), client.getClassId());
				 */
				multiQP.setCompanyID(product.getCompany().getID());

				multiQP.setProductID(product.getID());

				multiQP.setFaceAmt(Integer.parseInt(faceAmount));
				multiQP.setOriginalFaceAmount(Integer.parseInt(faceAmount));

				boolean isNonSmoker = true;

				if (smokingUp.equals("Y")) {
					isNonSmoker = false;
				}

				multiQP.getClient().setNonSmoker(isNonSmoker);
				multiQP.getClient().setClassId(product.getHealthClass().getID());

				if (referenceProduct.isEnhanced()) {
					System.out.println("is enchanded the propro");
					int compId = product.getCompanyID();

					// calc and set enhanced faceAmoimt
					enhancedProductFacade.processEnhancedProduct(multiQP, referenceProduct);

					if (referenceProduct.getEnhancedFaceAmount() > 0) {

						// process enhanced face amount. in the case it requires a new amount, the
						// original qp.originalFaceAmt is set to prod.originalFaceAmt
						Integer enhancedFaceAmount = referenceProduct.getEnhancedFaceAmount();

						Band band = referenceProduct.getBandForDetails(tmpAge, genderUp.charAt(0),
								multiQP.getClient().isSmoker(), multiQP.getFaceAmt());
						Integer minMaxFaceAmount = null;

						if (band != null) {
							minMaxFaceAmount = band.getMinMaxFaceAmount();
						}

						// set new face amount
						multiQP.setFaceAmt(enhancedFaceAmount); // EMA-Jan 29th 2017

						if (minMaxFaceAmount != null) {
							int tmp = Math.abs((enhancedFaceAmount) - minMaxFaceAmount);

							// upgrade original face amount
							if (tmp >= 0 && tmp <= 1000) {
								multiQP.setFaceAmt(minMaxFaceAmount); // EMA-Jan 29th 2017
							}
						}

						// set new face amount
						multiQP.setFaceAmt(enhancedFaceAmount);
						referenceProduct.setFaceAmount(enhancedFaceAmount);
					}

					product = quoteFacade.singleProductQuote(multiQP, referenceProduct);
					if (product != null) {
						System.out.println("product.getCompanyID(): " + product.getCompanyID());
						product.setCompanyID(compId);
						System.out.println("product.getCompanyID(): " + product.getCompanyID());
					}

				}

				if (product != null) {
					prodList.add(product);
				}
				System.out.println("passes the product");

			}

			for (Product product : prodList) {

				System.out.println("product2: " + product);

				if (product != null) {

					findPromotion(product);

					int tmpAge = 0;

					multiQP = getMultiQP();

					if (product.getAgeToUse().equalsIgnoreCase("N")) {
						tmpAge = multiQP.getClient().getNearestAge();
					} else {
						tmpAge = multiQP.getClient().getActualAge();
					}
					if (isHasPromotion() || product.hasPromoBuiltIn()) {

						product.setPromotions(product.getPromotions());

						promotionsHelper.applyPromotion(product, promo, tmpAge, multiQP.getFaceAmt(), multiQP);

					}
					promo = null;
				}
				System.out.println("passes the product 2");
			}
			Collections.sort(prodList, new TotalMonComparator()); // sort by monthly premium

			for (Product product : prodList) {
				System.out.println("product3: " + product);

				if (product != null) {
					int tmpAge = 0;

					multiQP = getMultiQP();

					if (product.getAgeToUse().equalsIgnoreCase("N")) {
						tmpAge = multiQP.getClient().getNearestAge();
					} else {
						tmpAge = multiQP.getClient().getActualAge();
					}
					/*
					 * product.getHealthClass().getID() Band activeBand =
					 * referenceProduct.getBandForDetails(age, client.getGender(),
					 * client.isSmoker(), qp.getOriginalFaceAmount(), client.getClassId());
					 */
					multiQP.setCompanyID(product.getCompany().getID());

					multiQP.setProductID(product.getID());

					multiQP.setFaceAmt(Integer.parseInt(faceAmount));
					multiQP.setOriginalFaceAmount(Integer.parseInt(faceAmount));

					boolean isNonSmoker = true;

					if (smokingUp.equals("Y")) {
						isNonSmoker = false;
					}

					multiQP.getClient().setNonSmoker(isNonSmoker);
					multiQP.getClient().setClassId(product.getHealthClass().getID());

					if ("ALL".equals(OUTPUT_TYPE)) {
						List<String> underwritingList = quoteFacade.getUnderwriting(product.getCompany().getID(),
								product.getUnderwritingGroupID(), faceAmountInt, product.getAge(),
								Character.toString(multiQP.getClient().getGender()));
						product.setUnderwriting(underwritingList);

						List<String> underwritingListFr = quoteFacade.getUnderwritingFr(product.getCompany().getID(),
								product.getUnderwritingGroupID(), faceAmountInt, product.getAge(),
								Character.toString(multiQP.getClient().getGender()));

						product.setUnderwritingFr(underwritingListFr);
					}

					++counter;
					if (counter > 1) {
						buf.append(", ");
					}

					quoteFacade.getProductShortDescriptions(product);

					Company company = product.getCompany();
					String companyNameEn = "";
					String companyNameFr = "";
					if (company != null) {
						companyNameEn = product.getCompany().getName();
						companyNameFr = product.getCompany().getNameFr();
					}
					if (DEBUG) {
						System.out.println(TAG + ".getResponseString: calling getResponseValuesInJsonForOneResult with"
								+ "\n counter {" + counter + "}" + "\n companyNameEn {" + companyNameEn + "}"
								+ "\n companyNameFr {" + companyNameFr + "}" + "\n product.getName() {"
								+ product.getName() + "}" + "\n product.getNameFr() {" + product.getNameFr() + "}"
								+ "\n provCodeUp {" + provCodeUp + "}"
								+ "\n getStringFromDouble(product.getProductMonthlyPremiumTotal()) {"
								+ Util.getStringFromDouble(product.getProductMonthlyPremiumTotal()) + "}"
								+ "\n getStringFromDouble(product.getProductAnnualPremiumTotal()) {"
								+ Util.getStringFromDouble(product.getProductAnnualPremiumTotal()) + "}"
								+ "\n product.getDescription() {" + product.getDescription() + "}"
								+ "\n product.getDescriptionFr() {" + product.getDescription() + "}"
								+ "\n product.getUnderwriting() {" + product.getUnderwriting() + "}");
					}

					// product data: company, product name,
					// yearly premium, monthly premium, short desc en, short desc fr
					// String uw = "";
					// String uwF = ""; //french underwriting

					// multiQP.setHealthClass(product.getHealthClass().getGlobalClass());
					/*
					 * Product newProduct = new Product(); if("ALL".equals(OUTPUT_TYPE)){ newProduct
					 * = quoteFacade.singleProductQuote(multiQP, product); }
					 */
					// Product newProduct = quoteFacade.singleProductQueryProdID(product.getID(),
					// multiQP);
					// int lowAge = tmpAge;
					// int highAge = 0;
					int convertibleAge = product.getLastConvertibleAge();
					int payableAge = product.getAgePayable();

					String healthClassEn = product.getHealthClass().getName();
					String healthClassFr = product.getHealthClass().getNameFr();

					if (product.getAgeToUse().equalsIgnoreCase("N")) {
						tmpAge = multiQP.getClient().getNearestAge();
					} else {
						tmpAge = multiQP.getClient().getActualAge();
					}

					multiQP.setCompanyID(product.getCompany().getID());

					multiQP.setProductID(product.getID());
					multiQP.setReferenceProduct(product);
					String classFlag = product.getHealthClass().getDescription();

					int levDecr = 0;
					String coverageType = "A"; // default A for all

					if (product.getInsuranceBen() != 0) {
						levDecr = product.getInsuranceBen();
					}
					if (levDecr == 2) {
						coverageType = "L"; // L=level D=decreasing
					} else {
						coverageType = "D"; // L=level D=decreasing
					}

					@SuppressWarnings("unused")
					BigDecimal percentage = product.getPercentageGainWithAgent().setScale(2, RoundingMode.HALF_UP);
					String res = JsonGenerator.getResponseValues(counter, companyNameEn, companyNameFr,
							product.getName(), product.getNameFr(), "" + product.getProductType().getID(),
							product.getProductTypeLabel(), provCodeUp,
							Util.getStringFromDouble(product.getProductMonthlyPremiumTotal()),
							Util.getStringFromDouble(product.getProductAnnualPremiumTotal()),
							// ""+percentage,
							product.getDescription(), product.getDescriptionFr(), convertibleAge + "", payableAge + "",
							healthClassEn, healthClassFr, classFlag, smokingUp, faceAmount, tmpAge, product,
							coverageType, PRODUCT_CLASS);
					buf.append(res);
				}

				System.out.println("passes the product3");
			} // for

			if (counter > 0) {
				buf.append(" ]");
				response = buf.toString();
			} else {
				response = "";
			}
		} catch (Exception ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: Reading the results raised: " + ex);
			}
			// response = "\"Reading the resultsSet raised:
			// "+cleanStringForJson(ex.getMessage())+"\"";
			throw new InsurfactPrivateException("Reading the results raised " + ex + " at counter " + counter
			// +" - "+ex.getMessage()
					+ "\n " + LogUtil.getStackTrace(ex, 10)
			// + "\n sql = " + sql
			);
		}

		try {
			if (con != null) { // here
				con.close();
			}
		} catch (SQLException e) {
			System.out.println("error closing connection con line 667 Generic e=" + e.getMessage());
		}

		return response;
	}

	private String getMultiGroupResponseString(final String productTypeId, final String faceAmount,
			final String dateOfBirth, final String gender, final String smoking, final String provCode,
			final String healthClass, final String permOrTerm, final String levelOrDecr, final String uwtype,
			final String limitedFull // uwtype
										// in
										// the
										// table
										// im_lifeproductdetail
										// 1=
										// Full
										// underwriting,
										// 2 =
										// Simplified
										// underwriting,
										// 3 =
										// Guaranteed
										// issue
	) throws InsurfactPrivateException {

		// http://10.10.88.22:28081/WS_INSF/webresources/lifeSingle/6/500000/1979-01-01/M/N/QC
		if (DEBUG) {
			System.out.println(TAG + ".getSingleResponseString: entering with " + "productTypeId {" + productTypeId
					+ "}" + "faceAmount {" + faceAmount + "}" + "dateOfBirth {" + dateOfBirth + "}" + "gender {"
					+ gender + "}" + "smoking {" + smoking + "}" + "provCode {" + provCode + "}");
		}

		// step 1: validate the input values from the URL, important for security
		// ----------------------------------------------------------------------
		int productTypeInt = 0;
		try {
			productTypeInt = Integer.parseInt(productTypeId);
		} catch (NumberFormatException e) {
			throw new InsurfactPrivateException("invalid productTypeId input {" + productTypeId + "}");
		}

		int faceAmountInt = 0;
		try {
			faceAmountInt = Integer.parseInt(faceAmount);
		} catch (NumberFormatException e) {
			throw new InsurfactPrivateException("invalid faceAmount input {" + faceAmount + "}");
		}

		if (dateOfBirth == null || dateOfBirth.isEmpty()) {
			throw new InsurfactPrivateException("invalid dateOfBirth input {" + dateOfBirth + "}");
		}
		String dob = dateOfBirth;

		// gender M F
		if (gender == null || gender.isEmpty() || (!gender.equalsIgnoreCase("M") && !gender.equalsIgnoreCase("F"))) {
			throw new InsurfactPrivateException("invalid gender input {" + gender + "}");
		}

		String genderUp = gender.toUpperCase();

		// uwtype A F S G only when life //limitedFull F A L when is crit
		if (PRODUCT_CLASS.equalsIgnoreCase("LIFE")) {
			if (uwtype == null || (!uwtype.equalsIgnoreCase("A") && !uwtype.equalsIgnoreCase("F")
					&& !uwtype.equalsIgnoreCase("S") && !uwtype.equalsIgnoreCase("G"))) {
				throw new InsurfactPrivateException("invalid uwtype/limitedFull input {" + uwtype + "}");
			}
		} else {
			if (uwtype == null || (!uwtype.equalsIgnoreCase("A") && !uwtype.equalsIgnoreCase("F")
					&& !uwtype.equalsIgnoreCase("L"))) {
				throw new InsurfactPrivateException("invalid uwtype/limitedFull input {" + uwtype + "}");
			}
		}

		// healthClass E P S
		if (healthClass == null || (!healthClass.equalsIgnoreCase("E") && !healthClass.equalsIgnoreCase("P")
				&& !healthClass.equalsIgnoreCase("S"))) {
			throw new InsurfactPrivateException("invalid healthClass input {" + healthClass + "}");
		}

		// smoking Y N
		if (smoking == null || smoking.isEmpty()
				|| (!smoking.equalsIgnoreCase("Y") && !smoking.equalsIgnoreCase("N"))) {
			throw new InsurfactPrivateException("invalid smoking input {" + smoking + "}");
		}
		String smokingUp = smoking.toUpperCase();

		if (provCode == null || provCode.length() != 2) {
			throw new InsurfactPrivateException("invalid provCode input {" + provCode + "}");
		}
		String provCodeUp = provCode.toUpperCase();

		try {
			// ------------------------------------------------------------------------------------------
			prepareQuoteParams(smokingUp, provCodeUp, faceAmountInt, productTypeInt, dob, genderUp, healthClass,
					permOrTerm, levelOrDecr);
			// ------------------------------------------------------------------------------------------
		} catch (InsurfactPrivateException e) {
			String s = "*prepareSingleQuoteParams(smokingUp, provCodeUp, faceAmountInt)* raised {" + e + "}";
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + s);
			}
			throw e;
		} catch (Throwable e) {
			String s = "*prepareSingleQuoteParams(smokingUp, provCodeUp, faceAmountInt)* raised {" + e + "}";
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + s);
			}
			throw new InsurfactPrivateException(s);
		}
		int uwInt = 0;

		if (limitedFull != null) {
			if (limitedFull.equals("F")) {// limited >= 10 illnesses
				multiQP.setNumIllness("F");
			}
			if (limitedFull.equals("L")) {// limited < 10 illnesses
				multiQP.setNumIllness("L");

			}
			if (limitedFull.equals("") || limitedFull.equals("A")) {// all
				multiQP.setNumIllness("A");

			}
		}

		if (uwtype != null) {
			if (PRODUCT_CLASS.equalsIgnoreCase("LIFE")) {
				if (!uwtype.equals("") && !uwtype.equalsIgnoreCase("A")) {
					// 1= Full underwriting, 2 = Simplified underwriting, 3 = Guaranteed issue
					if (uwtype.equalsIgnoreCase("F")) {
						uwInt = 1;
					}
					if (uwtype.equalsIgnoreCase("S")) {
						uwInt = 2;
					}
					if (uwtype.equalsIgnoreCase("G")) {
						uwInt = 3;
					}

				}
			} else {
				if (uwtype.equals("F")) {// limited >= 10 illnesses
					multiQP.setNumIllness("F");
				}
				if (uwtype.equals("L")) {// limited < 10 illnesses
					multiQP.setNumIllness("L");

				}
				if (uwtype.equals("") || limitedFull.equals("A")) {// all
					multiQP.setNumIllness("A");

				}
			}

		}

		multiQP.setUwType(uwInt);

		// step 3: run the sql and quote engine logic
		// ------------------------------------------
		// List<Product> productsWithPercentage = getProductsCombosSingle("S");
		List<Product> productsWithPercentage = getProductsCombos();

		// step 5: prepare results to return to client requester
		// -----------------------------------------------------
		String response = "\"No matching records.\"";

		if (productsWithPercentage == null || productsWithPercentage.isEmpty()) {

			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: " + "*productsWithPercentage = getProductsCombos()* "
						+ "returned null or empty list.");
			}

			return response;
		}

		List<Product> prodList = new ArrayList<>();

		int counter = 0;
		StringBuilder buf = new StringBuilder("[ ");
		try {
			Collections.sort(productsWithPercentage, new TotalAnnComparator());

			for (Product product : productsWithPercentage) {

				Product referenceProduct = productFacade.getIQ4LifeProductDetail(product.getID());
				multiQP.setReferenceProduct(referenceProduct);

				int tmpAge = 0;

				multiQP = getMultiQP();

				if (product.getAgeToUse().equalsIgnoreCase("N")) {
					tmpAge = multiQP.getClient().getNearestAge();
				} else {
					tmpAge = multiQP.getClient().getActualAge();
				}
				/*
				 * product.getHealthClass().getID() Band activeBand =
				 * referenceProduct.getBandForDetails(age, client.getGender(),
				 * client.isSmoker(), qp.getOriginalFaceAmount(), client.getClassId());
				 */
				multiQP.setCompanyID(product.getCompany().getID());

				multiQP.setProductID(product.getID());

				multiQP.setFaceAmt(Integer.parseInt(faceAmount));
				multiQP.setOriginalFaceAmount(Integer.parseInt(faceAmount));

				boolean isNonSmoker = true;

				if (smokingUp.equals("Y")) {
					isNonSmoker = false;
				}

				multiQP.getClient().setNonSmoker(isNonSmoker);
				multiQP.getClient().setClassId(product.getHealthClass().getID());

				if (referenceProduct.isEnhanced()) {

					// calc and set enhanced faceAmoimt
					enhancedProductFacade.processEnhancedProduct(multiQP, referenceProduct);

					if (referenceProduct.getEnhancedFaceAmount() > 0) {

						// process enhanced face amount. in the case it requires a new amount, the
						// original qp.originalFaceAmt is set to prod.originalFaceAmt
						Integer enhancedFaceAmount = referenceProduct.getEnhancedFaceAmount();

						Band band = referenceProduct.getBandForDetails(tmpAge, genderUp.charAt(0),
								multiQP.getClient().isSmoker(), multiQP.getFaceAmt());
						Integer minMaxFaceAmount = null;

						if (band != null) {
							minMaxFaceAmount = band.getMinMaxFaceAmount();
						}

						// set new face amount
						multiQP.setFaceAmt(enhancedFaceAmount); // EMA-Jan 29th 2017

						if (minMaxFaceAmount != null) {
							int tmp = Math.abs((enhancedFaceAmount) - minMaxFaceAmount);

							// upgrade original face amount
							if (tmp >= 0 && tmp <= 1000) {
								multiQP.setFaceAmt(minMaxFaceAmount); // EMA-Jan 29th 2017
							}
						}

						// set new face amount
						multiQP.setFaceAmt(enhancedFaceAmount);
						referenceProduct.setFaceAmount(enhancedFaceAmount);
					}

					product = quoteFacade.singleProductQuote(multiQP, referenceProduct);

				}

				prodList.add(product);

			}

			for (Product product : prodList) {

				findPromotion(product);

				int tmpAge = 0;

				multiQP = getMultiQP();

				if (product.getAgeToUse().equalsIgnoreCase("N")) {
					tmpAge = multiQP.getClient().getNearestAge();
				} else {
					tmpAge = multiQP.getClient().getActualAge();
				}
				if (isHasPromotion() || product.hasPromoBuiltIn()) {

					product.setPromotions(product.getPromotions());

					promotionsHelper.applyPromotion(product, promo, tmpAge, multiQP.getFaceAmt(), multiQP);

				}
				promo = null;
			}
			Collections.sort(prodList, new TotalMonComparator()); // sort by monthly premium

			for (Product product : prodList) {

				int tmpAge = 0;

				multiQP = getMultiQP();

				if (product.getAgeToUse().equalsIgnoreCase("N")) {
					tmpAge = multiQP.getClient().getNearestAge();
				} else {
					tmpAge = multiQP.getClient().getActualAge();
				}
				/*
				 * product.getHealthClass().getID() Band activeBand =
				 * referenceProduct.getBandForDetails(age, client.getGender(),
				 * client.isSmoker(), qp.getOriginalFaceAmount(), client.getClassId());
				 */
				multiQP.setCompanyID(product.getCompany().getID());

				multiQP.setProductID(product.getID());

				multiQP.setFaceAmt(Integer.parseInt(faceAmount));
				multiQP.setOriginalFaceAmount(Integer.parseInt(faceAmount));

				boolean isNonSmoker = true;

				if (smokingUp.equals("Y")) {
					isNonSmoker = false;
				}

				multiQP.getClient().setNonSmoker(isNonSmoker);
				multiQP.getClient().setClassId(product.getHealthClass().getID());

				if ("ALL".equals(OUTPUT_TYPE)) {
					List<String> underwritingList = quoteFacade.getUnderwriting(product.getCompany().getID(),
							product.getUnderwritingGroupID(), faceAmountInt, product.getAge(),
							Character.toString(multiQP.getClient().getGender()));
					product.setUnderwriting(underwritingList);

					List<String> underwritingListFr = quoteFacade.getUnderwritingFr(product.getCompany().getID(),
							product.getUnderwritingGroupID(), faceAmountInt, product.getAge(),
							Character.toString(multiQP.getClient().getGender()));

					product.setUnderwritingFr(underwritingListFr);
				}

				++counter;
				if (counter > 1) {
					buf.append(", ");
				}

				quoteFacade.getProductShortDescriptions(product);

				Company company = product.getCompany();
				String companyNameEn = "";
				String companyNameFr = "";
				if (company != null) {
					companyNameEn = product.getCompany().getName();
					companyNameFr = product.getCompany().getNameFr();
				}
				if (DEBUG) {
					System.out.println(TAG + ".getResponseString: calling getResponseValuesInJsonForOneResult with"
							+ "\n counter {" + counter + "}" + "\n companyNameEn {" + companyNameEn + "}"
							+ "\n companyNameFr {" + companyNameFr + "}" + "\n product.getName() {" + product.getName()
							+ "}" + "\n product.getNameFr() {" + product.getNameFr() + "}" + "\n provCodeUp {"
							+ provCodeUp + "}" + "\n getStringFromDouble(product.getProductMonthlyPremiumTotal()) {"
							+ Util.getStringFromDouble(product.getProductMonthlyPremiumTotal()) + "}"
							+ "\n getStringFromDouble(product.getProductAnnualPremiumTotal()) {"
							+ Util.getStringFromDouble(product.getProductAnnualPremiumTotal()) + "}"
							+ "\n product.getDescription() {" + product.getDescription() + "}"
							+ "\n product.getDescriptionFr() {" + product.getDescription() + "}"
							+ "\n product.getUnderwriting() {" + product.getUnderwriting() + "}");
				}

				// product data: company, product name,
				// yearly premium, monthly premium, short desc en, short desc fr
				// String uw = "";
				// String uwF = ""; //french underwriting

				// multiQP.setHealthClass(product.getHealthClass().getGlobalClass());
				/*
				 * Product newProduct = new Product(); if("ALL".equals(OUTPUT_TYPE)){ newProduct
				 * = quoteFacade.singleProductQuote(multiQP, product); }
				 */
				// Product newProduct = quoteFacade.singleProductQueryProdID(product.getID(),
				// multiQP);
				// int lowAge = tmpAge;
				// int highAge = 0;
				int convertibleAge = product.getLastConvertibleAge();
				int payableAge = product.getAgePayable();

				String healthClassEn = product.getHealthClass().getName();
				String healthClassFr = product.getHealthClass().getNameFr();

				if (product.getAgeToUse().equalsIgnoreCase("N")) {
					tmpAge = multiQP.getClient().getNearestAge();
				} else {
					tmpAge = multiQP.getClient().getActualAge();
				}

				multiQP.setCompanyID(product.getCompany().getID());

				multiQP.setProductID(product.getID());
				multiQP.setReferenceProduct(product);
				String classFlag = product.getHealthClass().getDescription();

				int levDecr = 0;
				String coverageType = "A"; // default A for all

				if (product.getInsuranceBen() != 0) {
					levDecr = product.getInsuranceBen();
				}
				if (levDecr == 2) {
					coverageType = "L"; // L=level D=decreasing
				} else {
					coverageType = "D"; // L=level D=decreasing
				}

				@SuppressWarnings("unused")
				BigDecimal percentage = product.getPercentageGainWithAgent().setScale(2, RoundingMode.HALF_UP);
				String res = JsonGenerator.getResponseValues(counter, companyNameEn, companyNameFr, product.getName(),
						product.getNameFr(), "" + product.getProductType().getID(), product.getProductTypeLabel(),
						provCodeUp, Util.getStringFromDouble(product.getProductMonthlyPremiumTotal()),
						Util.getStringFromDouble(product.getProductAnnualPremiumTotal()),
						// ""+percentage,
						product.getDescription(), product.getDescriptionFr(), convertibleAge + "", payableAge + "",
						healthClassEn, healthClassFr, classFlag, smokingUp, faceAmount, tmpAge, product, coverageType,
						PRODUCT_CLASS);
				buf.append(res);
			} // for

			if (counter > 0) {
				buf.append(" ]");
				response = buf.toString();
			} else {
				response = "";
			}
		} catch (Exception ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: Reading the results raised: " + ex);
			}
			// response = "\"Reading the resultsSet raised:
			// "+cleanStringForJson(ex.getMessage())+"\"";
			throw new InsurfactPrivateException("Reading the results raised " + ex + " at counter " + counter
			// +" - "+ex.getMessage()
					+ "\n " + LogUtil.getStackTrace(ex, 10)
			// + "\n sql = " + sql
			);
		}

		try {
			if (con != null) { // here
				con.close();
			}
		} catch (SQLException e) {
			System.out.println("error closing connection con line 667 Generic e=" + e.getMessage());
		}

		return response;
	}

	public void writeToFile(String urlStr) throws IOException {

		StringBuffer sb = new StringBuffer();

		SimpleDateFormat fmt = new SimpleDateFormat("ddMMMyyyy");
		Calendar cal = Calendar.getInstance();

		sb.append(fmt.format(cal.getTime()) + " - " + urlStr);

		File logFile = new File("/opt/174/payara41/glassfish/domains/payaradomain/docroot/tmp/wsLogs.txt");
		// If file doesn't exists, then create it
		if (!logFile.exists()) {
			logFile.createNewFile();
		}

		FileWriter fw = new FileWriter(logFile.getAbsoluteFile(), true);

		try (BufferedWriter bw = new BufferedWriter(fw)) {
			bw.newLine();
			bw.write(sb.toString());// +"\r\n\r\n"
			bw.flush();
		} catch (IOException e) {
			System.out.println("2809 Generic widget exception e=" + e.getMessage());
		}

		fw.close();
	}

	public void saveQuote(String ip, int ws_userID, String url, String face, String dob, String gender, String smoker,
			String prov, String compID, String prodID, String productType, String urlOrigin)
			throws IOException, InsurfactPrivateException {

		int faceAmountInt = 0;
		try {
			faceAmountInt = Integer.parseInt(face);
		} catch (NumberFormatException e) {
		}

		Statement insStmt = null;
		String sql = "";

		sql = " INSERT INTO WS_QUOTES  " + "   (WS_QUOTES_INT_ID,   " + "    QUOTED_DATE,        "
				+ "    IP_ADDRESS,         " + "    WEB_SERVICE_USERS,  " + "    LINK_NAME,          "
				+ "    FACE_AMOUNT,        " + "    DATE_OF_BIRTH,      " + "    GENDER,             "
				+ "    SMOKER,             " + "    PROVINCE_CODE,      " + "    COMPANY_ID,         "
				+ "    PRODUCT_ID,         " + "    PRODUCT_TYPE,       " + "    REQUEST_SITE)       "
				+ " VALUES (WS_QUOTES_SEQ.NEXTVAL, " + "         SYSDATE, '" + ip + "',   " + "         " + ws_userID
				+ ", '" + url + "'," + "         " + faceAmountInt + ", TO_DATE('" + dob + "', 'YYYY-MM-DD'),"
				+ "         '" + gender + "', '" + smoker + "'," + "         '" + prov + "','" + compID + "','" + prodID
				+ "','" + productType + "','" + urlOrigin + "')";

		try {
			Class.forName("oracle.jdbc.OracleDriver");
		} catch (ClassNotFoundException ex) {
			if (DEBUG) {
				System.out.println(
						TAG + ".getResponseString: *Class.forName(\"oracle.jdbc.OracleDriver\")* raised: " + ex);
			}
			throw new InsurfactPrivateException("*Class.forName(\"oracle.jdbc.OracleDriver\")* raised " + ex);
		}

		boolean forProdDb = false;
		try {
			forProdDb = InsurfactSdkProperties.isForProdDb();
		} catch (Throwable ex) {
			if (DEBUG) {
				System.out.println(TAG + ".getResponseString: *InsurfactSdkProperties.isForProdDb()* raised: " + ex);
			}
			throw new InsurfactPrivateException("*InsurfactSdkProperties.isForProdDb()* raised " + ex
			// +" - "+ex.getMessage()
					+ "\n" + LogUtil.getStackTrace(ex, 10));
		}

		if (DEBUG) {
			System.out.println(TAG + ".getResponseString: forProdDb = " + forProdDb);
		}

		try {

			con = InsurfactSdkProperties.getDb2Connection(forProdDb);// skynet -- getDb2Connection ins

		} catch (SQLException ex) {
			String connectionText = "";
			if (DEBUG) {
				connectionText = "\n" + Util.getTextOnDb2Connection(con);
				System.out.println(TAG + ".getResponseString: " + "*InsurfactSdkProperties.getDb2Connection(forProdDb {"
						+ forProdDb + "})* raised: " + ex + connectionText);
			}
			throw new InsurfactPrivateException(
					"*InsurfactSdkProperties.getDb2Connection(forProdDb {" + forProdDb + "})* raised " + ex
					// +" - "+ex.getMessage()
							+ connectionText + "\n" + LogUtil.getStackTrace(ex, 10));
		}

		if (DEBUG) {
			System.out.println(TAG + ".getResponseString: " + "*getTextOnDb2Connection()* returned "
					+ Util.getTextOnDb2Connection(con));
		}

		try {

			insStmt = con.createStatement();
			insStmt.executeUpdate(sql);

		} catch (SQLException e) {
			System.out.println(" sql Exception message:" + e.getMessage());
		} finally {
			try {
				if (insStmt != null) {
					insStmt.close();
				}
				if (con != null) {
					con.close();
				}
			} catch (SQLException ignored) {
				System.out.println("SQLException ignored: " + ignored.getMessage() + " " + sql);
			}
		}
	}

	public QuoteParams getMultiQP() {
		return multiQP;
	}

	public void setMultiQP(QuoteParams multiQP) {
		this.multiQP = multiQP;
	}

	public Promotions getPromo() {
		return promo;

	}

	private String promoCode;

	public String getPromoCode() {
		return promoCode;
	}

	public void setPromoCode(String promoCode) {
		this.promoCode = promoCode;
	}

	public void setPromo(Promotions promo) {
		// System.out.println("promo: " + promo + " " + this.promo);
		this.promo = promo;
	}

	public boolean isHasPromotion() {
		// System.out.println("isHasPromotion: " + promo);
		if (promo != null) {
			return true;
		}
		return false;
	}

	public void findPromotion(Product product) {
		if (product != null) {
			int age = getMultiQP().getClient().getNearestAge();
			if ((iq4Mode == IQ4Modes.JointSingleCompany || iq4Mode == IQ4Modes.JointMultiCompany)
					&& getMultiQP().getClientESA() != null) {
				age = getMultiQP().getClientESA().getActualAge();
			}
			product.getPromotions().clear();
			for (Promotions p : promotionsHelper.findPromoByProductId(product.getID())) {
				if (p.hasPromotion(getMultiQP(), age)) {
					product.getPromotions().add(p);
					if (getMultiQP().getFaceAmt() != null) { // && (p.getPromoCode() == null ||
																// p.getPromoCode().isEmpty())
						promo = p;
					} else {// if (getQuoteParams().getFaceAmt() == null && (p.getPromoCode() == null ||
							// p.getPromoCode().isEmpty())) {
						promo = null;
					}
				}
			}

			// product.setPromotions(promotionsFacade.findByProductId(product.getID()));
		}
	}

	public void findPromotionToTest(Product product) {
		if (product != null) {
			int age = getMultiQP().getClient().getNearestAge();
			if ((iq4Mode == IQ4Modes.JointSingleCompany || iq4Mode == IQ4Modes.JointMultiCompany)
					&& getMultiQP().getClientESA() != null) {
				age = getMultiQP().getClientESA().getActualAge();
			}
			product.getPromotions().clear();
			for (Promotions p : promotionsHelper.findByProductIdToTest(product.getID())) {
				if (p.hasPromotion(getMultiQP(), age)) {
					product.getPromotions().add(p);
					if (getMultiQP().getFaceAmt() != null) { // && (p.getPromoCode() == null ||
																// p.getPromoCode().isEmpty())
						promo = p;
					} else {// if (getQuoteParams().getFaceAmt() == null && (p.getPromoCode() == null ||
							// p.getPromoCode().isEmpty())) {
						promo = null;
					}
				}
			}

			// product.setPromotions(promotionsFacade.findByProductId(product.getID()));
		}
	}

}
