package com.insurfact.ins.repository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.stereotype.Repository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

// Import skynet entity classes
import com.insurfact.skynet.entity.Advisor;
import com.insurfact.skynet.entity.Contact;
import com.insurfact.skynet.entity.Address;
import com.insurfact.skynet.entity.Phone;
import com.insurfact.skynet.entity.Email;
import com.insurfact.skynet.entity.Province;
import com.insurfact.skynet.entity.Country;
import com.insurfact.skynet.entity.Users;

/**
 * Repository class for accessing Advisor Profile data from the database.
 * Uses JdbcTemplate with custom ResultSetExtractor to handle complex joins
 * and one-to-many relationships efficiently in a single query.
 *
 * This repository returns com.insurfact.skynet.entity.Advisor objects
 * populated from database queries, following the revised implementation plan.
 *
 * <AUTHOR> zhu
 * @version 1.0
 * @since 2025-01-01
 */
@Repository
public class AdvisorProfileRepository {

    private static final Logger log = LoggerFactory.getLogger(AdvisorProfileRepository.class);

    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public AdvisorProfileRepository(@Qualifier("skytestJdbcTemplate") JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * Finds a complete advisor profile by advisor ID.
     * Executes a single complex query with multiple LEFT JOINs to fetch
     * advisor, contact, address, phone, and email data efficiently.
     *
     * @param advisorId the unique identifier of the advisor
     * @return Advisor entity containing complete profile information, or null if not found
     * @throws DataAccessException if database access fails
     */
    public Advisor findProfileById(Long advisorId) {
        log.debug("Finding advisor profile for ID: {}", advisorId);

        String sql = buildAdvisorProfileQuery();
        
        try {
            Advisor result = jdbcTemplate.query(sql, new Object[]{advisorId}, new AdvisorEntityResultSetExtractor());
            log.debug("Found advisor profile: {}", result != null ? "Yes" : "No");
            return result;
        } catch (DataAccessException e) {
            log.error("Error finding advisor profile for ID {}: {}", advisorId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Builds the complex SQL query for fetching advisor profile data.
     * Based on actual SKYTEST database schema analysis.
     * Uses correct table relationships and field names.
     * CORRECTED: Added missing fields and fixed data types based on actual schema.
     */
    private String buildAdvisorProfileQuery() {
        return """
            SELECT
                -- Advisor fields
                a.ADVISOR_INT_ID,
                a.ADVISOR_ID,
                a.NAME,
                a.STATUS,
                a.ADVISOR_TYPE,
                a.CREATION_DATE,
                a.LAST_MODIFICATION_DATE,
                a.ADVISOR_NUMBER,
                a.NOTE,
                a.SIN,
                a.CORPORATE_NAME,
                -- Contact fields
                c.CONTACT_INT_ID,
                c.FIRSTNAME,
                c.MIDDLENAME,
                c.LASTNAME,
                c.BIRTH_DATE,
                c.GENDER,
                c.PREFERRED_LANGUAGE,
                c.SALUTATION,
                c.CONTACT_TYPE,
                c.JOB_TITLE,
                c.JOB_ROLE,
                c.MARITAL_STATUS,
                c.INCOME,
                -- User fields
                u.USERNAME,
                u.ACTIVE,
                u.USER_TYPE,
                -- Address fields
                addr.ADDRESS_INT_ID,
                addr.ADDRESS_LINE1,
                addr.ADDRESS_LINE2,
                addr.ADDRESS_LINE3,
                addr.CITY,
                addr.POSTAL_CODE,
                addr.IS_PRIMARY as ADDR_IS_PRIMARY,
                addr.TYPE as ADDRESS_TYPE,
                addr.CARE_OF,
                -- Province/Country
                prov.PROVINCE_CODE,
                prov.NAME_EN as PROVINCE_NAME_EN,
                prov.NAME_FR as PROVINCE_NAME_FR,
                ctry.COUNTRY_CODE,
                ctry.COUNTRY_NAME_EN,
                ctry.COUNTRY_NAME_FR,
                -- Phone fields
                ph.PHONE_INT_ID,
                ph.PHONE_NUMBER,
                ph.AREA_CODE,
                ph.EXTENSION,
                ph.IS_PRIMARY as PHONE_IS_PRIMARY,
                ph.TYPE as PHONE_TYPE,
                ph.DO_NOT_CALL,
                -- Email fields
                em.EMAIL_INT_ID,
                em.EMAIL_ADDRESS,
                em.IS_PRIMARY as EMAIL_IS_PRIMARY,
                em.TYPE as EMAIL_TYPE,
                em.SEND_SOLICITATION
            FROM ADVISOR a
                INNER JOIN CONTACT c ON a.ADVISOR_INT_ID = c.CONTACT_INT_ID
                LEFT JOIN USERS u ON c.CONTACT_INT_ID = u.USER_INT_ID
                LEFT JOIN CONTACT_ADDRESS ca ON c.CONTACT_INT_ID = ca.CONTACT
                LEFT JOIN ADDRESS addr ON ca.ADDRESS = addr.ADDRESS_INT_ID
                LEFT JOIN PROVINCE prov ON addr.PROVINCE = prov.PROVINCE_CODE
                LEFT JOIN COUNTRY ctry ON prov.COUNTRY = ctry.COUNTRY_CODE
                LEFT JOIN CONTACT_PHONE cp ON c.CONTACT_INT_ID = cp.CONTACT
                LEFT JOIN PHONE ph ON cp.PHONE = ph.PHONE_INT_ID
                LEFT JOIN CONTACT_EMAIL ce ON c.CONTACT_INT_ID = ce.CONTACT
                LEFT JOIN EMAIL em ON ce.EMAIL = em.EMAIL_INT_ID
            WHERE a.ADVISOR_INT_ID = ?
            ORDER BY addr.IS_PRIMARY DESC, ph.IS_PRIMARY DESC, em.IS_PRIMARY DESC
            """;
    }

    /**
     * Custom ResultSetExtractor to handle the complex result set from the advisor profile query.
     * Aggregates multiple rows into a single Advisor entity with nested collections.
     * Creates and populates com.insurfact.skynet.entity objects directly.
     */
    private static class AdvisorEntityResultSetExtractor implements ResultSetExtractor<Advisor> {
        
        @Override
        public Advisor extractData(ResultSet rs) throws SQLException, DataAccessException {
            Advisor advisor = null;
            Contact contact = null;
            Map<Long, Address> addressMap = new HashMap<>();
            Map<Long, Phone> phoneMap = new HashMap<>();
            Map<Long, Email> emailMap = new HashMap<>();
            Users user = null;

            while (rs.next()) {
                // Build advisor object (only once)
                if (advisor == null) {
                    advisor = buildAdvisorEntityFromResultSet(rs);
                }

                // Build contact object (only once)
                if (contact == null && rs.getLong("CONTACT_INT_ID") != 0) {
                    contact = buildContactEntityFromResultSet(rs);
                    advisor.setContact(contact);
                }

                // Build user object (only once)
                if (user == null && rs.getString("USERNAME") != null) {
                    user = buildUserEntityFromResultSet(rs);
                    advisor.setUsers(user);
                }

                // Collect addresses
                Long addressId = rs.getLong("ADDRESS_INT_ID");
                if (addressId != 0 && !addressMap.containsKey(addressId)) {
                    Address address = buildAddressEntityFromResultSet(rs);
                    addressMap.put(addressId, address);
                }

                // Collect phones
                Long phoneId = rs.getLong("PHONE_INT_ID");
                if (phoneId != 0 && !phoneMap.containsKey(phoneId)) {
                    Phone phone = buildPhoneEntityFromResultSet(rs);
                    phoneMap.put(phoneId, phone);
                }

                // Collect emails
                Long emailId = rs.getLong("EMAIL_INT_ID");
                if (emailId != 0 && !emailMap.containsKey(emailId)) {
                    Email email = buildEmailEntityFromResultSet(rs);
                    emailMap.put(emailId, email);
                }
            }

            // Set collections on contact
            if (contact != null) {
                // Note: The exact method names depend on the skynet entity implementation
                // These may need to be adjusted based on the actual entity class structure
                if (!addressMap.isEmpty()) {
                    contact.setAddressList(new ArrayList<>(addressMap.values()));
                }
                if (!phoneMap.isEmpty()) {
                    contact.setPhoneList(new ArrayList<>(phoneMap.values()));
                }
                if (!emailMap.isEmpty()) {
                    contact.setEmailList(new ArrayList<>(emailMap.values()));
                }
            }

            return advisor;
        }

        private Advisor buildAdvisorEntityFromResultSet(ResultSet rs) throws SQLException {
            Advisor advisor = new Advisor();

            // Set advisor fields based on actual SKYTEST schema
            advisor.setAdvisorIntId(rs.getLong("ADVISOR_INT_ID"));
            advisor.setAdvisorId(rs.getLong("ADVISOR_ID"));
            advisor.setName(rs.getString("NAME"));
            advisor.setStatus(rs.getInt("STATUS")); // STATUS is NUMBER(4) in schema
            advisor.setAdvisorType(rs.getInt("ADVISOR_TYPE")); // ADVISOR_TYPE is NUMBER(4) in schema
            advisor.setCreationDate(rs.getTimestamp("CREATION_DATE"));
            advisor.setLastModificationDate(rs.getTimestamp("LAST_MODIFICATION_DATE"));
            advisor.setAdvisorNumber(rs.getString("ADVISOR_NUMBER"));
            advisor.setNote(rs.getString("NOTE"));
            advisor.setSin(rs.getString("SIN")); // SIN is VARCHAR2(11) in schema
            advisor.setCorporateName(rs.getString("CORPORATE_NAME")); // CORPORATE_NAME is VARCHAR2(256) in schema

            return advisor;
        }

        private Contact buildContactEntityFromResultSet(ResultSet rs) throws SQLException {
            Contact contact = new Contact();

            // Set contact fields based on actual SKYTEST schema
            contact.setContactIntId(rs.getLong("CONTACT_INT_ID"));
            contact.setFirstname(rs.getString("FIRSTNAME")); // Field name is FIRSTNAME in schema
            contact.setLastname(rs.getString("LASTNAME")); // Field name is LASTNAME in schema
            contact.setMiddlename(rs.getString("MIDDLENAME")); // Field name is MIDDLENAME in schema
            contact.setBirthDate(rs.getDate("BIRTH_DATE"));
            contact.setGender(rs.getInt("GENDER")); // GENDER is NUMBER(4) in schema
            contact.setPreferredLanguage(rs.getInt("PREFERRED_LANGUAGE")); // NUMBER(4) in schema
            contact.setSalutation(rs.getInt("SALUTATION")); // NUMBER in schema
            contact.setContactType(rs.getInt("CONTACT_TYPE")); // NUMBER(4) in schema
            contact.setJobTitle(rs.getString("JOB_TITLE")); // JOB_TITLE is VARCHAR2(100) in schema
            contact.setJobRole(rs.getString("JOB_ROLE")); // JOB_ROLE is VARCHAR2(100) in schema
            contact.setMaritalStatus(rs.getInt("MARITAL_STATUS")); // MARITAL_STATUS is NUMBER(8) in schema
            contact.setIncome(rs.getBigDecimal("INCOME")); // INCOME is NUMBER(19,4) in schema

            return contact;
        }

        private Address buildAddressEntityFromResultSet(ResultSet rs) throws SQLException {
            Address address = new Address();

            // Set address fields based on actual SKYTEST schema
            address.setAddressIntId(rs.getLong("ADDRESS_INT_ID"));
            address.setType(rs.getInt("ADDRESS_TYPE")); // TYPE is NUMBER(8) in schema (CORRECTED)
            address.setAddressLine1(rs.getString("ADDRESS_LINE1")); // Field name is ADDRESS_LINE1
            address.setAddressLine2(rs.getString("ADDRESS_LINE2")); // Field name is ADDRESS_LINE2
            address.setAddressLine3(rs.getString("ADDRESS_LINE3")); // Field name is ADDRESS_LINE3 (NEW)
            address.setCity(rs.getString("CITY"));
            address.setPostalCode(rs.getString("POSTAL_CODE"));
            address.setIsPrimary("Y".equals(rs.getString("ADDR_IS_PRIMARY"))); // VARCHAR2(1) field
            address.setCareOf(rs.getString("CARE_OF")); // CARE_OF is VARCHAR2(254) in schema (NEW)

            // Set province if available
            if (rs.getString("PROVINCE_CODE") != null) {
                Province province = new Province();
                province.setProvinceCode(rs.getString("PROVINCE_CODE"));
                province.setNameEn(rs.getString("PROVINCE_NAME_EN"));
                province.setNameFr(rs.getString("PROVINCE_NAME_FR"));
                address.setProvince(province);
            }

            // Set country if available
            if (rs.getString("COUNTRY_CODE") != null) {
                Country country = new Country();
                country.setCountryCode(rs.getString("COUNTRY_CODE"));
                country.setCountryNameEn(rs.getString("COUNTRY_NAME_EN"));
                country.setCountryNameFr(rs.getString("COUNTRY_NAME_FR"));
                address.setCountry(country);
            }

            return address;
        }

        private Phone buildPhoneEntityFromResultSet(ResultSet rs) throws SQLException {
            Phone phone = new Phone();

            // Set phone fields based on actual SKYTEST schema
            phone.setPhoneIntId(rs.getLong("PHONE_INT_ID"));
            phone.setType(rs.getInt("PHONE_TYPE")); // TYPE is NUMBER(8) in schema (CORRECTED)
            phone.setAreaCode(rs.getString("AREA_CODE")); // AREA_CODE is VARCHAR2(4) in schema
            phone.setPhoneNumber(rs.getString("PHONE_NUMBER")); // PHONE_NUMBER is VARCHAR2(20) in schema
            phone.setExtension(rs.getString("EXTENSION"));
            phone.setIsPrimary("Y".equals(rs.getString("PHONE_IS_PRIMARY"))); // VARCHAR2(1) field
            phone.setDoNotCall("Y".equals(rs.getString("DO_NOT_CALL"))); // DO_NOT_CALL is VARCHAR2(1) in schema (NEW)

            return phone;
        }

        private Email buildEmailEntityFromResultSet(ResultSet rs) throws SQLException {
            Email email = new Email();

            // Set email fields based on actual SKYTEST schema
            email.setEmailIntId(rs.getLong("EMAIL_INT_ID"));
            email.setType(rs.getInt("EMAIL_TYPE")); // TYPE is NUMBER(8) in schema (CORRECTED)
            email.setEmailAddress(rs.getString("EMAIL_ADDRESS")); // EMAIL_ADDRESS is VARCHAR2(100) in schema
            email.setIsPrimary("Y".equals(rs.getString("EMAIL_IS_PRIMARY"))); // VARCHAR2(1) field
            email.setSendSolicitation("Y".equals(rs.getString("SEND_SOLICITATION"))); // SEND_SOLICITATION is VARCHAR2(1) in schema (NEW)

            return email;
        }

        private Users buildUserEntityFromResultSet(ResultSet rs) throws SQLException {
            Users user = new Users();

            // Set user fields based on actual SKYTEST schema
            // Note: USER_INT_ID references CONTACT_INT_ID, so we use the contact ID
            user.setUserIntId(rs.getLong("CONTACT_INT_ID"));
            user.setUsername(rs.getString("USERNAME")); // USERNAME is VARCHAR2(128) in schema
            user.setActive(rs.getString("ACTIVE")); // ACTIVE is VARCHAR2(1) field
            user.setUserType(rs.getInt("USER_TYPE")); // USER_TYPE is NUMBER(4) in schema (NEW)

            return user;
        }

    }
}
