/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2019 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.ins.MappingFiles;
 

import com.insurfact.sdk.properties.InsurfactSdkProperties;
import java.util.logging.Logger;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */

// http://10.10.88.22:28081/WS_INS_MAP/webresources/compProdMap/C
// http://10.10.88.22:28081/WS_INS_MAP/webresources/compProdMap/P
// http://10.10.88.22:28081/WS_INS_MAP/webresources/compProdMap/T

@RestController
@RequestMapping("/webresources/compProdMap") // Base path for the controller 
public class CompProdMap {

	private static final String TAG = CompProdMap.class.getName();

	@SuppressWarnings("unused")
	private static final Logger LOG = Logger.getLogger(TAG);

	private static boolean DEBUG = false;

	/**
	 * Creates a new instance of LifeSingle class.
	 */
	public CompProdMap() {
	}

	@GetMapping(value="/{map}", produces = MediaType.APPLICATION_JSON_VALUE)
	@ResponseBody
	public String getJson(@PathVariable("map") String map) {

		DEBUG = InsurfactSdkProperties.getBoolean("debug.enabled", false);

		FilesBuilder filesBuilder = new FilesBuilder();

		String responseValue = filesBuilder.getNoExceptionMapFile(map);

		String responseAll = "{\"response\": " + responseValue + " }";

		if (DEBUG) {
			System.out.println(TAG + ".getJson: returning $$" + responseAll + "$$");
		}

//System.out.println("134 LifeSingle carrierID="+carrierID);

		return responseAll;
	}

}
