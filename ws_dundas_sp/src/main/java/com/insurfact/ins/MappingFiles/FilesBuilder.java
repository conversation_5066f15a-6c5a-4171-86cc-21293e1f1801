/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package com.insurfact.ins.MappingFiles;
  
import com.insurfact.sdk.properties.InsurfactSdkProperties;
import com.insurfact.sdk.utils.InsurfactPrivateException;
import com.insurfact.sdk.utils.LogUtil; 
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class FilesBuilder {

    private static final String TAG = CompProdMap.class.getName();

    @SuppressWarnings("unused")
	private static final Logger LOG = Logger.getLogger(TAG);

    private static boolean DEBUG = false; 

    
        String getNoExceptionMapFile(String map) {
        
        //get debug flag from properties
        
        DEBUG = InsurfactSdkProperties.getBoolean("debug.enabled", false);
        
        String responseValue = "";
        try {
            responseValue = getMappingResponseString(map);
        } catch (IllegalArgumentException ex) {
            if (DEBUG) {
                System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex);
            }


        } catch (InsurfactPrivateException ex) {
            if (DEBUG) {
                System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex
                    + "\n" + ex.getMessage() + LogUtil.getStackTrace(ex, 10));
            }
        } catch (Throwable ex) {
            if (DEBUG) {
                System.out.println(TAG + ".getJson: *getResponseString(...)* raised: " + ex
                    + "\n" + ex.getMessage() + LogUtil.getStackTrace(ex, 10));
        }
        
      }
        
      return responseValue;
   }

    
    
    private String getMappingResponseString(String map) throws InsurfactPrivateException {


     Connection con = null;
     
     String sql = "";
     
     StringBuilder buf = new StringBuilder("[ ");
     String res = "";
     int counter = 0;
     
     boolean forProdDb = false;
     try {
         forProdDb = InsurfactSdkProperties.isForProdDb();
     } catch (Throwable ex) {
        throw new InsurfactPrivateException("*InsurfactSdkProperties.isForProdDb()* raised " + ex
             //+" - "+ex.getMessage()
             + " " + LogUtil.getStackTrace(ex, 10));
     }
       
     try {
         
          con = InsurfactSdkProperties.getDb1Connection(forProdDb);    
          
          ResultSet resRs = null;
          Statement resStmt = con.createStatement();
          
//mapping file: company_product          
        if(map.equalsIgnoreCase("C")){ //company / product mapping file
            
          buf = new StringBuilder("[ ");
          res = "";

          sql = "select c.companyid    compId," +
                "       c.NAMEENGLISH  compNameEn, " +
                "       c.NAMEFRENCH   compNameFr, " +
                "       p.productid    prodId,        " +
                "       p.PRODUCTCLASS prodClass,     " +
                "       p.ENGLISH_NAME prodNameEn, "+
                "       p.FRENCH_NAME  prodNameFr, " +
                "       p.SHORT_ENGLISH_DESC prodDescEn, " +
                "       p.SHORT_FRENCH_DESC  prodDescFr, " +
                "       p.PRODUCTTYPE  prodTypeId, " +
                "       t.DESCENGLISH  prodTypeEn, "+
                "       t.DESCFRENCH   prodTypeFr, "+
                "       de.description prodTypeDescEn, " +
                "       df.description prodTypeDescFr, " +
                "       d.MLOW_ISSUE_AGE   mLowAge, " +
                "       d.MHIGH_ISSUE_AGE  mHighAge, " +
                "       d.FLOW_ISSUE_AGE   fLowAge, " +
                "       d.FHIGH_ISSUE_AGE  fHighAge, " +
                "       d.MLOW_ISSUE_AGE_SMOKER  mLowAgeSm, " +
                "       d.MHIGH_ISSUE_AGE_SMOKER mHighAgeSm, " +
                "       d.FLOW_ISSUE_AGE_SMOKER  fLowAgeSm, " +
                "       d.FHIGH_ISSUE_AGE_SMOKER fHighAgeSm, " +
                "       (select min(PREMIUM_MIN_FACE_AMOUNT) from im_life_premium_rate r1 " +
                "         where r1.productid=p.productid) minFace, " +
                "       (select max(PREMIUM_MAX_FACE_AMOUNT) from im_life_premium_rate r2 " +
                "         where r2.productid=p.productid) maxFace " +
                "  FROM im_companyproduct p, im_company c,  " +
                "       im_lifeproductdetail d,             " +
                "       im_producttype t, type de, type df  " +
                " WHERE (c.companyid=p.companyid OR (c.parentcompany=p.companyid AND p.useparentcompany='Y'))  " +
                "   and d.productid=p.productid " +
                "   and p.PROFILE_TYPE in (0, 2) " +
                "   AND c.companytype='I' " +
                "   AND p.available='Y' " +
                "   AND (p.active_date <= SYSDATE OR p.active_date IS NULL) " +
                "   AND (p.inactive_date > SYSDATE OR p.inactive_date IS NULL) " +
                "   AND d.sing_jo1_joLast IN (1,3,5,7) " +
                "   AND t.producttype=p.producttype " +
                "   AND t.WS_ENABLED= 1 " +
                "   AND de.typeid=t.producttypeid " +
                "   AND de.typename='PRODUCTTYPE' " +
                "   AND de.language=1 " +
                "   AND df.typeid=t.producttypeid " +
                "   AND df.typename='PRODUCTTYPE' " +
                "   AND df.language=2  " +
                " ORDER BY 2, 5,6";
          
          resRs = resStmt.executeQuery(sql);
          
          counter = 0;
          
          while(resRs.next()){
             
            ++counter;
            if(counter>1){
              buf.append(", ");
            }

            res = JsonMapFilesGenerator.getResponseCompanyProduct(
                    resRs.getInt("compId"),
                    resRs.getString("compNameEn"),
                    resRs.getString("compNameFr"),
                    resRs.getInt("prodID"),
                    resRs.getString("prodClass"),
                    resRs.getString("prodNameEn"),
                    resRs.getString("prodNameFr"),
                    resRs.getString("prodDescEn"),
                    resRs.getString("prodDescFr"),
                    resRs.getInt("prodTypeId"),
                    resRs.getString("prodTypeEn"),
                    resRs.getString("prodTypeFr"),
                    resRs.getString("prodTypeDescEn"),
                    resRs.getString("prodTypeDescFr"),
                    resRs.getString("mLowAge"),
                    resRs.getString("mHighAge"),
                    resRs.getString("fLowAge"),
                    resRs.getString("fHighAge"),
                    resRs.getString("mLowAgeSm"),
                    resRs.getString("mHighAgeSm"),
                    resRs.getString("fLowAgeSm"),
                    resRs.getString("fHighAgeSm"),
                    resRs.getString("minFace"),
                    resRs.getString("maxFace"));
            
            buf.append(res);
              
          }
                  
        }
          
        if(map.equalsIgnoreCase("P")){ //mapping file: company_province
            
          buf = new StringBuilder("[ ");
          res = "";
            
          sql = "select c.companyid   compId," +
                "       p.productid   prodId, " +
                "       pp.PROVSTATE  provId, " +
                "       prov.PROVINCE provAbbr, " +
                "       prov.PROVINCENAMEE provNameEn, " +
                "       prov.PROVINCENAMEF provNameFr  " +
                "  from im_companyproduct p, im_company c, im_lifeproductdetail d, " +
                "       im_producttype t, product_by_prov pp, province prov " +
                " WHERE (c.companyid=p.companyid OR (c.parentcompany=p.companyid AND p.useparentcompany='Y'))  " +
                "   and d.productid=p.productid " +
                "   and p.PROFILE_TYPE in (0, 2) " +
                "   AND c.companytype='I' " +
                "   AND p.available='Y' " +
                "   AND (p.active_date <= SYSDATE OR p.active_date IS NULL) " +
                "   AND (p.inactive_date > SYSDATE OR p.inactive_date IS NULL) " +
                "   AND d.sing_jo1_joLast IN (1,3,5,7) " +
                "   AND t.producttype=p.producttype " +
                "   AND t.WS_ENABLED= 1 " +
                "   AND pp.productid=p.productid " +
                "   and pp.provstate=prov.provinceid " +
                " order by 1,2,3";


          resRs = resStmt.executeQuery(sql);
          
          counter = 0;
          
          while(resRs.next()){
             
            ++counter;
            if(counter>1){
              buf.append(", ");
            }
            res = JsonMapFilesGenerator.getResponseProvince(
                    resRs.getInt("compId"),
                    resRs.getInt("prodID"),
                    resRs.getString("provId"),
                    resRs.getString("provAbbr"),
                    resRs.getString("provNameEn"),
                    resRs.getString("provNameFr")
            );
            
            buf.append(res);
              
          }
        }
        
          
        if(map.equalsIgnoreCase("T")){ //used for mulit-company quote: product_type          
            
          buf = new StringBuilder("[ ");
          res = "";
            
          sql = "select distinct p.PRODUCTTYPE prodTypeID,   " +
                "       t.DESCENGLISH  prodTypeEn, "+
                "       t.DESCFRENCH   prodTypeFr, "+
                "       de.description prodTypeDescEn, " +
                "       df.description prodTypeDescFr, " +
                "       p.PRODUCTCLASS prodClass   " +
                "  from im_companyproduct p, im_company c, im_lifeproductdetail d, " +
                "       im_producttype t, type de, type df " +
                " WHERE (c.companyid=p.companyid OR (c.parentcompany=p.companyid AND p.useparentcompany='Y'))  " +
                "   and d.productid=p.productid " +
                "   and p.PROFILE_TYPE in (0, 2) " +
                "   AND c.companytype='I' " +
                "   AND p.available='Y' " +
                "   AND (p.active_date <= SYSDATE OR p.active_date IS NULL) " +
                "   AND (p.inactive_date > SYSDATE OR p.inactive_date IS NULL) " +
                "   AND d.sing_jo1_joLast IN (1,3,5,7) " +
                "   AND t.producttype=p.producttype " +
                "   AND t.WS_ENABLED= 1 " +
                "   AND de.typeid=t.producttypeid " +
                "   AND de.typename='PRODUCTTYPE' " +
                "   AND de.language=1 " +
                "   AND df.typeid=t.producttypeid " +
                "   AND df.typename='PRODUCTTYPE' " +
                "   AND df.language=2  " +
                " order by 4,2";
          
          
          resRs = resStmt.executeQuery(sql);
          
          counter = 0;
          
          while(resRs.next()){
             
            ++counter;
            if(counter>1){
              buf.append(", ");
            }

            res = JsonMapFilesGenerator.getResponseProdType(
                    resRs.getInt("prodTypeId"),
                     resRs.getString("prodClass"),
                    resRs.getString("prodTypeEn"),
                    resRs.getString("prodTypeFr"),
                    resRs.getString("prodTypeDescEn"),
                    resRs.getString("prodTypeDescFr")
            );
            
            buf.append(res);
              
          }
          
        }
          
          
        } catch (SQLException ex) {
            String connectionText = "";
            throw new InsurfactPrivateException(
                "*InsurfactSdkProperties.getDb1Connection(forProdDb {" + forProdDb + "})* raised " + ex
                //+" - "+ex.getMessage()
                +connectionText
                + " " + LogUtil.getStackTrace(ex, 10));
        }
     
       buf.append(" ]"); 
       String response = buf.toString();
     
       return response;
        
    }

    
    public FilesBuilder() {
    }
    
    
}
