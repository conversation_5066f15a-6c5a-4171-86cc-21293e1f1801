/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2019 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.ins.MappingFiles;
 
import com.insurfact.wsutil.JsonUtil;  

/**
 *
 * <AUTHOR>
 */
public class JsonMapFilesGenerator {

    /*
 "{\"response\":\""+responseString+"\"}"

responseString example = [ {"fundatakey": key, "f2": v2, "f3": v3 ...}, {  }, {  } ]
     */
    /**
     * Input product data: company, product name, yearly premium, monthly
     * premium, short desc en, short desc fr
     *
     * <p>
     * company name en company name fr product.getName(), product.getNameFr(),
     * province product.getMonthlyBaseSumPremium(),
     * product.getAnnualBaseSumPremium(), product.getDescription(),
     * product.getDescriptionFr()
     * </p>
     *
     * @return String formatted for JSON; example = {"rank": key, "f2": v2,
     * "f3": v3 ...}, { }, { }
     */
    static String getResponseCompanyProduct( //company and product mapping file
            int companyID,
            String companyNameEn,
            String companyNameFr,
            int prodID,
            String prodClass,
            String productNameEn,
            String productNameFr,
            String prodDescEn,
            String prodDescFr,
            int prodTypeId,
            String prodTypeEn,
            String prodTypeFr,
            String prodTypeDescEn,
            String prodTypeDescFr,
            String mLowAge,
            String mHighAge,
            String fLowAge,
            String fHighAge,
            String mLowAgeSm,
            String mHighAgeSm,
            String fLowAgeSm,
            String fHighAgeSm,
            String minFace,
            String maxFace
    ) {
        StringBuilder buf = new StringBuilder(" ");

        buf.append(" { "); //open response

        buf.append("\"companyId\"").append(" : \"").append(companyID).append("\", ");
        buf.append("\"companyNameEn\"").append(" : \"").append(JsonUtil.cleanStringForJson(companyNameEn)).append("\", ");
        buf.append("\"companyNameFr\"").append(" : \"").append(JsonUtil.cleanStringForJson(companyNameFr)).append("\", ");
        buf.append("\"productId\"").append(" : \"").append(prodID).append("\", ");
        buf.append("\"productClass\"").append(" : \"").append(JsonUtil.cleanStringForJson(prodClass)).append("\", ");
        buf.append("\"productNameEn\"").append(" : \"").append(JsonUtil.cleanStringForJson(productNameEn)).append("\", ");
        buf.append("\"productNameFr\"").append(" : \"").append(JsonUtil.cleanStringForJson(productNameFr)).append("\", ");
        buf.append("\"productDescEn\"").append(" : \"").append(JsonUtil.cleanStringForJson(prodDescEn)).append("\", ");
        buf.append("\"productDescFr\"").append(" : \"").append(JsonUtil.cleanStringForJson(prodDescFr)).append("\", ");
        buf.append("\"productTypeId\"").append(" : \"").append(prodTypeId).append("\", ");

        buf.append("\"productTypeGenericNameEn\"").append(" : \"").append(JsonUtil.cleanStringForJson(prodTypeEn)).append("\", ");
        buf.append("\"productTypeGenericNameFr\"").append(" : \"").append(JsonUtil.cleanStringForJson(prodTypeFr)).append("\", ");

        buf.append("\"productTypeDescEn\"").append(" : \"").append(JsonUtil.cleanStringForJson(prodTypeDescEn)).append("\", ");
        buf.append("\"productTypeDescFr\"").append(" : \"").append(JsonUtil.cleanStringForJson(prodTypeDescFr)).append("\", ");

        buf.append("\"maleLowAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(mLowAge)).append("\", ");
        buf.append("\"maleHighAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(mHighAge)).append("\", ");
        buf.append("\"femaleLowAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(fLowAge)).append("\", ");
        buf.append("\"femaleHighAge\"").append(" : \"").append(JsonUtil.cleanStringForJson(fHighAge)).append("\", ");

        buf.append("\"maleLowAgeSmoker\"").append(" : \"").append(JsonUtil.cleanStringForJson(mLowAgeSm)).append("\", ");
        buf.append("\"maleHighAgeSmoker\"").append(" : \"").append(JsonUtil.cleanStringForJson(mHighAgeSm)).append("\", ");
        buf.append("\"femaleLowAgeSmoker\"").append(" : \"").append(JsonUtil.cleanStringForJson(fLowAgeSm)).append("\", ");
        buf.append("\"femaleHighAgeSmoker\"").append(" : \"").append(JsonUtil.cleanStringForJson(fHighAgeSm)).append("\", ");
        
        buf.append("\"minimumFaceAmount\"").append(" : \"").append(JsonUtil.cleanStringForJson(minFace)).append("\", ");
        buf.append("\"maximumFaceAmount\"").append(" : \"").append(JsonUtil.cleanStringForJson(maxFace)).append("\" ");
    
        
        buf.append(" }"); //close response:{       
        return buf.toString();
    }
    

    static String getResponseProvince( //province mapping file
            int companyID,
            int prodID,
            String provId, 
            String provAbbr, 
            String provNameEn,
            String provNameFr)
    {
        StringBuilder buf = new StringBuilder(" ");

        buf.append(" { "); //open response

        buf.append("\"companyId\"").append(" : \"").append(companyID).append("\", ");
        buf.append("\"productId\"").append(" : \"").append(prodID).append("\", ");
        buf.append("\"provinceId\"").append(" : \"").append(JsonUtil.cleanStringForJson(provId)).append("\", ");
        buf.append("\"provinceAbbr\"").append(" : \"").append(JsonUtil.cleanStringForJson(provAbbr)).append("\", ");
 
        buf.append("\"provNameEn\"").append(" : \"").append(JsonUtil.cleanStringForJson(provNameEn)).append("\", ");
        buf.append("\"provNameFr\"").append(" : \"").append(JsonUtil.cleanStringForJson(provNameFr)).append("\" ");
        
        buf.append(" }"); //close response:{       
        return buf.toString();
    }


    static String getResponseProdType( //province mapping file
            int prodTypeId, 
            String prodClass,
            String prodTypeEn, 
            String prodTypeFr,
            String prodTypeDescEn,
            String prodTypeDescFr)
    {
        StringBuilder buf = new StringBuilder(" ");

        buf.append(" { "); //open response

        buf.append("\"productTypeId\"").append(" : \"").append(prodTypeId).append("\", ");
        
        buf.append("\"productClass\"").append(" : \"").append(JsonUtil.cleanStringForJson(prodClass)).append("\", ");
        
        buf.append("\"productTypeGenericNameEn\"").append(" : \"").append(JsonUtil.cleanStringForJson(prodTypeEn)).append("\", ");
        buf.append("\"productTypeGenericNameFr\"").append(" : \"").append(JsonUtil.cleanStringForJson(prodTypeFr)).append("\", ");

        buf.append("\"productTypeDescEn\"").append(" : \"").append(JsonUtil.cleanStringForJson(prodTypeDescEn)).append("\", ");
        buf.append("\"productTypeDescFr\"").append(" : \"").append(JsonUtil.cleanStringForJson(prodTypeDescFr)).append("\" ");

        
        
        buf.append(" }"); //close response:{       
        return buf.toString();
    }
    
    
}
