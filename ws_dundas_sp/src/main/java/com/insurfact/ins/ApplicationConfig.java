/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2019 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.ins;

//import java.util.Set;
//import javax.ws.rs.core.Application;

/**
 *
 * <AUTHOR>
 */
//@javax.ws.rs.ApplicationPath("webresources")
public class ApplicationConfig /*extends Application*/ {

    /*@Override
    public Set<Class<?>> getClasses() {
        Set<Class<?>> resources = new java.util.HashSet<>();
        addRestResourceClasses(resources);
        return resources;
    }*/

    /**
     * Do not modify addRestResourceClasses() method.
     * It is automatically populated with
     * all resources defined in the project.
     * If required, comment out calling this method in getClasses().
     */
    /*private void addRestResourceClasses(Set<Class<?>> resources) {
        resources.add(com.insurfact.ins.Crit.class);
        resources.add(com.insurfact.ins.Crit1.class);
        resources.add(com.insurfact.ins.CritGroupMultiComp.class);
        resources.add(com.insurfact.ins.CritMultiComp.class);
        resources.add(com.insurfact.ins.CritSingleComp.class);
        resources.add(com.insurfact.ins.CritSingleCompAll.class);
        resources.add(com.insurfact.ins.CritSingleProd.class);
        resources.add(com.insurfact.ins.Life.class);
        resources.add(com.insurfact.ins.Life1.class);
        resources.add(com.insurfact.ins.LifeGroupMultiComp.class);
        resources.add(com.insurfact.ins.LifeMultiCompSp.class);
        //resources.add(com.insurfact.ins.LifeJointMulti.class);
        //resources.add(com.insurfact.ins.LifeJointSingleComp.class);
        resources.add(com.insurfact.ins.LifeMultiComp.class);
        resources.add(com.insurfact.ins.LifeSingleComp.class);
        resources.add(com.insurfact.ins.LifeSingleCompAll.class);
        resources.add(com.insurfact.ins.LifeSingleProd.class);
        resources.add(com.insurfact.ins.MappingFiles.CompProdMap.class);
    }*/
    
}
