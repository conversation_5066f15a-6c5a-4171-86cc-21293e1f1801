package com.insurfact.ins.config;

import com.insurfact.ins.dto.JwtAuthenticationResponse;
import com.insurfact.ins.security.AuthenticationService;
import com.insurfact.ins.security.JwtTokenProvider;
import com.insurfact.ins.service.SessionAuthService;
import com.insurfact.ins.dto.ValidatedJwtPrincipal;
import io.jsonwebtoken.Claims;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * Custom authentication filter that integrates with BOTH existing authentication systems:
 * 1. UserValidationDundas (legacy token system used by /webresources endpoints)
 * 2. JWT system (SessionAuthService + JwtTokenProvider used by /api/v1/auth endpoints)
 *
 * This filter only processes requests to /api/v1/advisors endpoints and creates
 * Spring Security Authentication objects to enable @PreAuthorize annotations.
 *
 * It does NOT interfere with existing authentication mechanisms used by other endpoints.
 *
 * <AUTHOR> zhu
 * @version 1.0
 * @since 2025-01-01
 */
@Component
public class CustomAuthenticationFilter extends OncePerRequestFilter {

    private static final Logger log = LoggerFactory.getLogger(CustomAuthenticationFilter.class);

    @Autowired
    private AuthenticationService authenticationService;

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    @Autowired
    private SessionAuthService sessionAuthService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        try {
            // Only process requests to /api/v1/advisors endpoints
            // This ensures we don't interfere with existing authentication mechanisms
            String requestPath = request.getRequestURI();
            if (!requestPath.startsWith("/api/v1/advisors")) {
                filterChain.doFilter(request, response);
                return;
            }

            // Check if Spring Security context already has authentication
            if (SecurityContextHolder.getContext().getAuthentication() != null) {
                filterChain.doFilter(request, response);
                return;
            }

            // Extract authentication information
            String sessionId = extractSessionId(request);
            String ipAddress = getClientIpAddress(request);

            log.debug("Processing authentication for path: {}, sessionId: {}, IP: {}", requestPath, sessionId, ipAddress);

            if (sessionId != null && !sessionId.trim().isEmpty()) {
                try {
                    // Use SessionAuthService to issue JWT from session
                    JwtAuthenticationResponse authResponse = sessionAuthService.issueTokenFromSession(sessionId);

                    if (authResponse.isAuthenticated() && authResponse.getToken() != null) {
                        String jwtToken = authResponse.getToken();

                        // Validate the generated JWT
                        ValidatedJwtPrincipal jwtPrincipal = jwtTokenProvider.validateTokenAndGetPrincipal(jwtToken);
                        if (jwtPrincipal.isValid()) {
                            Claims claims = jwtTokenProvider.getAllClaimsFromToken(jwtToken);
                            Long userId = claims.get("userId", Long.class);
                            String username = claims.get("username", String.class);

                            if (userId != null) {
                                Authentication authentication = createAuthentication(userId.intValue(), username, request);
                                SecurityContextHolder.getContext().setAuthentication(authentication);
                                log.debug("Session-based authentication successful for user ID: {}, username: {}", userId, username);
                            }
                        } else {
                            log.debug("JWT validation failed: {}", jwtPrincipal.getErrorMessage());
                        }
                    } else {
                        log.debug("Failed to issue JWT from session: {}, message: {}", sessionId, authResponse.getMessage());
                    }
                } catch (Exception e) {
                    log.debug("Session authentication failed for session {}: {}", sessionId, e.getMessage());
                }
            } else {
                log.debug("No session ID found in request");
            }

        } catch (Exception e) {
            log.error("Error during authentication: {}", e.getMessage(), e);
            // Clear security context on error
            SecurityContextHolder.clearContext();
        }

        filterChain.doFilter(request, response);
    }

    /**
     * Extracts the session ID from the request.
     * Checks multiple sources: Authorization header, token header, token parameter.
     */
    private String extractSessionId(HttpServletRequest request) {
        // Check Authorization header first
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }

        // Check token header (this is likely the session ID)
        String tokenHeader = request.getHeader("token");
        if (tokenHeader != null && !tokenHeader.trim().isEmpty()) {
            return tokenHeader;
        }

        // Check token parameter
        String tokenParam = request.getParameter("token");
        if (tokenParam != null && !tokenParam.trim().isEmpty()) {
            return tokenParam;
        }

        // Check session parameter
        String sessionParam = request.getParameter("sessionId");
        if (sessionParam != null && !sessionParam.trim().isEmpty()) {
            return sessionParam;
        }

        return null;
    }



    /**
     * Gets the client IP address, handling proxy headers.
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }

    /**
     * Creates a Spring Security Authentication object from the user ID and username.
     *
     * This method assigns default roles based on business logic.
     * In a real implementation, roles should be fetched from the database.
     */
    private Authentication createAuthentication(int userId, String username, HttpServletRequest request) {
        // Use username as principal if available, otherwise use user ID
        String principal = username != null ? username : String.valueOf(userId);

        // TODO: Fetch actual roles from database based on user ID
        // For now, assign default roles - this should be replaced with actual role lookup
        List<SimpleGrantedAuthority> authorities = getDefaultAuthorities(userId);

        // Create authentication token
        UsernamePasswordAuthenticationToken authentication =
            new UsernamePasswordAuthenticationToken(principal, null, authorities);

        // Add additional details
        authentication.setDetails(request);

        return authentication;
    }

    /**
     * Gets default authorities for a user.
     * This is a placeholder implementation that should be replaced
     * with actual role lookup from the database.
     */
    private List<SimpleGrantedAuthority> getDefaultAuthorities(int userId) {
        // TODO: Replace with actual database lookup
        // This is a temporary implementation for testing
        
        // For demonstration, assign roles based on user ID ranges
        // In production, this should query the database for actual user roles
        if (userId == 1 || userId == 2) {
            // Admin users
            return Arrays.asList(
                new SimpleGrantedAuthority("ROLE_ADMIN"),
                new SimpleGrantedAuthority("ROLE_USER")
            );
        } else if (userId >= 100 && userId < 200) {
            // Manager users
            return Arrays.asList(
                new SimpleGrantedAuthority("ROLE_MANAGER"),
                new SimpleGrantedAuthority("ROLE_USER")
            );
        } else if (userId >= 200 && userId < 300) {
            // AGA users
            return Arrays.asList(
                new SimpleGrantedAuthority("ROLE_AGA"),
                new SimpleGrantedAuthority("ROLE_USER")
            );
        } else {
            // Regular advisor users
            return Arrays.asList(
                new SimpleGrantedAuthority("ROLE_ADVISOR"),
                new SimpleGrantedAuthority("ROLE_USER")
            );
        }
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) throws ServletException {
        // Filter all requests - let doFilterInternal decide what to process
        return false;
    }
}
