package com.insurfact.ins.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Spring Security configuration for the Advisor Profile API.
 *
 * This configuration enables method-level security for @PreAuthorize annotations
 * while maintaining full compatibility with the existing authentication systems:
 * 1. UserValidationDundas (legacy token system)
 * 2. JWT system (SessionAuthService + JwtTokenProvider)
 *
 * The configuration is designed to be minimally invasive during the transition period.
 *
 * <AUTHOR> zhu
 * @version 1.0
 * @since 2025-01-01
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true)
public class SecurityConfig {

    @Autowired
    private CustomAuthenticationFilter customAuthenticationFilter;

    /**
     * Configures the security filter chain.
     * 
     * This configuration:
     * 1. Disables CSRF for API endpoints
     * 2. Sets session management to stateless
     * 3. Permits access to legacy endpoints during transition
     * 4. Secures new API endpoints
     * 5. Adds custom authentication filter
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            // Disable CSRF for API endpoints
            .csrf(csrf -> csrf.disable())
            
            // Set session management to stateless
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            
            // Configure authorization rules - very permissive during transition
            .authorizeHttpRequests(authz -> authz
                // Allow all requests - authentication will be handled by @PreAuthorize annotations
                // This ensures we don't interfere with existing authentication mechanisms
                .anyRequest().permitAll()
            );

        return http.build();
    }
}
