package com.insurfact.ins.enums;

public enum Language {
    ENGLISH(9, "English"),
    FRENCH(12, "French");

    private final int value;
    private final String name;

    Language(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static Language fromString(String text) {
        for (Language language : Language.values()) {
            if (language.name.equalsIgnoreCase(text)) {
                return language;
            }
        }
        throw new IllegalArgumentException("No constant with text " + text + " found");
    }

    public static Language fromValue(int value) {
        for (Language language : Language.values()) {
            if (language.value == value) {
                return language;
            }
        }
        throw new IllegalArgumentException("No constant with value " + value + " found");
    }
}
