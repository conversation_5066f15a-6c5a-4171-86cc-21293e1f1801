/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2019 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.ins;
 
import com.insurfact.iq4.ejb.EnhancedProductFacade;
import com.insurfact.iq4.ejb.IQ4EngineFacade;
import com.insurfact.iq4.ejb.IQ4ProductFacade;
import com.insurfact.iq4.ejb.PromotionsHelper;

import com.insurfact.sdk.properties.InsurfactSdkProperties;
import com.insurfact.sdk.utils.InsurfactPrivateException;
import java.io.IOException;
import java.util.Base64;  
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.ejb.EJB;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.insurfact.websecuring.UserValidationDundas;

/**
 * REST Web Service for Life Insurance quotes: multi-company, single-product.
 * Life1 is for reduced json output parameters (compared to life). Life1 and
 * life both include percentages in the json.
 * 
 * <!-- TODO future live2 could be for a specific ins cie.; life3 could be with
 * a consumer email and phone number in the URL; we may need a session id in the
 * URL, and a generic user id before we get the email address which will be our
 * user id after we get it; TODO we need an encryption key (in the URL?) diannys
 * is working on that -->
 * 
 * <p>
 * The origin ip is the identifier of the widget user (an insurfact client).
 * </p>
 *
 * <p>
 * For testing: <br>
 * http://localhost:8080/WS_INS/webresources/life1/6/500000/1979-01-01/M/N/QC
 * <br>
 * http://localhost:8181/WS_INS/webresources/life1/6/500000/1979-01-01/M/N/QC
 * </p>
 *
 * <p>
 * For production: <br>
 * http://webservices.insurfact.com/WS_INS/webresources/life1/6/500000/1979-01-01/M/N/QC
 * <br>
 * https://***********:8181/WS_INS/webresources/life1/6/500000/1979-01-01/M/N/QC
 * </p>
 *
 * <p>
 * <code><pre>
 * &#64;PathParam("product_type_id") String productType = Master product type
 * &#64;PathParam("face_amount") String faceAmount
 * &#64;PathParam("date_of_birth") String dateOfBirth
 * &#64;PathParam("gender") String gender
 * &#64;PathParam("smoking") String smoking
 * &#64;PathParam("province_code") String provCode
 * </pre></code>
 * </p>
 *
 * <p>
 * Location of war file: /Users/<USER>/NBProjs/WS_INS/dist/WS_INS.war
 * </p>
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/webresources/life1") // Base path for the controller
public class Life1 {

	private static final String TAG = Life1.class.getName();
 

	private static boolean DEBUG = false;

//    private static final boolean SEND_EMAIL_FOR_ANOMALIES = true;

	private static final String PRODUCT_CLASS = "LIFE";

	@EJB
	private IQ4ProductFacade productFacade;

	@EJB
	private EnhancedProductFacade enhancedProductFacade;

	@EJB
	private IQ4EngineFacade quoteFacade;

	@EJB
	private PromotionsHelper promotionsHelper; 

//    public Connection con = null;

	@Autowired
	private final UserValidationDundas userValidation;

    public Life1(UserValidationDundas userValidation) {
        this.userValidation = userValidation;
    }


    /**
	 * Retrieves representation of an instance of com.insurfact.ins.Life
	 *
	 * @param productTypeId
	 * @param faceAmount
	 * @param dateOfBirth
	 * @param gender
	 * @param smoking
	 * @param provCode
	 *
	 * @return an instance of java.lang.String
	 */

	@GetMapping(value = "/{product_type_id}/{face_amount}/{date_of_birth}/{gender}/{smoking}/{province_code}", produces = MediaType.APPLICATION_JSON_VALUE)
	@ResponseBody
	public String getJson(HttpServletRequest request, HttpServletResponse response,
			@PathVariable("product_type_id") String productTypeId, @PathVariable("face_amount") String faceAmount,
			@PathVariable("date_of_birth") String dateOfBirth, @PathVariable("gender") String gender,
			@PathVariable("smoking") String smoking, @PathVariable("province_code") String provCode,
			@RequestHeader("token") String tokenCli) {

		DEBUG = InsurfactSdkProperties.getBoolean("debug.enabled", false);
		String responseAll = "{\"response\": token does not match }";
		String ipAddress = request.getRemoteAddr();

		String urlOrigin = request.getHeader("origin");// changed to origin from Referer Jan 17th, 2022 aa
		if (urlOrigin != null) {
			if (urlOrigin.equals(""))
				urlOrigin = "noOrigin";
		} else
			urlOrigin = "noOrigin";

		int wsUserID = 0;
		try {
			wsUserID = userValidation.getWSUserID(tokenCli, ipAddress);
		} catch (InsurfactPrivateException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		if (wsUserID != 0) {

			Generic generic = new Generic(PRODUCT_CLASS, "PERCENTAGE", quoteFacade, enhancedProductFacade,
					productFacade, promotionsHelper);
			String responseValue = generic.getNoException(productTypeId, faceAmount, dateOfBirth, gender, smoking,
					provCode, "A");

			try {
				generic.saveQuote(ipAddress, wsUserID, "critSingleProd", faceAmount, dateOfBirth, gender, smoking,
						provCode, "", "", "", urlOrigin);
			} catch (IOException e) {
				System.out.println("161 LifeSingleCompAll IOException e=" + e.getMessage());
			} catch (InsurfactPrivateException e) {
				System.out.println("164 LifeSingleCompAll InsurfactPrivateException e=" + e.getMessage());
			}

			responseAll = "{\"response\": " + responseValue + " }";

			if (DEBUG) {
				System.out.println(TAG + ".getJson: returning $$" + responseAll + "$$");
			}
		}

		return responseAll;
	}

	/*
	 * * retrieves all the available Generic ProductType for a class and perm/term
	 * combo
	 */
//    private void updateAllProductTypes() {
//
//        QuoteParams qp = multiQP; //getQuoteParams();
//
////        System.out.println("** updateAllProductTypes : "+ iq4Mode  );
//        List<ProductType> productTypes = qp.getAllProductTypes();
//
//        if (DEBUG) {
//            if (productTypes != null) {
//                //LOG.log(Level.ALL, "qp.getAllProductTypes() returned productTypes size = " + productTypes.size());
//                System.out.println(TAG+".updateAllProductTypes: *qp.getAllProductTypes()* returned productTypes size = " + productTypes.size());
//            } else {
//                //LOG.log(Level.ALL, "qp.getAllProductTypes() returned productTypes = null");
//                System.out.println(TAG+".updateAllProductTypes: *qp.getAllProductTypes()* returned productTypes = null");
//
//            }
//        }
//
//        if (productTypes != null) {
//            productTypes.clear();
//        }
//
//        productTypes = productFacade.findActiveIQ4ProductType(qp.getInsuranceTypeAbbr(), qp.getTermOrPerm());
//
//        if (DEBUG) {
//            if (productTypes != null) {
//                //LOG.log(Level.ALL, "findActiveIQ4ProductType() returned productTypes size = " + productTypes.size());
//                System.out.println(TAG+".updateAllProductTypes: *findActiveIQ4ProductType()* returned productTypes size = " + productTypes.size());
//            } else {
//                //LOG.log(Level.ALL, "findActiveIQ4ProductType() returned productTypes = null");
//                System.out.println(TAG+".updateAllProductTypes: *findActiveIQ4ProductType()* returned productTypes = null");
//            }
//        }
//        qp.setAllProductTypes(productTypes);
//    }

	public String HMAC_SHA256(String secret, String message) {
		String hash = "";
		try {
			Mac sha256_HMAC = Mac.getInstance("HmacSHA256");
			SecretKeySpec secret_key = new SecretKeySpec(secret.getBytes(), "HmacSHA256");
			sha256_HMAC.init(secret_key);

			hash = Base64.getUrlEncoder().encodeToString(sha256_HMAC.doFinal(message.getBytes("UTF-8")));

		} catch (Exception e) {

		}
		return hash.trim();
	}

}
