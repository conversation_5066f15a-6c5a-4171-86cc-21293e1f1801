/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2019 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.ins;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.insurfact.iq4.ejb.EnhancedProductFacade;
import com.insurfact.iq4.ejb.IQ4EngineFacade;
import com.insurfact.iq4.ejb.IQ4ProductFacade;
import com.insurfact.iq4.ejb.PromotionsHelper;
import com.insurfact.sdk.properties.InsurfactSdkProperties;
import com.insurfact.sdk.utils.InsurfactPrivateException;
import com.insurfact.websecuring.UserValidationDundas;
import com.insurfact.wsutil.IQ4Info;
import jakarta.ejb.EJB;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.insurfact.wsutil.Util.getNearestAgeFromString;

/**
 * REST Web Service for Life Insurance quotes: multi-company, single-product.
 * Life1 is for reduced json output parameters (compared to life). Life1 and
 * life both include percentages in the json.
 *
 * 
 * <p>
 * The origin ip is the identifier of the widget user (an insurfact client).
 * </p>
 *
 * <p>
 * For testing: <br>
 * </p>
 * <AUTHOR>
 */
@RestController
@RequestMapping("/webresources/info") // Base path for the controller
public class Info {

	private static final String TAG = Info.class.getName();


	private static boolean DEBUG = false;

//    private static final boolean SEND_EMAIL_FOR_ANOMALIES = true;

	@Autowired
	private IQ4Info info;

	@Autowired
	private final UserValidationDundas userValidation;

    public Info(UserValidationDundas userValidation) {
        this.userValidation = userValidation;
    }


    /**
	 * Retrieves representation of an instance of com.insurfact.ins.Life
	 *
	 * @return an instance of java.lang.String
	 */

	@GetMapping(value = "/insurance-companies", produces = MediaType.APPLICATION_JSON_VALUE)
	@ResponseBody
	public String getJson(HttpServletRequest request, HttpServletResponse response, @RequestHeader("token") String tokenCli) {

		DEBUG = InsurfactSdkProperties.getBoolean("debug.enabled", false);
		String responseAll = "{\"response\": token does not match }";
		String ipAddress = request.getRemoteAddr();
		ObjectMapper objectMapper = new ObjectMapper();

		int wsUserID = 0;
		try {
			wsUserID = userValidation.getWSUserID(tokenCli, ipAddress);
		} catch (InsurfactPrivateException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		if (wsUserID != 0) {
			try {
				List<Map<String, String>> companyList = info.getCompanyNameByCompanyType("I");

				Map<String, Object> responseMap = new HashMap<>();
				if (companyList == null || companyList.isEmpty()) {
					responseMap.put("response", "No companies found");
				} else {
					responseMap.put("response", companyList);
				}

				responseAll = objectMapper.writeValueAsString(responseMap);

			} catch (Exception e) {
				e.printStackTrace();
				responseAll = "{\"response\": \"Error occurred while processing the request\"}";
			}
		}

		if (DEBUG) {
			System.out.println(TAG + ".getJson: returning $$" + responseAll + "$$");
		}

		return responseAll;
	}

    @GetMapping(value = "/insurance-companies/{date_of_birth}/{face_amount}/{type_class}", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public String getJson(HttpServletRequest request, HttpServletResponse response, @PathVariable("date_of_birth") String dateOfBirth,
						  @PathVariable("face_amount") String faceAmount, @PathVariable("type_class") String typeClass,
						  @RequestHeader("token") String tokenCli) {

        DEBUG = InsurfactSdkProperties.getBoolean("debug.enabled", false);
        String responseAll = "{\"response\": token does not match }";
        String ipAddress = request.getRemoteAddr();
        ObjectMapper objectMapper = new ObjectMapper();

        int wsUserID = 0;
        try {
            wsUserID = userValidation.getWSUserID(tokenCli, ipAddress);
        } catch (InsurfactPrivateException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

        if (wsUserID != 0) {
            try {
				int age = getNearestAgeFromString(dateOfBirth);

                List<Map<String, String>> companyList = info.getCompaniesbyAgeAndFaceAmount(age, Integer.parseInt(faceAmount), typeClass);

                Map<String, Object> responseMap = new HashMap<>();
                if (companyList == null || companyList.isEmpty()) {
                    responseMap.put("response", "No companies found");
                } else {
                    responseMap.put("response", companyList);
                }

                responseAll = objectMapper.writeValueAsString(responseMap);

            } catch (Exception e) {
                e.printStackTrace();
                responseAll = "{\"response\": \"Error occurred while processing the request\"}";
            }
        }

        if (DEBUG) {
            System.out.println(TAG + ".getJson: returning $$" + responseAll + "$$");
        }

        return responseAll;
    }

	@GetMapping(value = "/jointProductTypes/{face_amount}/{client1DOB}/{client2DOB}/{jointType}/{levelOrDecr}", produces = MediaType.APPLICATION_JSON_VALUE)
	@ResponseBody
	public String getJson(HttpServletRequest request, HttpServletResponse response, @PathVariable("face_amount") String faceAmount,
						  @PathVariable("client1DOB") String client1DateOfBirth, @PathVariable("client2DOB") String client2DateOfBirth,
						  @PathVariable("jointType") String typeClass, @PathVariable("levelOrDecr") String levelOrDecr,
						  @RequestHeader("token") String tokenCli) {

		DEBUG = InsurfactSdkProperties.getBoolean("debug.enabled", false);
		String responseAll = "{\"response\": token does not match }";
		String ipAddress = request.getRemoteAddr();
		ObjectMapper objectMapper = new ObjectMapper();

		int wsUserID = 0;
		try {
			wsUserID = userValidation.getWSUserID(tokenCli, ipAddress);
		} catch (InsurfactPrivateException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		if (wsUserID != 0) {
			try {
				List<Map<String, String>> ProductTypeList = info.getJointInsuranceTypesbyCondition(client1DateOfBirth, client2DateOfBirth, Integer.parseInt(faceAmount), typeClass, levelOrDecr	);

				Map<String, Object> responseMap = new HashMap<>();
				if (ProductTypeList == null || ProductTypeList.isEmpty()) {
					responseMap.put("response", "No product types found");
				} else {
					responseMap.put("response", ProductTypeList);
				}

				responseAll = objectMapper.writeValueAsString(responseMap);

			} catch (Exception e) {
				e.printStackTrace();
				responseAll = "{\"response\": \"Error occurred while processing the request\"}";
			}
		}

		if (DEBUG) {
			System.out.println(TAG + ".getJson: returning $$" + responseAll + "$$");
		}

		return responseAll;
	}
}
