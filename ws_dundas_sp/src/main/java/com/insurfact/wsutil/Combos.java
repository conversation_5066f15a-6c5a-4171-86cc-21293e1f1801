/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2019 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.wsutil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Set;

/**
 * AKA combos, one instance for Life and one for Critical Illness.
 * 
 * <pre>
T10 = 6 plus 7,8
T15 = 7, plus 6,8
T20= 8 plus 6,7

T25 = 129 plus 66, 214
T30 = 66 plus 129, 214
T35 = 214 plus 129, 66

T65 = 9 plus 127, 10
T70 = 127 plus 9, 10
T75= 10 plus 9,127
* 
10 Pay= 15 plus 16, 17
15 Pay = 16 plus 15, 17
20 Pay = 17 plus 15,16

* 
T100 = 11 plus 12, 18
T100 w/values = 12 plus 11, 18
Pay 65 = 18 plus 11, 12

* </pre>
 * 
 * <AUTHOR>
 */
public class Combos {
    
    /**
     * ex.: 6, a; 7, a; 8, a; 129, b; 66, b; 214, b; 
     * 
     * <p>key = product code</p>
     * <p>value = seriesId id</p>
     */
    final HashMap<Integer,String> SERIES = new HashMap<>();
    
    /**
     * ex.: 6, T10; 7, T15; 8, T20; 129, T25
     */
    final HashMap<Integer,String> LABELS = new HashMap<>();
    
    /**
     * ex.: a {6, 7, 8}, b {129, 66, 214}, 
     */
    final HashMap<String,ArrayList<Integer>> LISTS = new HashMap<String, ArrayList<Integer>>();
    
    protected void buildLists(){
        Set<Integer> keys = SERIES.keySet();
        for(Integer key: keys){
            // ex.: key = 6
            // ex.: a
            String seriesId = SERIES.get(key);
            // ex.: a {6, 7, 8}, b {129, 66, 214}, 
            if(LISTS.containsKey(seriesId)){
                // ex.: {6, 7, 8}
                List<Integer> l = LISTS.get(seriesId);
                if( ! l.contains(key)) l.add(key);
            }else{
                ArrayList<Integer> l = new ArrayList<>();
                l.add(key);
                LISTS.put(seriesId,l);
            }
        }
    }
    
    /**
     * ex.: input 6 gives 6, 7, 8
     * @param key product key, such as 6, or 129, or 10
     * @return array of string combo product keys
     */
    public List<Integer> getCombos(final Integer key){
        if(key==null)throw new IllegalArgumentException("key {"+key+"} is null");
        String series = SERIES.get(key);
        if(series==null)throw new IllegalArgumentException("key {"+key+"} is not in the data");
        return LISTS.get(series);
    }
    
    /**
     * ex.: input 6 gives 6, 7, 8
     * @param key product key, such as 6, or 129, or 10
     * @return array of string combo product keys
     */
    public List<Integer> getCombosNoKey(final Integer key){
        if(key==null)throw new IllegalArgumentException("key {"+key+"} is null");
        String series = SERIES.get(key);
        if(series==null)throw new IllegalArgumentException("key {"+key+"} is not in the data");
        List<Integer> l = LISTS.get(series);
        l.remove(key);
        return l;
    }
    
    /**
     * 
     * @param key ex.: 6, 10
     * @return a label such as "T10" or null if key is not present.
     */
    public String getLabel(final Integer key){
        if(key==null)throw new IllegalArgumentException("key {"+key+"} is null");
        return LABELS.get(key);
    }
}
