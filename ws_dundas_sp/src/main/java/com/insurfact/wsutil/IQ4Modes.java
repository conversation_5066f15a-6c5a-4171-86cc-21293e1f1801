/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2019 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.wsutil;

/**
 *
 * <AUTHOR>
 */
public enum IQ4Modes {
    SingleCompany(1),
    MultiCompany(2),
    SingleCompanyTestPlatform(3),
    JointSingleCompany(4),
    JointMultiCompany(5),
    Ranking(6),
    FullReport(7),
    ValuesReport(8),
    SingleCompanyMortgage(9),
    MultiCompanyMortgage(10),
    JointSingleCompanyMortgage(11),
    SingleJointCompanyTestPlatform(12), //new added 10/03/2021
    MultiValuesReport(13);
    


    private final int mode;

    IQ4Modes(int mode) {
        this.mode = mode;
    }

	public int getMode() {
		return mode;
	}
}
