package com.insurfact.wsutil;

/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2019 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */

/**
 *
 * <AUTHOR>
 */
public class JsonUtil {
    
    
    /**
     * examples: "" (empty), or null (should show as "null" in json output, or
     * "null", or "N/A", or "(no value)".
     */
    public static final String TO_RETURN_WHEN_NULL_STRING_FROM_DB = "";
    /**
     * examples: "" (empty), or "N/A", or "(no value)".
     */
    public static final String TO_RETURN_WHEN_EMPTY_STRING_FROM_DB = "";

    /**
     *
     * @param dirty
     * @return a version of input dirty with the carriage-returns replaced by a
     * space and the double-quotes replaced by an asterisk.
     */
    public static String cleanStringForJson(final String dirty) {
        if (dirty == null) {
            return TO_RETURN_WHEN_NULL_STRING_FROM_DB;
        }
        if (dirty.isEmpty()) {
            return TO_RETURN_WHEN_EMPTY_STRING_FROM_DB;
        }
        String a = dirty.replace("\n", " ");
        String b = a.replace("\"", "*");
        
        return removeControlCharacters(b);
    }
    
    @SuppressWarnings("unused")
	private static String replaceControlCharacters(final String s, final char ch){
        
        char[] chars = s.toCharArray();
        int i = 0;
        for(char c:chars){
            
            if(Character.isISOControl(c)){
                
                chars[i] = ch;
            }
            
            ++i;
        }
        
        return String.copyValueOf(chars);
    }
    
    private static String removeControlCharacters(final String s){
        
        char[] chars = s.toCharArray(); 
        //List<Character> good = new LinkedList<>();
        StringBuilder buf = new StringBuilder();
        boolean prevWasControl = false;
        for(char c:chars){
            
            if( ! Character.isISOControl(c)){
                
                if(prevWasControl && (Character.isSpaceChar(c) || Character.isWhitespace(c) )){
                    // don't keep space after control
                }else{
                    buf.append(c);
                }
                
                prevWasControl = false;
                
            }else{
                
                prevWasControl = true;
                
            }
             
        }
        
        return buf.toString();
    }
    
}
