/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2019 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.wsutil;

/**
 * Singleton.
 * 
 * <AUTHOR>
 */
public class CombosCritNew extends Combos{
    
    public static final CombosCritNew IT = new CombosCritNew();
    
    /*
    series a: 8,21,69
T10 - 8
T20 - 21
T25 - 69
    
    series b: 4,31,5
T75= 4
T75 - Paid up at Age 65 = 31
T100 = 5
    
    series c: 62,42,40
10 Pay - 62 
15 Pay - 42
20 Pay - 40
       */
    
    private CombosCritNew(){
        
        super();
        
        SERIES.put(8, "a");
        SERIES.put(21, "a");
        SERIES.put(69, "a");
        
        SERIES.put(4, "b");
        SERIES.put(31, "b");
        SERIES.put(5, "b");
        
        SERIES.put(62, "c");
        SERIES.put(42, "c");
        SERIES.put(40, "c");
        
        LABELS.put(8, "T10");
        LABELS.put(21, "T20");
        LABELS.put(69, "T25");
        
        LABELS.put(4, "T75");
        LABELS.put(31, "T75 - Paid up at Age 65");
        LABELS.put(5, "T100");
        
        LABELS.put(62, "10 Pay");
        LABELS.put(42, "15 Pay");
        LABELS.put(40, "20 Pay");
        
        buildLists();
        
    }
    
}
