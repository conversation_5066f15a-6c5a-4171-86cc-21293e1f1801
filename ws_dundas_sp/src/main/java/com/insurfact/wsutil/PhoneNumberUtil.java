package com.insurfact.wsutil;

/**
 * Class to handle phone number operations
 */
public class PhoneNumberUtil {
    private final String areaCode;
    private final String number;

    /**
     * Constructor for PhoneNumber
     * @param phoneNumber Complete phone number string
     */
    public PhoneNumberUtil(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.isEmpty()) {
            this.areaCode = "";
            this.number = "";
            return;
        }

        // Clean the phone number first (remove any non-digit characters)
        String cleanNumber = phoneNumber.replaceAll("[^0-9]", "");

        // Extract area code and number
        if (cleanNumber.length() >= 3) {
            this.areaCode = cleanNumber.substring(0, 3);
            this.number = cleanNumber.length() > 3 ? cleanNumber.substring(3) : "";
        } else {
            this.areaCode = "";
            this.number = cleanNumber;
        }
    }

    /**
     * Get the area code
     * @return area code of the phone number
     */
    public String getAreaCode() {
        return areaCode;
    }

    /**
     * Get the phone number without area code
     * @return phone number without area code
     */
    public String getNumber() {
        return number;
    }

    /**
     * Get the complete phone number
     * @return complete phone number including area code
     */
    public String getFullNumber() {
        if (areaCode.isEmpty() && number.isEmpty()) {
            return "";
        }
        return areaCode + number;
    }

    /**
     * Check if the phone number is valid
     * @return true if the phone number is valid
     */
    public boolean isValid() {
        return !areaCode.isEmpty() && !number.isEmpty();
    }
}