/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2019 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.wsutil;

/**
 * Singleton.
 * 
 * <AUTHOR>
 */
public class CombosCrit extends Combos{
    
    public static final CombosCrit IT = new CombosCrit();
    
    /*
    series a:
    
    T10 - 5 plus 128, 218
T20 - 128 plus 5, 218
T25 - 218 plus 5, 128
    
    series b:
    
T75= 28 (152) plus 120, 27
T75 - Paid up at Age 65 - 120 plus 27, 28
T100 = 27 plus 120, 28
    
    series c:
10 Pay - 199 plus 173, 162
15 Pay- 173 plus 199, 162
20 Pay - 162 plus 199, 173
    */
    private CombosCrit(){
        
        super();
        
        SERIES.put(5, "a");
        SERIES.put(128, "a");
        SERIES.put(218, "a");
        
        SERIES.put(28, "b");
        SERIES.put(120, "b");
        SERIES.put(27, "b");
        
        SERIES.put(199, "c");
        SERIES.put(173, "c");
        SERIES.put(162, "c");
        
        LABELS.put(5, "T10");
        LABELS.put(128, "T20");
        LABELS.put(218, "T25");
        
        LABELS.put(28, "T75");
        LABELS.put(120, "T75 - Paid up at Age 65");
        LABELS.put(27, "T100");
        
        LABELS.put(199, "10 Pay");
        LABELS.put(173, "15 Pay");
        LABELS.put(162, "20 Pay");
        
        buildLists();
        
    }
    
}
