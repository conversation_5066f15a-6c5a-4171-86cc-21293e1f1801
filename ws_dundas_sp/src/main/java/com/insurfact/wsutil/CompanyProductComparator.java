package com.insurfact.wsutil;

import com.insurfact.iq.domain.Product;
import java.util.Comparator;

/**
 *
 * <AUTHOR>
 */
public class CompanyProductComparator implements Comparator<com.insurfact.iq.domain.Product> {
 
    @Override
    public int compare(Product o1, Product o2) {
        
        String name1 = o1.getCompany().getName()+" "+o1.getName();
        String name2 = o2.getCompany().getName()+" "+o2.getName();
  
        return name1.compareTo( name2 );
    }
}
