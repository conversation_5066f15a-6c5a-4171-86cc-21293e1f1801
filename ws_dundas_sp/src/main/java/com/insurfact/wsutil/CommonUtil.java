package com.insurfact.wsutil;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.util.StringUtils;

import java.util.logging.Level;
import java.util.logging.Logger;

public class CommonUtil {
    private static final Logger LOGGER = Logger.getLogger(CommonUtil.class.getName());
    private static final String X_FORWARDED_FOR_HEADER = "X-Forwarded-For";

    /**
     * Generic method to parse string amount to numeric type
     * Supports Integer and Double types
     *
     * @param amount String amount to parse can contain {}, commas, or other formatting
     * @param type Class type to return (Double.class or Integer. Class)
     * @param <T> Generic type extending Number
     * @return Parsed number of specified types, or 0 if parsing fails
     */
    public static <T extends Number> T parseAmount(String amount, Class<T> type) {
        if (amount == null || amount.trim().isEmpty()) {
            return getDefaultValue(type);
        }

        try {
            // Clean the amount string: remove {}, commas and extra spaces
            String cleanAmount = amount.replaceAll("[{},\\s]", "");

            if (type.equals(Double.class)) {
                return type.cast(Double.parseDouble(cleanAmount));
            } else if (type.equals(Integer.class)) {
                // For integers, remove any decimal part if present
                if (cleanAmount.contains(".")) {
                    cleanAmount = cleanAmount.substring(0, cleanAmount.indexOf("."));
                }
                return type.cast(Integer.parseInt(cleanAmount));
            }

            throw new IllegalArgumentException("Unsupported number type: " + type.getName());

        } catch (NumberFormatException e) {
            LOGGER.log(Level.WARNING,
                    String.format("Failed to parse amount '%s' to %s", amount, type.getSimpleName()),
                    e);
            return getDefaultValue(type);
        }
    }

    /**
     * Get default value (zero) for numeric types
     *
     * @param type Class type (Double.class or Integer.class)
     * @param <T> Generic type extending Number
     * @return Zero value of the specified type
     */
    @SuppressWarnings("unchecked")
    private static <T extends Number> T getDefaultValue(Class<T> type) {
        if (type.equals(Double.class)) {
            return (T) Double.valueOf(0.0);
        } else if (type.equals(Integer.class)) {
            return (T) Integer.valueOf(0);
        }
        throw new IllegalArgumentException("Unsupported number type: " + type.getName());
    }

    public static String extractClientIpAddress(HttpServletRequest request) {
        String xForwardedForHeader = request.getHeader(X_FORWARDED_FOR_HEADER);
        if (StringUtils.hasText(xForwardedForHeader)) {

            String clientIp = xForwardedForHeader.split(",")[0].trim();
            if (StringUtils.hasText(clientIp)) {
                return clientIp;
            }
        }
        return request.getRemoteAddr();
    }
    public static String extractUrlOrigin(HttpServletRequest request) {
        String urlOrigin = request.getHeader("origin");
        return (urlOrigin == null || urlOrigin.isEmpty()) ? "noOrigin" : urlOrigin;
    }
}
