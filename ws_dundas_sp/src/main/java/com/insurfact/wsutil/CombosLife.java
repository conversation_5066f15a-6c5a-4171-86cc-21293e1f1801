/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2019 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.wsutil;

/**
 * Singleton.
 * 
 * <AUTHOR>
 */



public class CombosLife extends Combos{
    
    public static final CombosLife IT = new CombosLife();
    
    private CombosLife(){
        
        super();
        
        SERIES.put(6, "a");
        SERIES.put(7, "a");
        SERIES.put(8, "a");
        
        SERIES.put(129, "b");
        SERIES.put(66, "b");
        SERIES.put(214, "b");
        
        SERIES.put(9, "c");
        SERIES.put(127, "c");
        SERIES.put(10, "c");
        
        SERIES.put(11, "d");
        SERIES.put(12, "d");
        SERIES.put(18, "d");
        
        SERIES.put(15, "e");
        SERIES.put(16, "e");
        SERIES.put(17, "e");
        
        LABELS.put(6, "T10");
        LABELS.put(7, "T15");
        LABELS.put(8, "T20");
        
        LABELS.put(129, "T25");
        LABELS.put(66, "T30");
        LABELS.put(214, "T35");
        
        LABELS.put(9, "T65");
        LABELS.put(127, "T70");
        LABELS.put(10, "T75");
        
        LABELS.put(11, "T100");
        LABELS.put(12, "T100 w/values");
        LABELS.put(18, "Pay 65");
        
        LABELS.put(15, "10 Pay");
        LABELS.put(16, "15 Pay");
        LABELS.put(17, "20 Pay");
        
        buildLists();
        
    }
    
}
