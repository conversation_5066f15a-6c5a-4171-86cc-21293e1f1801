
package com.insurfact.wsutil;

import com.insurfact.iq.domain.Rider;
import java.util.Comparator;

/**
 *
 * <AUTHOR>
 */
public class IncludedRidersOrderComparator  implements Comparator<com.insurfact.iq.domain.Rider>{
    
    @Override
    public int compare(Rider o1, Rider o2) {
        if(o1.getRiderOrder()< o2.getRiderOrder()) {
            return -1;
        }
        else if(o1.getRiderOrder() > o2.getRiderOrder()) {
            return 1;
        }
        else {
            return 0;
        }

    }
}