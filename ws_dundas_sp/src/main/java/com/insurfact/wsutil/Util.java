/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2019 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.wsutil;
 
import com.insurfact.sdk.properties.InsurfactSdkProperties;
import static com.insurfact.sdk.properties.InsurfactSdkProperties.getDb1Triplet;
import static com.insurfact.sdk.properties.InsurfactSdkProperties.getDb2Triplet;
import com.insurfact.skynet.entity.Client;
import com.insurfact.skynet.entity.Contact;
import java.sql.Connection;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.Period;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.Properties;
import java.util.Set;

/**
 *
 * <AUTHOR>
 */
public class Util {

    //    /**
    //     * examples: "" (empty), or null (should show as "null" in json output, or
    //     * "null", or "N/A", or "(no value)".
    //     */
    //    public static final String TO_RETURN_WHEN_NULL_STRING_FROM_DB = "";
    //    /**
    //     * examples: "" (empty), or "N/A", or "(no value)".
    //     */
    //    public static final String TO_RETURN_WHEN_EMPTY_STRING_FROM_DB = "";
    //
    //    /**
    //     *
    //     * @param dirty
    //     * @return a version of input dirty with the carriage-returns replaced by a
    //     * space and the double-quotes replaced by an asterisk.
    //     */
    //    private static String cleanStringForJson(final String dirty) {
    //        if (dirty == null) {
    //            return TO_RETURN_WHEN_NULL_STRING_FROM_DB;
    //        }
    //        if (dirty.isEmpty()) {
    //            return TO_RETURN_WHEN_EMPTY_STRING_FROM_DB;
    //        }
    //        String a = dirty.replace("\n", " ");
    //        String b = a.replace("\"", "*");
    //
    //        return removeControlCharacters(b);
    //    }
    //
    //    private static String replaceControlCharacters(final String s, final char ch){
    //
    //        char[] chars = s.toCharArray();
    //        int i = 0;
    //        for(char c:chars){
    //
    //            if(Character.isISOControl(c)){
    //
    //                chars[i] = ch;
    //            }
    //
    //            ++i;
    //        }
    //
    //        return String.copyValueOf(chars);
    //    }
    //
    //    private static String removeControlCharacters(final String s){
    //
    //        char[] chars = s.toCharArray();
    //        int i = 0;
    //        //List<Character> good = new LinkedList<>();
    //        StringBuilder buf = new StringBuilder();
    //        boolean prevWasControl = false;
    //        for(char c:chars){
    //
    //            if( ! Character.isISOControl(c)){
    //
    //                if(prevWasControl && (Character.isSpaceChar(c) || Character.isWhitespace(c) )){
    //                    // don't keep space after control
    //                }else{
    //                    buf.append(c);
    //                }
    //
    //                prevWasControl = false;
    //
    //            }else{
    //
    //                prevWasControl = true;
    //
    //            }
    //
    //            ++i;
    //        }
    //
    //        return buf.toString();
    //    }
    /*
    "{\"response\":\""+responseString+"\"}"
    responseString example = [ {"fundatakey": key, "f2": v2, "f3": v3 ...}, {  }, {  } ]
     */
    /* *
     * Input product data: company, product name,
     * yearly premium, monthly premium, short desc en, short desc fr
     *
     * <p>
     * company name en
     * company name fr
     * product.getName(),
     * product.getNameFr(),
    province
    product.getMonthlyBaseSumPremium(),
    product.getAnnualBaseSumPremium(),
    product.getDescription(),
    product.getDescriptionFr()
     * </p>
     *
     * @return String formatted for JSON; example = {"rank": key, "f2": v2,
     * "f3": v3 ...}, { }, { }
     */
    //    static String getResponseValuesInJsonForOneResult(
    //        int rank,
    //        String companyNameEn,
    //        String companyNameFr,
    //        String productNameEn,
    //        String productNameFr,
    //        String province,
    //        String monthlyBaseSumPremium,
    //        String annualBaseSumPremium,
    //        String descEn,
    //        String descFr
    //    ) {
    //        StringBuilder buf = new StringBuilder("{ ");
    //
    //        buf.append("\"rank\"").append(" : \"").append(rank).append("\", ");
    //        buf.append("\"company_name_en\"").append(" : \"").append(cleanStringForJson(companyNameEn)).append("\", ");
    //        buf.append("\"company_name_fr\"").append(" : \"").append(cleanStringForJson(companyNameFr)).append("\", ");
    //        buf.append("\"product_name_en\"").append(" : \"").append(cleanStringForJson(productNameEn)).append("\", ");
    //        buf.append("\"product_name_fr\"").append(" : \"").append(cleanStringForJson(productNameFr)).append("\", ");
    //        buf.append("\"province\"").append(" : \"").append(cleanStringForJson(province)).append("\", ");
    //        buf.append("\"monthly_premium\"").append(" : \"").append(cleanStringForJson(monthlyBaseSumPremium)).append("\", ");
    //        buf.append("\"yearly_premium\"").append(" : \"").append(cleanStringForJson(annualBaseSumPremium)).append("\", ");
    //        buf.append("\"desc_en\"").append(" : \"").append(cleanStringForJson(descEn)).append("\", ");
    //        buf.append("\"desc_fr\"").append(" : \"").append(cleanStringForJson(descFr)).append("\"");
    //
    //        buf.append(" }");
    //
    //        return buf.toString();
    //    }
    public static final String getStringFromDouble(final double d) {
    	NumberFormat format = NumberFormat.getCurrencyInstance(Locale.CANADA);
        return format.format(d);
    }

    public static String getTextOnDb1Con(final boolean isProdDb, final Connection con) {
        String inProdTxt = isProdDb ? "" : "not ";
        String[] triplet = getDb1Triplet(isProdDb);
        String login = "The URL to the first instance: {" + triplet[0] + "} login {" + triplet[1] + "} (password not show). ";
        String details = "";
        try {
            details = getConText(con);
        } catch (Throwable ex) {
            return ex.getMessage();
        }
        return "The DB connection is " + inProdTxt + "to the production DB. " + login + details;
    }
    

    public static final String getTextOnDb1Connection(final Connection con) {
        boolean isProdDb = InsurfactSdkProperties.isForProdDb();
        return getTextOnDb1Con(isProdDb, con);
    }

    public static String getTextOnDb2Con(final boolean isProdDb, final Connection con) {
        String inProdTxt = isProdDb ? "" : "not ";
        String[] triplet = getDb2Triplet(isProdDb);
        String login = "The URL to the second instance: {" + triplet[0] + "} login {" + triplet[1] + "} (password not show). ";
        String details = "";
        try {
            details = getConText(con);
        } catch (Throwable ex) {
            return ex.getMessage();
        }
        return "The DB connection is " + inProdTxt + "to the production DB. " + login + details;
    }
    

    public static final String getTextOnDb2Connection(final Connection con) {
        boolean isProdDb = InsurfactSdkProperties.isForProdDb();
        return getTextOnDb2Con(isProdDb, con);
    }

    public static Date get30YearsOld() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.YEAR, -35);
        int month = cal.get(Calendar.MONTH);
        if (month >= 5) {
            cal.set(Calendar.MONTH, 5);
        } else {
            cal.set(Calendar.MONTH, 0);
        }
        cal.set(Calendar.DATE, 1);
        return cal.getTime();
    }

    public static Contact create30YearsOldMaleContact() {
        Contact contact = new Contact();
        contact.setGender(1); // 1= male
        contact.setBirthDate(get30YearsOld());
        contact.setSalutation(0); // unknow
        //        contact.setPreferredLanguage(applicationBean.getUsers().getContact().getPreferredLanguage());
        contact.setContactType(10); // client type
        contact.setContactSource(8); // IQ4
        contact.setMaritalStatus(0); // unknown
        return contact;
    }

    //com.insurfact.skynet.entity.Contact
    public static String getContactInfo(final Contact contact) {
        StringBuilder buf = new StringBuilder("com.insurfact.skynet.entity.Contact info:");
        buf.append(" birth date {").append(contact.getBirthDate()).append("}");
        buf.append(" gender {").append(contact.getGender()).append("}");
        buf.append(" com.insurfact.skynet.entity.Client {").append(contact.getClient()).append("}");
        //com.insurfact.skynet.entity.Client
        Client client = contact.getClient();
        if (client != null) {
            buf.append(" Client info:");
            buf.append(" actual age: {").append(client.getActualAge()).append("}");
            buf.append(" nearest age: {").append(client.getNearestAge()).append("}");
            buf.append(" smoker: {").append(client.getSmoker()).append("}");
        }
        return buf.toString();
    }

    public static int getProvinceCodeInt(final String prov) {
        switch (prov) {
            case "AB":
                return 80; //"Province.Alberta";
            case "BC":
                return 81; //"Province.British.Columbia";
            case "MB":
                return 82; //"Province.Manitoba";
            case "NB":
                return 83; //"Province.New.Brunswick";
            case "NL":
                return 84; //"Province.Newfoundland";
            case "NT":
                return 85; //"Province.Northwest.Territories";
            case "NS":
                return 86; //"Province.Nova.Scotia";
            case "NU":
                return 87; //"Province.Nunavut";
            case "ON":
                return 88; //"Province.Ontario";
            case "PE":
                return 89; //Province.Prince.Edward.Island";
            case "QC":
                return 90; //"Province.Quebec";
            case "SK":
                return 91; //"Province.Saskatchewan";
            case "YT":
                return 92; //"Province.Yukon.Territory";
        }
        return 0;
    }

    /**
     * Calculates actual age. Note that the month parameter should be
     * zero-based; e.g. Jan=0, Feb=1, ...
     *
     * @param month
     * @param date
     * @param year
     * @return age
     */
    
    public static int calcActualAge(int month, int date, int year) {
		Calendar bday = Calendar.getInstance();
		bday.set(year, month, date);
		Calendar today = Calendar.getInstance();
		if (bday.compareTo(today) >= 0) {
			// System.out.println("*** calcActualAge - born after today!");
			return 0;
		}
		int age = today.get(Calendar.YEAR) - bday.get(Calendar.YEAR);
		if (today.get(Calendar.MONTH) < bday.get(Calendar.MONTH)) {
			--age;
		} else if (today.get(Calendar.MONTH) == bday.get(Calendar.MONTH)) {
			if (today.get(Calendar.DATE) < bday.get(Calendar.DATE)) {
				--age;
			}
		}
		if (age < 0) {
			age = 0;
		}
		// System.out.println("**** calcActualAge : "+ age);
		return age;
	}

    public static int actualAge(int bYr, int bMnt, int bDay) {
		// if(pr.bYr.value!='') {
		Calendar cal = Calendar.getInstance();
		Calendar bdayCal = Calendar.getInstance();
		bdayCal.set(bYr, bMnt - 1, bDay);
		// if(isIE4)
		// age = today.getYear()-pr.bYr.value;
		// else
		int age = cal.get(Calendar.YEAR) - bYr;
		age = age - 1900;
		if (cal.get(Calendar.MONTH) < (bMnt - 1)) {
			age--;
		} else if (cal.get(Calendar.MONTH) == (bMnt - 1)) {
			if (cal.compareTo(bdayCal) < 0) {
				age--;
			}
		}
		// actAge = age;
		// }
		return age;
	}

    //bDay bMnt bYr  actAge nearAge
    public static int nearestAge(int bYr, int bMnt, int bDay) {
		// Date today = new Date();
		// today.setMonth(today.getMonth()+7); //switching the month diff to 6 months as
		// per LG jan 24
		// today.setMonth(today.getMonth() + 6); //switching the month diff to 6 months
		// as per LG jan 24
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.MONTH, 6);
		Calendar bdayCal = Calendar.getInstance();
		bdayCal.set(bYr, bMnt - 1, bDay);
		int age = cal.get(Calendar.YEAR) - bdayCal.get(Calendar.YEAR);
		age = age - 1900;
		if (cal.get(Calendar.MONTH) < bdayCal.get(Calendar.MONTH)) {
			age--;
		} else if (cal.get(Calendar.MONTH) == bdayCal.get(Calendar.MONTH)) {
			if (cal.compareTo(bdayCal) < 0) {
				age--;
			}
		}
		return age;
	}
    /* *
     * PUT method for updating or creating an instance of Life
     * @ param content representation for the resource
     */
    //    @ PUT
    //    @ Consumes(MediaType.APPLICATION_JSON)
    //    public void putJson(String content) {
    //    }

    /**
     * Calculates nearest age. Note that the month parameter should be
     * zero-based; e.g. Jan=0, Feb=1, ...
     *
     * @param month
     * @param date
     * @param year
     */

    public static int calcNearestAge(int month, int date, int year) {
      int age = calcActualAge(month, date, year);      

      Calendar today = Calendar.getInstance();
      Calendar bday = Calendar.getInstance();
      bday.set(year, month, date);

      if(bday.compareTo(today) >= 0 ){
//          System.out.println("*** calcNearestAge - born after today!");
          return 0;
      }
      
      //if birthday month has passed
      if(bday.get(Calendar.MONTH) < today.get(Calendar.MONTH))
      {
         int monthsUntilNext = 12 - today.get(Calendar.MONTH) + bday.get(Calendar.MONTH);
         
         //if number of months until next birthday < 6, then increment age
         if(monthsUntilNext < 6)
            age++;
         else if (monthsUntilNext == 6)
         {
            //if bday date is less than todays date - meaning the 6 month point has passed - then increment age
            if(bday.get(Calendar.DATE) < today.get(Calendar.DATE))
               age++;
         }
         
      }
      //birthday month has NOT yet passed
      else {
         int monthsUntilNext = bday.get(Calendar.MONTH) - today.get(Calendar.MONTH);
         
         //if birthday is this month
         if(monthsUntilNext == 0) {
            //if date has not yet passed
            if(today.get(Calendar.DATE) < bday.get(Calendar.DATE))
               age++;
         }
         else if(monthsUntilNext < 6) {
            age++;
         }
         else if(monthsUntilNext == 6) {
            //if bday date is less than todays date - meaning the 6 month point has not passed - then increment age
            if(bday.get(Calendar.DATE) < today.get(Calendar.DATE))
               age++;
         }

      }
      
//      System.out.println("**** calcNearestAge : "+ age);
      
      
      return age;
   }

    /**
     * Calculates the nearest age based on a birth date string in "yyyy-MM-dd" format.
     *
     * @param birthDateString the birth date as a string in "yyyy-MM-dd" format
     * @return the age, rounded to the nearest whole number
     */
    public static int getNearestAgeFromString(String birthDateString) {
        // Parse the birth date string into a LocalDate object
        LocalDate birthDate = LocalDate.parse(birthDateString);

        // Get today's date
        LocalDate today = LocalDate.now();

        // Calculate the number of days between the birth date and today
        long daysBetween = ChronoUnit.DAYS.between(birthDate, today);

        // Convert the number of days into years (taking leap years into account)
        double ageInYears = daysBetween / 365.25;

        // Round the age to the nearest whole number and return it
        return (int) Math.round(ageInYears);
    }

    /**
     * Calculates the real age based on a birth date string in "yyyy-MM-dd" format.
     *
     * @param birthDateString the birth date as a string in "yyyy-MM-dd" format
     * @return the actual age in full years
     */
    public static int getActualAgeFromString(String birthDateString) {
        // Parse the birth date string into a LocalDate object
        LocalDate birthDate = LocalDate.parse(birthDateString);

        LocalDate today = LocalDate.now();

        return Period.between(birthDate, today).getYears();
    }

    
    public static String getConText(final Connection con) {
        if (con == null) {
            return "The database connection is null.";
        }
        StringBuilder buf = new StringBuilder("Database connection info: ");
        try {
            Properties props = con.getClientInfo();
            Set<Object> keys = props.keySet();
            for (Object key : keys) {
                String value = props.getProperty((String) key);
                buf.append(key).append(" = ").append(value).append("; ");
            }
        } catch (Exception ex) {
            buf.append(ex.getMessage());
        }
        try {
            buf.append("is closed = ").append(con.isClosed());
        } catch (SQLException ex) {
            buf.append("raised ").append(ex.getMessage());
        }
        try {
            buf.append("; is read-only = ").append(con.isReadOnly());
        } catch (SQLException ex) {
            buf.append("raised ").append(ex.getMessage());
        }
        return buf.toString();
    }

    /**
     *
     * @param client Client instance, could be null
     * @return selected data in a String
     */
    public static final String getClientData(final com.insurfact.iq.domain.Client client) {
        if (client == null) {
            return "Client value is null";
        }
        StringBuilder buf = new StringBuilder();
        buf.append("Client data:\n  actual age {").append(client.getActualAge()).append("}");
        buf.append("\n  birth date day of month {").append(client.getBirthDate()).append("}");
        buf.append("\n  birth month {").append(client.getBirthMonth()).append("}");
        buf.append("\n  birth year {").append(client.getBirthYear()).append("}");
        buf.append("\n  Full Birth Date {").append(client.getFullBirthDate()).append("}"); // <== from URL; test#1: 1979-01-01");
        buf.append("\n  Nearest Age {").append(client.getNearestAge()).append("}");
        buf.append("\n  gender {").append(client.getGender()).append("}"); // <== from URL; test#1: M");
        buf.append("\n  class id {").append(client.getClassId()).append("}");
        buf.append("\n  Province ID {").append(client.getProvinceID()).append("}"); // <== from URL; test#1: QC");
        buf.append("\n  isNonSmoker {").append(client.isNonSmoker()).append("}"); // <== from URL; test#1: N ==> true");
        return buf.toString();
    }
    
}
