/*
 * InsurFact Connect Inc.
 * Copyright (c) 2010-2019 All Right Reserved, http://www.insurfact.com
 * 
 * This source is subject to the InsurFact Connect Inc License.
 * Please see the License.txt file for more information.
 * Or email us at: <EMAIL>
 * All other rights reserved.
 * 
 * THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY 
 * KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
 * PARTICULAR PURPOSE.
 */
package com.insurfact.wsutil;

/**
 * Singleton.
 * 
 * <AUTHOR>
 */

/*
9  = T10,
10 = T15,
6  = T20

22 = T25
15 = T30
38 = T35

1 = T65
19 = T70
14 = T75

16 = T100
17 = T100 w/csv
12 = Pay to 65

11 = 10 pay
2 = 15 pay
3 = 20 pay
*/

public class CombosLifeNew extends Combos{
    
    public static final CombosLifeNew IT = new CombosLifeNew();
    
    private CombosLifeNew(){
        
        super();
/* new for Dundas only
16 = T100
17 = T100 w/csv


/// 1,2,3,12, new-> 18 , 28
1 = T65
2 = 15 pay
3 = 20 pay
12 = Pay to 65
*/
        SERIES.put(9, "a");
        SERIES.put(10, "a");
        SERIES.put(6, "a");
        
        SERIES.put(22, "b");
        SERIES.put(15, "b");
        SERIES.put(38, "b");
        SERIES.put(82, "b");
        
        SERIES.put(1, "c");
        SERIES.put(19, "c");
        SERIES.put(14, "c");
        
        SERIES.put(16, "d");
        SERIES.put(17, "d");
        //SERIES.put(12, "d");
        
        //SERIES.put(11, "e");
        //SERIES.put(2, "e");
        //SERIES.put(3, "e");
        
        
        SERIES.put(2, "e");
        SERIES.put(3, "e");
        SERIES.put(11, "e");
        SERIES.put(12, "e");
        SERIES.put(18, "e");
        SERIES.put(28, "e");
        
        LABELS.put(9, "T10");
        LABELS.put(10, "T15");
        LABELS.put(6, "T20");
        
        LABELS.put(22, "T25");
        LABELS.put(15, "T30");
        LABELS.put(38, "T35");
        LABELS.put(82, "T40");
        
        LABELS.put(1, "T65");
        LABELS.put(19, "T70");
        LABELS.put(14, "T75");
        
        LABELS.put(16, "T100");
        LABELS.put(17, "T100 w/values");
        //LABELS.put(12, "Pay 65");
        
       // LABELS.put(11, "10 Pay");
//        LABELS.put(2, "15 Pay");
//        LABELS.put(3, "20 Pay");
        
        LABELS.put(2, "15 Pay");
        LABELS.put(3, "20 Pay");
        LABELS.put(11, "10 Pay");
        LABELS.put(12, "Pay 65");
        LABELS.put(18, "Pay 25");  
        LABELS.put(28, "Pay 85"); 
        
        buildLists();
        
    }
    
}
