# Test configuration for Advisor Profile API

# Logging configuration for testing
logging.level.com.insurfact.ins=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.web=DEBUG

# Disable security for some tests
spring.security.enabled=false

# JWT configuration for testing
jwt.secret=test-secret-key-for-testing-only
jwt.expiration=3600000

# Test-specific settings
spring.jpa.show-sql=true
spring.jpa.hibernate.ddl-auto=none

# Disable banner in tests
spring.main.banner-mode=off

# Test database initialization
spring.sql.init.mode=never
