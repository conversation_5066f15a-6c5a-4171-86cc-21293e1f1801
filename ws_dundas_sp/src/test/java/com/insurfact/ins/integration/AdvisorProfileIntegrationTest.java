package com.insurfact.ins.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.insurfact.ins.dto.AdvisorProfileDTO;
import com.insurfact.ins.security.SessionAuthService;
import com.insurfact.ins.security.JwtTokenProvider;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for Advisor Profile API.
 * 
 * These tests verify the complete flow from HTTP request to database
 * and back to HTTP response.
 * 
 * <AUTHOR> zhu
 * @version 1.0
 * @since 2025-01-01
 */
@SpringBootTest
@AutoConfigureTestMvc
@ActiveProfiles("test")
class AdvisorProfileIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private SessionAuthService sessionAuthService;

    @MockBean
    private JwtTokenProvider jwtTokenProvider;

    @Test
    void getAdvisorProfile_WithValidSession_Success() throws Exception {
        // Given
        String sessionId = "test-session-123";
        String jwtToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."; // Mock JWT
        
        // Mock session authentication
        JwtAuthenticationResponse authResponse = new JwtAuthenticationResponse(true, jwtToken, 12345L, "Success");
        when(sessionAuthService.issueTokenFromSession(sessionId)).thenReturn(authResponse);
        
        // Note: This test requires a test database with sample data
        // For now, we'll test the endpoint structure and error handling
        
        // When & Then
        mockMvc.perform(get("/api/v1/advisors/12345")
                .header("token", sessionId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));
    }

    @Test
    void getAdvisorProfile_WithoutAuthentication_Forbidden() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/advisors/12345")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isForbidden());
    }

    @Test
    void getAdvisorProfile_InvalidAdvisorId_BadRequest() throws Exception {
        // Given
        String sessionId = "test-session-123";
        String jwtToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...";
        
        JwtAuthenticationResponse authResponse2 = new JwtAuthenticationResponse(true, jwtToken, 12345L, "Success");
        when(sessionAuthService.issueTokenFromSession(sessionId)).thenReturn(authResponse2);
        
        // When & Then
        mockMvc.perform(get("/api/v1/advisors/invalid")
                .header("token", sessionId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    void getAdvisorProfile_NonExistentAdvisor_NotFound() throws Exception {
        // Given
        String sessionId = "test-session-123";
        String jwtToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...";
        
        JwtAuthenticationResponse authResponse3 = new JwtAuthenticationResponse(true, jwtToken, 12345L, "Success");
        when(sessionAuthService.issueTokenFromSession(sessionId)).thenReturn(authResponse3);
        
        // When & Then
        mockMvc.perform(get("/api/v1/advisors/99999")
                .header("token", sessionId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.error").value("Resource Not Found"))
                .andExpect(jsonPath("$.message").value("Advisor with ID '99999' not found"));
    }

    @Test
    void checkAdvisorExists_ValidId_Success() throws Exception {
        // Given
        String sessionId = "test-session-123";
        String jwtToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...";
        
        JwtAuthenticationResponse authResponse4 = new JwtAuthenticationResponse(true, jwtToken, 12345L, "Success");
        when(sessionAuthService.issueTokenFromSession(sessionId)).thenReturn(authResponse4);
        
        // When & Then
        mockMvc.perform(get("/api/v1/advisors/12345/exists")
                .header("token", sessionId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void checkAdvisorExists_InvalidId_NotFound() throws Exception {
        // Given
        String sessionId = "test-session-123";
        String jwtToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...";
        
        JwtAuthenticationResponse authResponse5 = new JwtAuthenticationResponse(true, jwtToken, 12345L, "Success");
        when(sessionAuthService.issueTokenFromSession(sessionId)).thenReturn(authResponse5);
        
        // When & Then
        mockMvc.perform(get("/api/v1/advisors/99999/exists")
                .header("token", sessionId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

    @Test
    void apiDocumentation_SwaggerEndpoint_Accessible() throws Exception {
        // When & Then
        mockMvc.perform(get("/v3/api-docs")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));
    }

    @Test
    void errorHandling_GlobalExceptionHandler_ReturnsStandardFormat() throws Exception {
        // Given
        String sessionId = "test-session-123";
        String jwtToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...";
        
        JwtAuthenticationResponse authResponse6 = new JwtAuthenticationResponse(true, jwtToken, 12345L, "Success");
        when(sessionAuthService.issueTokenFromSession(sessionId)).thenReturn(authResponse6);
        
        // When & Then - Test 404 error format
        MvcResult result = mockMvc.perform(get("/api/v1/advisors/99999")
                .header("token", sessionId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andReturn();

        String responseBody = result.getResponse().getContentAsString();
        assertTrue(responseBody.contains("\"status\":404"));
        assertTrue(responseBody.contains("\"error\":\"Resource Not Found\""));
        assertTrue(responseBody.contains("\"path\":\"/api/v1/advisors/99999\""));
    }

    @Test
    void responseFormat_ValidAdvisor_MatchesAPIContract() throws Exception {
        // Given
        String sessionId = "test-session-123";
        String jwtToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...";
        
        JwtAuthenticationResponse authResponse7 = new JwtAuthenticationResponse(true, jwtToken, 12345L, "Success");
        when(sessionAuthService.issueTokenFromSession(sessionId)).thenReturn(authResponse7);
        
        // Note: This test assumes there's a test advisor with ID 1 in the test database
        // In a real test environment, you would set up test data
        
        // When & Then
        MvcResult result = mockMvc.perform(get("/api/v1/advisors/1")
                .header("token", sessionId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.advisorId").exists())
                .andExpect(jsonPath("$.advisorCode").exists())
                .andExpect(jsonPath("$.status").exists())
                .andExpect(jsonPath("$.advisorType").exists())
                .andExpect(jsonPath("$.hasLoginAccount").exists())
                .andReturn();

        // Verify the response can be deserialized to our DTO
        String responseBody = result.getResponse().getContentAsString();
        AdvisorProfileDTO advisor = objectMapper.readValue(responseBody, AdvisorProfileDTO.class);
        
        assertNotNull(advisor);
        assertNotNull(advisor.getAdvisorId());
        assertNotNull(advisor.getStatus());
    }

    @Test
    void authentication_DifferentTokenFormats_AllSupported() throws Exception {
        // Given
        String sessionId = "test-session-123";
        String jwtToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...";
        
        JwtAuthenticationResponse authResponse8 = new JwtAuthenticationResponse(true, jwtToken, 12345L, "Success");
        when(sessionAuthService.issueTokenFromSession(sessionId)).thenReturn(authResponse8);
        
        // Test 1: Authorization header
        mockMvc.perform(get("/api/v1/advisors/1")
                .header("Authorization", "Bearer " + sessionId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
        
        // Test 2: Token header
        mockMvc.perform(get("/api/v1/advisors/1")
                .header("token", sessionId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
        
        // Test 3: Token parameter
        mockMvc.perform(get("/api/v1/advisors/1")
                .param("token", sessionId)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }
}
